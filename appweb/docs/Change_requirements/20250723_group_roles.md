# 需求 [group_roles]

## 反馈
inreal admin可以设置vip和realtor
我们需要给group加roles，通过group role来控制权限
比如这个增加一个VIP Admin role可以给group。这个group的成员都可以管理VIP/realor

## 需求提出人:    Rain,Fred
## 修改人：       Liu rui

## 提出日期:      2025-07-19

## 原因
之前未提供


## 解决办法
1. admin可以管理group的权限
2. 权限检查时，判断用户权限，如果没有，获取用户所在的group，查看group是否有对应的权限，用户个人和所有组有对应权限都认为用户有该权限

## 是否需要补充UT

1. 不需要补充UT

## 确认日期:    2025-07-22

## online-step

1. 重启server
2. 将这个需求整体的修改方案添加到  文件的底部，并将这次对话内容保存到 文件