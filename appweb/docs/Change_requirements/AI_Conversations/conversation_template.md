# AI对话记录 - [主题]

**日期**: YYYY-MM-DD  
**参与者**: 用户 & Augment Agent  
**主题**: [具体主题描述]  
**目标**: [对话的主要目标]  
**相关文件**: [如有相关代码文件或文档，在此列出]  

## 对话概要
[简要描述对话的主要内容、背景和最终结果，2-3句话概括]

## 对话详情

### 用户问题 1
> [用户的具体问题或请求，使用引用格式]

**上下文信息**：
- 项目状态：[当前项目状态]
- 技术栈：[相关技术栈]
- 约束条件：[如有特殊要求或限制]

### AI回答 1
[AI的回答要点，使用结构化格式]

**主要建议**：
1. [建议1]
2. [建议2]
3. [建议3]

**代码示例**（如有）：
```javascript
// 示例代码
function example() {
    console.log("示例代码");
}
```

**相关资源**：
- [相关文档链接]
- [参考资料]

---

### 用户问题 2
> [继续记录后续问题]

### AI回答 2
[继续记录AI的回答]

---

## 关键收获

### 技术收获
- [技术相关的关键收获1]
- [技术相关的关键收获2]

### 沟通收获
- [沟通方式的改进点1]
- [沟通方式的改进点2]

### 最佳实践
- [总结出的最佳实践1]
- [总结出的最佳实践2]

## 决策记录

### 重要决策
| 决策点 | 选择的方案 | 理由 | 影响 |
|--------|------------|------|------|
| [决策1] | [选择的方案] | [决策理由] | [预期影响] |
| [决策2] | [选择的方案] | [决策理由] | [预期影响] |

### 待确认事项
- [ ] [需要进一步确认的事项1]
- [ ] [需要进一步确认的事项2]

## 后续行动

### 立即行动（今天完成）
- [ ] [紧急任务1]
- [ ] [紧急任务2]

### 短期行动（本周完成）
- [ ] [短期任务1]
- [ ] [短期任务2]

### 长期行动（本月完成）
- [ ] [长期任务1]
- [ ] [长期任务2]

## 相关对话记录
- [YYYYMMDD_相关对话主题.md](./YYYYMMDD_相关对话主题.md)
- [其他相关对话记录]

## 问题和解决方案

### 遇到的问题
1. **问题描述**：[具体问题]
   - **解决方案**：[采用的解决方案]
   - **效果**：[解决效果]

2. **问题描述**：[具体问题]
   - **解决方案**：[采用的解决方案]
   - **效果**：[解决效果]

## 改进建议

### 对AI沟通的改进
- [如何更好地与AI沟通的建议]
- [问题描述方式的改进]

### 对工作流程的改进
- [工作流程优化建议]
- [效率提升方法]

## 附录

### 相关代码片段
```javascript
// 重要的代码片段
```

### 相关配置
```json
{
  "重要的配置": "配置值"
}
```

### 参考资料
- [外部文档链接]
- [相关技术文档]
- [学习资源]

---

**记录人**: [记录人姓名]  
**最后更新**: YYYY-MM-DD  
**状态**: [进行中/已完成/需要跟进]
