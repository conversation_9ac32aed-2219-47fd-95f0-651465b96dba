# AI对话记录目录

本目录用于存储与Augment Agent的对话记录，以提高沟通效率和知识积累。

## 目录结构

```
AI_Conversations/
├── README.md                    # 本文件，目录说明
├── conversation_template.md     # 对话记录模板
├── conversation_index.md        # 对话索引
└── YYYYMMDD_topic_name.md      # 具体对话记录
```

## 文件命名规范

### 对话记录文件
格式：`YYYYMMDD_ai_conversation_[主题关键词].md`

示例：
- `20250703_ai_conversation_sharing_guide.md` - 对话分享指南讨论
- `20250704_ai_conversation_code_review.md` - 代码审查相关讨论
- `20250705_ai_conversation_bug_fix.md` - Bug修复讨论

### 主题关键词建议
- `sharing_guide` - 分享相关
- `code_review` - 代码审查
- `bug_fix` - Bug修复
- `feature_dev` - 功能开发
- `optimization` - 性能优化
- `architecture` - 架构设计
- `deployment` - 部署相关
- `testing` - 测试相关
- `documentation` - 文档相关
- `troubleshooting` - 问题排查

## 使用指南

### 1. 开始新对话记录
1. 复制 `conversation_template.md` 模板
2. 按命名规范重命名文件
3. 填写对话基本信息
4. 记录对话过程

### 2. 更新对话索引
每次创建新的对话记录后，在 `conversation_index.md` 中添加索引条目。

### 3. 定期回顾
建议每周回顾对话记录，总结有效的沟通模式和关键收获。

## 沟通效率提升技巧

### 问题描述最佳实践
1. **提供上下文**：说明项目背景、技术栈、当前状态
2. **明确目标**：清楚表达期望的结果
3. **指定格式**：如需要代码、文档或特定格式的回答
4. **设置约束**：说明限制条件、优先级、时间要求

### 有效沟通模式
1. **分步骤提问**：复杂问题分解为多个小问题
2. **确认理解**：让AI复述问题确保理解正确
3. **提供反馈**：对回答质量给出具体反馈
4. **引用历史**：参考之前的对话记录

### 记录要点
1. **关键决策**：记录重要的技术决策和理由
2. **解决方案**：记录有效的解决方案和实施步骤
3. **踩坑经验**：记录遇到的问题和解决方法
4. **最佳实践**：总结有效的沟通和工作模式

## 质量标准

### 对话记录质量检查清单
- [ ] 标题清晰，能够概括对话主题
- [ ] 基本信息完整（日期、参与者、主题、目标）
- [ ] 对话过程记录完整，包含关键的问答
- [ ] 代码示例格式正确，包含语言标识
- [ ] 关键收获和行动项明确
- [ ] 文档结构清晰，易于后续查阅

### 定期维护
- **每周**：回顾本周对话记录，更新索引
- **每月**：总结常见问题和解决方案
- **每季度**：优化沟通模式和记录模板

## 相关资源

- [对话分享指南](../20250703_ai_conversation_sharing_guide.md)
- [项目文档规范](../Dev_standard/gitCommitFormat.md)
- [Markdown语法参考](https://www.markdownguide.org/)

## 更新日志

- 2025-07-03：创建AI对话记录目录和规范
