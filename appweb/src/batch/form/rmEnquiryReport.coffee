###
Description:   统计form_input表中报名表数据，生成4个部分的统计报表：
1. Follow Up Boss Leads Report - 根据PAGE_TYPE进行分类统计
以下部分仅统计mls
2. Follow Up Boss Leads Prop Type - 根据mls房源类型和价格统计
3. Follow Up Boss Leads Area - 根据省份和区域划分统计
4. Follow Up Boss Leads Language - 根据语言进行统计
生成Excel报表并发送邮件
如果type选择m，则统计数据为输入日期的上个月1号到上个月最后一天的数据，默认是日期是本月
如果type选择w，则统计数据为输入日期的上周一到上周日的数据，默认是日期上周

Options:
-t --type: 统计时间类型，m代表按月统计，w代表按周统计 (必需)
-d --date: 统计数据的时间，格式 YYYY-MM-DD
-f --file: 输出路径+文件名，默认文件名是 date-EnquiryReport.xlsx
-e --email: email地址 (必需)

usage: sh start.sh -t batch -n rmEnquiryReport -cmd "lib/batchBase.coffee batch/form/rmEnquiryReport.coffee -t m -d 2024-09-01 -e <EMAIL> -f xxx"

createDate: 2025-07-01
Author: ZhangMan
###

os = require 'os'
fs = require 'fs'
path = require 'path'
xlsx = require 'xlsx'
yargs = require('yargs')
debug = DEBUG()

# 项目内依赖
ObjectId = INCLUDE('lib.mongo4').ObjectId
helpers = INCLUDE 'lib.helpers'
speed = INCLUDE 'lib.speed'
{isSale} = INCLUDE 'libapp.properties'
sendMail = SERVICE 'sendMail'
{isEmailArray} = INCLUDE 'lib.helpers_string'
{dateFormat, date2num} = INCLUDE 'lib.helpers_date'

# 数据库集合
FormInputCol = COLLECTION('chome', 'form_input')
PropertiesCol = COLLECTION('vow', 'properties')

speedMeter = speed.createSpeedMeter {
  intervalTriggerCount: 1000
}
processStartTs = new Date()

# 参数解析
yargs
  .option 'type', { alias: 't', description: 'time type: m for monthly, w for weekly', required: true}
  .option 'date', { alias: 'd', description: 'date as yyyy-mm-dd'}
  .option 'file', { alias: 'f', description: 'customize output file name, default is __dirname/date-enquiryReport.xlsx'}
  .option 'email', { alias: 'e', array:true, description: 'email address', required: true}
  .help('help')

argv = yargs.argv
startDate = null
endDate = null

# 计算时间范围
getDateRange = (dateStr, timeType) ->
  if dateStr
    referenceDate = new Date(dateStr)
    if isNaN(referenceDate.getTime())
      return [null, null]
  else
    referenceDate = new Date()
  
  if timeType is 'm'
    # 按月统计：统计给出时间/当前时间的上个月1号开始到月底
    lastMonth = referenceDate.getMonth() - 1
    year = referenceDate.getFullYear()
    if lastMonth < 0
      lastMonth = 11
      year = year - 1
    start = new Date(year, lastMonth, 1, 0, 0, 0, 0)
    end = new Date(year, lastMonth + 1, 0, 23, 59, 59, 999)
  else if timeType is 'w'
    # 按周统计：输入日期的上个礼拜一到礼拜天
    dayOfWeek = referenceDate.getDay() # 0是周日，1是周一...6是周六
    daysToLastMonday = if dayOfWeek is 0 then 7 else dayOfWeek + 6
    
    start = new Date(referenceDate)
    start.setDate(referenceDate.getDate() - daysToLastMonday)
    start.setHours(0, 0, 0, 0)
    
    end = new Date(start)
    end.setDate(start.getDate() + 6)
    end.setHours(23, 59, 59, 999)
  else
    return [null, null]
    
  return [start, end]

# 检查路径是否为目录
isDir = (path)->
  try
    stats = fs.lstatSync(path)
    return stats.isDirectory()
  catch err
    return false

# 全局变量
gOutputPath = null
gOutputFileName = null

# 统计数据容器
stats = {
# 第一部分：Follow Up Boss type
  bossType: {
    module1: {
      'app.mls': 0
      'web.mls': 0
      'app.evaluation': 0
      'website': 0
      'app.rmlisting': 0
      'web.student_rental': 0
    }
    module2: {
      'app.project': 0
      'email.project': 0
      'web.project': 0
      'app.precon': 0
      'web.precon': 0
    }
    module3: {
      'app.assignment': 0
      'app.trustedassignment': 0
      'web.trustedassignment': 0
    }
  }
  # 第二部分：Follow Up Boss prop type
  propType: {
    'lease_total': 0
    'lease_less_1800': 0
    'lease_more_1800': 0
    'sale_total': 0
    'sale_less_1mil': 0
    'sale_more_1mil': 0
  }
  # 第三部分：leader city
  leaderCity: {
    'BC': 0
    'AB_Calgary': 0
    'AB_Edmonton': 0
    'Toronto': 0
    'Ottawa': 0
    'Kitchener,Waterloo': 0
    'York': 0
    'Peel': 0
    'Durham': 0
    'ON_Other': 0
    'Other': 0
  }
  # 第四部分：语言
  language: {
    'Chinese': 0
    'Cantonese': 0
    'English': 0
    'Other': 0
  }
}

PAGE_TYPE = ['mls', 'project', 'trustedAssignment', 'saleTrustedAssign', 'evaluation', 'rmListing', 'student_rental']

# 存储需要查询Properties的记录
needPropertyQuery = []
propertyMap = {}

# 语言标准化逻辑
normalizeLanguage = (locales) ->
  return 'English' unless locales?.length
  # 转换为数组
  if typeof locales is 'string'
    locales = locales.split(',')
  # 检查是否只有English
  if locales.every((locale) -> locale?.toLowerCase() in ['en', 'english'])
    return 'English'
  # 如果有其他语言，按优先级选择
  for locale in locales
    continue unless locale
    locale = locale.toLowerCase()
    if locale in ['zh-cn', 'mandarin']
      return 'Chinese'
    else if locale in ['cantonese', 'zh']
      return 'Cantonese'
    else
      return 'Other'
  return 'English'

# 获取Follow Up Boss type分类
getBossTypeCategory = (src, page, tp) ->
  # 转换为小写并处理空值
  src = src?.toLowerCase().trim() or ''
  page = page?.toLowerCase().trim() or ''
  key = if src and page then "#{src}.#{page}" else tp
  unless key
    return null
  module1 = Object.keys(stats.bossType.module1)
  module2 = Object.keys(stats.bossType.module2)
  module3 = Object.keys(stats.bossType.module3)
  # Module 1
  if key in module1
    return { module: 1, key: key }
  # Module 2
  else if key in module2
    return { module: 2, key: key }
  # Module 3
  else if key in module3
    return { module: 3, key: key }
  else if key is 'app.saletrustedassign'
    return { module: 3, key: 'app.trustedassignment' }
  return null

# 获取prop type分类
getPropTypeCategory = (saletp, price) ->
  return null unless saletp and price
  if isSale({saletp})
    if price < 1000000
      return 'sale_less_1mil'
    else
      return 'sale_more_1mil'
  else
    if price < 1800
      return 'lease_less_1800'
    else
      return 'lease_more_1800'

# ON省根据area字段分区域
getAreaCategory = (area) ->
  return 'ON_Other' unless area
  areaCase = switch area
    when 'york' then 'York'
    when 'peel' then 'Peel'
    when 'durham' then 'Durham'
    else 'ON_Other'
  return areaCase

# 获取leader city分类
getLeaderCityCategory = (prov, city, area) ->
  return 'Other' unless prov
  if prov is 'BC'
    return 'BC'
  if prov is 'AB'
    if city is 'calgary'
      return 'AB_Calgary'
    else if city is 'edmonton'
      return 'AB_Edmonton'
    else
      return 'Other'
  if prov is 'ON'
    if city is 'toronto'
      return 'Toronto'
    else if city is 'ottawa'
      return 'Ottawa'
    else if city in ['kitchener', 'waterloo']
      return 'Kitchener,Waterloo'
    else
      return 'ON_Other'
  return 'Other'

# 收集form_input数据并进行统计
collectFormInputStats = (cb) ->
  try
    formInputCur = await FormInputCol.find({ 
      ts: { $gte: startDate, $lte: endDate },
      $or: [
        { page: { $in: PAGE_TYPE } },
        { tp: 'website' }
      ]
    })
  catch err
    debug.error err
    return cb(err)
  stream = formInputCur.stream()
  obj =
    verbose: 1
    high: 1
    stream: stream
    end: (err) ->
      if err
        debug.error err
        return cb(err)
      debug.info speedMeter.toString()
      processTs = (new Date().getTime() - processStartTs.getTime())/(1000*60)
      debug.info "FormInput total process time #{processTs} mins #{speedMeter.toString()}"
      return cb()
    process: (formInput, callBack) ->
      speedMeter.check {processCount:1}

      # 第一部分：Follow Up Boss type统计
      bossCategory = getBossTypeCategory(formInput.src, formInput.page, formInput.tp)
      if bossCategory
        moduleKey = "module#{bossCategory.module}"
        stats.bossType[moduleKey][bossCategory.key]++
      
      # 第二三四部分只统计mls数据
      unless formInput.page is 'mls'
        speedMeter.check {updated:1}
        return callBack()
      # 第二部分：Follow Up Boss prop type统计（仅mls）
      propCategory = getPropTypeCategory(formInput.saletp, formInput.lp or formInput.lpr)
      if propCategory
        stats.propType[propCategory]++
        stats.propType.lease_total++ if propCategory.startsWith('lease_')
        stats.propType.sale_total++ if propCategory.startsWith('sale_')
      
      # 第三部分：leader city统计(仅mls)
      city = formInput.city?.toLowerCase()
      if (formInput.prov is 'ON') and formInput.id and (city not in ['toronto', 'ottawa', 'kitchener', 'waterloo'])
        # 需要查询Properties表获取area字段
        needPropertyQuery.push({
          # formInput: formInput
          city: formInput.city
          id: formInput.id
        })
        propertyMap[formInput.id] = 1
      else
        # 直接统计
        cityCategory = getLeaderCityCategory(formInput.prov, city)
        stats.leaderCity[cityCategory]++
      
      # 第四部分：语言统计
      languageCase = normalizeLanguage(formInput.locale)
      stats.language[languageCase]++
      
      speedMeter.check {updated:1}
      return callBack()
  helpers.streaming obj

# 批量查询Properties获取area字段
queryPropertiesForArea = () ->
  ids = Object.keys(propertyMap)
  return unless ids.length
  properties = await PropertiesCol.findToArray({ _id: { $in: ids } }, {projection:{ _id: 1, region: 1 }})
  unless properties.length
    return
  # 创建id到area的映射
  areaMap = {}
  for prop in properties
    areaCase = prop.region?.toLowerCase()
    areaMap[prop._id] = areaCase
  # 处理每个需要area的记录
  for record in needPropertyQuery
    area = areaMap[record.id]
    cityCategory = getAreaCategory(area)
    stats.leaderCity[cityCategory]++
  return

# 通用函数：添加统计模块数据
addModuleStats = (rows, moduleData) ->
  total = 0
  for key, count of moduleData
    rows.push([key, count])
    total += count
  rows.push(['Total', total])
  rows.push([''])
  return rows

# 通用函数：添加报表部分
addReportSection = (rows, title, data) ->
  rows.push([title])
  for item in data
    rows.push(item)
  rows.push([''],[''])
  return

# 生成Excel报表
generateExcelReport = () ->
  # 表头
  reportType = if argv.type is 'm' then 'Monthly' else 'Weekly'
  titleRow = ["#{dateFormat(startDate,'YYYY-MM-DD')} - #{dateFormat(endDate,'YYYY-MM-DD')} RealMaster Enquiry #{reportType} Report"]
  
  rows = [titleRow, ['']]  # 标题和空行
  
  # 第一部分：Follow Up Boss Leads Report
  rows.push(['Follow Up Boss Leads Report'])
  rows.push(['Source', 'Total'])
  
  # Module 1
  rows = addModuleStats(rows, stats.bossType.module1)
  # Module 2  
  rows = addModuleStats(rows, stats.bossType.module2)
  # Module 3
  rows = addModuleStats(rows, stats.bossType.module3)
  rows.push([''])
  
  # 第二部分：Follow Up Boss Leads Prop Type
  propTypeData = [
    ['Property Inquiry lease total', stats.propType.lease_total]
    ['Property Inquiry less than $1800', stats.propType.lease_less_1800]
    ['Property Inquiry more than $1800', stats.propType.lease_more_1800]
    ['Property Inquiry sale total', stats.propType.sale_total]
    ['Property Inquiry less than 1mil', stats.propType.sale_less_1mil]
    ['Property Inquiry more than 1mil', stats.propType.sale_more_1mil]
  ]
  addReportSection(rows, 'MLS Follow Up Boss Leads Prop Type', propTypeData)
  
  # 第三部分：Follow Up Boss Leads Area
  areaData = [
    ['BC', stats.leaderCity.BC]
    ['AB_Calgary', stats.leaderCity.AB_Calgary]
    ['AB_Edmonton', stats.leaderCity.AB_Edmonton]
    ['Other', stats.leaderCity.Other]
    ['']
    ['Toronto', stats.leaderCity.Toronto]
    ['York', stats.leaderCity.York]
    ['Peel', stats.leaderCity.Peel]
    ['Durham', stats.leaderCity.Durham]
    ['Ottawa', stats.leaderCity.Ottawa]
    ['Kitchener,Waterloo', stats.leaderCity['Kitchener,Waterloo']]
    ['ON_Other', stats.leaderCity.ON_Other]
  ]
  addReportSection(rows, 'MLS Follow Up Boss Leads Area', areaData)
  
  # 第四部分：MLS Follow Up Boss Leads Language
  languageData = [
    ['Chinese', stats.language.Chinese]
    ['Cantonese', stats.language.Cantonese]
    ['English', stats.language.English]
    ['Other', stats.language.Other]
  ]
  addReportSection(rows, 'MLS Follow Up Boss Leads Language', languageData)

  # 创建工作簿
  wb = xlsx.utils.book_new()
  ws = xlsx.utils.aoa_to_sheet(rows)
  xlsx.utils.book_append_sheet(wb, ws, "Enquiry Report")

  # 写入文件
  gOutputPath = path.join(gOutputPath, gOutputFileName)
  xlsx.writeFile(wb, gOutputPath)
  return

# 删除文件
deleteFile = (filePath,cb)->
  fs.unlink filePath,(err)->
    if err
      debug.warn "#{filePath} deleted faild"
    return cb()

# 发送邮件
sentFile = (cb) ->
  if not argv.email
    return cb()
  debug.info 'Preparing Email', 'outputFile', gOutputPath
  fs.readFile gOutputPath, (err, file) ->
    if err
      debug.error err
      return cb(err)
    base = new Buffer(file).toString('base64')
    engine = 'rmMail'
    dateRange = "#{dateFormat(startDate,'YYYY-MM-DD')} - #{dateFormat(endDate,'YYYY-MM-DD')}"
    emailConfig = {
      engine: engine
      from: sendMail.getFromEmailByEngine(engine)
      to: argv.email,
      subject: "#{dateRange} RealMaster Enquiry Report",
      html: "Please see attachment for enquiry report.",
      attachments: [
        {
          filename: gOutputFileName
          content: base
          encoding: 'base64'
        }
      ],
      listId: 'EnquiryReport'
      eventType: 'BatchEnquiryReport'
    }
    sendMail.sendMail emailConfig, (error, result) ->
      if error
        debug.error 'Sent email error', error, argv.email
      else
        debug.info 'Email sent', argv.email
      # 发送成功后删除文件
      deleteFile gOutputPath, ->
        return cb(error)

# 主流程
main = ->
  # 验证必需参数
  unless argv.email
    debug.error 'Email parameter is required', argv.email
    return EXIT(0)
  unless isEmailArray(argv.email)
    debug.error 'Invalid email format', argv.email
    return EXIT(0)
  unless argv.type in ['m', 'w']
    debug.error 'Invalid time type, must be m or w', argv.type
    return EXIT(0)
  # 计算时间范围
  [startDate, endDate] = getDateRange(argv.date, argv.type)
  if (not startDate) or (not endDate)
    debug.error 'Invalid date format or time type', argv.date, argv.type
    return EXIT(0, 'Invalid date range')
  # 设置输出文件路径
  if argv.file?.length > 0
    regex = /\/(?:.(?!\/))+$/
    if isDir(argv.file)
      gOutputPath = argv.file
      gOutputFileName = dateFormat(endDate, 'YYYYMMDD') + '-enquiryReport.xlsx'
    else if isDir(argv.file.replace(regex,''))
      gOutputPath = argv.file.replace(regex,'')
      gOutputFileName = "#{argv.file.match(regex)[0].replace('/','')}"
      if not /\.xlsx$/.test(gOutputFileName)
        gOutputFileName = "#{gOutputFileName}.xlsx"
    else
      debug.error 'Invalid file path', argv.file
      return EXIT(0)
  else
    gOutputPath = os.tmpdir()
    gOutputFileName = dateFormat(endDate, 'YYYYMMDD') + '-enquiryReport.xlsx'
  
  # 执行主要流程
  collectFormInputStats (err) ->
    if err
      return EXIT(0, err)
    try
      await queryPropertiesForArea()
      generateExcelReport()
    catch err
      debug.error err
      return EXIT(0, err)
    sentFile (err) ->
      return EXIT 1, err if err
      return EXIT 0

main()
