@import '../../mixins';
@import '../../base';
@import '../../components/forum/forum.scss';
@import '../components/forum/forumSummaryCard.scss';
@import '../components/forum/reportForumForm.scss';
@import '../components/checkNotification.scss';

.reportForumIcon {
  position: absolute;
  top: 50%;
  right: 10px;
  @include css3('transform','translateY(-50%)');
}

.height-100{
  height: 100%;
  overflow: auto;
}

.mask {
  background: rgba(0,0,0,0.8);
  // opacity: 0.7;
  z-index: 18;
}
.filtre-l{
  -webkit-mask: -webkit-linear-gradient(to right, transparent, white 10%,white 90%, transparent);
  -webkit-mask: linear-gradient(to right, transparent, white 10%,white 90%, transparent);
}
.tag-filter span {
  font-size: 15px;
  margin-left: 5px;
  padding-left: 5px!important;
  padding-right: 5px!important;
  border-width: 1px!important;
}
#forum-main{
  height: 100%;
  overflow: hidden;
  .bar-nav{
    .title{
      font-weight: bold;
    }
    .pull-right{
      z-index: 1;
    }
    .bar-right {
      overflow: hidden;
      display: inline-block;
      padding: 11px 10px;
      position: relative;
      white-space: nowrap;
    }
    .fa-rmsearch{
      position: relative;
      margin-left: -42px;
      margin-top: 4px;
      display: inline-block;
      padding: 9px;
    }
  }
}
#forum-containter {
  overflow-y: scroll;
  height: 100%;
  padding-top: 44px;
  position: relative;
  width: 100%;
  overflow-y: scroll;
  padding-bottom: 51px;
  &.news{
    padding-top: 94px;
  }
}
::-webkit-scrollbar {
   display: none;
}
[v-cloak] {
  display: none;
}
.bar .fa-back {
  font-size: 21px;
}

.all-filter span:not(:first-of-type)::before{
  /*border-left: 1px solid #f0eeee;*/
  content: '';
  display: inline-block;
  height: 14px;
  border-left: 1px solid #dddddd;
  padding-left: 10px;
}
.filterIcon {
  float: right;
  width: 3px;
}
.popover{
  position: fixed;
  top: 60px;
  left: 50%;
  z-index: 20;
  width: 280px;
  margin-left: -140px;
  background-color: white;
  border-radius: 6px;
  @include css3('box-shadow',0 0 15px rgba(0, 0, 0, .1));
  @include css3('transition','all .25s linear');
  @include css3('transform','translate3d(0.-15px,0)');
  &:before {
    position: absolute;
    top: -15px;
    left: 50%;
    width: 0;
    height: 0;
    margin-left: -15px;
    content: '';
    border-right: 15px solid transparent;
    border-bottom: 15px solid white;
    border-left: 15px solid transparent;
  }
}
.title .add-new {
  padding-right: 10px;
  z-index: 20;
  font-size: 17px;
}
.table-view-cell .icon-right{
  position: absolute;
  top: 50%;
  right: 15px;
  transform: translateY(-50%);
}
.table-view{
  margin: 0px;
}
.table-view-cell .fa-check-square-o{
  color: #e03131;
}
.table-view-cell .fa-square-o{
  padding-right: 2px;
}
.bar-header-secondary~.content{
  padding-top: 84px;
  background: #f0eeee;
}
.stat-container, .today-choices, .news-container, .qnas-container, .all-posts-container{
  width: 100%;
  background: #fff;
  /*margin-bottom: 10px;*/
  overflow-y: hidden;
}
.stat-container {
  height: 80px;
  padding-top: 15px;
}
.stat-container > div{
  display: inline-block;
  width: 24%;
  height: 100%;
  padding: 0px 7px 0 8px;
  overflow: hidden;
  text-align: center;
}

.bar-forum-section {
  border-bottom: 1px solid #F0EEEE;
  height: 50px;
}
.bar-forum-section-news {
  text-align:center;
  color:red;
  height:50px;
  padding-top:15px;
}
.bar-forum-section-header{
  padding-top: 10px;
  padding-left: 10px;
  color: red;
  float: left
}
.bar-forum-section-right {
  float: right;
  padding-top: 15px;
  padding-right: 10px;
  font-size: 14px;
  color: grey;
}
.stat-container .data {
  padding-top: 10px;
  color:red;
  font-size:30px
}
.stat-container .bottom{
  padding-top: 10px;
  font-size: 12px;
  color:grey;
}
.fa-chevron-right {
  padding-left:5px;
  padding-top:3px;
}
.halfDrop {
  position: fixed;
  top: 88px;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 15;
  background-color: rgba(0, 0, 0, .8);
}
.pull-down-container {
  padding-top:82px;
}
.pull-down-header{
  background-color: white!important;
}
.tag-filter {
  color: #00b2ee!important;
  line-height: 44px;
}
.tab-list {
  background-color: #efefef;
  top:44px;
  height: initial;
}
.bar-standard .icon {
  font-size: 15px;
}
.bar-standard  .red-button {
  padding: 3px 3px!important;
}
.frame{
  position: absolute;
  z-index: 18;
  width: 78%;
  min-height: 305px;
  background: white;
  top: 50%;
  transform: translateY(-50%);
  margin: 0 11%;
  padding: 10px 12px 10px 12px;
}
.iframe-wrapper{
  height: 240px;
  width: 270px;
  -webkit-overflow-scrolling: touch;
  overflow-y: scroll;
}
.frame .iframe{
  width: 100%;
  height: 100%;
  padding:10px;
  /* overflow: scroll; */
}
.termTitle {
  font-weight: bold;
  font-size: 16px;
  text-align: center;
}
.frame .btn-wrapper{
  margin-top: 11px;
}
.canReadLangs {
  margin-top: 15px;
  @include flexbox();
  justify-content: center;
  span {
    display: inline-block;
    margin-left: 20px;
    label {
      margin-left: 10px;
    }
  }
}
.forum-searchbar {
  position: fixed;
  top: 44px;
  left: 0;
  width: 100%;
  background-color: #fff;
  z-index: 19;
  input {
    width: 100%;
    padding: 10px 20px;
  }
  .icon-close {
    position: absolute;
    left: 0;
    top: 10px;
    font-size: 20px;
  }
  .icon-search {
    position: absolute;
    right: 10px;
    top: 10px;
    font-size: 20px;
  }
}