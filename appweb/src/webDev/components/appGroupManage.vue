<template lang="pug">
div#appGroupManage(v-cloak, style="height:100%")
  page-spinner(:loading.sync="loading")
  confirm-with-input()
  div.modal#notificationModal
    header.bar.bar-nav
      a.icon.icon-close.pull-right(href="javascript:;", @click="toggleModal('notificationModal')")
      //- a.icon.fa.fa-ellipsis-h.pull-right(href="javascript:;", @click="delGroup()")
      h1.title
        span {{_('Notification')}}
    div.content
      div.tl
        input(type="text", :placeholder="_('Title')",v-model="pnTitle")
      div.split
      div.msg
        textarea(rows="8", type='text', v-model='pnMsg', :placeholder="_('Send Something')")
        div.count
          span.fa.fa-group
          | {{_('Select')}} {{selectedUser.length}}
      div.split
      div.methods
        div.eml
          span {{_('Send by email')}}
          switched.pull-right(:value='sendMethods.mail', @on-change="updateSendMethod", label="mail", style='margin-right: 7px;')
        div.text
          span {{_('Send by text messages')}}
          switched.pull-right(:value='sendMethods.sms', @on-change="updateSendMethod", label="sms", style='margin-right: 7px;')
        div.noti
          span {{_('Send push notify')}}
          switched.pull-right(:value='sendMethods.pn', @on-change="updateSendMethod", label="pn", style='margin-right: 7px;')
    div.bar.bar-footer
      span(@click="sendNotifies()") {{_('Send')}}
  div.modal#inviteModal
    header.bar.bar-nav
      a.icon.icon-close.pull-right(href="javascript:;", @click="hideAddPeople()")
      //- a.icon.fa.fa-ellipsis-h.pull-right(href="javascript:;", @click="delGroup()")
      h1.title
        span {{_('Add People')}}
    div.content
      div.tl
        span {{_('Invite')}}
        //- input(type="text")
        //- span &nbsp;{{_('Or')}}&nbsp;
        span.qrcode.btn.btn-positive.pull-right(@click="showQRCode()", v-show="dispVar.isDevGroup") {{_('Use QRCode')}}
      div.split
      div.emls-wrapper()
        textarea(rows="8", type='text', v-model='inviteEmls', :placeholder="_('Input emails, separated by , ; or change line.')")
        //- div.eml(v-for="e in inviteEmls")
        //-   | {{e}}
        //-   span.del.fa.fa-close()

    div.bar.bar-footer
      span(@click="addMembers()") {{_('Add')}}
  div.modal#groupEditModal
    header.bar.bar-nav
      a.icon.fa.fa-back.pull-left(href="javascript:;", @click="hideGroupDetail()")
      a.icon.fa.fa-ellipsis-h.pull-right(href="javascript:;", @click="delGroup()")
      h1.title
        span {{curGroup.nm}}
    div.bar.bar-standard.bar-footer.footer-tab
      button.pull-left.btn.badge.del(:class="{disabled:!selectedUser.length}", @click="deleteUsers()") {{_('Delete')}}
      button.pull-left.btn.badge.sel(@click="toggleSelect()") {{_('Toggle All')}}
      //- a.pull-left.btn.sel {{_('De-select All')}}
      a.pull-right(
        v-show="curGroupVar.isAdmin || curGroupVar.isOwner",
        href="javascript:void 0",
        @click="sendNotification()")
        div.icon.fa.fa-bell
        div.tab-label {{_('Send Notification')}}
      a.pull-right(
        v-show="curGroupVar.isAdmin || curGroupVar.isOwner",
        href="javascript:void 0",
        @click="invitePeople()")
        div.icon.fa.fa-user-plus
        div.tab-label {{_('Add People')}}
    div.content
      div.basic
        div.name
          span.input {{_('Name')}}
          input(type="text", v-model="groupNameInput", :disabled="disableGroupNameInput", :class="{disabled:disableGroupNameInput}")
        div.desc
          span.input {{_('Description')}}
          textarea(rows="3", type='text', v-model='groupDescInput', placeholder="Group policies")
      div.filter
        span.fa.fa-rmsearch
        input(type="text", v-model="groupMemberFilter", :placeholder="_('Search')")
        span.cnt
          span.fa.fa-group
          | {{curGroup.cnt}}
      div.members
        div.status(v-show="pendingMembers.length")
          span.dot.pending
          | {{_('Pending')}}
          span.count {{pendingMembers.length}}
          span.pull-right.fa(@click="hideList('pending')", :class="[showpending?'fa-chevron-down':'fa-chevron-up']")
        div.pending.ul(v-show="showpending")
          div.member(v-for="r in pendingMembers")
            div.avt
              div.img
                img(:src="r.avt")
              //- div.status {{_('Pending')}}
              div.accept(@click="acceptPending(r)") {{_('Accept')}}
            div.detail
              div.nm {{r.fnm}}
              div.mbl
                span.fa.fa-phone
                | {{r.mbl}}
              div.eml
                span.fa.fa-envelope-o
                | {{r.eml}}
              div.wx
                span.fa.fa-wechat
                | {{r.wx}}
            div.ops
              span.fa.fa-trash-o(@click="deletePending(r)")
        div.status
          span.dot.current
          | {{_('Member')}}
          //- TODO: length
          span.count {{currentMembers.length}}
          span.pull-right.fa(@click="hideList('current')", :class="[showcurrent?'fa-chevron-down':'fa-chevron-up']")
        div.current.ul(v-show="showcurrent")
          div.member(v-for="r in filteredCurrentMembers")
            div.avt
              div.pull-left
                span.fa.fa-square-o(v-show="!isChecked(r)", @click="selectUser(r,'add')")
                span.fa.fa-check-square-o(v-show="isChecked(r)", @click="selectUser(r,'remove')")
              div.img
                img(:src="r.avt")
              div.u-status.admin(v-show="!r.isOwner && r.isAdmin") {{_('Admin')}}
              div.u-status.owner(v-show="r.isOwner") {{_('Owner','group')}}
            div.detail
              div.nm {{r.fnm}}
              div.mbl
                span.fa.fa-phone
                | {{r.mbl}}
              div.eml
                span.fa.fa-envelope-o
                | {{r.eml}}
              div.wx
                span.fa.fa-wechat
                | {{r.wx}}
            div.ops
              div.fa.fa-ellipsis-h(@click="showUserAct(r)", v-show="(r.uid !== curGroupVar.uid) && (!r.isOwner) && !(!curGroupVar.isOwner && r.isAdmin)")
              div.pn-wrapper(@click="pnUser(r)")
                span.fa.fa-bell()
                span.text {{_('PN','push notify')}}
              div.view-showing(@click="viewShowing(r)",v-if='curGroup.nm == ":inReal" && dispVar.isShowingAdmin')
                span.text {{_('View showing')}}
                //- dispvar, curMod, not self, not owner
            div.dropdown(v-show="showUserOps && (curUser.eml == r.eml) && (r.uid !== curGroupVar.uid)")
              div(@click="adminOps('owner',r)", v-show="curGroupVar.isOwner") {{_('Give Ownership')}}
              //- (!r.isAdmin && curGroupVar.isAdmin)
              div(@click="adminOps('admin',r)", v-show="curGroupVar.isOwner") {{_('Toggle Admin status')}}
              div(@click="adminOps('delete',r)", v-show="(!r.isAdmin && curGroupVar.isAdmin) || curGroupVar.isOwner") {{_('Delete this person')}}
        div.status(v-show="quitMembers.length")
          span.dot.quit
          | {{_('Quit')}}
          span.count {{quitMembers.length}}
          span.pull-right.fa(@click="hideList('quit')", :class="[showquit?'fa-chevron-down':'fa-chevron-up']")
        div.quit.ul(v-show="showquit")
          div.member(v-for="r in quitMembers")
            div.avt
              div.img
                img(:src="r.avt")
            div.detail
              div.nm {{r.fnm}}
              div.mbl
                span.fa.fa-phone
                | {{r.mbl}}
              div.eml
                span.fa.fa-envelope-o
                | {{r.eml}}
              div.wx
                span.fa.fa-wechat
                | {{r.wx}}
  flash-message
  header#header-bar.bar.bar-nav
    a.icon.fa.fa-back.pull-left(@click="goBack()")
    a.icon.fa.fa-qrcode.pull-right(v-show="dispVar.isGroupAdmin", @click="openCamera()")
    h1.title
      span {{_('Groups')}}
  div.bar.bar-standard.bar-footer.footer-tab
    a.pull-left(
      href="javascript:void 0",
      @click="createGroup()")
      div.icon.fa.fa-plus
      div.tab-label {{_('Create/Join')}}
    //- a.pull-left(
    //-   href="javascript:void 0",
    //-   @click="joinGroup()")
    //-   div.icon.fa.fa-sign-in
    //-   div.tab-label {{_('Join')}}
  div.content#groupList
    div.load.btn.btn-block.btn-positive(v-show="dispVar.isGroupAdmin", @click="loadMore()") {{_('LoadMore')}}
    div.input(v-show="groups.length")
      span.fa.fa-rmsearch
      input(type="text", v-model="groupFilter", :placeholder="_('Search')")
    div.group-list
      div.group(v-for="g in filteredGroups",@click="editGroup(g)")
        div.img
          img(:src="g.img")
          div.label
            span.owner(v-show="g.isOwner") {{_('Owner','group')}}
            span.admin(v-show="!g.isOwner && g.isAdmin") {{_('Admin')}}
            span.member(v-show="!g.isOwner && !g.isAdmin && g.isMember") {{_('Member')}}
        div.detail
          div.nm {{g.nm}}
          div.number
            span.fa.fa-group
            | {{g.cnt}} {{_('Members')}}
          div.article
            span.fa.fa-bookmark(@click.stop="goToForum(g)")
            span(@click.stop="goToForum(g)") {{g.faCnt}} {{_('Forum article')}}
        div.edit
          div(v-if="g.isOwner || g.isAdmin || dispVar.isGroupAdmin || (dispVar.isShowingAdmin && (g.nm == ':inReal'))",@click.stop="editGroup(g)") {{_('Edit')}}
          div(v-if="!(g.isOwner || g.isAdmin) && g.isMember" @click.stop="quitGroup(g)") {{_('Quit')}}
          div(v-if="!(g.isOwner || g.isAdmin)" @click.stop="viewGroup(g)") {{_('View')}}

</template>

<script>
import pagedata_mixins from '../../coffee4client/components/pagedata_mixins'
import Switched from './frac/Switch.vue'
import ConfirmWithInput from './frac/ConfirmWithInput.vue'
import FlashMessage from './frac/FlashMessage.vue'
import PageSpinner from './frac/PageSpinner.vue'

// import imgSelectModal from './frac/Confo.vue'
// import imgPreviewModal from './frac/ImgPreviewModal.vue'

export default {
  mixins: [pagedata_mixins],
  props:{
  },
  watch:{
    groupNameInput:function(newNm, oldNm){
      if (!newNm || newNm == this.curGroup.nm) {
        return;
      }
      this.updateGroupNameDesc();
    },
    groupDescInput:function(newDesc,oldDesc){
      if (!newDesc || newDesc == this.curGroup.desc) {
        return;
      }
      this.updateGroupNameDesc()
    }
  },
  data () {
    return {
      sendMethods:{
        pn:false,
        sms:false,
        mail:true
      },
      pg:0,
      groupFilter:'',
      groupMemberFilter:'',
      pnTitle:'',
      pnMsg:'',
      showUserOps:false,
      loading:false,
      curUser:{},
      inviteEmls:'',
      groupNameInput:'',
      disableGroupNameInput:false,
      groupDescInput:'',
      curGroup:{},
      curGroupVar:{
        isOwner:false,
        isAdmin:false,
      },
      groups:[],
      pendingMembers:[],
      currentMembers:[],
      quitMembers:[],
      selectedUser:[],
      showpending:true,
      showcurrent:true,
      showquit:true,
      datas:[
        'isCip',
        'isApp',
        'userCity',
        'isLoggedIn',
        'lang',
        'forumAdmin',
        'sessionUser',
        'reqHost',
        'isVipRealtor',
        'isAdmin',
        'isGroupAdmin',
        'isRealtor',
        'isDevGroup',
        'isShowingAdmin'
      ],
      dispVar:{isDevGroup:false,isShowingAdmin:false},
      nomore:false,
    };
  },
  computed:{
    computedImgHeight(){
      return (window.innerWidth || 375) * 0.8;
    },
    filteredCurrentMembers(){
      var self = this;
      return self.currentMembers.filter(function (g) {
        if (!self.groupMemberFilter) {
          return true;
        }
        var mblTest = false,fnmTest = false;
        if (g.mbl) {
          mblTest = (''+g.mbl).indexOf(self.groupMemberFilter) !== -1
          // console.log('+++',g.mbl,mblTest);
        }
        if (g.fnm) {
          fnmTest = (g.fnm.indexOf(self.groupMemberFilter) !== -1)
          // console.log('+++',g.fnm,fnmTest);
        }
        return (fnmTest || (g.eml.indexOf(self.groupMemberFilter) !== -1) || mblTest)
      })
    },
    filteredGroups(){
      var self = this;
      return self.groups.filter(function (g) {
        if (!self.groupFilter) {
          return true;
        }
        return g.nm.indexOf(self.groupFilter) !== -1
      })
    }
  },
  methods: {
    viewShowing(r){
      var url = '/1.5/showing?uid='+r.uid+'&d='+encodeURIComponent('/group?d=/1.5/more&grp='+this.curGroup._id);
      document.location.href =url;
    },
    openCamera(){
      RMSrv.scanQR('/1.5/iframe?u=');
    },
    goBack() {
      if(vars.d) {
        document.location.href = vars.d;
      } else {
        window.history.back();
      }
    },
    deletePending(r){
      var d = {
        gid:this.curGroup._id,
        uids:r.uid
      };
      // console.log(d);
      // return;
      var self = this;
      self.$http.post('/group/rmMembers',d).then(
        function(ret) {
          ret = ret.body;
          setTimeout(function () {
            self.loading = false;
          }, 500);
          // console.log(ret);
          if(ret.ok) {
            // self.getGroupList()
            self.editGroup(self.curGroup,true);
            return window.bus.$emit('flash-message', ret.msg);
          } else {
            return window.bus.$emit('flash-message', ret.err);
          }
        },
        function(err) {
          ajaxError(err);
        }
      )
    },
    goToForum(g) {
      var url = '/1.5/forum?section=my_group&group='+g._id+"&gnm="+g.nm+'&d='+encodeURIComponent('/group?d=/1.5/more');
      document.location.href =url;
    },
    updateSendMethod(val,which){
      // console.log(val,which);
      this.sendMethods[which] = val;
    },
    sendNotifies(){
      var uids = [], self = this;
      if (!this.selectedUser.length) {
        return;
      }
      if (self.loading) {
        return;
      }
      for (let u of this.selectedUser) {
        uids.push(u.uid);
      }
      uids = uids.join(',');
      var methods = [];
      for (let i of ['mail','sms','pn']) {
        if (this.sendMethods[i]) {
          methods.push(i)
        }
      }
      if (!methods.length) {
        return;
      }
      // console.log(methods);
      // console.log(uids);
      if (!(this.pnTitle || this.pnMsg)) {
        return;
      }
      var d = {
        uids:uids,
        title:this.pnTitle,
        msg:this.pnMsg,
        gid:this.curGroup._id,
        methods:methods.join(',')
      }
      self.loading = true;
      self.$http.post('/group/sendNotification',d).then(
        function(ret) {
          ret = ret.body;
          setTimeout(function () {
            self.loading = false;
          }, 500);
          // console.log(ret);
          if(ret.ok) {
            // self.getGroupList()
            // self.editGroup(self.curGroup,true);
            return window.bus.$emit('flash-message', ret.msg);
          } else {
            return window.bus.$emit('flash-message', ret.err);
          }
        },
        function(err) {
          ajaxError(err);
        }
      )
    },
    acceptPending(r){
      var self = this;
      if (self.loading) {
        return;
      }
      var d = {
        gid:self.curGroup._id,
        uids:r.uid
      }
      self.loading = true;
      self.$http.post('/group/approveMembers',d).then(
        function(ret) {
          ret = ret.body;
          setTimeout(function () {
            self.loading = false;
          }, 500);
          if(ret.ok) {
            // self.getGroupList()
            self.editGroup(self.curGroup,true);
            return window.bus.$emit('flash-message', ret.msg);
          } else {
            return window.bus.$emit('flash-message', ret.err);
          }
        },
        function(err) {
          ajaxError(err);
        }
      )
    },
    updateGroupNameDesc(){
      var self = this;
      if (self.nmDescTimeout) {
        clearTimeout(self.nmDescTimeout);
      }
      self.nmDescTimeout = setTimeout(function () {
        if (self.loading) {
          return;
        }
        var d = {
          gid:self.curGroup._id,
          nm:self.groupNameInput,
          desc:self.groupDescInput
        };
        self.loading = true;
        self.$http.post('/group/update',d).then(
          function(ret) {
            ret = ret.body;
            setTimeout(function () {
              self.loading = false;
            }, 500);
            if(ret.ok) {
              // self.getGroupList()
              self.curGroup = Object.assign(self.curGroup,d);
              return window.bus.$emit('flash-message', ret.msg);
            } else {
              return window.bus.$emit('flash-message', ret.err);
            }
          },
          function(err) {
            ajaxError(err);
          }
        )
      }, 3000);
    },
    quitGroup(g){
      var group = g;
      var self = this;
      var data = {
        msg:this._('Quit this group?'),
        cb:function(idx){
          if (idx == 2) {
            // console.log(group._id);
            self.$http.post('/group/quit',{gid:group._id}).then(
              function(ret) {
                ret = ret.body;
                setTimeout(function () {
                  self.loading = false;
                }, 500);
                if(ret.ok) {
                  self.pg = 0
                  self.getGroupList()
                  return window.bus.$emit('flash-message', ret.msg);
                } else {
                  return window.bus.$emit('flash-message', ret.err);
                }
              },
              function(err) {
                ajaxError(err);
              }
            )
          }
        }
      };
      window.bus.$emit('show-confirm-dialog',data);
    },
    createGroup(){
      var self = this;
      var data = {
        hasInput:true,
        inputDesc:this._('Name:'),
        confirmWording:this._('Create/Join'),
        cb:function(idx,name){
          if (idx == 2) {
            self.loading = true;
            self.$http.post('/group/createOrJoin',{name:name}).then(
              function(ret) {
                ret = ret.body;
                setTimeout(function () {
                  self.loading = false;
                }, 500);
                if(ret.ok) {
                  window.bus.$emit('flash-message', ret.msg);
                  self.pg = 0
                  self.getGroupList()
                } else {
                  return window.bus.$emit('flash-message', ret.err);
                }
              },
              function(err) {
                ajaxError(err);
              }
            )
          }
        }
      };
      window.bus.$emit('show-confirm-dialog',data);
    },
    pnUser(r){
      this.selectedUser = [r];
      // this.selectedUser.push(r);
      toggleModal('notificationModal','open');
    },
    deleteUsers(){
      if (!this.selectedUser.length) {
        return;
      }
      var ids = [];
      for (let u of this.selectedUser) {
        ids.push(u.uid);
      }
      var r = {
        _id:ids.join(',')
      }
      // console.log(r);
      // return;
      this.adminOps('delete',r);
    },
    adminOps(tp,r){
      var self = this;
      var url = '/group/rmMembers';
      if (tp == 'owner') {
        url = '/group/changeOwner';
      } else if(tp == 'admin'){
        url = '/group/toggleAdmin';
      }
      var d = {
        gid:self.curGroup._id,
        uids:r._id,
      }
      if (self.loading) {
        return;
      }
      self.loading = true;
      self.$http.post(url,d).then(
        function(ret) {
          ret = ret.body;
          setTimeout(function () {
            self.loading = false;
          }, 500);
          if(ret.ok) {
            window.bus.$emit('flash-message', ret.msg);
            self.showUserOps = false;
            self.editGroup(self.curGroup,true);
            self.pg = 0;
            self.getGroupList();
          } else {
            return window.bus.$emit('flash-message', ret.err);
          }
        },
        function(err) {
          ajaxError(err);
        }
      )
    },
    hideAddPeople(){
      toggleModal('inviteModal','close');
    },
    showQRCode(){
      var self = this;
      if (self.loading) return;
      self.loading = true;
      var gid = this.curGroup._id; //TODO: to check if null
      var d = {data:{gid:gid},action:'joinGroup',exp:new Date(Date.now() + 7 * 24 * 3600000)};
      self.$http.post('/qrcode/createAction',d).then(
        function(ret) {
          ret = ret.body;
          setTimeout(function () {
            self.loading = false;
          }, 500);
          if(ret.url) {
            RMSrv.openInAppBrowser(ret.url);
          } else {
            return window.bus.$emit('flash-message', ret.err);
          }
        },
        function(err) {
          ajaxError(err);
        }
      )
    },
    addMembers(){
      var self = this;
      if (self.loading) {
        return;
      }
      self.loading = true;
      var d = {
        gid:this.curGroup._id,
        emls:this.inviteEmls
      }
      self.$http.post('/group/addMembers',d).then(
        function(ret) {
          ret = ret.body;
          setTimeout(function () {
            self.loading = false;
          }, 500);
          if(ret.ok) {
            self.hideAddPeople();
            self.editGroup(self.curGroup,true);
            // self.getGroupList()
          } else {
            return window.bus.$emit('flash-message', ret.err);
          }
        },
        function(err) {
          ajaxError(err);
        }
      )
    },
    invitePeople(){
      toggleModal('inviteModal')
    },
    toggleModal(a,b){
      toggleModal(a,b)
    },
    sendNotification(){
      toggleModal('notificationModal');
    },
    toggleSelect(){
      if (this.selectedUser.length) {
        this.selectedUser = []
      } else {
        for (let u of this.currentMembers) {
          this.selectedUser.push(u);
        }
      }
    },
    selectUser(r,cmd){
      if (cmd == 'add') {
        this.selectedUser.push(r);
        return;
      }
      var idx = -1;
      for (var i = 0; i < this.selectedUser.length; i++) {
        if(this.selectedUser[i].eml == r.eml){
          idx = i;
          break;
        }
      }
      if (idx != -1) {
        this.selectedUser.splice(idx,1);
      } else {
        console.error('Not in selected array');
      }
    },
    isChecked(r){
      for (let u of this.selectedUser) {
        if (r.eml == u.eml) {
          return true;
        }
      }
      return false;
    },
    showUserAct(r){
      if (this.curUser.eml != r.eml) {
        this.showUserOps = true;
      } else {
        this.showUserOps = !this.showUserOps;
      }
      this.curUser = r;
    },
    hideList(which){
      var listName = 'show'+which;
      this[listName] = !this[listName];
    },
    delGroup(){
      var group = this.curGroup;
      var self = this;
      var data = {
        msg:this._('Delete this group'),
        cb:function(idx){
          if (idx == 2) {
            if (self.loading) {
              return;
            }
            self.loading = true;
            self.$http.post('/group/rmGroup',{gid:group._id}).then(
              function(ret) {
                ret = ret.body;
                setTimeout(function () {
                  self.loading = false;
                }, 500);
                if(ret.ok) {
                  toggleModal('groupEditModal','close');
                  // self.editGroup(self.curGroup,true);
                  self.pg = 0
                  self.getGroupList()
                } else {
                  return window.bus.$emit('flash-message', ret.err);
                }
              },
              function(err) {
                ajaxError(err);
              }
            )
          }
        }
      };
      window.bus.$emit('show-confirm-dialog',data);
    },
    hideGroupDetail(){
      this.curUser = {};
      this.curGroupVar = {
        isOwner:false,
        isAdmin:false,
      };
      this.curGroup = {};
      this.groupNameInput = '';
      this.disableGroupNameInput = false;
      this.groupDescInput = '';
      // this.groupFilter = '';
      this.groupMemberFilter = '';
      this.selectedUser = [];
      toggleModal('groupEditModal','close');
    },
    viewGroup(g){
      var self = this;
      // params.exlang = self.dispVar.sessionUser.exlang;
      self.loading = true;
      var d = {
        id:g._id,
        view:true
      }
      self.$http.post('/group/detail',d).then(
        function(ret) {
          ret = ret.body;
          setTimeout(function () {
            self.loading = false;
          }, 100);
          if(ret.ok) {
            RMSrv.dialogAlert(ret.desc);
          } else {
            return window.bus.$emit('flash-message', ret.err);
          }
        },
        function (err) {
          ajaxError(err);
        }
      )
    },
    editGroup(g,notoggle){
      var self = this;
      // params.exlang = self.dispVar.sessionUser.exlang;
      if(!(g.isOwner || g.isAdmin || self.dispVar.isGroupAdmin || (self.dispVar.isShowingAdmin && (g.nm == ':inReal')))){
        return ;
      }
      self.loading = true;
      var d = {
        id:g._id
      }
      self.$http.post('/group/detail',d).then(
        function(ret) {
          ret = ret.body;
          setTimeout(function () {
            self.loading = false;
          }, 100);
          if(ret.ok) {
            var group = ret.group;
            self.curGroup = group;
            self.curGroupVar = ret.user;
            self.groupNameInput = group.nm;
            if (/^:/.test(group.nm)) {
              self.disableGroupNameInput = true;
            }
            self.groupDescInput = group.desc;
            self.pendingMembers = group.pendings || [];
            self.currentMembers = group.members || [];
            self.quitMembers = group.quit || [];
            if (!notoggle) {
              toggleModal('groupEditModal');
            }
          } else {
            return window.bus.$emit('flash-message', ret.err);
          }
        },
        function(err) {
          ajaxError(err);
        }
      )
    },
    loadMore(){
      var self = this;
      self.pg += 1;
      self.getGroupList({pg:self.pg});
    },
    getGroupList(params={}) {
      var self = this;
      // params.exlang = self.dispVar.sessionUser.exlang;
      if(self.nomore){
        return window.bus.$emit('flash-message', 'No more')
      }
      self.loading = true;
      var d = {};
      if (params.pg)
        d.pg = params.pg
      self.$http.post('/group/list',d).then(
        function(ret) {
          ret = ret.body;
          setTimeout(function () {
            self.loading = false;
          }, 500);
          if(ret.ok) {
            // console.log('++++',self.pg);
            if(ret.list.length==0){
              self.nomore = true;
              return window.bus.$emit('flash-message', 'No more')
            }
            if (self.pg == 0) {
              self.groups = ret.list;
            }else {
              self.groups = self.groups.concat(ret.list);
            }
          } else {
            return window.bus.$emit('flash-message', ret.e);
          }
        },
        function(err) {
          ajaxError(err);
        }
      )
    }
  },
  mounted () {
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    var self = this;
    self.getPageData(self.datas, {}, true);
    window.bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
      if(vars.grp){
        var curGrp = self.groups.find((g)=>{
          return g._id == vars.grp
        })
        self.editGroup(curGrp);
      }
    });
    if (vars.groups) {
      self.groups = vars.groups;
    }
    // self.doSearch()
  },
  components: {
    FlashMessage,
    PageSpinner,
    Switched,
    ConfirmWithInput
  }
}
</script>
<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.bar.bar-standard.bar-footer a{
  color: black;
}
#notificationModal.active,
#inviteModal.active,
#groupEditModal.active{
  transform: translate3d(0,0,0);
}
#groupEditModal .filter,
#groupList .input{
  padding: 10px;
  border-bottom: 1px solid #f1f1f1;
}
#groupEditModal .filter input,
#groupList input{
  border-radius: 5px;
  margin-bottom: 0;
  color: #777;
  padding-left:33px;
  background: #f1f1f1;
}
#groupEditModal .filter .cnt,
#groupEditModal .filter > .fa,
#groupList .input .fa{
  position: absolute;
  top: 18px;
  left: 20px;
  color: #777;
  font-size: 17px;
}
#groupEditModal .filter .cnt{
  right: 20px;
  left: calc(100% - 50px);
}
#groupEditModal .filter .cnt .fa{
  font-size: 13px;
  padding-right: 5px;
}
.group-list .group{
  padding: 12px 10px;
  border-bottom: 1px solid #f1f1f1;
}
.group-list .group > div{
  display: inline-block;
  vertical-align: top;
}
.group-list .group .img{
  width: 70px;
  text-align: center;
}
.group-list .group img{
  width: 45px;
  height: 45px;
  border-radius: 50%;
  border:1px solid #f1f1f1;
}
.group-list .group .label{
  text-align: center;
  font-size: 10px;
  color: white;
}
.group-list .group .label span{
  padding: 3px 5px;
  border-radius: 9px;
}
.group-list .group .admin{
  background: #f99543;
}
.group-list .group .owner{
  background: #e03131;
}
.group-list .group .detail{
  color: #777;
  font-size: 12px;
  padding-left: 15px;
}
.group-list .group .detail .fa{
  width: 18px;
  padding-right: 3px;
  display: inline-block;
}
.group-list .group .detail .article{
  color: rgb(66, 139, 202);
}
.group-list .group .detail .fa-bookmark{
  font-size: 14px;
  padding-left: 1px;
}
.group-list .group .nm{
  color: black;
  padding-bottom: 5px;
  font-size: 15px;
}
.group-list .group .member{
  background: #41b433;
}
.group-list .group .edit{
  width: 30px;
  font-size: 13px;
}
.group-list .group .edit div{
  padding: 4px 0px;
}
.group-list .group .detail{
  width: calc(100% - 100px);
}
.bar-footer.footer-tab a{
  text-align: center;
  vertical-align: middle;
  height: 50px;
  cursor: pointer;
  padding: 4px 7px 0 7px;
}
.bar-footer.footer-tab a .icon {
  color: #e03131;
  top: 3px;
  width: 24px;
  height: 24px;
  padding-top: 0;
  padding-bottom: 0;
  display: block;
  margin: auto;
  font-size: 21px;
}
.bar-footer.footer-tab a .tab-label {
  display: block;
  font-size: 10px;
  color: #666;
}
#groupEditModal .filter,
#groupEditModal .members,
#groupEditModal .basic{
  background: white;
}
#groupEditModal .basic .desc > *,
#groupEditModal .basic .name > *{
  display: inline-block;
  vertical-align: top;
}
#groupEditModal .basic .name{
  padding: 10px;
  border-bottom: 1px solid #f1f1f1;
}
#groupEditModal .basic .desc{
  padding: 10px;
}
#groupEditModal .name .input{
  padding-top: 10px;
}
#groupEditModal .basic .input{
  width: 90px;
}
#groupEditModal .basic input,
#groupEditModal .basic textarea{
  width: calc(100% - 90px);
  /* color: #666; */
  margin-bottom: 0;
  border: none;
}
::placeholder { /* Chrome, Firefox, Opera, Safari 10.1+ */
  color: #777;
  opacity: 1; /* Firefox */
}
#groupEditModal .filter{
  margin-top: 10px;
  padding: 10px 10px 20px;
  /* border-top: 10px solid #f1f1f1; */
}
#groupEditModal .content{
  background: #f1f1f1;
}
#groupEditModal .members .avt .pull-left{
  width: 0;
}
#groupEditModal .members .avt .pull-left .fa{
  font-size: 17px;
  color: rgb(224, 49, 49);
}
#groupEditModal .members{
  margin-bottom: 30px;
}
#groupEditModal .members > .status{
  background: #f7f7f7;
  padding: 2px 10px;
  font-size: 14px;
}
#groupEditModal .members > .status .pull-right{
  color: #888;
}
#groupEditModal .members > .status .count{
  padding-left: 7px;
  font-size: 10px;
  color: #666;
}
#groupEditModal .members .dot{
  background: #666;
  border-radius: 50%;
  width: 10px;
  height: 10px;
  display: inline-block;
  margin-right: 8px;
}
#groupEditModal .members .dot.current{
  background: #41b433
}
#groupEditModal .members .dot.pending{
  background: #f99543;
}
#groupEditModal .ul .member{
  position: relative;
  padding: 12px 0 12px 10px;
  border-bottom: 1px solid #f1f1f1;
}
#groupEditModal .ul .member > div{
  display: inline-block;
  color: #666;
  font-size: 13px;
  vertical-align: top;
}
#groupEditModal .ul .member .detail .fa{
  display: inline-block;
  width: 22px;
}
#groupEditModal .ul .member .avt{
  width: 90px;
  text-align: center;
}
#groupEditModal .ul .member .avt img{
  width: 45px;
  height: 45px;
  border-radius: 50%;
}
#groupEditModal .ul .member .accept{
  display: inline-block;
  padding: 0px 5px;
  background: #f99543;
  color: white;
  font-size: 10px;
  border-radius: 12px;
  min-width: 56px;
  /* margin-top: 5px; */
}
#groupEditModal .ul .member .ops{
  width: 45px;
  text-align: right;
}
#groupEditModal .ul .member .detail{
  width: calc(100% - 140px);
  padding-left: 14px;
}
#groupEditModal .ul .member .detail .nm{
  color: black;
  font-size: 15px;
}
#groupEditModal .ul.quit .member .detail .nm,
#groupEditModal .ul.pending .member .detail .nm,
#groupEditModal .ul.quit .member .detail,
#groupEditModal .ul.pending .member .detail{
  color: #b5b5b5;
}
#groupEditModal .dropdown{
  background: white;
  position: absolute;
  right: 0;
  top: 41px;
  z-index: 20;
  float: right;
  border: 1px solid #f1f1f1;
  box-shadow: -1px 3px 5px #bfbfbf;
}
#groupEditModal .dropdown > div{
  padding: 3px 10px;
  border-bottom: 1px solid #f1f1f1;
}
#groupEditModal .ops .fa{
  font-size: 14px;
  color: #888;
  padding: 7px 3px 7px 5px;
}
#groupEditModal .ops .pn-wrapper{
  padding: 7px 0px 7px 0px;
  margin-top: 10px;
}
#groupEditModal .ops .pn-wrapper .text{
  font-size: 10px;
}
#groupEditModal .u-status{
  display: inline-block;
  font-size: 10px;
  padding: 0px 5px;
  border-radius: 10px;
  color: white;
  background: #41b433;
}
#groupEditModal .u-status.admin{
  background: #f99543;
}
#groupEditModal .u-status.owner{
  background: #e03131;
}
#groupEditModal .bar .btn{
  font-size: 10px;
  padding: 5px 9px;
  top: 13px;
}
#notificationModal{
  z-index: 20;
}
#inviteModal .bar-footer,
#notificationModal .bar-footer{
  text-align: center;
}
#inviteModal {
  z-index: 30;
}
#inviteModal .qrcode{
}
#inviteModal .bar-footer span,
#notificationModal .bar-footer span{
  border-radius: 13px;
  font-size: 15px;
  background: #e03131;
  color: white;
  padding: 2px 10px;
  margin-top: 9px;
  display: inline-block;
}
#notificationModal .methods > div{
  padding: 10px;
}
#notificationModal .split,
#inviteModal .split{
  padding: 3px 0 5px 0;
  border-top: 3px solid #f1f1f1;
}
#notificationModal .msg,
#inviteModal .emls-wrapper{
  padding: 10px;
}
#notificationModal .split,
#inviteModal .split{
  margin:0px 10px 0 10px;
}
#notificationModal .tl input{
  font-size: 16px;
  border:none;
  margin: 0;
}
#notificationModal textarea,
#inviteModal textarea{
  padding: 10px 15px;
  border: 2px solid #f1f1f1;
}
/* #notificationModal textarea{
  border:none;
} */
#notificationModal .tl,
#inviteModal .tl{
  padding: 30px 10px 0 10px;
}
#notificationModal .count{
  font-size: 12px;
  color: #888;
}
#notificationModal .count .fa{
  font-size: 12px;
  padding-right: 7px;
}
#inviteModal .tl{
  padding-bottom: 10px;
}
#groupEditModal input.disabled{
  background: #f1f1f1;
  color: #888;
}
</style>
