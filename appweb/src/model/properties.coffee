###
#        This file contains all property realated methods
#  0. to use model, add '<%this.srcPath%>/model' to tpls/config.coffee->apps
#  1. BLACK BOX API, if Switching from mongo -> SQL, do i need to change orig code?
#  2. DRY
#
# 关于查找 la.agnt的数据只有mls房源存在，仍然查props_part
###
config = CONFIG(['serverBase','propertiesBase','share'])
chinaMode = config.serverBase?.chinaMode

# NOTE: 如果useSearchEngine不是ES，则抛出错误
if config.propertiesBase?.useSearchEngine isnt 'ES'
  throw new Error('useSearchEngine is not ES')

debugHelper = INCLUDE 'lib.debug'
# do not leave debug level in production code. can use config or system default
debug = debugHelper.getDebugger()
libPropertiesSqlCreateTable = INCLUDE 'libapp.propertiesSqlCreateTable'
propertyTagHelper = INCLUDE 'libapp.propertyTagHelper'

i18n = INCLUDE 'lib.i18n'
cityHelper = INCLUDE 'lib.cityHelper'
libUser = INCLUDE 'libapp.user'
libP = INCLUDE 'libapp.properties'
statHelper = INCLUDE 'libapp.stat_helper'
propSearch = INCLUDE 'libapp.propSearch'
propTranslate = INCLUDE 'libapp.propertiesTranslate'
# 删除条件判断，直接引入
elasticsearchLib = INCLUDE 'libapp.propElasticSearch'
{inputToDateNum,OhzArrConve24HoursTo12Hours,dateFormat} = INCLUDE 'lib.helpers_date'
libPropertyImage = INCLUDE 'libapp.propertyImage'
# Diff = = INCLUDE 'lib.diff'
libStaticListing = INCLUDE 'model.staticRMlisting'
#require '/Users/<USER>/Documents/RealMaster/realmaster-appweb/src/model/staticRMlisting.coffee'
helpers = INCLUDE 'lib.helpers'
engine = INCLUDE 'libapp.evaluationEngine'
GeoCoder = MODEL 'GeoCoder'
Users = COLLECTION 'chome', 'user'
UserProfile = COLLECTION 'chome', 'user_profile'
UserListingCol = COLLECTION 'chome', 'user_listing'
sqlDBs = INCLUDE 'lib.sqlDBs'
PropertiesCol = COLLECTION 'vow','properties'
libFilterProp = INCLUDE 'libapp.filterProperties'

Projects = COLLECTION 'chome','pre_constrct'
if not chinaMode
  MlsTrebMasterRecords = COLLECTION 'rni','mls_treb_master_records'
  MlsCreaDdfRecords = COLLECTION 'rni','mls_crea_ddf_records'
  MlsTrebMasterIds = COLLECTION 'rni','mls_treb_master_ids'
  MlsCreaDdfIds = COLLECTION 'rni','mls_crea_ddf_ids'
  RESO_TREB_MEDIA_QUEUE= COLLECTION 'rni','reso_treb_evow_media_queue'
BOUNDARY = COLLECTION 'vow','boundary'
School = COLLECTION 'vow','sch_rm_merged'
TransitStop = COLLECTION 'vow','transit_stop'
Census2016 = COLLECTION 'vow','census2016'
Census2021 = COLLECTION 'vow','census2021'

PropertiesDeleted = COLLECTION 'vow','properties_deleted'
Bookkeeping = COLLECTION 'chome', 'bookkeeping'
PropFav = COLLECTION 'vow', 'prop_fav'
CmtyImageCol = COLLECTION 'vow', 'cmty_image'
PropRltrs = COLLECTION 'vow','prop_rltrs'

AdminOpLogs = COLLECTION 'chome', 'admin_op_logs'

util = INCLUDE 'util'
geolib = require 'geolib'
ObjectId = INCLUDE('lib.mongo4').ObjectId
Sqfts_Aggregate = COLLECTION 'vow','sqfts_aggregate'
propAddress = INCLUDE 'lib.propAddress'
similarEngine = INCLUDE 'libapp.similarPropEngine'

UserWatchModel = MODEL 'UserWatch'
PropStatsModel = MODEL 'PropStats'
UserModel = MODEL 'User'
ProvAndCity = MODEL 'ProvAndCity'
{findObjectDiff,deepCopyObject} = INCLUDE 'lib.helpers_object'
isOwnCtrlContactCity = ProvAndCity.isOwnCtrlContactCity

###
geoquery/unt/_id/geoq/loc->$near
[{nm:'addr_i',tp:'like'},{nm:'unt_addr_i',tp:'like'}]
la.agnt._id
la2.agnt._id
la._id
la2._id
aid/bid
{ $text: { $search: '"'+input+'"' } }
[fixddfunt] -> ddfID
[fixPropertyValue] city/prov/cmty
[dailyNotify] -> {saletp:'Lease',prov:cityObj.prov,city:cityObj.city,status:'A',ts:{$gt:baseTs}}
[edmProperties] -> savedSearch
[evaluation] -> loc+ptype+ptype2
[getAgentWithListings] -> la/la2+ptype+saletp+ts
[evaluation/edmproperty] -> slddt
bid_1_status_1_mt_-1?

###
if not chinaMode
  PropertiesCol.createIndex({id:1},{background:true})
  PropertiesCol.createIndex({sid:1},{background:true}) #sid(search/bcreImport)/id/_id
  PropertiesCol.createIndex({ts:-1},{background:true})
  PropertiesCol.createIndex({onD:1},{background:true})
  PropertiesCol.createIndex({rmmt:-1},{background:true}) #[movePropRecordsPostSql] -> mt/rmmt
  PropertiesCol.createIndex({mt:-1,spcts:-1}) #[edmProperties] -> spcts+mt
  PropertiesCol.createIndex {'la.agnt._id':1},{background:true}
  PropertiesCol.createIndex {'la.agnt.id':1},{background:true}
  PropertiesCol.createIndex {'la2.agnt._id':1},{background:true}
  PropertiesCol.createIndex {loc:'2dsphere'},{background:true}
  PropertiesCol.createIndex {topTs:-1},{background:true}
  PropertiesCol.createIndex {_sysexp:1},{expireAfterSeconds:0}
  PropertiesCol.createIndex({'uid':1})
  PropertiesCol.createIndex({uaddr:1,onD:-1})

  UserListingCol.createIndex({id:1})
  UserListingCol.createIndex {ts:-1},{background:true}
  # NOTE: find fav user by prop id, used in watch and pn
  PropFav.createIndex {'id':1}
  # edm find by uid only
  PropFav.createIndex {'uid':1,'g':1,'ts':-1}
  # check if prop faved by user, dont care group
  PropFav.createIndex {'uid':1,'id':1}


#index for boundary query
# PropertiesCol.createIndex({'bndCmty._id':1},{background:true})
# PropertiesCol.createIndex({'cs16':1})

#may need for future query
# PropertiesCol.createIndex({'trnst':1})
# PropertiesCol.createIndex({'provErr':1})


# isSearchEngine = (engine)->
#   return config.propertiesBase?.useSearchEngine is engine

#use global constant
# IS_ELASTICSEARCH = config.propertiesBase?.useSearchEngine is 'ES'

# SQL =
Colls = {
  'Properties':PropertiesCol
}
MSG_STRINGS.def {
  NO_AGENT_ID: 'Agent ID not set yet!',
  NO_AGENT_BID:'Agent BID not set yet!',
  CAN_NOT_VERIFY: 'Can not verify',
  AGREEMENT_SUCCEED:'Verification succeeded',
  AGREEMENT_CANCEL:'Cancel verification',
  REVOKED:'Revoked',
}
# 1. use: MSG_STRINGS
# 2. \n between funcs
# 3. use full name funcs
# 4. libProperties -> libP.MAP_PROP_FIELDS
# 5. group by sub-fns
AUTOCOMPLETE_MIN_LENGTH = 3
MINIAPP_PROP_FIELDS = null
ALL_FIELDS =  libP.ALL_FIELDS #Object.assign {},libP.ALL_FIELDS,{topMt:1}
AUTOCOMPLETE_TIMEOUT = 6000
MARKET_RCMD_FIELDS = {uid:1, avt:1, fn:1, ln:1, cpny:1, bdrms:1, br_plus:1, \
  bthrms:1, lat:1, lng:1, mt:1, ml_num:1, sid:1, pic:1, pho:1, id:1, \
  status:1, lp:1, daddr:1, addr:1, ptp:1, pstyl:1, ts:1, prov:1, city:1, \
  zip:1, market:1, ltp:1, stp:1, topTs:1,topup_pts:1}
# user cant see this fields
USER_EXCLUDES = {
  comm:0,
  owner:0,bm:0,la:0,la2:0,board:0,
  anaClk:0
  anaVw:0
  age1:0
  age2:0
  # sqft1:0 #used in eval
  # sqft2:0
  bths:0
  com_coopb:0
  # ddfID:0 #del later
  faddr:0
  # front_ft:0 #displayed propdetail.vue
  flt:0
  geo:0
  # his:0 #displayed propdetail.vue
  loc:0
  llq:0
  mmap_col:0
  mmap_page:0
  mmap_row:0
  seller:0
  ts_sql:0
  trbbr:0
  bow:0
  agent_id:0
  co_lagt_id:0
  co_logt_ph:0
  co_list:0
  code_treb:0
  coop:0
  holdover:0
  vow:0
  input_date:0
  lagt_ph:0
  # phodl:0
  ps_lsc:0
  # psn #displayed propdetail.vue
  # psn_en:0
  srchst_num:0
  spcts:0
  timestamp:0
  trbbt:0
  fields:0
  rmmt:0
  # trbtp:0 #[propsearch] needLoginToViewProp
  # slddt:0 # displayed for sld prop
  td:0,
  editHis:0
}
# diff role can see diff fields
adminFields = ['vcmobile','vcwechat','vcbrowser','shrp','shrapp','shrpappc','shrpappr', 'shrc','shrr','shrappc','shrappr','vcapp','vcappr','vcappc','vcr','vcc','vcd','bm']
agentFields = ['la','la2','board','occupancy']
groupFields = ["possesion","isEstate","isPOS","missingLvls","comm","offerD",'bm']

# append exclude fields to user
for i in adminFields
  USER_EXCLUDES[i] = 0 #user cant see admin fields
for i in agentFields
  USER_EXCLUDES[i] = 0 #user cant see agent fields
for i in groupFields
  USER_EXCLUDES[i] = 0 #user cant see group fields

ADMIN_EXCLUDES = Object.assign {},USER_EXCLUDES
VIP_REALTOR_EXCLUDES = Object.assign {}, USER_EXCLUDES
GROUP_EXCLUDES =  Object.assign {}, USER_EXCLUDES
for i in adminFields
  delete ADMIN_EXCLUDES[i]
for i in agentFields
  delete ADMIN_EXCLUDES[i] #admin can see agent fields
  delete VIP_REALTOR_EXCLUDES[i]
  delete GROUP_EXCLUDES[i] #group can see agent fields
for i in groupFields
  delete ADMIN_EXCLUDES[i] #admin can see group fields
  delete GROUP_EXCLUDES[i]

###
#-------------------------------------------------------------
#           helper functions
###

addTagsAsync = helpers.addAsyncSupport propertyTagHelper.addtags

# user listing get rcmd
getOneTypeRecommend = (q,opt,cb)->
  limit = 3
  limit = opt.limit if opt.limit
  UserListingCol.findToArray q, {fields:MARKET_RCMD_FIELDS, sort:{topTs:-1,mt:-1}, limit:limit, skip:0}, (err, lret) ->
    lret ?= []
    return cb err,lret

# test if prop is rmlisting
isRMlisting = (id)->
  return /^RM1/.test id
# isMongoId = PropertiesCol.isObjectIDString# (id)-> #
# /^[a-f0-9]{24}$/.test id

appendStp = (prop)->
  return prop.stp if prop.stp
  prop.stp = if /lease|rent/.test prop.saletp then 'Rent' else 'Sale'

# 200<m props
buildGeoqQuery =(p,range)->
  p.lng = helpers.formatLng p.lng
  query =
    geoq:
      $gte:100
    loc:
      $near:
        $geometry:
          type: 'Point'
          coordinates:[parseFloat(p.lng), parseFloat(p.lat)]
        $maxDistance:range
  query

# find user listings by user aid/ddf_rid using elasticsearch
featurListingEsOptions = (user)->
  result = {
    status:'A',
  }
  queryShould = []

  if user.aid
    queryShould.push {bool: {must: [
      {nested: {path: 'la.agnt',query: {term: {'la.agnt._id': user.aid}}}}
    ]}}

  if user.ddf_rid
    queryShould.push {bool: {must: [
      {nested: {path: 'la2.agnt',query: {term: {'la2.agnt._id': user.ddf_rid}}}}
    ]}}

  if user.rid
    if /^\d+$/.test user.rid
      queryShould.push {bool: {must: [
        {nested: {path: 'la.agnt',query: {term: {'la.agnt._id': 'TRB'+user.rid}}}}
      ]}}
    else
      queryShould.push {bool: {must: [
        {nested: {path: 'la.agnt',query: {term: {'la.agnt.id': ''+user.rid}}}}
      ]}}

  if queryShould.length
    result.queryShould = {bool:{should:queryShould}}
  return result


# 搜索独家房，可以多个id一起。
# opt.input = RM1-1234(,RM1-2345)?
searchRMListings = (opt,cb)->
  fields = opt.fields
  {fields,limit,hasloc,skip,limit2,isAdmin,searchMergeProps,searchDelProps} = opt
  rmfields = Object.assign {}, fields, {market:1,'58':1, sp:1,uid:1}
  input = opt.input?.toUpperCase()
  opts =
    limit:limit
    input:input
    # req: req
    skip: skip
    hasloc: hasloc
  searchTxt = input
  if input.indexOf(',') > 0
    searchTxt = input.split(',')
  isAssignAdmin = opt.isAssignAdmin or false
  params = {searchTxt,limit,skip,isAssignAdmin,searchMergeProps,searchDelProps}
  try
    {props} = await Properties.findListingsByElasticSearch {params,fields:rmfields}
  catch err
    debug.error err
    return cb(err)
  processRet props, opts, cb
  return

processRet = (list, opt, cb)->
  return cb(null, {list:[],cnt:0,searchAfter:opt.searchAfter}) unless list?.length
  # NOTE: 优先使用limit2(inputSearch使用的是limit2查询,使用limit会导致返回结果过滤问题)
  limit = opt.limit2 or opt.limit
  input = opt.input
  # req = opt.req
  isRMid = if /^RM1/i.test input then true else false
  hasloc = opt.hasloc
  cnt = opt.cnt or list.length
  cntRemain = opt.cntRemain or 0
  addrList = []
  uids = []
  for p in list
    # if prop not show addr, prop can not be search by addr unless admin
    # rm prop can always be searched by id
    isNotDaddr = ((p.daddr is 'N') or (p.rmdaddr is 'N'))
    isMls = libP.isMlsPrefix(p._id)
    if isNotDaddr
      # id search, not show addr?
      if isMls and (not opt.isAdmin)
        p.addr = ''
      if (not isRMid) and (not isMls) and (not opt.isAdmin)
        continue
    if p.uid
      uids.push p.uid?.toString()
    p.sYear = new Date(p.ts).getFullYear()
    # BUG: in native autocomplete no lpr, remove after native 6.2.5
    p.lp = p.lpr if not p.lp
    addrList.push p
    convertPropDetailDisplayInfo p
  # https://stackoverflow.com/questions/6913512/how-to-sort-an-array-of-objects-by-multiple-fields
  # sort by saletp:1, _id:1, year:1,
  if not opt.noReSort # NOTE: id/addr检索不进行二次排序
    addrList = addrList.sort((a,b)->
      if /Sale/.test(a.saletp) and not /Sale/.test(b.saletp)
        return -1
      if /Sale/.test(b.saletp) and not /Sale/.test(a.saletp)
        return 1
      # FEEDBACK: @sophie 非treb房源排位靠后 eg.DDF24271913
      # if /^TRB/.test(a._id) and not /^TRB/.test(b._id)
      #   return -1
      # if /^TRB/.test(b._id) and not /^TRB/.test(a._id)
      #   return 1
      # if a.ts > b.ts
      #   return -1
      # if a.ts < b.ts
      #   return 1
      return 0
    ).sort((a,b)->
      if a.sYear > b.sYear
        return -1
      if a.sYear < b.sYear
        return 1
      return 0
    )
  #排序后返回limit个
  # console.log 'limit:'+limit
  addrList = addrList.slice(0,limit)
  # NOTE: translated after cb
  # addrList = translate_rmprop_list(req, addrList)
  addrList = addrList.map (p)->
    param =
      p: p
      isRM: isRMid
      hasloc: hasloc
      isMls: isMls
      # req: req
    addKVandModifyFields(param)
  # ret = ret.concat addrList
  # if (isMl_num or isCnServer)
  # console.log '++++++processret',addrList
  finalRet = {list:addrList,cnt:cnt,searchAfter:opt.searchAfter,cntRemain}
  if (not isRMid) or (not opt.isAdmin)
    return cb(null,finalRet)
  Users.findToArray {_id:{$in:uids}},{fields:{eml:1,avt:1}}, (err,uList)->
    if err
      debug.critical err
      return cb MSG_STRINGS.DB_ERROR
    uList ?= []
    uMap = {}
    for u in uList
      uMap[u._id] = u
    # console.log '++++',uMap
    # console.log '++++',addrList
    for p in addrList
      p.avt = uMap[p.uid]?.avt
      eml = uMap[p.uid]?.eml
      p.eml = if Array.isArray eml then eml[0] else eml
    return cb(null, finalRet)

# input is list of ids, splited by ,
isSearchByIds= (input)->
  if input.includes(',')
    ids = input.split(',')
    return ids.every (id) ->
      trimmedId = id.trim()
      libP.isPropObjectId(trimmedId) or
      libP.isMlNum(trimmedId) or
      isRMlisting(trimmedId)
  else
    # 单个ID的情况
    return libP.isPropObjectId(input) or libP.isMlNum(input)

# NOTE : 从ids中提出mongoid，生成objectid列表
filterMongoIds = (ids)->
  mongoIDs = []  #_id 054101015D3D270E15673C8F9586346E
  stringIDs = [] #_id TRBW/BRE
  rmids = []  #id, RM1-xxx
  sids = [] #sid, W12345
  for id in ids
    lid = id.trim().toLowerCase()
    if /^RM/.test id
      rmids.push id
    else if /^[a-z]\d+/.test lid
      sids.push id.toUpperCase()
    else if libP.isPropObjectId(lid)#/\w{24}/.test(input)
      mongoIDs.push new ObjectId(lid)
    else
      stringIDs.push id
  [mongoIDs,stringIDs,rmids,sids]

getSqlFields = (opt,fields)->
  sqlFields = {}
  if config.propertiesBase?.usePostgresql
    # TODO: fields = !opt.fields, = SQL fields, otherwise cause error
    # @Julie @Rain
    keys = Object.keys(fields)
    laFields = ['_id','_iid','_0agnt_iid','_0agnt_id','_0agnt_bid',\
      '_1agnt_iid','_1agnt_id','_1agnt_bid','_2agnt_iid','_2agnt_id',\
      '_2agnt_bid']
    for k in keys
      if k in ['topTs','ddfID','picUrl']
        sqlFields[helpers.capital2dash(k,'_')] = 1
      # check move favU correct.
      else if k in ['la','la2']
        for lav in laFields
          sqlFields[k+lav] = 1
      # TODO: compare to all sql fields list
      else if k not in ['favU','ohz']
        sqlFields[k] = 1
  sqlFields

#search sidList or _id
searchByIds = (opt,cb)->
  # NOTE:must UpperCase for _id,sid
  input = opt.input?.toString().toUpperCase()
  # NOTE:ID检索时根据角色限制能否查询del房源
  libP.genSearchMergeOrDelProps opt
  #array, split by ','
  if not opt.sqlFields
    opt.sqlFields = getSqlFields(opt,opt.fields)
  # console.log '+++++opt',opt
  #list query
  if input.indexOf(',')>0
    sidList = input.split(',') or []
    opt.qOr = []
    values = []
    mongoQ = {$or:[]}
    [mongoIDs,stringIDs,rmids,sids] = filterMongoIds(sidList)
    hasValidId = sids.length or rmids.length or mongoIDs.length
    # 检查是否没有有效ID并且stringIDs不是全部符合MLS前缀格式
    allStringIDsAreMlsPrefix = (stringIDs.length > 0) and stringIDs.every((id)-> libP.isMlsPrefix(id))
    if (not hasValidId) and (not allStringIDsAreMlsPrefix)
      return cb('Invalid Input')

    if sids.length
      opt.qOr.push {nm:'sid',tp:'in',length:sids.length}
      values = values.concat sids
      # # Deprecated: 房源列表检索不推荐使用mongo查找
      mongoQ.$or.push {sid:{$in:sids}}
    if stringIDs.length or mongoIDs.length#/\w{24}/.test(input)
      # sidList = mongoIDs.concat(stringIDs) if mongoIDs.length
      # NOTE: psql db _id all upper case
      upperCaseList = []
      for i in stringIDs
        upperCaseList.push i.toUpperCase()
      opt.qOr.push {nm:'_id',tp:'in',length:stringIDs.length}
      values = values.concat(upperCaseList)

      mongoIdArray = mongoIDs.concat stringIDs
      mongoQ.$or.push {_id:{$in:mongoIdArray}}
    if rmids.length #input.indexOf('RM')>=0
      opt.qOr.push {nm:'id',tp:'in',length:rmids.length}
      values = values.concat(rmids)
      mongoQ.$or.push {id:{$in:rmids}}
  else
    # only id query, use like, rm id query goes to
    #searchRMListings,here only sid or _id
    # TODO: if query by id only, should also query merged properties
    # NOTE: ddf merged prop cound not find by id; otherwise mongo.properties search
    #  affect id search perfomance <.1s -> 10s
    values = [input+'%']
    mongoInput = generateAddrRegex(input, true)
    # useMongo = true
    if libP.isMlsPrefix(input) # id不一定给全,使用正则查询
      opt.qAnd=[{nm:'_id',tp:'like'}]
      mongoQ = {_id:mongoInput}
    else if libP.isPropObjectId(input)
      opt.qAnd=[{nm:'_id',tp:'like'}]
      mongoQ = {_id:new ObjectId(input)}
    else
      opt.qAnd = [{nm:'sid',tp:'like'}]
      # mongoQ = {sid:mongoInput}
      # Deprecated: 房源列表检索不推荐使用mongo查找
      mongoQ = {$or:[{sid:mongoInput},{'aSIDs.sid':mongoInput}]}
  useMongo = false
  opt.Col = PropertiesCol
  opt.searchTxt = input #for elasticsearch
  opt.noReSort = true # 不进行2次排序
  # console.log '++++beforeQueryDb',mongoQ
  queryDB {mongoQ, opt,values, useMongo},(err,ret)->
    return cb err if err
    # 非admin，相同sid只取local board房源，去除ddf房源
    # admin local board房源在ddf房源之前
    libP.filterPropsByPermission ret,opt.isPropAdmin,opt.isShare
    return cb null,ret unless input.indexOf(',') > 0
    list = ret.list
    # 排序，根据传入的ids顺序返回结果列表
    listMap = {}
    sortList = []
    for l in list
      # NOTE：sidList传入时已转大写，数据库查找的数据_id为小写
      listMap[l._id.toString().toUpperCase()] = l
      if l.sid
        # NOTE:sid可能存在merged情况,propAdmin需要都可以看到
        if listMap[l.sid]
          listMap[l.sid].push l
        else
          listMap[l.sid] = [l]
      if /^RM/.test l.id
        listMap[l.id] = l
    for id in sidList
      if listMap[id]
        if Array.isArray(listMap[id])
          sortList = sortList.concat listMap[id]
        else
          sortList.push listMap[id]
    ret.list = sortList
    return cb null,ret

# search by id/sid
generateAddrRegex = (input, caseSensitive)->
  return unless input
  if not caseSensitive
    input = input.toLowerCase()
  reg = /[^a-z0-9\s\-\_\.]/ig
  input = input.replace reg,'.'
  # Got exception: Invalid regular expression: /^.*+1 (647) 836-3588.*$/: Nothing to repeat
  input = input.replace /\s+/g,'\\s+'
  input = input.replace '.','\.'
  if caseSensitive
    return new RegExp('^'+input)
  return new RegExp('^'+input, 'i')

#do search by addr
searchAddr = (opt,cb)->
# TODO: use searchCnt is a bad design, change to more meaningful param
  opt.searchCnt = 1 unless opt.searchCnt
  debug.debug "searchAddr count is #{opt.searchCnt} for #{opt.input}"
  # NOTE:addr检索时根据角色限制能否查询del房源
  libP.genSearchMergeOrDelProps opt
  input = opt.input.slice()#create copy of input
  if input.indexOf(',') > -1
    delete opt.city
    # handle addr like 339 Bain Ave, Toronto
    addrArray = input.split(',')
    input = addrArray[0].trim()
    if opt.searchCnt is 1#first time search with city,second time search without city
      city = addrArray[1].trim()
      opt.city = city
  opt.searchTxt = input
  opt.qAnd = [{nm:'addr_i',tp:'like'}]
  values = [input+'%']
  arr =  input.split(' ')
  if /^\d/.test arr?[1]
    opt.qAnd = []
    opt.qOr = [{nm:'addr_i',tp:'like'},{nm:'unt_addr_i',tp:'like'}]
    values.push input+'%'
  mongoQ = { $text: { $search: '"'+input+'"' } }
  opt.Col = PropertiesCol
  opt.noReSort = true # 不进行2次排序
  queryDB {mongoQ, opt,values},(err,ret)->
    # if addr with city has no result
    # console.log '+++++',ret
    if ret?.list?.length
      # 非admin，相同sid只取local board房源，去除ddf房源
      # admin local board房源在ddf房源之前
      libP.filterPropsByPermission ret,opt.isPropAdmin
    if (opt.input.indexOf(',') > -1) and (ret?.list?.length is 0)
      if (opt.searchCnt is 1)
        opt.searchCnt++
        #TODO: dont use recursion here, search twice in caller
        return searchAddr opt,cb
      return cb(err,ret)
    cb(err,ret)

#search mls listings of mine or my comany or my branch
searchMymlsListing = (opt,cb)->
  input = opt.input
  limit = 50 if limit < 50
  sort = [['mt','descending']]
  user = opt.user
  unless opt.isVipRealtor
    return cb null, 'For VIP user only!'
  if input is '@Me'
    if user.aid #la.[0-3]agent._id
      opt.qOr = [{nm:'la_0agnt_iid'},{nm:'la_1agnt_iid'},{nm:'la_2agnt_iid'}]
      values = [user.aid,user.aid,user.aid]
      mongoQ = { 'la.agnt._id': user.aid }
      opt.agentField = 'la.agnt._id'
      opt.agentValue = user.aid
      opt.searchTxt = input
      queryDB {mongoQ, opt,values},cb
    else if user.ddf_rid #la2.[0-3]agent._id
      opt.qOr = [{nm:'la2_0agnt_iid'},{nm:'la2_1agnt_iid'},{nm:'la2_2agnt_iid'}]
      values = [user.ddf_rid,user.ddf_rid,user.ddf_rid]
      mongoQ = { 'la2.agnt._id': user.ddf_rid }
      opt.agentField = 'la2.agnt._id'
      opt.agentValue = user.ddf_rid
      opt.searchTxt = input
      queryDB {mongoQ, opt,values},cb
    else
      return cb MSG_STRINGS.NO_AGENT_ID
  else if input is '@Branch'
    if user.bid
      opt.qAnd = [{nm:'la_iid'}]
      values = [user.bid]
      mongoQ = { 'la._id': user.bid }
      opt.agentField = 'la._id'
      opt.agentValue = user.bid
      opt.searchTxt = input
      queryDB {mongoQ, opt,values}, cb
    else if user.ddf_bid
      opt.qAnd = [{nm:'la2_iid'}]
      values = [user.ddf_bid]
      mongoQ = { 'la2._id': user.ddf_bid }
      opt.agentField = 'la2._id'
      opt.agentValue = user.ddf_bid
      opt.searchTxt = input
      queryDB {mongoQ, opt,values}, cb
    else
      return cb MSG_STRINGS.NO_AGENT_BID
  else if input is '@Company'
    if user.bid
      bidreg = $regex: ('^' + user.bid.substr(0,7)) #(user.bid.length-2))
      opt.qAnd = [{nm:'la_iid', tp:'like'}]
      values = [user.bid.substr(0,7)+'%']
      mongoQ = { 'la._id': bidreg}
      limit = 80 if limit < 80
      opt.agentField = 'la._id'
      opt.agentValue = bidreg
      opt.searchTxt = input
      queryDB {mongoQ, opt,values}, cb
    else
      return cb MSG_STRINGS.NO_AGENT_BID
  else
    cb MSG_STRINGS.BAD_PARAMETER
  # Deal with not matching with any of previous cases

# search by agent id
searchAgent =(opt,cb)->
  aid = opt.input.substr 1
  opt.qOr = [{nm:'aid'},{nm:'bid'}]
  values = [aid,aid]
  mongoQ = { $or: [{aid:aid},{bid:aid}]}
  queryDB {mongoQ, opt, values}, cb

#mongodb q
queryMongoDB = (q, params,cb) ->
  return cb null, [] unless q
  cfg = {}
  cfg.fields = params.fields
  cfg.limit = params.limit2
  cfg.skip = params.skip
  if sort = params.sort
    cfg.sort = sort
  Col = params.Col
  req = params.req
  if params.soldOnly
    q.lst = {$in:['Sld','Pnd','Cld']}
  if params.apisrc is 'miniapp.sold'
    q.ptype = 'r'
  # 非assignAdmin权限的人不能看楼花的 private is true 的房源
  unless params.isAssignAdmin
    q.private = {$ne: true}
  unless params.searchMergeProps
    q.merged = {$exists:false}
  unless params.searchDelProps
    q.del = {$exists:false}
  Col.findToArray q, cfg, (err, ret)->
    # console.log '+++++',err,ret
    if err
      debug.critical err
      return cb MSG_STRINGS.DB_ERROR
    processRet ret, params, cb

addKVandModifyFields = (opt)->
  p = opt.p
  return {} unless p
  # req = opt.req
  k = null
  v = null
  # if opt.hasloc
  #   k = p.addr
  #   v = p.addr
  if opt.isRM
    k = p.addr
    v = p.addr #'('+p.id+') '+
    # NOTE: nolonger disps id in title, can be found on pic, balack bg
  else if opt.isMls
    k = p.sid+' '+p.addr
    v = p.addr
    if /^RM1/.test p.id
      v += ' ('+p.id+')'
    else
      # origUnt只是为了显示原始的unt，不会被搜索，搜索时用unt
      unt = p.origUnt or p.unt or p.apt_num or ''
      v = if unt then (unt+' '+v) else v
  else
    k = p.addr
    unt = p.origUnt or p.unt or p.apt_num or ''
    v = k = if unt then (unt+' '+k) else k
  #sql 里可能有{"DDF10391867"}
  if ddfID = p.ddf_i_d  # from sql, it is arry ,need to split
    ddfID = ddfID.replace(/\{|\}|"/g,'')
    ddfID = ddfID.split(',')
  tmp = Object.assign {},p, {
    tp:'prop',k:k, v:v,
    ddfID: ddfID or p.ddfID,
    picUrl: p.pic_url or p.picUrl
  }
  if opt.hasloc or opt.isRM
    tmp.lat = p.lat
    tmp.lng = p.lng
  if opt.isRM
    tmp = Object.assign tmp, {uid:p.uid, ltp:p.ltp, ptp:p.ptp, pstyl:p.pstyl}
    if p.market
      tmp.marketSt = p.market.st
    if p['58']
      tmp['58St'] = p['58'].st
  for key, val of tmp #删除空值
    if val is null
      delete tmp[key]
  return tmp

# query
# TODO: naming, DRY with getTranslatedPropListBySearchCond
queryDB = ({mongoQ, opt, values, useMongo}, cb)->
  # limit2 is used in query limit, limit is return cnt
  # opt.limit2 = opt.limit2+1
  # searchMyMlsListing support?
  params = {
    searchTxt:opt.searchTxt,
    city:opt.city,
    agentField:opt.agentField,
    agentValue:opt.agentValue,
    limit:opt.limit2
    page:opt.pageNum or opt.page,
    skip:opt.skip,
    searchAfter:opt.searchAfter,
    sort:opt.sort or '',
    isAssignAdmin: opt.isAssignAdmin or false,
    searchMergeProps:opt.searchMergeProps or false,
    searchDelProps: opt.searchDelProps or false
  }
  try
    {searchAfter,props,cnt,cntRemain} = await Properties.findListingsByElasticSearch {params, fields:opt.fields}
  catch err
    debug.error err
    return cb(err)
  opt.cnt ?= cnt
  opt.cntRemain = cntRemain
  opt.searchAfter = searchAfter if searchAfter?
  processRet props, opt, cb

#ptype and ptype2 mapping
_ptype_ptype2_object =
  '_id': 'PtypeList'
  'list': [
    'Residential'
    'Commercial'
    'Assignment'
    'Landlord'
    'Exclusive'
    'Other'
  ]
  'ptypes':
    'Commercial': [
      'Accommodation'
      'Agricultural'
      'Apartment'
      'Apparel'
      'Art Gallery'
      'Art Supplies'
      'Arts and Entertainment'
      'Automobile'
      'Automotive Related'
      'Bakery'
      'Bank'
      'Banquet Hall'
      'Bar/Tavern/Pub'
      'Beauty Salon'
      'Bed & Breakfast'
      'Business'
      'Butcher/Meat'
      'Cabins/Cottages'
      'Cafe'
      'Campgrounds'
      'Car Wash'
      'Cash Crop'
      'Caterer/Cafeteria'
      'Church'
      'Coffee/Donut Shop'
      'Coin Laundromat'
      'Commercial Condo'
      'Commercial/Retail'
      'Construction'
      'Convenience/Variety'
      'Cooler/Freezer/Food Inspect'
      'Copy/Printing'
      'Crafts/Hobby'
      'Dairy Products'
      'Day Care'
      'Delicatessen'
      'Delivery/Courier'
      'Designated'
      'Detached'
      'Distributing'
      'Drugstore/Pharmacy'
      'Dry Clean/Laundry'
      'Education Services'
      'Electronics'
      'Entertainment'
      'Factory/Manufacturing'
      'Farm'
      'Fast Food/Take Out'
      'Fishing and Hunting'
      'Fitness/Training'
      'Florist'
      'Food Court Outlet'
      'Food Services and Beverage'
      'Footwear'
      'Forestry'
      'Free Standing'
      'Fruit/Vegetable Market'
      'Furniture'
      'Gas Stations'
      'Golf Course'
      'Golf Driving Range'
      'Grocery/Supermarket'
      'Hair Salon'
      'Hardware/Tools'
      'Health & Beauty Related'
      'Health Care and Social Assistance'
      'Highway Commercial'
      'Hobby'
      'Home Improvement'
      'Horse'
      'Hospitality'
      'Hospitality/Food Related'
      'Hotel/Motel/Inn'
      'House'
      'Industrial'
      'Industrial Condo'
      'Institutional'
      'Investment'
      'Laboratory'
      'Land'
      'Livestock'
      'Manufacturing'
      'Marina'
      'Medical/Dental'
      'Mining and Oil and Gas'
      'Multi-Unit'
      'Multi-Use'
      'Office'
      'Other'
      'Personal Services'
      'Pizzeria'
      'Professional'
      'Professional Office'
      'Real Estate Office'
      'Recreation'
      'Recreational'
      'Remediation Services'
      'Residential'
      'Resort'
      'Restaurant'
      'Retail'
      'Retail Store Related'
      'Retail and Wholesale'
      'Sale Of Business'
      'Schools'
      'Scientific and Hi Tech Services'
      'Service'
      'Service Related'
      'Spa/Tanning'
      'Special Purpose'
      'Sports/Entertainment'
      'Store With Apt/Office'
      'Transportation'
      'Transportation and Warehousing'
      'Travel Agency'
      'Utilities'
      'Warehousing'
      'With Property'
      'Without Property'
      'Woodworking'
    ]
    'Other': [
      'Land'
      'Locker'
      'Parking'
      'Store With Apt/Office'
      'Residential Commercial Mix'
      'Other'
    ]
    'Residential': [
      'House'
      'Detached'
      'Semi-Detached'
      'Townhouse'
      'Apartment'
      'Loft'
      'Room'
      'Bungalow'
      'Cottage'
      'Other'
      #'2-Storey'
      #'Multiplex'
      #'Store With Apt/Office'
      #'Residential Commercial Mix'
    ]
    'Assignment':[
      'Detached'
      'Semi-Detached'
      'Townhouse'
      'Apartment'
      'Land'
      'Room'
      'Other'
    ]
    'Landlord':[
      'Detached'
      'Semi-Detached'
      'Townhouse'
      'Apartment'
      'Land'
      'Room'
      'Other'
    ]
    'Exclusive':[
      'Detached'
      'Semi-Detached'
      'Townhouse'
      'Apartment'
      'Room'
      'Other'
    ]
### ----major entry class
#
#  @projection?: fields that needed for return value, = fields for mongodb
#  @user?: current or param user object
#  @locale: req.locale() or param('locale') param('lang')
#  [fileName] function invoked in this file
#
###
langPtypes = {}

pclsToPtype = {
  'c':'r'
  'b':'b'
  'f':'r'
}
#change pcls to ptype, and then convert to full version to show.
convertPropPtype=(prop)->
  return unless prop
  if (not prop.ptype) and prop.pcls
    prop.ptype = pclsToPtype[prop.pcls] or 'o'
  if prop.ptype
    prop.pclass = [prop.ptype]
    prop.ptype = libP.getPtypeFull(prop.ptype)
  # prop.ptype ?= 'r'
  
convertPropsPtypeAndAddr=(props)->
  if Array.isArray props
    for prop in props
      convertPropDetailDisplayInfo prop

convertPropDetailDisplayInfo = (prop)->
  return unless prop
  convertPropPtype prop
  convertPropAddrInfo prop
  appendStp(prop)
  if prop.saletp and (not Array.isArray(prop.saletp))
    prop.saletp = [prop.saletp]
  prop.bsmt = prop.origBsmt if prop.origBsmt
  prop.flt = prop.flt.toFixed(2) if prop.flt
  # processHis(prop)
  # processSqft(prop)
  # if prop.hideBuiltYr
  #   delete prop.rmBltYr

# @fields:
# rmSqft: estimated sqft from RM, also displayed in frontend
# hideSqft: admin setted field
processSqft = (prop)->
  if prop.rmSqft and prop.hideSqft
    delete prop.rmSqft
  if 'number' is typeof prop.sqft or (prop.sqft1 and prop.sqft2)
    return
  else if (prop.rmSqft1 and prop.rmSqft2)
    prop.rmSqft = prop.rmSqft or "#{prop.rmSqft1}-#{prop.rmSqft2}"

HIS_HIDE_FIELDS = ['chM','chVturl','addPic','chOh','sp']

# NOTE: not all history shows to user
processHis = (prop)->
  return unless prop?.his
  newHis = []
  firstPcChange = null
  for his in prop.his
    # do not show status change to user.
    if (his.s is 'Chg') and (his.o in ['A','U'])
      continue
    if his.s is 'Pc' and (not firstPcChange)
      firstPcChange = his
    if his.s not in HIS_HIDE_FIELDS
      newHis.push his
  prop.his = newHis
  if (olp = firstPcChange?.o) and ((not prop.olp) or (prop.olp is prop.lp))
    prop.olp = olp

convertPropAddrInfo=(prop)->
  return unless prop
  # prop.addr = prop.showAddr if prop.showAddr
  # showAddr is different with origAddr.
  # showAddr do partially correct, removed unt out but not formated.
  # addr is correct addr.

  # sometimes computed city,cmty is not corrected, use origCity.
  # NOTE: do not change these fields, will cause bugs in front-end links, eg.lstdhist, cmty-img ...
  # NOTE: if just display should change front-end
  # for fld in ['unt','city','cmty']
  #   # if has orignal fields, show original.
  #   origFld = libP.ADDR_TO_ORIG_FLD_MAPPING[fld]
  #   if prop[origFld]
  #     prop[fld] = prop[origFld]
      
  if prop.prov
    prop.prov = cityHelper.getProvFullName(prop.prov)
    prop.prov_abbr = cityHelper.getProvAbbrName(prop.prov)

findOnePropByQuery = ({Col,query, opt},cb)->
  option = opt or {}
  Col.findOne query,option,(err,prop)->
    if err
      debug.critical err
      return cb MSG_STRINGS.DB_ERROR
    convertPropDetailDisplayInfo(prop)
    cb null,prop

findManyPropByQuery = ({Col,query, params,opt},cb)->
  option = opt or {}
  Col.findToArray query,option,(err,props)->
    if err
      debug.critical err
      return cb MSG_STRINGS.DB_ERROR
    convertPropsPtypeAndAddr props #TODO:for loop 效率
    sortPropListByTopTs params, props if params #
    cb null,props

#TODO:fix sort
round = (d, p) ->
  p ?= 5
  Math.floor(d * 10 ** p) / 10 ** p

clusterKey = (l) ->
  round(l.lat) + ',' + round(l.lng)

isTopListing = (p)->
  now = new Date()
  return true if(p.topup_pts and new Date(p.topTs) > now)
  false

getTopUid = (p) ->
  if p?.topuids?.length > 0
    lastIdx = p.topuids.length - 1
    return p.topuids[lastIdx]
  return null


sortPropListByTopTs=(params, list)->
  if params?.sort
    sort = params.sort.split '-'
    if sort[0] is 'auto'
      list.sort (a,b)->
        return new Date(b?.topTs) - new Date(a?.topTs)
  cluster = {}
  sameClusterHasTop = false
  # propMap = {}
  for p in list
    # propMap[p._id] = p
    if isTopListing(p)
      key = clusterKey(p)
      cluster[key] ?= []
      cluster[key].push p
      if cluster[key].length > 1
        sameClusterHasTop = true
  # console.log 'sameClusterHasTop=',sameClusterHasTop,'cluser=',cluster
  if sameClusterHasTop
    for k,v of cluster
      if v.length > 1
        #remove from list
        for p in v
          list.splice(list.findIndex((key)->
            key._id is p._id
          ),1)
        helpers.shuffle(v)
        list = v.concat(list)
  # console.log('list is now=>')
  # for i in list
  #   console.log i._id,'\t',i.topTs,i.topup_pts
  list

getSortedPropsByIds = (ids,props)->
  sortProps = []
  return sortProps unless props?.length
  propMap = {}
  for p in props
    id = p._id #DDF has no id field
    if p.id and isRMlisting p.id
      rmId = p.id
      propMap[rmId] = p
    propMap[id] = p
  for id in ids
    sortProps.push p if p = propMap[id]
  return sortProps

getAndSortPropListByIds = ({ids,q,obj={},searchMergeProps,searchDelProps,favSort,limit,page},cb)->
  return cb null,[] unless ids.length
  params = {propIds:ids,limit:limit or ids.length}
  if favSort
    params.favSort = favSort
    params.sort = 'auto-ts'
    params.page = page
  params.searchMergeProps = searchMergeProps or false
  params.searchDelProps = searchDelProps or false
  try
    {props} = await Properties.findListingsByElasticSearch {params}
  catch err
    debug.critical 'getAndSortPropListByIds Elasticsearch ids: ',ids,err
    return cb MSG_STRINGS.DB_ERROR
  sortedProps = getSortedPropsByIds ids,props
  return cb(null,sortedProps)

getTrustedAssignCityList = (results)->
  list={}
  if not results?.length
    return list
  for data in results
    prov = cityHelper.getProvFullName data._id.prov
    pr = cityHelper.getProvAbbrName data._id.prov
    city = data._id.city
    if prov and city
      cityUrl = "/#{encodeURIComponent(city).replace(/\-/g,'%2D')}-#{pr}"
      url = "trusted-assignment#{cityUrl}"
      list[prov]?= {count:0,cityList:{}}
      list[prov].cityList[city]?= {count:0,url}
      list[prov].cityList[city].count = data.count
  return list

getAndSortPropListByIdsAsync = ({ids,q,obj={},searchMergeProps,searchDelProps,favSort,limit,page})->
  return new Promise((resolve,reject)->
    Properties.getAndSortPropListByIds {
      ids,
      q,
      obj,
      searchMergeProps,
      searchDelProps,
      favSort,
      limit,
      page
    },(err,ret)->
      if err
        # debug.error err,'Properties.getAndSortPropListByIdsAsync'
        return reject(err)
      resolve(ret)
    )

# aSIDs去除房源本身的ID记录,还需要去除重复ID记录,方便前端展示
filterASIDsForPropDetail = (aSIDs,propId)->
  return [] unless Array.isArray(aSIDs) and aSIDs.length and propId
  # 1. 首先移除id与prop._id相同的记录
  filteredASIDs = aSIDs.filter (item) -> item.id isnt propId
  # 2. 处理剩余记录中id重复的情况
  idGroups = {}
  # 按id分组
  for item in filteredASIDs
    idGroups[item.id] ?= []
    idGroups[item.id].push(item)
  result = []
  # 处理每个分组
  for id, group of idGroups
    if group.length > 1
      # 如果存在重复id，查找sid与id（去除前三位）匹配的记录
      idSuffix = id[3..] # 去除前三位后的id
      matchedItem = group.find (item) -> item.sid is idSuffix
      # 如果找到匹配的记录就使用它，否则使用第一条记录
      result.push(matchedItem or group[0])
    else
      # 如果没有重复，直接添加该记录
      result.push(group[0])
  return result

class Properties #extends L10n
  ### --- simple find functions, findById/fundByUid
  # if no param or already find, use getXXX
  ###
  # 1ms
  # [resource] hide vip/admin detail for prop
  @getAndSortPropListByIds:getAndSortPropListByIds

  @getAndSortPropListByIdsAsync:getAndSortPropListByIdsAsync
  # NOTE: hide internal fields and generate front-end display fields
  @addPropAdditionalFields:({
    prop={}, user,
    params,
    isAllowedPropAdmin,
    isAllowedVipUser,
    isAllowedShowSoldPrice,
    # apisrc,
    fromStr,
    l10n,
    _ab,
    isRealGroup,
  simplfyPtp})->
    #NOTE: used to use trbtp.length [03_propFunctions]@isDDFProp
    #TODO: prop.topTs -> new Date(+1) hide detail
    prop.hasDDFID = prop?.ddfID?.length
    prop.isTRBProp = /^TRB/.test(prop._id)
    prop.isBREProp = /^BRE/.test(prop._id) #to confirm.
    prop.isRHBProp = /^RHB/.test(prop._id)
    prop.isOTWProp = /^OTW/.test(prop._id)
    prop.isCLGProp = /^CLG/.test(prop._id)
    prop.isEDMProp = /^EDM/.test(prop._id)
    prop.isCARProp = /^CAR/.test(prop._id)
    # TODO: decide amount here
    # prop.amount = 'XXXXXXX'
    if not (prop.isTRBProp or prop.isBREProp or prop.isRHBProp or prop.isOTWProp or prop.isCLGProp and prop.isEDMProp or prop.isCARProp)
      if /^RM1/.test prop.id
        prop.isRMProp = true
        libP.setupRMListingFields(prop,{l10n})
      else
        prop.isDDFProp = true#if /^DDF/.test(prop._id) then true else false
    if prop.market?.rmProp or prop.market_rmProp # 真楼花
      prop.marketRmProp = 'Verified'
    if prop.dpst and (not isNaN(parseFloat(prop.dpst)))
      prop.dpst = libP.currencyFormat(prop.dpst,'$',0)
    if prop.origpr and (not isNaN(parseFloat(prop.origpr)))
      prop.origpr = libP.currencyFormat(prop.origpr,'$',0)

    # NOTE: user may not be logged in
    # if user and not user._id
    #   debug.error 'addPropAdditionalFields: has user but no _id:',user
    userId = user?._id?.toString()
    if prop.topuids?.length and userId
      prop.isUserTopListing = prop.topuids.indexOf(userId) >= 0
    # TODO: move to libP, dup with above
    prop.canEditOhs = libUser.canEditOpenHouse({isAllowedPropAdmin,prop,user,isInGroup:isRealGroup})
    prop.hasOh = libP.nearestOhDate(prop) if prop.ohz
    prop.showOhz = OhzArrConve24HoursTo12Hours(prop.ohz) if prop.ohz?.length
    #TODO: favUsr may cause performance issue if has a lot favUser
    prop.favcnt = prop.favcnt or 0
    if (not prop.br_plus?) and prop.tbdrms
      prop.br_plus = prop.tbdrms - parseInt(prop.bdrms or 0)
    prop.dom = libP.computePropDom(prop)
    prop.cmty ?= prop.bndCmty?.nm
    if simplfyPtp
      simpRet = libP.simplfyPtp prop
      prop.ptp   = simpRet[0]
      prop.pstyl = simpRet[1]
    prop.cmstn = prop.market.cmstn if prop.market?
    propSpLstFieldOpt = {isAllowedPropAdmin,isAllowedVipUser}
    # TODO: all props now can display sold price so remove
    libP.filterPropSpLstField(propSpLstFieldOpt, prop)
    libP.filterPropDphoField prop,{isAllowedPropAdmin,isRealGroup}
    libP.setUpPropRMPlusFields(prop,l10n,_ab)
    libP.setupTradeMarkFields(prop)
    libP.setupPropTags(prop,{l10n})
    libP.filterAddress prop,{isAllowedPropAdmin,isAllowedVipUser,isRealGroup}
    # 过滤prediction 相关字段
    libP.filterPredictionValues {isAllowedShowSoldPrice,user,prop}
    prop.propType = (libP.getStandardTypes prop).join('')
    prop.isBuilding = libP.isCondo prop
    if prop.market?.isV and (prop.ltp is 'exlisting' or prop.ltp is 'assignment')
      prop.isAgreementVerified = true
    if prop.bndCmty and prop.bndCmty._id
      prop.hasCmtyId = true
    if (lat = params?.centerLat) and (lng = params?.centerLng) and prop.lat and prop.lng
      dist = geolib.getDistance({latitude:prop.lat,longitude:prop.lng},{latitude:lat,longitude:lng})
      prop.dist = "#{(dist/1000).toFixed(1)}km"
    processHis(prop)
    processSqft(prop)
    prop.rms = libP.getRooms({rms:prop.rms,devType:fromStr}) if prop.rms
    prop.pnSrc ?= []
    # 处理 la/la2 生成 brkgs 字段
    if prop.la or prop.la2
      prop.brkgs = libP.parseLa2(prop.la,prop.la2)
    # if Array.isArray prop.la
    #   ### la:{}
    #         [{}]=>{}
    #         [{},{}]传入前台显示
    #   ###
    #   if prop.la.length is 1
    #     prop.la = prop.la[0]

  ###
  # @description 列表需要
  # @description 删除敏感字段
  ###
  @deletePropPriviteFields:({
    prop={},user,
    isAllowedPropAdmin=false,
    isShare=false,
    isSearchEngine=false,
    isListData=false
  })->
    privateFields = [
      'favUsr' # old favourite users id
      'favU'
      # NOTE: Display to admin only
      # 'rmlog'  # rm operation log by admin
      'phosrc' # gen picUrls
      'pclass' # used in list
      'trbtp'  # [propsearch] needLoginToViewProp
      'ctrdt'  # [03_propFunctions] calc dom
      # 'phodl'
    ]
    if not isAllowedPropAdmin
      privateFields.push 'rmlog'
    if isSearchEngine #搜索的引擎搜到的detail不应展示售价
      # 如果直接delete会导致显示错误，从而显示要价
      prop.priceValStrRed = helpers.maskNumber(prop.priceValStrRed)
      delete prop.sp
    arrFields = ['favGrp']
    #dont send empty array
    for i in arrFields
      if not prop[i]?.length
        delete prop[i]
    #del private fields
    for i in privateFields
      delete prop[i]
    #hide field detail, only bool
    for i in ['topup_pts']
      prop[i] = true if prop[i]
    delete prop.ddfID
    delete prop.ddf_i_d
    delete prop.topuids
    if prop.hideBuiltYr
      delete prop.rmBltYr
    delete prop.trademarkDesc
    # NOTE: not used and may cause json error; will be fixed in new prop/list fields branch
    delete prop.anaClk
    delete prop.anaVw
    # 未登录不展示上市天数(分享房源需要展示)
    if (not user) and (not isShare)
      delete prop.dom
    # 列表数据不需要过多图片,删除图片相关数据,native需要使用picUrls字段，只保留第一张图片
    if isListData
      # 删除图片相关字段以减少数据量
      photoFields = [
        'pho',
        'phodl',
        'phoIDs',
        'phomt',
        'phosrc',
        'PhotoDlDate',
        'photonumbers',
        'phoUrls',
        'pic',
        'picUrl'
      ]
      # 批量删除所有图片相关字段
      delete prop[field] for field in photoFields
      
      # 只保留第一张图片URL
      if prop?.picUrls?.length
        prop.picUrls = [prop.picUrls[0]]
      # NOTE: native需要picUrls字段, 后续需要修改native对picUrls字段的处理
      else if prop?.thumbUrl
        prop.picUrls = [prop.thumbUrl]

  #[sitemap] find rm prop
  @findRMListingsForSitemap:(cb)->
    fields = libStaticListing.SITE_MAP_PROP_FIELDS
    limit = 1000
    params = {src:'rm',saletp:'Sale',limit,ltps:['assignment','exlisting']}
    try
      {props} = await Properties.findListingsByElasticSearch {params,fields}
    catch err
      debug.critical 'findRMListingsForSitemap Elasticsearch',err
      return cb MSG_STRINGS.DB_ERROR
    return cb(null,props)

  # [sitemap] find sold prop
  @findSoldPropForSitemap:(cb)->
    fields = libStaticListing.SITE_MAP_PROP_FIELDS
    limit = 1000
    params = {dom:-90,ptype:'Residential',saletp:'Sale',limit,soldOnly:1}
    try
      {props} = await Properties.findListingsByElasticSearch {params,fields}
    catch err
      debug.critical 'findSoldPropForSitemap Elasticsearch',err
      return cb MSG_STRINGS.DB_ERROR
    return cb(null,props)

  # [sitemap] find 1000 listings sort by ts-1
  @findPropForSitemap:({saletp,limit=1000},cb)->
    fields = libStaticListing.SITE_MAP_PROP_FIELDS
    query = {ptype:'r',status:'A'}
    query.saletp = saletp if saletp
    params = Object.assign query
    params.limit = limit
    try
      {props} = await Properties.findListingsByElasticSearch {params,fields}
    catch err
      debug.critical 'findPropForSitemap Elasticsearch',err
      return cb MSG_STRINGS.DB_ERROR
    return cb(null,props)

  # [prediction/evaluation]
  @findOneByID:(id,{projection},cb)->
    opt = {fields:projection}
    findOnePropByQuery {Col:PropertiesCol,query:{_id:id},opt},cb

  # [rm0Mylisting]
  @findRMOneByID:(id,fields)->
    unless id
      throw MSG_STRINGS.BAD_PARAMETER
    q = {_id:id}
    if isRMlisting id
      q = {id:id}
    projection = {editHis:0}
    if fields
      projection = fields
    return await UserListingCol.findOne q,{projection}

  # [showing]
  @findListingsByIDs:(ids,{projection},cb)->
    query = {_id:{$in:ids}}
    opt = {fields:projection}
    findManyPropByQuery {Col:PropertiesCol,query,opt},cb

  @findListingsByIDsAsync:(ids,{projection},cb)->
    return null unless ids
    query = {_id:{$in:ids}}
    return await PropertiesCol.findToArray query, {fields:projection}

  # [forum/html2img/chat] general, find by fuzz id
  @findOneByFuzzID:(id,{projection},cb)->
    opt = {}
    # throw new Error('test return')
    if projection
      opt.fields = projection
    if libP.isPropObjectId(id) or isRMlisting(id)
      q = {id:id}
      if libP.isPropObjectId(id)
        q = {_id:id}
      UserListingCol.findOne q, opt, (err, prop)->
        if err
          debug.critical err
          return cb MSG_STRINGS.DB_ERROR
        return cb 'No result found!' unless prop
        # nprop = get_translated_prop_fields(req, prop)
        cb null, prop
    else
      # if id.substr(0,2) isnt 'RM'
      q = {}
      if libP.isMlsPrefix(id)
        q = {_id:id}
      else
        q = {sid:id}
      findOnePropByQuery {Col:PropertiesCol,query:q,opt},cb
      # PropertiesCol.findOne q, opt, (err, prop)->
      #   if err
      #     debug.critical err
      #     return cb MSG_STRINGS.DB_ERROR
      #   # nprop = get_translated_prop_fields(req, prop)
      #   convertPropPtype prop
      #   cb null, prop

  # [eval] when has ids
  @findPartialListingsByIDs:(ids,{projection,sort},cb)->
    opt = {}
    if projection
      opt.fields = projection
    if sort
      opt.sort = sort
    query = {_id:{$in:ids}}
    findManyPropByQuery {Col:PropertiesCol,query,opt},cb

  # [evaluation] find listings within 15m range
  @findNearByListings:(p,{projection,sort,limit=51,range=15},cb)->
    # query = buildGeoqQuery p,range
    opt = {
      ignoreStatus:true,
      centerLat:parseFloat(p.lat),
      centerLng:parseFloat(p.lng),
      dist:range,
      geoq:100,
      limit
    }
    if sort
      opt.sort = sort
    if projection
      opt.fields = projection
    try
      {props} = await elasticsearchLib.search opt
    catch err
      debug.error 'elasticsearchLib.search', opt, err
      return cb MSG_STRINGS.DB_ERROR
    convertPropsPtypeAndAddr props
    cb null,props
  
  @findOneByUaddr:({uaddr},cb)->
    query = {uaddr}
    PropertiesCol.findOne query,{fields:{_id:1}},(err,prop)->
      if err
        debug.error 'findOneByUaddr', uaddr, err
        return cb(err)
      cb(null,prop)
  
  @findListingsByElasticSearch:({params,fields})->
    fields = Object.assign({}, libP.MAP_PROP_FIELDS,{sp:1,searchAddr:1}) unless fields
    # addr search dont need m
    if params.searchTxt
      delete fields.m
      delete fields.m_zh
    params.fields = fields
    esRet = await elasticsearchLib.search params
    {props,readable,q,cnt,cntRemain} = esRet
    return {props:[],readable,q} if (cnt is 0)
    return esRet

  # [resource] list/map search props_part
  ###*
   * 查找房源列表
   * @param {Object} params - 查询参数
   * @param {Boolean} isRealtor - 是否为经纪人
   * @returns {Object} 返回查询结果
  ###
  @findListings:({params,isRealtor})->
    # 初始化基础字段
    fields = deepCopyObject libP.LIST_SIMPLE_FIELDS
    # 如果是地图搜索,使用地图所需字段
    if params.bbox or params.needLoc
      fields = deepCopyObject libP.MAP_SIMPLE_FIELDS

    # 如果是真楼花房源,添加多语言和面积字段
    if params.rmProp and (params.ptype is 'Assignment')
      Object.assign(fields, {
        m: 1     # 英文描述
        m_zh: 1  # 中文描述  
        sqft: 1  # 面积
      })

    # 如果是小程序已售房源,使用特定字段
    if params.apisrc is 'miniapp.sold'
      # 缓存小程序房源字段配置
      MINIAPP_PROP_FIELDS ?= Object.assign({}, libP.MAP_PROP_FIELDS, {
        sp: 1     # 销售价格
        sldd: 1   # 售出日期
        slddt: 1  # 售出时间
      })
      fields = MINIAPP_PROP_FIELDS
    # 设置排序
    params.sort = libP.getPropSearchSort params.sort

    # 使用ES搜索
    {props,readable,q,cnt,searchAfter} = await Properties.findListingsByElasticSearch {params,fields}
    sortPropListByTopTs params, props
    return {result:props,readable,q,cnt,searchAfter}

  # [resource] list/map search RMlistings
  # 查询数据切换至Props-Part
  # TODO: no longer used
  @findRMListings:({data,isRealtor,user,ret,limit,skip},cb)->
    fields = libStaticListing.RM_PROP_FIELDS
    qry = {q:{}}
    qry = propSearch.createRMQuery(qry, data, isRealtor)
    sort = qry.sort or {topTs:-1, _id : -1}
    ret.q = qry.q
    UserListingCol.findToArray qry.q,{fields:fields, sort:sort, limit:limit, skip:skip},(err,lret)->
      if err
        debug.critical err
        return cb MSG_STRINGS.DB_ERROR
      lret = sortPropListByTopTs data, lret
      cb null,lret

  # [webPropList] get assignment/exclusive/trustedAssignment city count
  @findCityCountRMListings:({isSiteMap},cb)->
    if isSiteMap
      sitemapUrls = []
    else
      assignmentListings = {}
      exclusiveSaleListings = {}
      exclusiveRentListings = {}
      trustedAssignmentListings = {}
    oneMonth = new Date( new Date().valueOf() - 3600000 * 24 * 31 * 1)
    match = {sid:{$exists:false},status: 'Active','market.st':'Promoted',mt:{$gte:oneMonth}}

    fields = {prov:1,city:1,stp:1,ltp:1,'market.cmstn':1}
    # TODO: refactor
    UserListingCol.findToArray match,{fields},(err,ret)->
      if err
        debug.critical err
        return cb MSG_STRINGS.DB_ERROR
      for {prov,city,stp,ltp,market} in ret
        prov = cityHelper.getProvFullName prov
        pr = cityHelper.getProvAbbrName prov
        if prov and city
          cityUrl = "/#{encodeURIComponent(city).replace(/\-/g,'%2D')}-#{pr}"
          if stp is 'Sale' and ltp is 'assignment'
            url = "assignment#{cityUrl}"
            if isSiteMap
              sitemapUrls.push(url) unless (url in sitemapUrls)
            else
              assignmentListings[prov]?= {count:0,cityList:{}}
              assignmentListings[prov].cityList[city]?= {count:0,url}
              assignmentListings[prov].cityList[city].count++
          if stp is 'Sale' and ltp is 'exlisting'
            url = "exclusive-for-sale#{cityUrl}"
            if isSiteMap
              sitemapUrls.push(url) unless (url in sitemapUrls)
            else
              exclusiveSaleListings[prov]?= {count:0,cityList:{}}
              exclusiveSaleListings[prov].cityList[city]?= {count:0,url}
              exclusiveSaleListings[prov].cityList[city].count++
          if stp is 'Rent' and ltp is 'rent'
            url = "exclusive-for-rent#{cityUrl}"
            if isSiteMap
              sitemapUrls.push(url) unless (url in sitemapUrls)
            else
              exclusiveRentListings[prov]?= {count:0,cityList:{}}
              exclusiveRentListings[prov].cityList[city]?= {count:0,url}
              exclusiveRentListings[prov].cityList[city].count++
      try
        trustedAssignCount = await Properties.statisTrustedAssignByProvAndcity()
      catch err
        debug.critical err
        return cb MSG_STRINGS.DB_ERROR
      trustedAssignmentListings = getTrustedAssignCityList(trustedAssignCount)
      if isSiteMap
        ret = sitemapUrls
      else
        ret = {
          'assignment':assignmentListings,
          'exclusive-for-sale':exclusiveSaleListings,
          'exclusive-for-rent':exclusiveRentListings,
          'trusted-assignment':trustedAssignmentListings
        }
      cb null, ret


  # # [resource]
  # @findTrebRecordByID:(id,cb)->
  #   fields =
  #     # refreshPic:1
  #     predFlw:1
  #   MlsTrebMasterRecords.findOne {_id:id},{fields:fields},(err,prop)->
  #     if err
  #       debug.critical err
  #       return cb MSG_STRINGS.DB_ERROR
  #     prop ?= {}
  #     cb null,prop

  @genExcludes:({isVipRealtor,isAllowedPropAdmin,isDevGroup,isRealGroup,brokerRmrkToVip,isVipPlus})->
    if isAllowedPropAdmin
      return ADMIN_EXCLUDES
    # Dev or realGroup could be vip too so return first
    if isDevGroup or isRealGroup
      return GROUP_EXCLUDES
    if isVipRealtor
      if brokerRmrkToVip and isVipPlus
        excludesObj = Object.assign {}, VIP_REALTOR_EXCLUDES
        delete excludesObj.bm
        return excludesObj
      return VIP_REALTOR_EXCLUDES
    USER_EXCLUDES

  # [03_propFunctions]
  # NOTE: excludes = {nm:0}, return prop obj no 'nm' field
  @findPropertyDetail:({id,src,sid,excludes},cb)->
    params = arguments[0]
    q = {}
    if id
      q._id = id
    else
      q.src = src if src
      q.sid = sid
    fields = {comm:0, owner:0, bm:0}
    query = {_id:id}
    opt = {fields}
    if excludes
      opt.fields = excludes
    findOnePropByQuery {Col:PropertiesCol,query,opt},(err,prop)->
      return cb(err,prop)


  ###
  # @description 查询房源的社区边界
  # @param {string} propertyId - the id of property
  # @return {object} Returns {bnds: object} if found, {} if no boundary exists
  ###
  @getCmtyBnds:(propertyId)->    
    # Get bndCmty from property
    propertyDoc = await PropertiesCol.findOne(
      {_id: propertyId}, 
      {fields: {bndCmty: 1}}
    )
    return {} unless propertyDoc?.bndCmty?._id
    # Get cmty boundary data using cmty ID
    boundaryDoc = await BOUNDARY.findOne(
      {_id: propertyDoc.bndCmty._id},
      {fields: {bnds: 1, nm: 1}}
    )
    return boundaryDoc or {}

  @getPropCmtyImage:(body={},cb)->
    cmtyId = body.id
    unless cmtyId
      #some props have no bndCmty, but has cmty
      #for example: PE island DDF24032678
      p_ab = cityHelper.getProvAbbrName body.prov
      cmtyId = "#{body.cnty}:#{p_ab}:#{body.city.toUpperCase()}:#{body.cmty.toUpperCase()}"
    CmtyImageCol.findOne {_id:cmtyId},{fields:{o:1,b:1}},(err,cmtyImage)->
      return cb(err) if err
      return cb() unless cmtyImage
      cmty_image = null
      if cmtyImage.b
        cmty_image = {fn:"/img/imgstocks/cmtys/#{cmtyImage.b.fn}.jpg",bbox:cmtyImage.b.bbox}
      else if cmtyImage.o
        cmty_image = {fn:"/img/imgstocks/cmtys/#{cmtyImage.o.fn}.jpg",bbox:cmtyImage.o.bbox}
      cb(null,cmty_image)

  # [propDetail] get prop.m and replace fields, only m
  @getPropMbyID:({id},cb)->
    replaceM = (m) ->
      for i in libStaticListing.propMReplaceList
        m = m.replace i.reg, i.to
      return m
    genRet = (ret,prop)->
      if prop?.m_zh
        ret.m_zh = prop.m_zh
      else
        ret.m = replaceM(prop.m)
    ret = {ok:1}
    opt = {fields:{m:1,m_zh:1}}
    if isRMlisting(id)
      UserListingCol.findOne {id:id}, opt, (err,prop)->
        if err
          debug.critical err
          return cb MSG_STRINGS.DB_ERROR
        genRet(ret, prop)
        return cb null,ret
    else
      PropertiesCol.findOne {_id:id}, opt, (err,prop)->
        if err
          debug.critical err
          return cb MSG_STRINGS.DB_ERROR
        genRet(ret, prop)
        cb null, ret

  # [webindex]
  # @getXMLresponse:({},cb)->
  #   PropertiesCol.distinct 'city', (err, items) ->
  #     if err
  #       debug.critical err
  #       return cb MSG_STRINGS.DB_ERROR
  #     response = {}
  #     response.locations = items
  #     PropertiesCol.distinct 'PropertyType', (err, items) ->
  #       if err
  #         debug.critical err
  #         return cb MSG_STRINGS.DB_ERROR
  #       response.types = items
  #       # console.log response
  #       cb null, response

  # [evaluation] ?
  @countPartialEvalNearbyProp:(p,{projection,range=200},cb)->
    query = {bool:{
      must_not: [
        {exists:{field:'merged'}},
        {exists:{field:'del'}}
      ],
      must: [
        {range:{geoq:{gte:100}}},
        {geo_distance:{
            distance: "#{range}m",  # maxDistance，单位为米
            loc: {lat:parseFloat(p.lat), lon:parseFloat(p.lng)}
        }}
      ]
    }}
    try
      cnt = await elasticsearchLib.countDocuments {query}
    catch err
      debug.critical err
      return cb MSG_STRINGS.DB_ERROR
    propcnt = cnt or 0
    return cb null, {propcnt} unless p.getOneProp
    options = {
      ignoreStatus: true,
      geoq: 100,
      centerLat: parseFloat p.lat,
      centerLng: parseFloat p.lng,
      dist: range,
      limit: 1,
      fields: projection
    }
    try
      {props} = await elasticsearchLib.search options
    catch err
      debug.critical err
      return cb MSG_STRINGS.DB_ERROR
    convertPropDetailDisplayInfo props[0]
    return cb null, {prop: props[0], propcnt}

  ###*
   * [resource] find listing history
   * 查找房源地址历史记录
   *
   * 重构说明：
   * - 从 MongoDB 查询重构为 Elasticsearch 查询实现
   * - 保持原有函数签名和返回值格式不变
   * - 支持地址精确匹配和地理位置范围查询
   * - 查询逻辑：uaddr 精确匹配 OR 地理位置范围匹配（20米）
   *
   * @param {Object} params - 查询参数
   * @param {string} params.zip - 邮编
   * @param {string} params.id - 房源ID
   * @param {string} params.prov - 省份
   * @param {string} params.city - 城市
   * @param {string} params.addr - 地址
   * @param {string} params.unt - 单元号
   * @param {number} params.lat - 纬度
   * @param {number} params.lng - 经度
   * @param {string} params.faddr - 完整地址
   * @param {string} params.uaddr - 统一地址
   * @param {string} params.type - 类型
   * @param {string} params.status - 状态
   * @param {string|Array} params.lst - 列表状态
   * @param {string} params.st_num - 街道号码
   * @param {Function} cb - 回调函数
   * @returns {Object} 返回格式: {l: Array, ok: 1, msg?: string}
  ###
  @findAddrHistory: ({zip,id,prov,city,addr,unt,lat,lng,faddr,uaddr,type,status,lst,st_num},cb)->
    # case1 addr diff, lat same
    # case2 addr same, lat diff
    # case3 addr diff, lat diff
    # 1, addr,city,prov-> uaddr+unt , (faddr, street# gone)
    # 2, lat,lng geo near (dist(3,5,20))+unt
    # 3, faddr? similarity test?, addr/sqft/ptype/bthrm/bdrm

    # 定义返回字段（保持与原实现一致）
    fields =
      sid:1,
      id:1, lp:1, lpr:1, sp:1, lst:1,
      onD:1,
      offD:1,
      zip:1,
      mt:1,
      unt:1,
      exp:1,
      sldd:1,
      addr:1,
      del:1,
      st_num:1,
      MlsStatus:1, # 以下几个字段是获取saleTpTag需要
      src:1,
      status:1,
      saletp:1

    # 构建 Elasticsearch 查询参数
    esParams = {
      fields: fields
      sort: 'onD-desc'  # 按上市日期倒序排列
      limit: 100        # 设置合理的查询限制
      searchMergeProps: false # 不搜索合并的房源
      searchDelProps: false # 不搜索删除的房源
      ignoreStatus: true # 忽略status检索
      must: []
    }

    # 添加基础查询条件
    if unt
      esParams.must.push {term: {unt: unt + ''}}
    if status
      esParams.must.push {term: {status: status}}
    if lst
      if lst in ['Sld','Pnd','Cld']
        esParams.must.push {terms: {lst: ['Sld','Pnd','Cld']}}
      else
        esParams.must.push {term: {lst: lst}}

    # 构建地址匹配查询（uaddr 精确匹配 OR 地理位置范围匹配）
    if lat and lng
      esParams.similarShould = {
        uaddr:uaddr,
        geoq:100,
        centerLat: lat,
        centerLng: lng,
        dist: 20
      }
      esParams.similarShould.st_num = st_num if st_num
    else
      esParams.uaddr = uaddr

    try
      # 使用 Elasticsearch 查询替代 MongoDB 查询
      {props} = await elasticsearchLib.search esParams
    catch err
      debug.critical err
      if cb
        return cb MSG_STRINGS.DB_ERROR
      else
        throw MSG_STRINGS.DB_ERROR

    # 保持原有的结果处理逻辑
    # remove dup records
    retList = libP.filterAddrHistoryByAccuracy props,{id,addr,zip}
    ret = {l:retList, ok:1}
    if retList.length is 0
      ret.msg = 'No History Found'
    if cb
      return cb null,ret
    else
      return ret

  # [index] after toplisting, if < 5, getRecentPropsByProvAndCity
  @getRecentPropsByProvAndCity:({city, prov, projection}, cb)->
    # debug.info '+++++',arguments[0]
    opt = {}
    if projection
      opt.fields = projection
    ts = new Date(Date.now() - (3 * 24 * 3600000))
    q2 = {
      status:'A',
      saletp:'Sale',
      ptype:'r',
      topup_pts:{$exists:false},
      pho:{$gt:0},
      ts:{$gt:ts}}
    q2.prov = cityHelper.getProvAbbrName prov if prov
    q2.city = city if city

    opt.limit = 5#limit
    opt.sort = {vc:-1}
    if prov is 'ON'
      opt.skip = Math.floor(Math.random() * 16)
    params = Object.assign q2,opt
    params.ts = ts
    params.pho = true
    params.sort = 'vc-desc'
    try
      {props} = await Properties.findListingsByElasticSearch {params,fields:projection}
    catch err
      debug.critical 'getRecentPropsByProvAndCity Elasticsearch',err
      return cb MSG_STRINGS.DB_ERROR
    return cb(null,props)

  # [40_search_prop_api_sql] search based on user input,
  @inputSearch:({
    user,
    input, # [id] or string:addr
    fields,
    hasloc,
    isUserAdmin,
    isPropAdmin,
    isVipRealtor,
    searchAfter,
    isAdmin,
    limit,
    share,
    incLimit,
    apisrc,
    skip,
    pageNum,
    soldOnly,
    isAssignAdmin
  },cb)->
    if Array.isArray input
      input = input.join(',')
    validInput = ('string' is typeof input) and \
      (input.length>=AUTOCOMPLETE_MIN_LENGTH) and \
      (/[a-z0-9]{2,}/i.test input)
    specialInput = input in ['@Me','@Branch','@Company']
    if (not validInput) and (not specialInput)
      return cb "At lease #{AUTOCOMPLETE_MIN_LENGTH} characters"

    opt = arguments[0]
    fields = fields or libStaticListing.DEFAULT_PROP_FIELDS
    fields.del = 1  # 返回列表需要过滤del房源
    fields.merged = 1  # 返回列表需要过滤merged房源
    fields.origUnt = 1 # 返回列表addr展示需要
    favFields = null
    ohFields = null
    sqlFields = getSqlFields(opt,fields)
    # sql injection check is done in ps node driver
    # https://github.com/brianc/node-postgres/wiki/FAQ
    # remove extra blank space
    input = input.toString().trim()
    input = input.replace(/\s+/g, ' ')
    # admin can find agent
    # agent can find self
    ret = []
    limit = limit or 10
    # user list share can be 40 props;
    unless share
      limit = Math.min(limit,20) # 输入参数，不超过20.
    limit2 = if incLimit then 2*limit else limit
    if hasloc
      fields.lat = true
      fields.lng = true
      sqlFields.lat = 1
      sqlFields.lng = 1

    isMl_num = false
    opt = Object.assign {},opt,{
      input,
      fields,
      sqlFields,
      limit,
      limit2,
      skip: skip or 0,
      favFields,
      ohFields,
      # sort:'auto',
    }

    # if /^TRB|DDF/.test input
    #   searchById opt,cb
    # listing id: TRB[A-Z] TRBW4639043,
    # agent id: TRB\d+
    if (/^\@(TRB\d+)$/.test input) and isUserAdmin # 查经纪
      searchAgent opt, cb
    else if (input in ['@Me','@Branch','@Company'])
      searchMymlsListing opt, cb
    else if isSearchByIds(input)
      searchByIds opt,cb
    else if /^RM1/i.test input
      searchRMListings opt,cb
    else
      #deal with unit
      for char in ['-','_','#']
        input = input.replace char,' '
      searchAddr opt,cb

  @inputRMListingsSearch:({uid,isAllowedPropManage,input,props,limit,skip,isAssignAdmin},cb)->
    input = input.toString().toUpperCase().trim()
    input = input.replace(/\s+/g, ' ')
    # market.st = Promoted  所有人都可以搜索
    if (not props.length) and (/^RM1/i.test input)
      projection = libStaticListing.RM_PROP_FIELDS
      query = {}
      if input.indexOf(',')>0
        query = {id:{$in:input.split(',')}}
      else
        idReg = new RegExp('^'+input)
        query = {id:idReg}
      if not isAllowedPropManage
        if uid
          query['$or'] = [{'market.st':'Promoted'},{uid}]
        else
          query['market.st'] = 'Promoted'
      unless isAssignAdmin
        query.private = {$ne: true}
      limit = limit or 20
      skip = skip or 0
      UserListingCol.findToArray query, {projection,limit,skip,sort:{topTs:-1,ts:-1}}, (err, ret) ->
        cb err if err
        ret = propSearch.convertRMListingsDispToTreb ret
        cb null,ret
    else
      cb null,props

  @searchByIds:searchByIds
  @ptpValuesList:libStaticListing.ptpValuesList
  @getRMListingTemplete: libStaticListing.getRMListingTemplete

  ### ----user related, user_listing and user fav actions
  #
  ###
  # [forum] find user rcmd listing, by uid
  @findUserRcmdListings:(uid,cb)->
    UserListingCol.findToArray {uid:new ObjectId(uid), rcmd:1}, {limit:30, sort:{mt:-1}}, (err, plist)->
      return cb err,plist

  # [mylisting] get user all listings, show in my listing page
  @findUserAllListings:({locale, user,skip,search,isAssignAdmin},cb)->
    fields = {
      vc:1, vcd:1, shr:1, shrd:1, addr58:1, tl:1,
      bdrms:1, br_plus:1, bthrms:1, mt:1,stp:1,
      ltp:1, sid:1, ml_num:1, pic:1, pho:1, id:1,
      status:1, lp:1, addr:1, ptp:1, pstyl:1, ts:1, prov:1,
      city:1, '58':1,  market:1, rcmd:1, isValid:1,marketPromoTs:1,rvkRsn:1}
    opt = {
      fields:fields,
      sort:{mt: -1},
      limit:20,
      skip:skip}
    query = {uid:user._id}
    if search
      query.id = new RegExp(search,'i')
      fields.uid = 1
    if isAssignAdmin
      delete query.uid
    UserListingCol.findToArray query,opt,(err,lret)->
      if err
        debug.critical err
        return cb err,[]
      # _t = getTranslator locale
      _t = i18n.getFun locale
      for prop in lret
        prop.pic?.l = libPropertyImage.getRMListingsImages {sid:prop.sid,imgUrls:prop.pic?.l}
        for i in ['stp','ptp','pstyl','prov','city']
          prop[i + '_en'] = prop[i]
          prop[i] = _t prop[i],'prop' if prop[i]
      return cb null,lret

  # [wesite] get user created active listings
  @findUserActiveListings:({locale, user, page, limit,promoted}, cb) ->
    opt = {
      fields: libStaticListing.RM_PROP_FIELDS,
      sort: {ts: -1},
      skip: (page-1) * (limit-1),
      limit: limit}
    q = {
      uid:user._id,
      ltp:{$in:['exlisting','assignment','rent']},
      status:'Active',
      isValid:true}
    if promoted
      q['market.st']='Promoted'
    UserListingCol.findToArray q,opt,(err, listings) ->
      if err then return cb(err)
      # listings = convertRmListingToTreb(locale, listings)
      return cb(null, listings)

  # [sysAdmin] find 1 prop has agent id -> get bid
  @findOneByAgentId:(aid,cb)->
    q = {$or:[{aid:aid},{aid2:aid}],bid:{$exists:1}}
    opt = {fields:{bid:1},sort:[['mt','descending']]}
    PropertiesCol.findOne q,opt,cb

  # [chat] find user, when from signup
  @findUIDByID:(id,cb)->
    q = {_id:id}
    db = PropertiesCol
    if isRMlisting id
      q = {id:id}
      db = UserListingCol
    try
      ret = await db.findOne q,{fields:{uid:1,topuids:1}}
    catch err
      debug.critical err
      return cb err if cb
      throw err
    return cb null,ret if cb
    return ret
  
  #used in contactRealtor src/libapp/contactRealtor.coffee
  @findRmListingUid:(id)->
    ret = await Properties.findUIDByID id
    return ret?.uid


  #used in contactRealtor
  # @input
  # type id objectId|string; # prop id
  @findMlsTopUid:(id)->
    fields = {topup_pts:1,topTs:1,topuids:1}
    ret = await PropertiesCol.findOne {_id:id}, {fields}
    unless ret
      err = "findMlsTopUid:No prop found for id #{id}"
      # debug.error "No prop found for id #{id}"
      throw err # return ret
    if not isTopListing(ret)
      return null
    return getTopUid(ret)

  @findRecentFavProps:({user,limit,isAllowedVipUser,isAllowedPropAdmin},cb)->
    PropFav.aggregate [
      {$match:{uid:user._id}},
      {$group:{_id:'$id',ts:{$first:'$ts'}}},
      {$sort:{ts:-1}},
      {$limit:limit}
    ], (err, faved)->
      if err
        debug.critical err
        return cb MSG_STRINGS.DB_ERROR
      if not faved.length > 0
        return cb null,[]
      ids = []
      for fav in faved
        id = fav._id
        ids.push id
      q = {}
      libP.filterPropInSearch({isAllowedPropAdmin,isAllowedVipUser,q})
      getAndSortPropListByIds {ids,q,obj:{fields:libP.MAP_PROP_FIELDS}},(err,ret)->
        if err
          debug.critical err
          return cb MSG_STRINGS.DB_ERROR
        cb null,ret


  # [resource] get user fav listings
  @findUserFavProps:({user,locale,page,limit,grp,grps,isAllowedPropAdmin,isAllowedVipUser,favSort})->
    # saleDesc字段存在，表示需要根据指定房源售卖类型sale/sold/delisted进行查找,由于prop_fav表只有id信息，需要把指定文件夹的id全部拿出来
    limit = limit or 20
    skip = 0
    if page?
      skip = limit*(parseInt(page,10) or 0)
    # q = {
    #   favU:{
    #     u:new ObjectId(user._id)
    #     g:grp
    #   }
    # }
    favQ = {
      uid:new ObjectId(user._id)
      g:grp
    }
    sort = {'ts':-1}
    opt = {sort:sort}
    unless favSort
      opt = {skip:skip, limit:limit, sort:sort}
    faved = await PropFav.findToArray favQ, opt
    if not faved.length > 0
      return {}
    ids = []
    for fav in faved
      id = fav.id
      ids.push id
    q = {}
    libP.filterPropInSearch({isAllowedPropAdmin,isAllowedVipUser,q,favSort})
    # 收藏房源查询merged房源
    try
      ret = await getAndSortPropListByIdsAsync {ids,q,obj:{fields:Object.assign(libP.MAP_PROP_FIELDS,{uaddr:1})},searchMergeProps:true,favSort,limit,page}
    catch err
      debug.error err
    # NOTE: prop in prop_fav but not in props_part_alias, should continue search
    count = await PropFav.countDocuments favQ
    return {ret,count}

  # [resource] add prop to user fav
  @addUserFavProp:({id,user,grp,mode},cb)->
    if libP.isPropObjectId(id)#/\w{24}/.test(input)
      id = new ObjectId(id)
    q = {uid:new ObjectId(user._id), g:grp, id:id}
    obj = {}
    msg = 'Saved'
    num = 1
    if mode is 'unfavor'
      num = -1
    Properties.updateFavcnt {ids:id,num},(err)->
      if mode is 'unfavor'
        msg = 'Unsaved'
        PropFav.deleteOne q, (err, ret)->
          debug.critical err if err
          cb err, msg
      else
        obj = Object.assign {},q
        obj.ts = new Date()
        PropFav.updateOne q, {$set:obj},{upsert:true}, (err, ret)->
          debug.critical err if err
          cb err, msg

  # [resource] find prop to user recently viewed
  @findUserViewedProps:({logs,searchMergeProps,searchDelProps},cb)->
    if not logs.length > 0
      return cb null,[]
    ids = []
    for l in logs
      id = l.id
      ids.push id
    getAndSortPropListByIds {ids,searchMergeProps,searchDelProps},(err,ret)->
      cb null, ret

  ###*
   * 为房源添加收藏相关字段
   * @param {Object} options 配置项
   * @param {Array|Object} options.props 房源列表或单个房源对象
   * @param {Boolean} options.detail 是否为详情页
   * @param {String} options.uid 用户ID
   * @param {Function} cb 回调函数
   ###
  @addPropFavFields:({props,detail,uid})->
    # 处理详情页返回单个房源
    returnProps = (props)->
      if detail then return props[0] else return props
      
    # 统一转为数组处理
    props = if detail then [props] else props
    
    # 初始化收藏字段
    props.forEach (prop)->
      prop.fav = false
      prop.favGrp = []
      
    # 未登录直接返回
    unless uid?
      return returnProps(props)
      
    # 获取房源ID列表
    ids = props.map (prop)->
      # es获取的props的_id是字符串，针对rm房源需要转换一下格式，后面用mongo查询才能查到
      if libP.isPropObjectId(prop._id)
        return new ObjectId(prop._id)
      return prop._id
      
    # 查询用户收藏
    query = {id:{$in:ids}, uid:new ObjectId(uid)}
    ret = await PropFav.findToArray(query)

    # 构建收藏映射
    favMap = {}
    ret?.forEach (tmp)->
      id = tmp.id
      favMap[id] ?= []
      if tmp?.g not in favMap[id]
        favMap[id].push(tmp?.g or 0)
        
    # 设置收藏状态
    props.forEach (prop)->
      if favMap[prop._id]
        prop.fav = true
        prop.favGrp = favMap[prop._id]
        
    return returnProps(props)

  @updateFavcnt:({ids,num},cb)->
    if not Array.isArray(ids)
      ids = [ids]
    hasRM = false
    for id in ids
      # 这里不太适合用 isMongoId(id)：PropertiesCol.isObjectIDString 去校验是否是rm房源
      # 因为如果检验的是'64ccc63d8dd13e877f6c79ce',结果为true;但是如果校验的是 new ObjectId("64ccc63d8dd13e877f6c79ce") 结果为false；
      # 调用 updateFavcnt 传入的 ids 存在已经转换为ObjectId的情况，因此可以更换为 ObjectId.isValid(id): 判断给定的值是否可以转换成ObjectId
      # TODO: 后面如果有需要写batch更新一下user_listing的favcnt
      if libP.isPropObjectId(id)#/\w{24}/.test(input)
        id = new ObjectId(id)
        hasRM = true
    if num < 0
      query = {_id:{$in:ids},favcnt:{$gt:0}}
    else
      query = {_id:{$in:ids}}
    PropertiesCol.updateMany query,{$inc:{favcnt:num}},(err)->
      debug.critical err if err
      cb null
      if hasRM
        UserListingCol.updateMany query,{$inc:{favcnt:num}}, (err, ret)->
          debug.critical err if err
          debug.debug 'backend update rmlisting fav count'


  # [propDetail] remove prop by admin, due to CREA/TRB cert @fred 2020-08-05
  @removeListing:({id,user,hide},cb)->
    q = {_id:id}
    if hide?
      set = {$set:{del:1}}
      if hide is 0
        set = {$unset:{del:1}}
      set.$push = {rmlog:{
        k:'del',
        uid:user._id,
        ts:new Date()
        v:hide
      }}
      PropertiesCol.updateOne q,set,(err,ret)->
        if err
          debug.critical err
          return cb null,MSG_STRINGS.DB_ERROR
        return cb null,'hide'
      return
    PropertiesCol.deleteOne q,(err,ret)->
      if err
        debug.critical err
        return cb null,MSG_STRINGS.DB_ERROR
      # NOTE: watch will auto sync to props_part and propSql
      UserProfile.updateOne {_id:user._id},{$push:{delprop:id}},(err,ret)->
        return cb null,'remove'

  # [rm0Mylisting] delete one prop, by user/admin
  @removeUserListing:({isAdmin,id,uid,_id},cb)->
    libP.deleteAndBackupEvowProp \
    {isAdmin,id,uid, RMdb:UserListingCol,db:PropertiesCol, db_deleted:PropertiesDeleted},(err, ret)->
      if err
        debug.critical err
        return cb null,MSG_STRINGS.DB_ERROR
      prop = {id,uid,_id}
      libP.revokeRMPropRecord {prop:prop,db:PropertiesCol},(err,ret)->
        debug.critical err if err
        cb null,ret

  # [wesite] get user cpny or posted mls listing
  @findFeatureListings:({user, page, limit}, cb) ->
    return cb(null) if (not user.aid) and (not user.bid) and (not user.rid) and (not user.ddf_rid)
    esOpt = featurListingEsOptions user
    options = Object.assign {
      fields: libP.LIST_PROP_FIELDS,
      sort: {ts: -1},
      skip: (page-1) * (limit-1),
      limit: limit
    }, esOpt
    try
      {props} = await elasticsearchLib.search options
    catch err
      debug.critical err
      return cb MSG_STRINGS.DB_ERROR
    convertPropsPtypeAndAddr props
    cb null, props

  # [wesite] count user listings
  @countUserListings:(user,cb)->
    ret = []
    q = {
      uid :user._id,
      ltp:{$in:['exlisting','assignment','rent']},
      status:'Active'}
    UserListingCol.countDocuments q, (err, cnt)->
      if err
        debug.critical err
        return cb MSG_STRINGS.DB_ERROR
      ret.push cnt
      if (not user.aid) and (not user.rid) and (not user.bid)
        return cb null,ret
      esOpt = featurListingEsOptions user
      query = {bool:{
        must:[{term:{status:'A'}}],
        must_not: [
          {exists:{field:'merged'}},
          {exists:{field:'del'}}
        ]
      }}
      if esOpt.queryShould
        query.bool.must.push esOpt.queryShould
      try
        cnt = await elasticsearchLib.countDocuments {query}
      catch err
        debug.critical err
        return cb MSG_STRINGS.DB_ERROR
      ret.push cnt
      cb null ,ret

  # [resource]
  @clearUserFavsByGroup: ({user,grp},cb)->
    # update = $pull:{}
    # update.$pull['favU'] = {u:new ObjectId(user._id), g:grp}
    q =
      uid:new ObjectId(user._id)
      g:grp
    PropFav.findToArray q,(err, favs)->
      debug.critical err if err
      PropFav.deleteMany q,(err, ret)->
        debug.critical err if err
        ids=[]
        for fav in favs
          ids.push fav.id
        Properties.updateFavcnt {ids,num:-1},(err)->
          debug.critical err if err
          cb null,'Cleared'

  ### ----RMListing, promote and revoke
  #
  ###
  @updateRMListing:({q,update,opt,rmProp},cb)->
    curDate = new Date()
    update['$set'].mt = curDate
    if city = update['$set'].city
      update['$set'].city = cityHelper.replaceSymbols city
    #Todo:convert prov to abbr version in import
    if prov = update['$set'].prov
      update['$set'].prov = cityHelper.getProvAbbrName prov
    updatedTime = 1000 * 60 * 60 * 24 * 120 # 120天的毫秒数
    if rmProp is 'self'
      updatedTime = 1000 * 60 * 60 * 24 * 210 # 210天的毫秒数
    _sysexp = helpers.getAppendedDate {date:curDate,updatedTime}
    UserListingCol.findOneAndUpdate q, update, opt, (err,result)->
      # update this prop in properties, altoughprop may not exists in propertiesCol
      # if this prop not promoted, this prop not exists in UserListingCol
      set = {_sysexp,mt:curDate}
      # only promote sets spcts for first time
      if spcts = update['$set'].spcts
        set.spcts = spcts
      PropertiesCol.updateOne {id:q.id}, {$set:set}, (err1,ret1)->
        debug.critical err if err1
        cb(err,result)

  # [rm0Mylisting] revoke from market
  @revokeListingMarket:({to,cmstn,id},cb)->
    return cb MSG_STRINGS.BAD_PARAMETER, 0 unless id
    update = {$set:{}}
    ret = {
      mt : new Date()
      st : 'Revoked'
      cmstn: cmstn
    }
    update.$set[to + '.mt'] = ret.mt
    update.$set[to + '.st'] = ret.st
    opt = {upsert:false, returnDocument:'after'}
    UserListingCol.findOneAndUpdate {id: id}, update,opt, (err,result)->
      if err
        debug.critical err
        return cb MSG_STRINGS.DB_ERROR
      return cb 'Error: no listing found' unless result?.value?.id
      opt = {prop:result.value,db:PropertiesCol}
      libP.revokeRMPropRecord opt,(err,ret)->
        if err
          debug.critical err
          return cb MSG_STRINGS.DB_ERROR
        # console.log '++++++revoke',ret
        cb null, { msg:'market revoked', val:ret}

  # [rm0Mylisting] promote -> market
  @promoteListingMarket:({
    marketPromoTs,
    to,
    cmstn,
    adok,
    id,
    agrmntImg,
    isV,
    rmProp,
    sortOrder,
    nm,
    mbl
  },cb)->
    return cb MSG_STRINGS.BAD_PARAMETER, 0 unless id
    # updateListingAfter3p data, (err, ret)->
    #   return cb err if err
    #   cb null, ret
    update = {
      $set:{
        adok:adok
      }
    }
    curDate = new Date()
    ret = {
      ts: curDate
      # set state, Promoting/Promoted/Promoting Error
      st: 'Promoted'
      cmstn: cmstn or ''
      adok: adok
    }
    if agrmntImg
      ret.agrmntImg = agrmntImg
    if rmProp
      ret.rmProp = rmProp
    if sortOrder
      ret.sortOrder = sortOrder
    if nm
      ret.nm = nm
    if mbl
      ret.mbl = mbl
    ret.isV = isV or false
    update.$set[to] = ret
    update.$set.spcts = curDate # ES sort by spcts
    update.$set.marketPromoTs = curDate unless marketPromoTs
    opt = {upsert:false, returnDocument:'after'}
    q = {id}
    Properties.updateRMListing {q,update,opt,rmProp},(err,result)->
      if err
        debug.critical err
        return cb MSG_STRINGS.DB_ERROR
      return cb 'Error: no listing found' unless result?.value?.id
      if result?.value?.editHis # 楼花的editHis历史不进入Properties和props_part
        delete result.value['editHis']
      cfg = {prop:result.value,db:PropertiesCol}
      # if getConfig('useMysql') == 1
      # if config.useMysql
      #   cfg.dbNm ='vow'
      #   cfg.dbCfg = dbCfg
      # # else if getConfig('usePostgresql') == 1
      # else
      if config.propertiesBase?.usePostgresql
        cfg.dbNm = 'listingPostsql'
      libP.importRMPropRecord cfg,(err,ret2)->
        if err
          debug.critical err
          return cb MSG_STRINGS.DB_ERROR
        cb null, {val:ret, msg:'market promoted'}

  # [rm0Mylisting] save rmlisting
  # 1-10 params
  #{user,prop={complexObject},promoteDest}
  #{addr,city,prov,lat....20}
  @saveRMListing: ({prop,user},cb)->
    #save to listings use findOneandUpdate
    return cb MSG_STRINGS.BAD_PARAMETER unless prop.id
    # update = data.prop
    update = {}
    unset = {}
    fields = libStaticListing.rmPropFields
    if prop.prov
      prop.prov = cityHelper.getProvAbbrName prop.prov, prop.cnty
    for k, v of prop
      if k in fields
        if v?#v or v is 0 or v is ''
          update[k] = v
        else
          unset[k] = 1
    unset.rvkRsn = 1 if prop.rvkRsn is ''
    update.isValid = prop.isValid or false
    update.addr58 = update.addr if update.addr
    update.ptp = update.ptp[0] if update.ptp
    update.uid = user._id
    # update.mt = new Date() #last modify time
    # NOTE: has case where toped this listing; default = .ts
    update.topTs = new Date() unless prop.topTs
    update.stp = if (prop.ltp in ['rent','mlsrent']) then 'Rent' else 'Sale'

    if 'string' is typeof update.sqft
      if update.sqft.length is 0 # sqft为空字符串需要删除sqft1和sqft2，否则他们还保持着上一次的值
        if update.sqft1
          delete update.sqft1
          unset.sqft1 = 1
        if update.sqft2
          delete update.sqft2
          unset.sqft2 = 1
      else
        [sqft1,sqft2] = libP.getEdges update,'sqft'
        update.sqft1 = sqft1 if sqft1?
        update.sqft2 = sqft2 if sqft2?

    if 'string' is typeof update.age
      [age1,age2] = libP.getEdges update,'age'
      update.age1 = age1 if age1?
      update.age2 = age2 if age2?

    update.src = update.src or 'RM'
    #allow other people to advertise
    update.adok = prop.adok or 0 #1
    update.ts = if update.ts then new Date(update.ts) else new Date()
    update.exp = new Date(update.exp) #convert update.exp
    #if not a number or < now, set to 3 month later
    if isNaN(update.exp) or (update.exp.valueOf() < new Date().valueOf())
      update.exp = new Date( new Date().valueOf() + 3600000 * 24 * 90)

    if update.psn and update.psn not in ['immed','tba']
      d = new Date(update.psn)
      update.psn = if isNaN(d) then 'tba' else d

    else if update.psnf and update.psnt
      update.psnf = new Date(update.psnf)
      update.psnt = new Date(update.psnt)

    if update['psn']
      delete update.psnf
      delete update.psnt
      unset.psnf = 1
      unset.psnt = 1
    if update['psnf']
      delete update.psn
      unset.psn = 1
    loc = {
      type:'Point',
      coordinates:[prop.lng,prop.lat]
    }
    tagProp = Object.assign prop,{geoq: 130,loc: loc,useMlatlng: true}
    tagParms = {
      prop: tagProp,
      Boundary: BOUNDARY,
      School,
      TransitStop,
      Census2016,
      Census2021,
    }
    try
      tags = await addTagsAsync tagParms
    catch err
      return cb MSG_STRINGS.DB_ERROR
    for key of propertyTagHelper.getPropertyAllTagFields()
      if tags[key]
        update[key]= tags[key]
    # 在选择地址时已经进行过判断，传入的prop如果有subCity，更新，如果没有可能是地址变更或者本来就是主城市，则需要去掉该字段
    if prop.subCity
      update.subCity = prop.subCity
    else
      unset.subCity = 1
    # 如果城市是subcity，city改成主城市,并且添加subCity字段(针对之前保存的楼花修改除了地址的其他参数进行了保存)
    unless prop.city
      debug.warn 'prop.city is null',prop
    isSubCity = cityHelper.isSubCityAndGetParentCity(prop.city)
    if isSubCity.result
      update.subCity = prop.city
      update.city = isSubCity.city
      delete unset.subCity
    if update.pic?.l
      update.pic.l = libPropertyImage.removeHostFromUrls(update.pic.l)
    set = {$set:update}
    set.$unset = unset if Object.keys(unset).length
    opt = {upsert:true,returnDocument:'after'}
    if prop._id and (prop.ltp is 'assignment')
      try
        oldRmInfo = await Properties.findRMOneByID prop._id
      catch err
        debug.error err
        return cb MSG_STRINGS.DB_ERROR
      # NOTE: oldRmInfo cound not exists
      if not oldRmInfo
        debug.error 'user_listing maybe deleted, update info:',update
      [ ...diffFields ] = fields
      diffFields.push('subCity')
      diffData = findObjectDiff oldRmInfo,update,diffFields
      if (diffData.result is false) and Object.keys(diffData.diff)?.length > 0
        diffData.diff.ts = new Date()
        diffData.diff.uid = user._id
        push = diffData.diff
        set.$push = {editHis:push}
      if oldRmInfo and (update.uid isnt oldRmInfo.uid)
        update.uid = oldRmInfo.uid
    q = {id: update.id, uid:update.uid}
    Properties.updateRMListing {q,update:set,opt},(err,ret)->
      if err
        debug.critical err
        return cb MSG_STRINGS.DB_ERROR
      # NOTE: need this value._id for revoke
      return cb 'Error: no listing saved' unless ret.value
      # https://mongodb.github.io/node-mongodb-native/3.6/api/Collection.html#~findAndModifyWriteOpResult
      # Document returned from the findAndModify command. If no documents were found, value will be null by default even if a document was upserted unless returnDocument is specified as 'after', in which case the upserted document will be returned.
      # NOTE: might be a bug with mongodb driver 3.6.6 on ca8, local 3.6.12 has no issue
      if update.status isnt 'Active'# ['Inactive','Terminated']
        # update.sts = new Date()
        prop = {id:update.id,_id:ret.value._id,uid:update.uid}
        if (ret.value.status is 'Sold') and ret.value.market?.rmProp # 用于真楼花在mls的home页的sold中可以查找到
          prop.lst = 'Sld'
        opt = {changeStatus:true,prop:prop,db:PropertiesCol}
        libP.revokeRMPropRecord opt,(err,ret)->
          debug.warn err
      cb null, 'saved, ' + update.id

  # [rm0Mylisting], promote to 58
  # data={to,from}
  @promoteListing58:({req,to,tl,addr58,id,user},cb)->
    update = {$set:{}}
    ret = {
      mt : new Date()
      st : 'To Be Promoted'
    }
    update.$set[to + '.mt'] = ret.mt
    update.$set[to + '.st'] = ret.st
    if to is '58'
      update.$set['tl']     = tl if tl
      update.$set['tllck']  = 1 if tl
      update.$set['addr58'] = addr58 if addr58
    data = {to, id, tp:'mylisting'}
    # NOTE: need req.params in url
    urlRet = libP.generate_share_url_from_data(req, config.share?.secret, data)
    update.$set[to + '.surl'] = (urlRet.url + 'lang=zh-cn&wDl=1&aid=' + user?._id) if urlRet.ok
    opt = {upsert:false, returnDocument:'after'}
    Properties.updateRMListing {q:{id},update,opt},(err,result)->
      if err
        debug.critical err
        return cb MSG_STRINGS.DB_ERROR
      return cb 'Error: no listing found' unless result.value
      cb null, {msg:to + ' promoted', val:ret }

  # [rm0Mylisting], find prop by id and uid to revoke it
  @revokeListing58:({id,uid,to},cb)->
    UserListingCol.findOne {id, uid} , (err,ret)->
      if err
        debug.critical err
        return cb MSG_STRINGS.DB_ERROR
      return cb 'Error: No listing found' unless ret
      return cb 'Error: Not published yet' unless ret['58']
      if ret['58'].st in ['Revoke Pending'] #'To Be Revoked',
        return cb 'Error: ' + ret['58'].st
      update = {$set:{}}
      # if mt changed, it will affect listing order
      ret = {
        ts: new Date()
        st: 'To Be Revoked'
      }
      update.$set[to + '.mt'] = ret.mt
      update.$set[to + '.st'] = ret.st
      opt = {upsert:false, returnDocument:'after'}
      Properties.updateRMListing {q:{id},update,opt},(err,result)->
        if err
          debug.critical err
          return cb MSG_STRINGS.DB_ERROR
        return cb 'Error: no listing found' unless result.value
        cb null, {msg:to + ' tobeRevoked', val:ret}

  ### ----topListing related, add/revoke
  #
  ###

  # [propTopUp] get city top listings
  @getTopUpListingsByCity:({city,prov},cb)->
    unless city and prov
      return cb null, []
    now = new Date()
    options = {
      ignoreStatus:true,
      must: [
        {term: {city}}
        {term: {prov}}
        {range: {topup_pts: {gte: 500}}}
        {range: {topTs: {gte: now}}}
      ],
      sort:{topTs:-1},
      fields:{
        addr:1,
        _id:1,
        topup_pts:1,
        topTs:1,
      },
      limit:10
    }
    try
      {props} = await elasticsearchLib.search options
    catch err
      debug.critical err
      return cb MSG_STRINGS.DB_ERROR
    cb null, props

  # [propTopUp] init toplisting
  @findAllTopListing:({src},cb)->
    fields = Object.assign ALL_FIELDS,{hideInfo:1}
    params = {topTs:1,limit:1000,searchMergeProps:1}
    try
      {props,readable,q} = await Properties.findListingsByElasticSearch {fields,params}
    catch err
      debug.critical 'findAllTopListing Elasticsearch',err
      return cb MSG_STRINGS.DB_ERROR
    return cb(null,props)

  # [propTopUp] after verify do topup steps
  @doTopListing:({id,amount,uid,days,tlBcc,hideInfo},cb)->
    points = parseInt(amount,10) or 0
    q = {_id:id}
    updateMt = false
    isRMProp = false
    if libP.isPropObjectId id
      isRMProp = true
      updateMt = true
    PropertiesCol.findOne q, {fields:{topTs:1, city:1, prov:1}}, (err, ret)->
      if err
        debug.critical err
        return cb MSG_STRINGS.DB_ERROR
      if not ret
        err = 'No prop found'
        return cb err
      #amount are in cents -> miliseconds
      #use max of 2
      topTsDate = new Date(ret.topTs or new Date())
      topTs = Math.max(+topTsDate, Date.now())
      # points = 10*(24*60*60)*points/PRICE_PER_DAY
      topTsDate = new Date(topTs)
      unless days
        return cb 'No days'
      tsNew = days*(24*60*60)*1000
      newTs = new Date(+topTs+tsNew)
      update = {
        $inc:{
          topup_pts:amount
        }
        $push:{
          topuids:uid.toString()
        }
      }
      update.$set = {
        topTs:newTs
        topMt:new Date()
      }
      if updateMt
        update.$set.mt = new Date()
      if tlBcc
        update.$set.tlBcc = tlBcc
      # NOTE: hideInfo hides top agent info
      if hideInfo is true
        update.$set.hideInfo = true
      else
        update.$unset = {hideInfo: 1}
      PropertiesCol.findOneAndUpdate q, update, {returnDocument:'after'}, (err, uret)->
        if err
          debug.critical err
          return cb MSG_STRINGS.DB_ERROR
        ret = uret.value
        # get old topTs for send receipt email
        ret.topTs = topTsDate
        debug.info 'Increased '+id+' '+points
        ret.newTs = newTs
        if isRMProp
          UserListingCol.updateOne q,update, (err, uret)->
            debug.critical err if err
        cb null, ret

  # [propDetail]
  @removeTopListing:({id,user},cb)->
    now = new Date()
    #   now = new Date(ts) or now
    set = {topTs:now}
    update = {$set:set}
    #dbStartTransaction
    PropertiesCol.updateOne {_id:id}, update, {upsert:false}, (err,ret)->
      if err
        debug.critical err
        return cb(MSG_STRINGS.DB_ERROR)
      cb null,'removed'
        # TODO: if rmlisting, remove top?
        # use mongo transaction
      doc = {
        type:'RemovePropTopUp',
        item:id,
        ts:new Date(),
        uid:user._id,
        eml:user.eml
      }
      Bookkeeping.insertOne doc,(err,ret)->
        #dbCommitTransaction (err)->
        #  dbRollback()
        debug.critical err if err

  # [realtorVerify] prop->agent list; find top user
  @findTopUpUIDById:(id,cb)->
    if not id
      return cb null,null
    PropertiesCol.findOne {_id:id,topTs:{$gte:new Date()}},{fields:{topuids:1,_id:1}},cb

  ### ---- Update Properties
  #
  ###

  # [x_apiShare] inc listing status(vc/shr/shrd)(anything runs on inc), use req.body
  @incListingStats:({
    ids,
    isView, # view flag, else is for share
    pre,    # flag for pre-share, share clicked but not shard
    user,   # user obj
    suffix, # suffix indicated view location, wb/yx/dz/app
    devType # app or web
  }, cb)->
    getPrefix = ()->
      if isView
        return 'vc'
      if pre then 'shrp' else 'shr'
    getIncObj = (prefix)->
      inc = {}
      inc[prefix] = 1
      inc[prefix+'d'] = 1
      inc
    opt = arguments[0]
    # console.log '++++++++',ids,opt
    unless cb
      cb = (err,ret)->
        debug.verbose 'incListingStats: err=',err,'ret=',ret
    return cb(MSG_STRINGS.BAD_PARAMETER) unless ids
    if ('string' is typeof ids) #and /,/.test ids[0]
      ids = ids.split(',')
    return cb(MSG_STRINGS.BAD_PARAMETER) unless (Array.isArray(ids) and ids.length)
    prefix = getPrefix()
    inc = getIncObj(prefix)
    # devType区分app/web
    if devType is 'app'
      prefix += 'app'
    if suffix
      inc[prefix+suffix] = 1
    role = 'c'
    if user and libUser.isRealtor(user)
      role = 'r'
    inc[prefix+role] = 1
    _ids = [] #TRB/DDF
    rmids = [] #RM1-
    rm_ids = [] #a787bc0idd0e9f8
    # hasRMProp = false
    for id in ids
      if isRMlisting id
        rmids.push id
      else if libP.isPropObjectId id#/\w{24}/.test id
        rm_ids.push id
      else if libP.isMlsPrefix(id)
        _ids.push id
    q = {_id:{$in:_ids}}
    debug.debug '_ids: ',_ids
    # if not (rm_ids.length or rmids.length or _ids.length)
    cb null, 'done'
    if _ids.length
      PropertiesCol.updateMany q, {$inc:inc}, {upsert:false}, (err,ret)->
        if err
          debug.error err,'PropertiesCol.updateMany'
          # return cb('Not Updated')
    debug.debug 'rmids: ',rmids
    if rm_ids.length or rmids.length
      q = {$or:[{id:$in:rmids},{_id:$in:rm_ids}]}
      UserListingCol.updateMany q, {$inc:inc}, {upsert:false}, (err, ret)->
        if err
          debug.error err,'UserListingCol.updateMany'
        #   return cb('Not Updated')
        # return cb null, 'done'

  @incListingShareStatsAsync:({id,user,devType})->
    fieldStr = 'shr'
    if devType is 'app'
      fieldStr = 'shrapp'
    if user
      role = if libUser.isRealtor(user) then 'r' else 'c'
      fieldStr += role
    inc = {shr:1}
    inc[fieldStr] = 1
    if /^RM1/.test id
      await UserListingCol.updateOne { id }, { $inc: inc }
    else
      await PropertiesCol.updateOne { _id:id }, { $inc: inc }

  # [rm0Mylisting] setRMPropRecommand
  @setRMPropRecommand: ({id,rcmd},cb)->
    UserListingCol.updateOne {id:id}, {$set:{rcmd:rcmd}}, (err,prop)->
      if err
        debug.critical err
        return cb MSG_STRINGS.DB_ERROR
      cb null,{result:prop?.result}

  # [resource] set edm flag
  @updateRMpropEDM:({id,del},cb)->
    q = {_id:id}
    edmts = new Date()
    cmd = {$set: {edm:edmts}}
    if del
      cmd = {$unset:{edm:1}}
      edmts = null
    UserListingCol.updateOne q, cmd, {upsert:false}, (err,ret)->
      if err
        debug.critical err
        return cb MSG_STRINGS.DB_ERROR
      cb null,edmts

  # [propDetail]
  @updatePropVideoLink:({id,vurlcn,ytvid,vimgcn,yturl,clearVid},cb)->
    opt = arguments[0]
    set = {vurlcn,ytvid,vimgcn,yturl,clearVid}
    update = {$set:set}
    if clearVid
      unset = {}
      for i in ['yturl','ytvid','vurlcn','vimgcn']
        unset[i] = 1 if not opt[i]
      update = {$unset:unset}
    debug.debug update
    PropertiesCol.updateOne {_id:id}, update, {upsert:false}, (err,ret)->
      return cb(err) if err
      if libP.isPropObjectId id#/^[a-f0-9]{24}$/.test id
        UserListingCol.updateOne {_id:id}, update, (err,ret)->
          return cb(err) if err
          return cb null, {ok:1} #, q:update}
      else
        return cb null, {ok:1} #, q:update}
    return

  # [propDetail]
  # @ohs = open house time
  # @ohvs = video open house time
  @updateOhs:({isInGroup,id,ohs,ohv,ohvs,user,isAllowedPropAdmin},cb)->
    return cb MSG_STRINGS.BAD_PARAMETER unless id
    PropertiesCol.findOne {_id:id},(err,prop)->
      unless prop
        return cb MSG_STRINGS.NOT_FOUND
      unless libUser.canEditOpenHouse({isAllowedPropAdmin,prop,user,isInGroup})
        return cb MSG_STRINGS.NOT_FOUND
      update = {$set:{ohz:ohs}}
      rmlog = {
        k:'setOhz',
        uid:user?._id,
        ts:new Date()
        v:{ohs}
      }
      if Array.isArray ohvs
        update.$set.ohzv = ohvs
        rmlog.v.ohzv = ohvs if ohvs.length
      update.$push = {rmlog}
      PropertiesCol.updateOne {_id:id}, update, (err,ret)->
        if err
          debug.critical err
          return cb(MSG_STRINGS.DB_ERROR)
        if libP.isPropObjectId id#/^[0-9A-F]{24}$/i.test id
          UserListingCol.updateOne {_id:id}, update, (err,ret)->
            if err
              debug.critical err
              return cb(MSG_STRINGS.DB_ERROR)
            return cb null,{ok:1}
        else
          return cb null, prop

  # [propDetail],
  # change phoNum in source records.
  @updatePictureNum = ({id,src,rmPho},cb)->
    if chinaMode
      return cb MSG_STRINGS.CONNECT_RNI_ERROR
    if src is 'TRB'
      Coll = MlsTrebMasterRecords
    else if src is 'DDF'
      Coll = MlsCreaDdfRecords
    else
      return cb("src #{src} is not support")
    return Coll.updateOne {_id:id}, {$set:{picNum:rmPho}},(err,ret)->
      if err
        debug.critical err
        return cb MSG_STRINGS.DB_ERROR
      return cb null,ret?.result?.nModified

  # set download max number of photos for treb
  @setMaxPhotoDownload = ({sid,src,rmPho},cb)->
    if chinaMode
      return cb MSG_STRINGS.CONNECT_RNI_ERROR
    if src is 'TRB'
      Coll = MlsTrebMasterIds
    else
      return cb("src #{src} is not support for setMaxPhotoDownload")
    return Coll.updateOne {_id:sid}, \
    {$set:{maxPhoDownload:true},$unset:{phoDl:1}},(err,ret)->
      if err
        debug.critical err
        return cb MSG_STRINGS.DB_ERROR
      return cb null,ret?.result?.nModified

  # updateTrebPhoQueue
  @updateTrebPhoQueue: (sid,cb)->
    if not RESO_TREB_MEDIA_QUEUE
      return cb MSG_STRINGS.CONNECT_RNI_ERROR
    # 50000 is the highest priority, 30000 may wait serveral hours
    update = {priority: 50000, dlShallEndTs: new Date('1970-01-01T00:00:00.0Z')}
    RESO_TREB_MEDIA_QUEUE.updateOne {_id:sid},{$set:update}, {upsert:true}
    return cb null,1,'inserted: _id:'+sid

  #refresh Pic by unset phoDl in MlsTrebMasterIds or MlsCreaDdfIds
  #photoDownload process will download phto again if no phoDl
  #unset phoDl in MlsTrebMasterIds and MlsTrebMasterRecords, watch will unset phodl in properties.
  @refreshPic:({sid,id,src},cb)->
    if chinaMode
      return cb MSG_STRINGS.CONNECT_RNI_ERROR
    unless (id and src)
      debug.error 'missing id or src id:', id, 'src:',src
      return cb 'missing id or src'

    CollQuery = {_id:id}
    if src is 'TRB'
      # NOTE: rets no longer supported
      # CollIds = MlsTrebMasterIds
      # Coll = MlsTrebMasterRecords
      # CollIdsQuery = {_id: sid}
      return Properties.updateTrebPhoQueue(sid,cb)
    else if src is 'DDF'
      CollIds = MlsCreaDdfIds
      Coll = MlsCreaDdfRecords
      CollIdsQuery = {_id: id.replace 'DDF',''}
    else
      return cb("src #{src} is not support")
    #unset
    CollIds.findOne CollIdsQuery, { projection: { ts: 1, phoMt: 1 }}, (err, ret) ->
      if err
        debug.critical err
        return cb MSG_STRINGS.DB_ERROR
      if not ret?
        return cb MSG_STRINGS.NOT_FOUND
      # TODO: set mannual flag
      update = {$unset:{phoDl:1}, $set:{manulPhoTs:new Date()}}
      if not ret.phoMt
        # update.$set = {phoMt:ret.ts}
        update.$set.phoMt = ret.ts
      CollIds.updateOne CollIdsQuery, update,(err,ret)->
        if err
          debug.critical err
          return cb MSG_STRINGS.DB_ERROR
        Coll.updateOne CollQuery, update,(err,ret)->
          if err
            debug.critical err
            return cb MSG_STRINGS.DB_ERROR
          # sid may match multiole properties, so use id to unset phodl
          PropertiesCol.updateOne CollQuery, {$unset:{phodl:1}},(err,ret)->
            if err
              debug.critical err
              return cb MSG_STRINGS.DB_ERROR
            cb null,ret?.result?.nModified,update

  # [propDetail] useTrebPhoto flag
  @setUseTrebPhoto:({id,useTrebPhoto},cb)->
    set = {useTrebPhoto}
    PropertiesCol.updateOne {_id:id}, {$set:set}, {upsert:false}, (err,ret)->
      if err
        debug.critical err
        return cb(MSG_STRINGS.DB_ERROR)
      return cb null,{ok:1}

  # [propDetail] maually modify postion
  @updateLocation:({id,user,lat,lng,cnty,prov,city,zip,st,st_num,st_sfx,isRm},cb)->
    cnty ?= MSG_STRINGS.CNTY_CANADA
    loc = {
      type:'Point',
      coordinates:[lng,lat]
    }

    # st_sfx可能不存在,不判断会导致st可能存在undefined
    st = "#{st} #{st_sfx or ''}"
    { cnty, prov, city, zip, addr, st, st_num, st_sfx, faddr, showAddr } =\
      propAddress.formatProvAndCity {cnty, prov, city, zip, addr, st, st_num}

    set1 =
      lat: lat
      lng: lng
      geoq: 130
      loc: loc
      city: city
      prov: prov
      cnty: cnty,
      zip: zip,
      addr: addr,
      st: st,
      st_num: st_num,
      st_sfx: st_sfx,
      faddr: faddr,
      showAddr: showAddr,
      useMlatlng: true
    set1.uaddr = uaddr = helpers.unifyAddress set1
    rmlog = {
      k:'loc',
      uid:user._id,
      ts:new Date()
      v:[lng,lat]
    }

    unset = propertyTagHelper.getPropertyLocTagFileds()
    update = {$set:set1, $push:{rmlog:rmlog}, $unset:unset}

    fields = {addr:1, st:1, st_num:1,  prov:1, city:1, \
    origAddr:1, origSt:1, origStNum:1,origProv:1, origCity:1}
    
    PropertiesCol.findOne {_id:id}, {fields}, (err,ret)->
      rturn cb err if err
      return cb "#{id} not found" unless ret
      # if no original fld, save the previous value to original fld
      for fld in ['addr','st','st_num','st_sfx','prov','city']
        origFld = libP.ADDR_TO_ORIG_FLD_MAPPING[fld]
        debug.debug "origFld #{origFld} #{ret[origFld]} ret[fld] #{ret[fld]}"
        if (update.$set[fld] isnt ret[fld]) and (not ret[origFld])
          update.$set[origFld] = ret[fld]

      tagParms = {
        prop: update.$set,
        Boundary: BOUNDARY,
        School,
        TransitStop,
        Census2016,
        Census2021,
      }
      tags = await addTagsAsync tagParms
      for key of propertyTagHelper.getPropertyAllTagFields()
        if tags[key]
          update.$set[key]= tags[key]
          delete update.$unset[key]

      PropertiesCol.findOneAndUpdate {_id:id}, update, {returnDocument:'before'}, (err,ret)->
        return cb err if err
        origProp = ret?.value
        aUaddr = origProp?.uaddr
        #if city is changed,use new uaddr else keep old uaddr as _id of
        origCity = origProp.city
        if origCity isnt city
          aUaddr = uaddr

        isOrigPropExist = if origProp then 1 else 0
        set =
          lat:lat
          lng:lng
          q: 130
          src: 'manual'
          uid: user._id
          mt: new Date()
          city: city
          prov: prov,
          cnty: cnty,
          addr: addr,
          st:st,
          st_num:st_num,
          zip:zip
        set.faddr = faddr if faddr
        debug.debug 'uaddr',uaddr
        return cb 'no uaddr' unless (uaddr and aUaddr)
        uaddrZip = helpers.unifyAddress set,true
        GeoCoder.saveGeoCache {
          uaddrAfterFormat: aUaddr,
          uaddrZipAfterFormat: uaddrZip,
          geoCodingResult: set
        }, (err, ret) ->
          if err
            debug.critical err
            return cb(err.toString())
          if ret
            isGeoCacheUpdated = 1
          #update rni records, trigger reimport, to update other properties with same address.
          #do not update self
          #find ids for this uaddr. update in records.
          try
            # 如果返回的uaddr与原本uaddr不一致,需要更新房源的uaddr
            if ret?._id? and (ret._id isnt aUaddr)
              await PropertiesCol.updateOne {_id:id},{$set:{uaddr:ret._id}}
            idObjs = await PropertiesCol.findToArray {uaddr,_id:{$ne:id}},{fields:{_id:1}}
            debug.debug "ids find by uaddr, #{uaddr}",ids
            ids = idObjs.map (obj)-> return obj._id
            debug.debug 'ids to fix', ids
            {modifiedTreb,modifiedDdf} = await Properties.addManMtToSource ids
          catch error
            return cb error
          
          msg = "properties: #{isOrigPropExist}, geo_cache: #{isGeoCacheUpdated}, \
            modifiedTreb: #{modifiedTreb}, modifiedDdf: #{modifiedDdf}"
          return cb null, {ok:1,n:msg}

  @addManMtToSource:(ids)->
    if chinaMode
      throw new Error(MSG_STRINGS.CONNECT_RNI_ERROR)
    trebRet = await MlsTrebMasterRecords.updateMany \
      {_id:{$in:ids}}, {$set:{manMt:new Date()}}
    modifiedTreb = trebRet.result.n
    ddfRet = await MlsCreaDdfRecords.updateMany \
      {_id:{$in:ids}}, {$set:{manMt:new Date()}}
    modifiedDdf = ddfRet.result.n
    return {modifiedTreb,modifiedDdf}

  #
  # @param {string []} ids [id1,id2,id3]
  # @param {string []} tagTypes ['schol','transit']
  # no return
  #
  @triggerReimport:(ids,tagTypes)->
    fieldsToUnset = propertyTagHelper.getPropertyFiledsByTagType(tagTypes)
    unset = {}
    debug.debug 'fieldsToUnset',fieldsToUnset
    for fld in fieldsToUnset
      unset[fld] = 1
    query = {_id:{$in:ids}}
    debug.debug 'query', query, 'unset', unset
    ret = await PropertiesCol.updateMany query, {$unset:unset}
    debug.debug 'ret', ret
    await Properties.addManMtToSource ids

  # [propDetail]
  @updateMZh:({m_zh,propId})->
    # NOTE: no need to record trans log in properties
    vals = {m_zh}
    update = {$set:vals}
    ret = await PropertiesCol.updateOne {_id:propId}, update, {upsert: false}
    return 'Updated'

  # [propDetail] one of ['priceChange','status','daddr','rmdpho','dpred','useMlatlng','tlBcc']
  @updatePropStatus:({id,keys,user,editData},cb)->
    rmlog = {
      uid:user._id,
    }
    set = {
      # NOTE:editData可能没有status,导致原本为U的房源改成active
      # status:editData.status or 'A'
      rmmt:new Date()
    }
    if editData.status
      set.status = editData.status
    unset = {}
    aggregateFlag = false
    rmBltYr = null
    for key,value of editData
      if value
        rmlog.k = key
        rmlog.v = value
        set[key] = value
      if key in ['rmSqft','rmBltYr']
        aggregateFlag = true
        rmBltYr = value if key is 'rmBltYr'
      if key in ['hideSqft','hideBuiltYr','hideEstVal'] and (value is false)
        rmlog.k = key
        rmlog.v = value
        unset[key] = value
    push = {rmlog}
    update = {$set:set, $push:push}
    if Object.keys(unset).length >0
      update.$unset = unset
    PropertiesCol.updateOne {_id:id}, update, {upsert:false}, (err,ret)->
      if err
        debug.critical err
        return cb(err.toString())
      if not aggregateFlag
        return cb null, set
      Properties.deleteSqftsAggregate id,rmBltYr,(err,result)->
        return cb err if err
        AdminOpLogs.insertOne {act:'adminEditProp', ts:new Date(), \
        uid:user._id, ueml:user.eml, op:update},(err,log)->
          return cb err if err
          return cb null, set

  # [propdetail] update phodl flag
  # @updatePhoDownloadDate:({id},cb)->
  #   MlsTrebMasterRecords.findOne {_id:id},{fields:{Pix_updt:1,phodl:1}},(err,ret)->
  #     if err
  #       debug.critical err
  #       return cb(MSG_STRINGS.DB_ERROR)
  #     return cb('Record not found') unless ret
  #     Pix_updt = (new Date(ret.Pix_updt)).getTime()
  #     Pix_updt = new Date(Pix_updt - 1000*3600*24)
  #     Pix_updt = helpers.dateFormat Pix_updt
  #     set = {phodl:Pix_updt}
  #     MlsTrebMasterRecords.updateOne {_id:id}, {$set:set},(err,updret)->
  #       if err
  #         debug.critical err
  #         return cb(MSG_STRINGS.DB_ERROR)
  #       cb null, {ok:1, set:set, phodl:ret.phodl, Pix_updt:ret.Pix_updt}

  # [propdetail] update prop view count status
  @updateListingStatus:({suffix,_id,fromWeb,role,rmid,db}, cb)->
    community = null
    #faddr -> formated address, key in db
    #suffix -> vc + translate suffix
    q = {_id}
    update = {
      $inc:{
        vc : 1
        vcd : 1 # vc daily
      }
    }
    update.$inc['vcweb'] = 1 if fromWeb
    update.$inc['vc'+libP.get_shared_to_from_share(suffix)] = 1 if suffix
    if role in ['r','c']
      update.$inc['vcapp'+role] = 1
    props = Projects
    if rmid
      q = {id:rmid}
      # TODO: after projects refactor
      props = UserListingCol
    props = db if db
    props.updateOne q, update, (err, ret)->
      console.log err if err
      cb err, ret if cb
    null

  #[mapSearchProp]
  @getAllTranslatedPtypes:(lang)->
    _t = i18n.getFun lang
    return ptypes if ptypes = langPtypes[lang]
    ret = []
    for p in _ptype_ptype2_object.list
      ret.push {k:p, v:_t(p)}
    langPtypes[lang] = ret
    return ret

  @getAllTranslatedPtype2s:({lang,ptype})->
    _t = i18n.getFun lang
    return {ptype2s:[],ptype2sShort:[]} unless ptype
    # key = lang+':'+ptype
    # return ptype2s if ptype2s = langPtypes[key]
    list = _ptype_ptype2_object.ptypes[ptype]
    #TODO: dynamic sort list
    return {ptype2s:[],ptype2sShort:[]} unless list
    ret = []
    ret2 = []
    for ptype in list
      v = _t(ptype)
      # TODO: a better approach
      ret2.push {k:ptype, v} if ptype.length < 9#v isnt ptype
      ret.push {k:ptype, v}
    # langPtypes[key] = ret
    return {ptype2s:ret,ptype2sShort:ret2}
  # [status] admin func
  @countAllRMListings:(cb)->
    #NOTE: count {} returns 0
    # countDocuments not defined
    UserListingCol.countDocuments {status:'Active'},(err,c)->
      # console.log '++++++',err,c
      return cb err,c

  # [index.coffee]
  @getMarketRecommend:({provName,isAssignAdmin}, cb)->
    isAssignAdmin ?= false
    provName = cityHelper.getProvAbbrName {prov:provName}
    oneWeek = new Date( new Date().valueOf() - 3600000 * 24 * 7 * 1)
    # {$in:['exlisting','assignment','rent']}
    oneMonth = new Date( new Date().valueOf() - 3600000 * 24 * 31 * 1)
    ret = {l:[]}
    q = {
      prov:provName
      'market.st':'Promoted',
      topTs:{$lte:new Date()},
      $or:[
        # {status:'Active', topTs:{$gte:new Date()}}
        {status:'Active', mt:{$gte:oneMonth}},
        {status:'Sold', mt:{$gte:oneWeek}},
        {status:'Leased', mt:{$gte:oneWeek}}
      ],
    }
    q.mt = {$gte:oneMonth}
    q.ltp = 'assignment'
    unless isAssignAdmin
      q.private = {$ne:true}
    getOneTypeRecommend q,{},(err,lret)->
      return cb(err) if err
      if lret?.length
        ret.l = ret.l.concat lret
      q.ltp = 'exlisting'
      getOneTypeRecommend q,{},(err,lret)->
        return cb(err) if err
        if lret?.length
          ret.l = ret.l.concat lret
        q.ltp = 'rent'
        q.$or = []
        # q.$or = []#[{status:'Active', topTs:{$gte:new Date()}}]
        q.status = 'Active'
        q.$or.push {'market.cmstn': {$exists:false}}
        q.$or.push {'market.cmstn': {$eq:''}}
        getOneTypeRecommend q,{},(err,lret)->
          return cb(err) if err
          if lret?.length
            ret.l = ret.l.concat lret
          q.ltp = 'rent'
          q.$and = []
          delete q.$or
          # q.$or = [{status:'Active', topTs:{$gte:new Date()}}]
          q.$and.push {'market.cmstn': {$exists:true}}
          q.$and.push {'market.cmstn': {$ne:''}}
          getOneTypeRecommend q,{},(err,lret)->
            return cb(err) if err
            if lret?.length
              for i in lret
                if i.market?.cmstn?
                  i.cmstn = i.market.cmstn
                  # i.adok = true
              ret.l = ret.l.concat lret
            cb null, ret
  
  @findListingsBySavedSearch:({savedSearch,type,edmts,propIds},cb)->
    edmts = new Date(Date.now()  - 3600 * 24 * 1000) unless edmts
    opts = {}
    query = {}
    opts = savedSearch.k
    delete opts.page #ignore page when searching with savedsearch
    q = propSearch.createQuery opts
    query = Object.assign query, q.q
    if propIds
      query._id = {$in:propIds}
      opts.ids = propIds
    if type is 'newprop'
      query.ts = {$gt:edmts}
      opts.ts = edmts
    if type is 'soldprop'
      query.status = 'U'
      query.sldd = {$gt:edmts}
      opts.status = 'U'
      opts.sldd = inputToDateNum(edmts)
      delete opts.ts
    fields = {id:1}
    limit = 15
    try
      {props} = await Properties.findListingsByElasticSearch {params:opts,fields}
    catch err
      debug.error 'findListingsBySavedSearch ES',err
      return cb(err,null)
    return cb(null,props)

  @findListingsByUserFavs:({uid,edmts},cb)->
    PropFav.findToArray {uid}, {sort: {ts:-1}, limit:30}, (err, faved)->
      if err
        debug.critical err
        return cb MSG_STRINGS.DB_ERROR
      ids = []
      for fav in faved
        id = fav.id
        ids.push id
      options = {
        ignoreStatus:true,
        must_not:[{terms:{src:['RM','RMTRB']}}]
        must:[{range:{spcts:{gt:edmts}}}]
        propIds:ids,
        fields:{_id:1},
        sort:{ts:-1},
        limit:30
      }
      console.log JSON.stringify(options)
      try
        {props} = await elasticsearchLib.search options
      catch err
        debug.error err
        return cb MSG_STRINGS.DB_ERROR
      cb null,props or []

  @findUserListingsForEdm:({edmts,type},cb)->
    query = {
      src:'RM',
      # $or:[{ts:{$gt:edmts}}, {edm:{$gt:edmts}}],
      edmSent: {$exists:false},
      status: 'Active',
      'market.st':'Promoted',
      stp:'Sale'
    }
    query.ltp = 'assignment' if type is 'assignment'
    query.ltp = 'exlisting' if type is 'exlisting'
    debug.debug JSON.stringify query
    UserListingCol.findToArray query, {fields:libStaticListing.RM_PROP_FIELDS, sort: {ts:-1}, limit:30}, (err, ret) ->
      Properties.updateRMListingEdmStatus ret
      cb(err,ret)
  
  @updateRMListingEdmStatus:(props)->
    if props?.length
      ids = []
      for p in props
        ids.push p._id
      UserListingCol.updateMany {_id:{$in:ids}},{$set:{edmSent:new Date()}},(err)->
        debug.error 'findAssignmentForEdm', err if err
  
  # edm expire notify
  @findRMListingsBeforeExpByDays:(days,cb)->
    query = {
      $or: [
        {ltp:'assignment'}
        {ltp:'exlisting'}
        {ltp:'rent'}
      ]
      status: 'Active'
      $and:[
        {mt:
          {$lt: new Date( Date.now() - 3600000 * 24 * (30 - parseInt(days)) )}
        }
        {mt:
          {$gt: new Date( Date.now() - 3600000 * 24 * (30 + parseInt(days)) )}
        }
      ]
    }
    debug.debug JSON.stringify query
    UserListingCol.findToArray query, {sort: {mt: -1}}, (err,listings)->
      cb(err,listings)

  # TODO: no longer used
  @findUserListingsByIds:(ids,cb)->
    fields = libStaticListing.RM_PROP_FIELDS
    UserListingCol.findToArray {_id: {$in:ids}}, {fields}, (err, ret) ->
      cb(err,ret)
  
  # NOTE: edm不再使用evaluation,暂时改用properties查询,添加warning log
  # TODO: 后面观察是否存在调用该函数的log,如果不存在 可以直接删除
  @findListingsForEvaluationEdm:(ids,cb)->
    debug.warn 'use findListingsForEvaluationEdm'
    PropertiesCol.findToArray {_id: {$in:ids}}, {fields: libP.MAP_PROP_FIELDS}, (err, ret) ->
      cb(err,ret)
  
  @findListingsByEvaluations:({evaluation,user,days},cb)->
    range = user.range
    tp = user.tp
    query={}
    query.$or=[{status:'A'}, {sp:{$exists:true}}]
    query.ptype = 'Residential'
    query.ptype2 = tp
    query.geoq = $gte:100
    query.loc =
      $near:
        $geometry:
          type: "Point"
          coordinates:[evaluation.lng,evaluation.lat]
        $maxDistance:range
    ts = new Date(Date.now() - 3600 * 1000 * 24 * days)
    ts = if user.ts and new Date(user.ts) > ts then user.ts else ts
    query.mt = {$gt: ts}
    debug.debug JSON.stringify query
    PropertiesCol.findToArray query, {fields:libP.MAP_PROP_FIELDS}, (err, ret)->
      cb(err,ret)

  @getPromotions:({req,ctx,ret,
  projects=[], #等待随机获取的列表
  opt=[] #需要的类型
  },cb)->
    promotions = []
    promotionsObj =
      listing:{promotionTp:'listing',tl:'Top Your Listing',stl:'20x greater exposure'},
      topExclusives:{promotionTp:'topExclusives',tl:'Top Exclusive Listing',stl:'20x greater exposure'},
      project:{promotionTp:'project',tl:'Top Pre-Construction',stl:'Become exclusive agent'},
      exlisting:{promotionTp:'exlisting',tl:'Post Exclusive Listing',stl:'Exclusive, Assignment, Rental'}
    if ctx.toplistings?.length > 0
      tl = helpers.getRandomOne ctx.toplistings
      tl.adrltr.nm = libUser.fullNameOrNickname(req.locale(),tl.adrltr) if tl.adrltr
      promotionsObj.listing.prop = tl
    if ctx.topExclusives?.length > 0
      tl = helpers.getRandomOne ctx.topExclusives
      tl.adrltr.nm = libUser.fullNameOrNickname(req.locale(),tl.adrltr) if tl.adrltr
      promotionsObj.topExclusives.prop = tl
    if projects?.length > 0
      project = helpers.getRandomOne projects
      promotionsObj.project.prop = project
    # if ctx.isOntarioRealtor
    #   promotions.push {promotionTp:'mlsAgent',tl:'MLS Listing',stl:'Only for Treb Agents'}
    done = ()->
      for tp in opt
        promotions.push promotionsObj[tp]
      ctx.promotions = promotions
      return cb ctx

    if ctx.exclusives?.length > 0
      exclusive = helpers.getRandomOne ctx.exclusives
      fields = {nm:1,eml:1,roles:1,_id:1,fn:1,ln:1,nm_en:1,nm_zh:1,\
        cpny_zh:1,cpny_en:1,avt:1,cpny:1,mbl:1,tel:1}
      Users.findOne {_id:exclusive.uid},{fields},(err,user)->
        debug.error err if err
        if user
          user.nm = libUser.fullNameOrNickname(req.locale(), user)
          user.avt = libPropertyImage.replaceRM2REImagePath user.avt if user.avt
          exclusive.adrltr = user
        promotionsObj.exlisting.prop = exclusive
        done()
    else
      done()

  @countSameUaddrDiffLatLng:({uaddr,lat,lng},cb)->
    # 计数相同uaddr下与传入lat，lng不同的房源数量
    q = {uaddr:uaddr,lat:{$ne:lat},lng:{$ne:lng}}
    PropertiesCol.countDocuments q,(err,cnt)->
      if err
        debug.critical err
        return cb MSG_STRINGS.DB_ERROR
      cb null,cnt

  @updateSameUaddrDiffLatLng:({uaddr,lat,lng},cb)->
    # 更新相同uaddr下与传入lat，lng不同的房源数量
    set =
      lat : lat
      lng : lng
    q = {uaddr:uaddr,lat:{$ne:lat},lng:{$ne:lng}}
    update = {$set:set}
    PropertiesCol.updateMany q,update,(err)->
      return cb err if err
      cb null

  # assignment 验证上传协议真伪
  # return {msg,err}
  @agreementVerify:({
    id, #required
    verify #optional
  })->
    update={}
    msg = MSG_STRINGS.AGREEMENT_SUCCEED
    update['market.isV'] =  verify or false
    msg = MSG_STRINGS.AGREEMENT_CANCEL if not verify
    try
      q = {_id: id}
      if /^RM1/.test id
        q = {id}
      ret = await UserListingCol.findOne q, {fields:{market:1}}
      if not ret
        return {err:MSG_STRINGS.CAN_NOT_VERIFY}
      # 真楼花不用检查是否有认证图片
      if (not ret.market?.rmProp) and (not ret.market?.agrmntImg) and (verify is true)
        return {err:MSG_STRINGS.CAN_NOT_VERIFY}
      await UserListingCol.updateOne q, {$set:update}
      await PropertiesCol.updateOne q, {$set:update}
      return {msg}
    catch err
      debug.critical err
      return {err:MSG_STRINGS.DB_ERROR}

  # admin add Revoke Reason
  # return err,msg
  @addAdminRevokeReason:({
    id, #required
    reason #required
  }, cb)->
    update={rvkRsn:reason}
    msg = MSG_STRINGS.REVOKED
    UserListingCol.updateOne {id: id}, {$set:update},(err)->
      if err
        debug.critical err
        return cb MSG_STRINGS.DB_ERROR
      cb null,msg

  # find RM aggregated stories and missing level of uaddr(condo building)
  # @param {string} uaddr
  @getRmStoreyInfo:(uaddr, cb)->
    Sqfts_Aggregate.findOne {_id:uaddr}, \
      {fields:{rmStoreys:1, missingLvls:1}},cb
  
  # @param {string} uaddr
  @getRmStoreyInfoAsync:(uaddr)->
    return null if not uaddr
    return ret = await Sqfts_Aggregate.findOne {_id:uaddr}, \
      {fields:{rmStoreys:1, missingLvls:1}}

  # @description 该函数根据_id去查找房源，如果用RM1-查询房源，不用这个函数查找
  # @param {string|objectId} id - prop's _id
  # @param {object} projection - filter fields
  @findOneByIDAsync:(id,projection)->
    if libP.isPropObjectId(id)
      id = new ObjectId(id)
    prop = await PropertiesCol.findOne {_id:id},{projection}
    return prop

  ###
  # @description find similar properties
  # @param {object} params -query conditions
  # @return {object} result -results of find similar properties
  ###
  @getSimilarSaleProps:({params,rental,isSold,user})->
    # 1.build query
    {query,options,range,month,esParams} = similarEngine.buildSimilarQuery {params, rental, isSold}
    # 2.find properties using elasticsearch
    esParams.skip = options.skip
    esParams.limit = options.limit
    esParams.sort = 'dist-asc,mt-desc'
    esParams.fields = options.projection
    {props,cnt} = await elasticsearchLib.search esParams

    # 3.handle properties
    if props.length
      convertPropsPtypeAndAddr props
      result = similarEngine.addWeightAndFilter {params, range, month, props}
      # NOTE: add Fav before add weight only if fav is a weight-factor
      if result
        props = await Properties.addPropFavFields {props:result.ret?.usedProps,uid:user?._id}
        result.ret?.usedProps = props
        result.ret?.cnt = cnt
          # 4.return results
        return result.ret
    return null

  ###
  # @descripting get properties in the same building
  # @params {object} - {type,saletp,saleDesc,page,limit,id,bdrms,lat,lng,uaddr}
  ###
  @getBuildingProps:(params)->
    esParams = Object.assign {},params
    delete esParams.id
    delete esParams.uaddr
    esParams.notPropId = params.id
    esParams.ptype = 'r'

    if params.type
      esParams.ptype2 = params.type
    if params.saleDesc in ['Sold','Leased']
      esParams.soldOnly = true
    dist = 10
    if params.type in ['Detached']
      dist = 5
    else if params.type in ['Semi-Detached','Townhouse']
      dist = 3

    if (params.lat and params.lng) and params.uaddr
      esParams.similarShould = {
        uaddr:params.uaddr,
        geoq:100,
        centerLat: params.lat,
        centerLng: params.lng,
        dist: dist
      }
    else if params.uaddr
      esParams.uaddr = params.uaddr
    else if (params.lat and params.lng)
      esParams.geoq = 100
      esParams.centerLat = params.lat
      esParams.centerLng = params.lng
      esParams.dist = dist

    options =
      sort:{mt:-1}
    limit = (helpers.parseNumber options.limit) or 20
    options.skip = (helpers.parseNumber params.page) * limit if params.page
    options.limit = limit
    options.projection = Object.assign({}, libP.MAP_PROP_FIELDS,{sp:1,isV:1})
    props = []
    
    esParams.skip = options.skip
    esParams.limit = options.limit
    esParams.sort = 'mt-desc'
    esParams.fields = options.projection
    {props} = await elasticsearchLib.search esParams

    convertPropsPtypeAndAddr props
    return props

  # @param {string} propId
  # @param {number} rmBltYr
  # NOTE:和fred确认结果：只从ids中取出id，不从sqfts中取出sqft
  @deleteSqftsAggregate:(propId,rmBltYr,cb)->
    update =
      $pull:{ids:propId}
    if rmBltYr
      update['$set'] = {rmBltYr}
    Sqfts_Aggregate.updateOne {ids:propId,_id:/#/},update,(err,ret)->
      return cb err if err
      return cb null,ret

  ###
  # !!!NOT DONE, DO NOT USE
  # @description 根据地图/列表检索条件查询房源列表
  # TODO：对比 getPropList/findListings，增加toplisting的房源，需要单独的接口
  # @param {object} user - current user info
  # @param {object} params - the filter and sort criteria,and other translation language,etc
  # @param {object} additionalConfig - the config of add additional fields for prop
  # @param {object} projection - the showing fields of searching result through db
  ###
  @getTranslatedPropListBySearchCond:({user,params,additionalConfig,projection = {}})->
    projection = Object.assign(projection, libP.MAP_PROP_FIELDS,{sp:1,isV:1})
    params.sort = libP.getPropSearchSort params.sort
    {q,limit,sort,skip,readable} = propSearch.createQuery params

    trans = params.trans
    fromStr = params.from
    dm = params.dm

    hasMore = false
    params.fields = projection
    try
      {props,cnt} = await elasticsearchLib.search params
    catch err
      return {err}

    if props.length > limit
      props = props.slice(0,limit)
      hasMore = true
    convertPropsPtypeAndAddr props
    sortPropListByTopTs {sort:params.sort},props

    filterParams = {props,fromStr,dm,trans,user}
    filterParams.isChinaIP = additionalConfig.isChinaIP
    filterParams.isPad = additionalConfig.isPad
    filterParams.use3rdPic = config.serverBase?.use3rdPic
    filterParams.shareHostNameCn = config.share?.hostNameCn
    filterParams.transDDF = additionalConfig.transDDF
    ret = libFilterProp.filterListingList filterParams
    ret.hasMore = hasMore
    ret.total = cnt if cnt
    try
      ret.list = await Properties.addPropFavFields {props:ret.list,uid:user?._id}
    catch err
      return {err}
    return ret

  ###
  # @description 查询房源详细信息
  # @param {string} id - the id of property
  # @param {string} fromStr - web/app
  # @param {string} dm - domain name
  # @param {string} trans - translation language
  # @param {boolean} disKeyOnly - flag of needing disclaimer content
  # @param {object} user - current user info
  # @param {object} additionalConfig - the config of add additional fields for prop, see: getBasePropDetailOpt
  # @param {object} projection - the showing fields of searching result through db, could be exclusition
  # @return {prop:object, ok:number, dis:[string]}
  ###
  @getTranslatedPropDetailByID:({id,fromStr,dm,trans,disKeyOnly,user,additionalConfig,projection = {}})->
    # RM房源，特殊查找
    if isRMlisting id
      prop = await UserListingCol.findOne {id},{projection}
      if prop
        creatorUser = await UserModel.findByIdAsync prop.uid,{projection:{roles:1}}
        prop.creatorIsVipRealtor = UserModel.accessAllowed 'vipRealtor', creatorUser
    else
      prop = await PropertiesCol.findOne {_id:id},{projection}
    if not prop
      return {err:MSG_STRINGS.NOT_FOUND}
    # admin,inReal,vip以外用户不能查看隐藏房源详情
    if prop.del and (not libP.canViewDelProp additionalConfig)
      return {err:MSG_STRINGS.NOT_FOUND}
    # add record to user_log
    if (fromStr is 'app') and user
      if isRMlisting id
        UserModel.logRMListingHistory(user._id,id)
      else
        UserModel.logPropertiesHistory(user._id,id)
    if isRMlisting id
      prop = Properties._deleteAndCalcFieldsForRMPropDetail {prop,user,fromStr}
      prop = propSearch.convertRMListingsDispToTreb([prop])[0]
      convertPropPtype prop
    else
      convertPropDetailDisplayInfo(prop)
    params = {prop,fromStr,dm,trans,disKeyOnly,user}
    params.isChinaIP = additionalConfig.isChinaIP
    params.isPad = additionalConfig.isPad
    params.use3rdPic = config.serverBase?.use3rdPic
    params.shareHostNameCn = config.share?.hostNameCn
    params.transDDF = additionalConfig.transDDF
    # 分享房源,edm发送房源,fub发送房源 不需要登录也可以查看
    params.isShare = additionalConfig.isShare or additionalConfig.fubSrc or additionalConfig.isEdm
    params.isSearchEngine = additionalConfig.isSearchEngine if additionalConfig.isSearchEngine
    # NOTE： 列表需要， 修改房源显示字段
    ret = libFilterProp.setLoginAndDisclaimerDetail params
    prop = ret.detail
    if prop.aSIDs?.length and additionalConfig.isAllowedAdmin
      prop.aSIDs = filterASIDsForPropDetail prop.aSIDs,prop._id
    else
      delete prop.aSIDs
    if prop.needLogin
      return ret
    # NOTE： 列表需要， 增加收藏信息
    ret.detail = await Properties.addPropFavFields {props:ret.detail,uid:user?._id,detail:true}
    additionalConfigParam = Object.assign {},additionalConfig,{user,prop,fromStr,simplfyPtp:additionalConfig.slp}
    # NOTE: additionalConfig = libP.getBasePropDetailOpt req,user
    # NOTE: 列表需要， daddr rmplus priceValStrRed 会在这里添加
    Properties.addPropAdditionalFields(additionalConfigParam)

    # NOTE： 列表需要， 生成房源图片
    type = libFilterProp.getListingType(prop)
    if type is 'RM'
      prop.pic.l = libPropertyImage.getRMListingsImages {sid:prop.sid,imgUrls} if imgUrls = prop.pic?.l
    else
      # TODO(future: change param of genListingPicReplaceUrls)
      libP.genListingPicReplaceUrls(prop, additionalConfig.isChinaIP, {
        use3rdPic:config.serverBase?.use3rdPic,
        isPad:additionalConfig.isPad,
        useThumb:additionalConfig.useThumb,
        shareHostNameCn:config.share?.hostNameCn,
        isLarge:additionalConfig.isLarge
      })

    # NOTE: 列表不需要
    prop = await Properties._addWatchingStoreyCmtyStatToProp {prop:ret.detail,user}
    
    # NOTE: 列表不需要，单独统计
    # 统计prop 行为
    suffix = ''
    if fromStr is 'app'
      suffix = 'A'
    # console.log 'xxxxxxxx',suffix,message,msg
    suffix = libP.get_shared_to_from_share(suffix)
    ids = [prop._id]
    # RM prop need update user listing too
    if /^RM/.test prop.id
      ids.push prop.id
    Properties.incListingStats {ids,isView:true,user,suffix}

    # NOTE: 列表需要， translate prop in final step
    if params.trans
      prop = propTranslate.translate_rmprop_detail {
        locale:params.trans,
        transDDF:params.transDDF}, prop
    prop.formattedCity = propTranslate.getFormatCityName prop, params.trans
    # NOTE: 列表需要，
    Properties.deletePropPriviteFields {
      user,prop,
      isAllowedPropAdmin:additionalConfig.isAllowedPropAdmin,
      isShare:additionalConfig.isShare,
      isSearchEngine:params.isSearchEngine
    }
    prop.unitKeys = libP.getUnitTypeKeys prop
    # NOTE: fce改为了数组,需要转为字符串给前端使用
    prop.fce = prop.fce.join(',') if Array.isArray(prop.fce)
    ret.detail = prop
    ret.ok = 1
    # hideInfo is hideing topAgent info for toplisting
    if prop.hideInfo
      return ret
    if not (prop.topuids?.length)
      return ret
    # use last topped id of user
    propUid = prop.topuids[length-1]
    retu = await UserModel.findPublicInfoAsync {lang:locale, id:propUid, noSas:1}
    prop.tlAgent = retu
    return ret

  # if isWatching, will show a bell in propdetail wating fields, eg.cmty,building
  @_setPropWatingFields:({prop,user})->
    userWatchings = await UserWatchModel.getWatchingStautsByPropertyAsync {prop,uid:user?._id}
    userWatchings ?= []
    for userWatching in userWatchings
      watching = UserWatchModel.formatWatching userWatching
      if userWatching.tp is 'cmty'
        prop.isCmtyFav = true
        prop.cmtyWatch = watching
      else if userWatching.uaddr is prop.uaddr
        prop.isWatching = true
        prop.watching = watching
    return prop
  ###
  # # @description 列表不需要
  # @description 修改房源查看次数,对DB查询出来的房源信息做后续字段添加，eg:用户对房源，社区的关注信息，房源价格预测等
  ###
  @_addWatchingStoreyCmtyStatToProp:(opt={})->
    {prop, user} = opt
    if not isRMlisting prop.id
      # NOTE: rmcontact is no longer used !!! overwritten by forceShowSignup
      # BUG: TODO(future): remove rmcontact from prop completely
      prop.rmcontact = isOwnCtrlContactCity(prop)
      prop.isWatching = false
      prop.isCmtyFav = false
      prop.cmtyWatch = {}
      prop.watching = {}
    # user watching will send pn and record to user feeds
    prop = await Properties._setPropWatingFields {prop, user}
  
    # 显示storey info, TODO: skip non-apartment??
    rmStoreyInfo = await Properties.getRmStoreyInfoAsync prop.uaddr
    prop.rmStoreys = rmStoreyInfo.rmStoreys if rmStoreyInfo?.rmStoreys?
    
    # NOTE: inject prop.cmty avgS/avgR to prop
    if not (prop.city and prop.prov)
      return prop
    ret = await PropStatsModel.findPropCityCmty prop
    prop = statHelper.injectCmtyStatResultToProp({ret,prop})
    return prop

  ###
  # @description 列表需要
  # @description 对RM房源格式转换为TREB类型，房源类型返回格式修改，添加/删除部分字段
  ###
  @_deleteAndCalcFieldsForRMPropDetail:({prop,user,fromStr})->
    unless 'app' is fromStr
      prop.noSEOIndex = prop.market?.st isnt 'Promoted'
    if prop.market?.cmstn?
      prop.cmstn = prop.market.cmstn
    if prop.market?.agrmntImg?
      prop.agrmntImg = prop.market.agrmntImg
    prop.isV = prop.market?.isV or false
    if prop.market?.rmProp?
      prop.marketRmProp = prop.market.rmProp
      prop.mbl = prop.market.mbl if prop.market.mbl
    unless (user?.id is prop.uid)
      delete prop['58']
      delete prop['market']
      delete prop['uhouzz']

    prop.ptype = libP.getPtypeFull prop.ptype if prop.ptype
    # already done in addAddtionalFields
    # prop.dom = libP.computePropDom(prop)
    
    prop.isOwner = false
    if prop?.uid?.toString() is user?._id?.toString()
      prop.isOwner = true
    return prop


  # @param {array} property list
  @getRmStoreyInfoList:(list)->
    uaddrs = []
    rmStoreyMap = {}
    for p in list
      uaddrs.push p.uaddr
    rmStoreyInfoList = await Sqfts_Aggregate.findToArray {_id:{$in:uaddrs}},{fields:{rmStoreys:1, missingLvls:1}}
    if ((not rmStoreyInfoList) or (rmStoreyInfoList.length is 0))
      return list
    for s in rmStoreyInfoList
      rmStoreyMap[s._id] = s
    for prop in list
      if rmStoreyInfo = rmStoreyMap[prop.uaddr]
        for info in ['rmStoreys','missingLvls']
          prop[info] = rmStoreyInfo[info] if rmStoreyInfo[info]
    return list

  @getRltrsFromProperties:(rltr,cb)->
    return cb null,[] unless rltr
    PropRltrs.findToArray {_id:helpers.str2reg(rltr)},{sort:{_id:1},limit:10},(err,ret)->
      if err
        return cb err
      rltrs = []
      for i in ret
        rltrs.push i._id
      return cb null,rltrs

  @getAndSortPropListByIdsInProperties:(ids,fields) ->
    return [] unless ids.length
    mlsIds = []
    rmIds = []
    for id in ids
      if isRMlisting id
        rmIds.push id
      else if libP.isPropObjectId(id)
        mlsIds.push new ObjectId(id)
      else
        mlsIds.push id
    query = {$or:[{_id:{$in:mlsIds}},{id:{$in:rmIds}}]}
    option = {projection:fields}
    ret = await PropertiesCol.findToArray query,option
    ret ?= []
    if ret.length is 0
      return ret
    sortRet = getSortedPropsByIds ids,ret
    return sortRet
  
  ###
  # @description 隐藏/显示楼花转让房源
  # @param {string} id - A necessary param, prop id/_id
  # @param {boolen} hide - A necessary param,status
  # @return {ObjectId} {ok, msg}
  ###
  @hideAssignListing:(id,hide)->
    update = {}
    update.private = hide or false
    if /^RM1/.test id
      query = {id}
    else if libP.isPropObjectId(id)
      query = {_id: new ObjectId(id)}
    else
      query = {_id: id}
    ret = await UserListingCol.findOne query, {fields:{ltp:1}}
    if not ret
      return {ok:0,msg:'Prop Not Found'}
    unless (ret.ltp is 'assignment')
      return {ok:0,msg:'Wrong Type'}
    await UserListingCol.updateOne query, {$set:update}
    # NOTE: @zhangman prop.private 不会影响ES，可能要设置为{del:true}
    await PropertiesCol.updateOne query, {$set:update}
    return {ok:1,msg:'Success'}

  ###
  统计真楼花在各个城市和省份的个数
  ###
  @statisTrustedAssignByProvAndcity:()->
    query = {
      bool:{
        must:[
          {exists:{field:'market_rmProp'}},
          {term:{status:'A'}},
          {term:{src:'RM'}},
          {range:{_sysexp:{gt:new Date()}}}
        ]
      }
    }
    aggs = {
      group_by_prov_city: {
        composite: {
          sources: [
            {prov: {terms: {field: 'prov'}}},
            {city: {terms: {field: 'city'}}}
          ]
        }
      }
    }

    # NOTE: 添加try catch防止mongo启动时，es索引未创建时查询失败时影响正常功能
    try
      response = await elasticsearchLib.aggregateDocuments {query,aggs}
    catch err
      debug.error 'statisTrustedAssignByProvAndcity error', err
      return []

    results = []
    for bucket in response?.aggregations?.group_by_prov_city?.buckets
      obj = {}
      if bucket?.key?.prov? and bucket?.key?.city? and bucket?.doc_count?
        obj = {
          _id:{
            prov: bucket.key.prov,
            city: bucket.key.city
          },
          count: bucket.doc_count
        }
        results.push obj
    return results
    
  ###
  #统计真楼花有哪些ptype2类型
  NOTE: 有一些楼花hide或者del了，但是还是会统计到
  ###
  @statisTrustedAssignPtype2:()->
    query = {
      bool:{
        must:[
          {exists:{field:'market_rmProp'}},
          {term:{status:'A'}},
          {term:{src:'RM'}},
          {range:{_sysexp:{gt:new Date()}}}
        ]
      }
    }
    aggs = {
      ptype2_groups:{
        terms:{field:'ptype2'}
      }
    }

    # NOTE: 添加try catch防止mongo启动时，es索引未创建时查询失败时影响正常功能
    try
      response = await elasticsearchLib.aggregateDocuments {query,aggs}
    catch err
      debug.error 'statisTrustedAssignPtype2 error', err
      return []

    ptype2 = []
    results = []
    for bucket in response?.aggregations?.ptype2_groups?.buckets
      ptype2.push bucket.key if bucket.key
    if ptype2.length > 0
      results.push({_id:{ptype2}})
    return results
  
  ###
  # @description 给一个房源_id,查找这个房源的最高等级的merged房源
  # @param {string|objectId} id - 房源的_id
  # @return {object} {mergedTopId, aSIDs, extraInfo} - mergedTopId: 最高优先级的房源_id, aSIDs: 子房源的id信息, extraInfo: 需要额外字段的信息
  ###
  @findMergedTopPropAsids = (id,extraField) ->
    getExtraInfo = (prop) ->
      tmp = {}
      for k,v of extraField
        tmp[k] = prop[k] if prop[k]
      return tmp
    
    result = {mergedTopId: null, aSIDs: [],extraInfo: {}}
    extraInfo = {}
    unless id
      return result
    # 如果id是ObjectId格式的字符串，转换为ObjectId类型
    if libP.isPropObjectId(id)
      id = new ObjectId(id)
    projection = {_id: 1, aSIDs: 1,merged:1}
    if extraField
      projection = Object.assign(projection, extraField)
    prop = await PropertiesCol.findOne {_id:id}, {projection: projection}
    unless prop
      throw new Error(MSG_STRINGS.NOT_FOUND)
    unless prop?.merged # 如果当前房源没有merged字段则直接认为是最高级，获取需要的额外字段信息进行返回
      extraInfo[prop._id] = getExtraInfo(prop)
      return {mergedTopId: prop._id, aSIDs: prop.aSIDs or [], extraInfo}
    query = {merged:{$exists:false},aSIDs: {$elemMatch: {id: id}}}
    props = await PropertiesCol.findToArray query, {projection: projection}
    unless props.length
      result.mergedTopId = id
      return result
    if props.length is 1
      prop = props[0]
      if extraField
        extraInfo[prop._id] = getExtraInfo(prop)
      return {mergedTopId: prop._id, aSIDs: prop.aSIDs, extraInfo}
    mergedTopId = ''
    maxASIDs = []
    
    props.forEach((list) ->
      tmp = {}
      if extraField
        extraInfo[list._id] = getExtraInfo(list)
      # else 如果不需要获得额外字段，不进行信息获取
      if list.aSIDs.length > maxASIDs
        mergedTopId = list._id
        maxASIDs = list.aSIDs
      # else 表示继续查找下一个进行比较，不会存在merged字段不存在，aSIDs长度相同的情况
    )
    return {mergedTopId: mergedTopId, aSIDs: maxASIDs, extraInfo}

  # 将merged最高级别的房源aSIDs字段进行去重处理,返回id的集合，例如：["CAR40677779","TRBW10433487"]
  @getMergedIdsFromMergedTop = (propMergedTop)->
    aSIDs = [propMergedTop.mergedTopId]
    unless propMergedTop.aSIDs?.length
      return aSIDs
    propMergedTop.aSIDs.forEach((list) ->
      aSIDs.push(list.id)
    )
    return Array.from(new Set(aSIDs))

MODEL 'Properties',Properties
#module.exports.Properties = Properties
