debug = DEBUG()
crypto = INCLUDE 'crypto'
{nanoid} = INCLUDE 'nanoid'
checksum = INCLUDE 'lib.checksum'
helpers = INCLUDE 'lib.helpers'
objectHelpers = INCLUDE 'lib.helpers_object'
{formatPhoneNumber} = INCLUDE 'lib.helpers_number'
{formatCmntTs} = INCLUDE 'lib.helpers_date'
fileHelper = INCLUDE 'libapp.fileHelper'
libUser = INCLUDE 'libapp.user'
libOauth = INCLUDE 'libapp.oauth'
libWecard = INCLUDE 'libapp.wecard'
libProject = INCLUDE 'libapp.project'
libForum = INCLUDE 'libapp.forum'
libRmListing = INCLUDE 'libapp.rmlisting'
libPropertyImage = INCLUDE 'libapp.propertyImage'
libPropNotes = INCLUDE 'libapp.propNotes'
util = INCLUDE 'util'
config = CONFIG(['share','user_file','serverBase','fileBase','equifax'])
equifaxDB = config.equifax.db
equifaxCol = config.equifax.col
EquifaxCol = COLLECTION equifaxDB, equifaxCol
UserCol = COLLECTION 'chome', 'user'
UserFile = COLLECTION 'chome', 'user_file'
WechatUser = COLLECTION 'chome','wechat_user'
UserLog = COLLECTION 'chome','user_log'
UserProfile = COLLECTION 'chome','user_profile'
UserLoc = COLLECTION 'chome','user_loc'
AdminOpLogs = COLLECTION 'chome', 'admin_op_logs'
DevUniqID = COLLECTION 'chome', 'device_id'
EdmLog =  COLLECTION 'chome','edmLog'
UserStream = COLLECTION 'chome','user_stream'

TopupAgents = COLLECTION 'chome', 'topup_agents'
ChatDb = COLLECTION 'chome', 'chat'
# TODO: move group operations to user model
Properties = COLLECTION 'vow','properties'
PropFav = COLLECTION 'vow', 'prop_fav'
CondosCol = COLLECTION 'vow','condos'
UserListing = COLLECTION 'chome', 'user_listing'
Crm = COLLECTION 'chome','crm'
Showing = COLLECTION 'chome','showing'
WechatUser = COLLECTION 'chome','wechat_user'
Form = COLLECTION 'chome','form'
Forminput = COLLECTION 'chome','form_input'
Wecard = COLLECTION 'chome', 'wecard'
Group = COLLECTION 'chome','rm_group'
UserDel = COLLECTION 'chome','user_del'
ForumCol = COLLECTION 'chome', 'forum'
ProjectCol = COLLECTION 'chome', 'pre_constrct'
AdsCol = COLLECTION 'chome', 'ads'
UserWatchLocationCol = COLLECTION 'chome','user_watch_location'

Auth = COLLECTION 'chome','login'
RToken = COLLECTION 'chome', 'rtoken'
SysData = COLLECTION 'chome', 'sysdata'
PropNotesCol = COLLECTION 'chome', 'prop_notes'
PropNotesPersonalCol = COLLECTION 'chome', 'prop_notes_personal'
# TODO: use predefcol
UUIDCol = COLLECTION 'chome', 'user_uuid'

VcodeCol = COLLECTION 'chome', 'vcode'
VERIFY_EMAIL = 'verifyEmail'

ObjectId = ObjectID = INCLUDE('lib.mongo4').ObjectId

baseSendSMS = SERVICE 'sendSMS'
pushNotifyModel = MODEL 'PushNotify'
PnToken = COLLECTION 'chome', 'pn_token'

userAccessControl = INCLUDE 'model.userAccessControl'
sysdataId = 'accessControl'
cityHelper = INCLUDE 'lib.cityHelper'
functionHelper = INCLUDE 'lib.helpers_function'
{SOURCE_TYPE} = INCLUDE 'libapp.pushNotifyMsg'
TIME_MS_ONE_YEAR = 365*24*3600*1000
TIME_MS_7_DAYS = 7*24*3600*1000
MAX_PROP_LOG_COUNT = 500

WeChat = INCLUDE 'lib.wechat'
{isProxyEmail} = INCLUDE 'lib.proxyEmail'
{getValidatedEmail} = INCLUDE 'lib.sendMailValidate'

SHARE_SECRET = config.share?.secret
# getUserBaseUrlWithPath = fileHelper.getUserBaseUrlWithPath
postThirdPartyImg = fileHelper.postThirdPartyImg

FILE_WEB_BASE_DOMAIN = config.user_file?.wwwbase
FILE_LOCAL_FOLDER_BASE = config.user_file?.folder
unless FILE_LOCAL_FOLDER_BASE
  debug.error 'No user_file img folder defined',config.user_file
  throw new Error('No user_file img folder defined',)

if FILE_LOCAL_FOLDER_BASE.slice(-1) is '/'
  FILE_LOCAL_FOLDER_BASE = FILE_LOCAL_FOLDER_BASE.substr(0,FILE_LOCAL_FOLDER_BASE.length-1)
FILE_WEB_PROTOCOL = config.user_file?.protocol

# fileHelper.configSetup(appConfig)

# NOTE: user_profile findOneAndUpdate {"_id":{"type":"Buffer","data":[85,153,152,65,218,241,10,51,213,62,121,214]}}
# param was incorrected passed, id was an object
isValidId = (id)->
  if id instanceof Auth.ObjectId
    return true
  else if ('string' is typeof id) and /^[0-9A-F]{24}$/i.test id
    return true
  else if /\w+/.test id
    return true
  return false

ensureIndexErr =(err)->
  if err
    debug.error err
    throw err unless err.code is 85

if config.serverBase?.masterMode
  UserStream.createIndex {"ts": 1},{background:true}
  ForumCol.createIndex {'uid':1,'thumb':1}
  ForumCol.createIndex {'uid':1,'photos':1}
  UserListing.createIndex {'uid':1,'pic.l':1}
  CondosCol.createIndex {'img.l':1}
  CondosCol.createIndex {'floorplan.img':1}

  UserCol.createIndex {'flwng':1},ensureIndexErr
  UserCol.createIndex {'lts':1},ensureIndexErr
  # NOTE: ts for edm user without emlV
  UserCol.createIndex {'ts':1},ensureIndexErr
  UserCol.createIndex {'stars':1},ensureIndexErr
  UserCol.createIndex {'roles':1},ensureIndexErr
  UserCol.createIndex {'category.id':1,'vip_mt':1},ensureIndexErr
  UserCol.createIndex {'fornm':1}, {unique:true,sparse: true},ensureIndexErr
  UserCol.createIndex {'pn':1},{sparse:true},ensureIndexErr
  UserCol.createIndex {'fubAgentId':1},{unique:true,sparse:true},ensureIndexErr
  UserCol.createIndex {'fubId':1},ensureIndexErr
  UserCol.createIndex {'fubEml':1},ensureIndexErr
  UserCol.createIndex {'wxuid':1,},ensureIndexErr
  UserCol.createIndex {'wxAppOID':1},ensureIndexErr
  UserCol.createIndex {'wxMobOID':1},ensureIndexErr
  UserCol.createIndex {'_exp24h':1},{expireAfterSeconds:0}

  Auth.createIndex {'unikey':1},{unique:true,sparse: true},ensureIndexErr
  Auth.createIndex {'uid':1},ensureIndexErr
  Auth.createIndex {'_exp24h':1},{expireAfterSeconds:0}

  UserProfile.createIndex {'wpHost':1}, {sparse: true},ensureIndexErr
  UserProfile.createIndex {'_mt':-1}, ensureIndexErr
  UserProfile.createIndex {'favCmtys.nm':1}, {sparse: true},ensureIndexErr
  UserProfile.createIndex {'locale':1, 'splang':1,'pnNoNews':1}, {sparse: true},ensureIndexErr
  UserProfile.createIndex {'savedSearch.ts':-1}, ensureIndexErr
  # TODO: NOTE: index reason
  UserLog.createIndex {'id':1,'uid':1,'tp':1},ensureIndexErr
  UserLog.createIndex {'uid':1},ensureIndexErr
  UserLog.createIndex {'uid':1,'tp':1,'edm':1},ensureIndexErr
  UserLog.createIndex {'uid':1,'tp':1,'date':-1},ensureIndexErr
  # NOTE: for logPropChangeStatus query
  UserLog.createIndex {'uid':1,'id':1,'date':-1},ensureIndexErr
  UserLog.createIndex {'uid':1,'tp':1,'mt':-1},ensureIndexErr
  # NOTE: for too many logs deletion query
  UserLog.createIndex {'uid':1,'tp':1,'_mt':-1,'noDelBefore':-1},ensureIndexErr
  UserLog.createIndex {'tp':1,'id':1,'mt':-1},ensureIndexErr

  # NOTE: for natural expire of log
  UserLog.createIndex {'exp':1},{expireAfterSeconds:0} # 60*60*24*365

# TODO: add comment and input checks

MSG_STRINGS.def {
  NO_PROFILE: 'Incorrect profile, please contact admin',
  BAD_LOGIN_ID: 'Bad login id',
  UNKNOWN_LOGIN_ID:'Unknown login id',
  NOT_VAILD_USER_PARAMTER:'Not a valid user parameter',
  NO_MATCHED_TOKEN:'No matched token',
  NO_TOKEN:'No token',
  ID_TOO_SHORT:'ID too short, at leaset 5 chars!',
  ID_BEEN_USED:'This ID has already been used!',
  NO_PUSH_NOTIFY_ID:'No Push Notify ID',
  NO_USER_FOR_THIS_LOGIN:'No User for this login.',
  WECHAT_ID_ONLY_USE:'Wecard ID: Only a-z A-Z and -',
  NO_FOLLOWED_USER:'No Followed User',
  NO_ENOUGH_PARAMETERS:'No enough parameters',
  NO_AUTHORIZED:'Not Authorized',
  NO_LOGIN_ID_OR_PWD:'No Login ID or Password',
  ID_OR_PWD_NOT_MATCH:'Login ID or Password does not match.'
  EMAIL_NOT_VERIFIED:'Email not verified yet.',
  NO_WECHAT_USER_ID:'No Wechat User ID'
  WECHAT_AUTH_PERFORMED:'WeChat Auth Performed.',
  CANT_GET_USERUNIQUEID_RECORD:"Can't get UserUniqueID record",
  LOGIN_ERROR:'Login Error',
  CANT_FIND_LOGIN_ID:"Can't find the login id.",
  NO_METHOD_TO_SEND:'No method to send notification',
  ERR_CELL_PHONE_NUMBER:'Error Cell Phone Number',
  RLOGIN_ERROR:'RLogin Error',
  NO_LOGIN_INFO:'No login info',
  NO_ACCESS_TOKEN:'No access token',
  NOT_ENOUGHT_AUTHORITY:"Not enought authority to change other user's permits.",
  NOT_A_VAILD_CB:'Not a valid callback function.',
  NO_REMOTE_SERVER:'No remote server',
  NOT_FIND_LOGIN_ID:'Can not find specified login id',
  NO_ID:'No ID',
  CAN_NOT_FIND_URL:"Can't find Open ID URL",
  UN_SUPPORTED_PROVIDER:'Un-supported Open ID provider',
  CAN_NOT_CREATE_USER_FILE:"Can't create user file",
  NO_USER:'No User',
  CAN_NOT_CREATE_KEY:"Can't create key",
  AUTH_ERROR:'Auth Error',
  CAN_NOT_FIND_USER:'Can not find user'
  NO_USER_FOR_THE_EMAIL:'No User for the email',
  NO_LOGIN_FOR_THE_EMAIL:'No Login for the email',
  NEW_EMAIL_ALREADY_EXISTS:'New email already exists',
  NO_AUTHED:'Not authed',
  DIFF_PROV:'User diff prov',
  NO_PROV:'No prov',
  NO_TREB_ID:'No TREB ID',
  NO_LIC:'No LIC',
  NO_CPM_NAME:'No CPM NAME',
  USER_NOT_LOGIN:'User not login',
  USER_CANT_DELETE:'Users cannot be deleted',
  EMAIL_MISSING:'Email is missing or invalid',
  EMAIL_VERIFY:'EMAIL_VERIFY',
  OVERSBSCB:'Up to 3 saved search notifications allowed.',
  OVERSBSCB_REALTOR:'Upgrade to VIP to turn on unlimited saved search notifications.',
  NO_APPID:'No AppId',
  REQUEST_EDMNO:'client request unsubscribe',
}

tblModules = {}
tblDeny = {}
PROFILE_STARS = 'profileStars'
LIMIT_SAVEDSEARCH_SBS = 3
tblAllow = userAccessControl.TBLALLOW
tblDefault = userAccessControl.TBLDEFAULT
USER_SAVE_SEARCH_LIMIT = 30
# 添加appAuth日期，commitId:2d88e6b
IGNORE_EMLV_DATE = new Date('2020-07-25')


createUniqueUserID = (cb)->
  SysData.findOneAndUpdate {_id:'UserUniqueID'},{$inc:{lastID:1}},{upsert:true,returnDocument:'after'},(err,r)->
    return gFindError err,{f:'createUniqueUserID'},cb if err
    if not r?.value?.lastID then return cb new Error(MSG_STRINGS.CANT_GET_USERUNIQUEID_RECORD)
    cb null,'U' + helpers.randomCode(7) + r.value.lastID

PLSWAIT '_user_unique_id_'
# should create index before findOne
ensureIDIndex = ->
  UserCol.createIndex {'id':1},{unique:true,sparse:false,name:'unique_user_realmaster_id'},(err)->
    if err then ensureIndexErr err
    PROVIDE '_user_unique_id_'
ensureIDIndex()

ensureID = ->
  UserCol.findOne {id:$exists:false},{readPreference:UserCol.ReadPreferencePrimary,fields:{_id:1}},(err,user)->
    if err then throw err
    if not user? then return null
    createUniqueUserID (err,uid)->
      if err then throw err
      UserCol.updateOne {_id:user._id},{$set:id:uid},{},(err,r)->
        if err then throw err
        process.nextTick ensureID
ensureID()

_load = (cb)->
  SysData.findOne {_id:sysdataId},(err,data)->
    if err then throw err
    if data?.deny
      tblDeny = helpers.shallowExtendObject tblDeny,data.deny
      tblAllow = helpers.shallowExtendObject tblAllow,data.allow
      tblDefault = helpers.shallowExtendObject tblDefault,data.def
      tblModules = helpers.shallowExtendObject tblModules,data.mdls
    cb() if cb


PLSWAIT sysdataId
_load -> PROVIDE sysdataId

# keep_in/cm_pk is for keep user logged in status. Not designed for APP users
PK_PARAM = 'keep_in'
PK_COOKIE = 'cm_pk'
MAX_LOGIN_LENGTH = 30
FailedIPs = {}

# TODO: 2 levels, 1 critical(notify admin), 2 error
gFindError = (err, { f, id, eml, info}, cb) ->
  if not (err instanceof Error)
    err2 = new Error(err)
  # FEAT: use throw?
  debug.error (err2 or err), f, 'user _id:', id, 'user eml:', eml, 'other info: ', info
  cb err if cb

# TODO: check 647/426/289/198?
isValidMbl = (mbl='')->
  return false if not mbl
  return false if mbl.length < 9 #canada
  return false if mbl.length > 14 #+86133123456789
  # return false if not mbl.match(/^1[3-9]\d{9}$/)
  return true

# 6476337777 -> +16476337771
# 13363399336 -> +8613363399336
# NOTE: only support china/canada mobile number?
# NOTE: this step is done in sendSMS lib
# TODO: test if support cn phone number, change tonormalizePhoneNumberCN
normalizeMbl = (mbl='')->
  if /^\+\d/.test mbl
    return mbl
  cntyCode = '+1'
  if mbl.match(/^1[3-9]\d{9}$/)
    cntyCode = '+86'
  return cntyCode+mbl

###  ###
sendSMS = (user,body,cb)->
  if not isValidMbl(user?.mbl)
    return gFindError MSG_STRINGS.ERR_CELL_PHONE_NUMBER,{
      f:'sendSMS',
      eml:user.eml,
      info: 'mbl: '+user?.mbl},cb
  sms =
    To: user.mbl
    Body: body
    From: '+19056142609'
  # debug.log sms
  baseSendSMS sms,(err,response)->
    return gFindError err,{f:'baseSendSMS',eml:user.eml,info:'mbl: '+sms.To},cb if err

# loginUser?，login_login? 暂未使用
# _auth_pk
authPk = ({
  req,
  cki #user info
  },cb)->
    q = {_id:cki.id,'pks.pk':cki.pk}
    Auth.findOne q,{readPreference:Auth.ReadPreferencePrimary},(err,login)->
      return gFindError err,{f:'authPk',eml:cki.id},cb if err
      return gFindError MSG_STRINGS.NO_LOGIN_INFO,{f:'authPk',eml:cki.id},cb if not login?
      # found login
      login_login {req,login,fromMethod:'authPk'},cb

# loginUser，login_login
# _domain2key
domain2key = (dm,req)->
  dm?.toLowerCase().replace(/[\.\$]/g,'@') + '@@' + req.getDevType()

# auth_login，login_login
# _failed_once
failedOnce = ({req,
    login #Auth找到的登录信息
    },cb)->
  if not login
    login = null
  # update failed IPs
  wait = 0
  ip = req.remoteIP()
  if FailedIPs[ip]
    wait = FailedIPs[ip]++
    # TODO: attack prevention
  else
    FailedIPs[ip] = 1

  if login
    # login failed counter++
    login.failed = (login.failed or 0) + 1
    Auth.updateOne {_id:login._id},{$inc:{failed:1}},{safe:true},(err,info)->
      cb login.failed
  else
    helpers.wait (wait * 1000),-> cb()

# login_login
# _login_user
loginUser = ({req,
    login,
    user,
    fromMethod}, cb)->
  debug.error 'loginUser: no login._id' unless login?._id
  debug.error 'loginUser: no user._id' unless user?._id
  id = login._id.toLowerCase()
  dm = req.hostname
  remoteIP = req.remoteIP()
  his = {dt:(new Date()),ip:remoteIP,dm:dm,f:fromMethod}
  if oid = req.session.get('openid')
    his.openid = true
  set = {failed:0}
  push = {his:{$each:[his],$slice:-MAX_LOGIN_LENGTH}}
  setPK = (resp,id)->
    # set and return pk
    npk = helpers.randomString().substr(5,12)
    val = "#{id}:#{npk}"
    cookie_option = {maxAge:(60000*60*24*365),path:'/'}
    resp.cookie PK_COOKIE,val,cookie_option
    npk
  done = ->
    Auth.updateOne {_id:id},{$set:set,$push:push},{safe:true, writeConcern:{wtimeout:3000}},(err,info)->
      if err
        req.logger.error err
        debug.error err,'loginUser, eml: ',id
        gFindError MSG_STRINGS.LOGIN_ERROR,{f:'loginUser',id},cb if cb
        return null
      set =
        ip:remoteIP,
        lts:new Date(),
        cip:req.isRealChinaIP()
      if appVer = req.cookies['apsv']
        set.appVer = appVer
      # set.login_id = login._id
      UserCol.updateOne {_id:user._id},{$set:set},{writeConcern: {wtimeout:3000}},(err)->
        if err then debug.error err,'loginUser, id:',user._id
        req.session.set 'login_id',login._id.toString()
        # user.login_id = login._id.toString()
        #user._id = user._id.toString()
        if req.isMobile()
          req.session.maxAge req,3600 * 1000 * 3 # 3 hour
        if login.remote_return_url # remote login, with return url
          req.session.set 'home_url',login.remote_return_url
        req.session.set 'user',user,->
          cb null,user,login if cb # for chinaMode, allow login pass, then set the real user later
  if req.resp and ( ((pk = req._auth_pk) and pk.pk) or req.param(PK_PARAM) )
    npk = setPK req.resp,id
    push.pks = {$each:[{pk:npk,ts:Date.now(),sid:req.session.id()}],$slice:-3}
    if pk
      return Auth.updateOne {_id:id},{$pull:{pks:{pk:pk.pk}}},{safe:true,writeConcern: {wtimeout:3000}},(err)->
        if err then debug.error err,'loginUser, eml:',_id
        done()
  else
    # only update sid, when not keep_in and not auth_pk; or from websocket
    set['sid.' + domain2key(dm,req)] = req.session.id()
  done()

# _login_login
login_login = ({req,
    login,
    fromMethod
    }, cb)->
  if not login.uid?
    req.logger.warn "Empty User for Login '#{login._id}'"
    return failedOnce {req,login},(failed)->
      return cb MSG_STRINGS.NO_USER_FOR_THIS_LOGIN,null,failed
  findOneByid = ->
    UserCol.findOne {_id:login.uid},{readPreference:UserCol.ReadPreferencePrimary},(err,user)->
      if err
        debug.error err,'login_login,id: ',login.uid
        return failedOnce {req,login},(failed)->
          return cb MSG_STRINGS.DB_ERROR,null,failed
      if not user?
        req.logger.warn "No User '#{login.uid}' for Login '#{login._id}'"
        return failedOnce {req,login},(failed)->
          return cb MSG_STRINGS.NO_USER_FOR_THIS_LOGIN,null,failed
      return loginUser {req,login,user,fromMethod},cb unless req.resp  # when from websocket
      dm = req.hostname
      kdm = domain2key dm,req
      if (not req._auth_pk?) and (not req.param(PK_PARAM)?) and (sid = login.sid?[kdm]) and (sid isnt req.session.id())
        return req.session.destroy sid, ->
          req.resp.clearCookie PK_COOKIE unless req.resp.finished
          return loginUser {req,login,user,fromMethod},cb
      else
        return loginUser {req,login,user,fromMethod},cb
  if config.serverBase?.chinaMode
    # 加速db查询，TODO:cb use helper.callOnce
    return UserCol.findOne {_id:login.uid},{readPreference:UserCol.ReadPreferenceNearest},(err,user)->
      if err
        debug.error err,'login_login,id: ',login.uid
        return findOneByid()
      if not user?
        debug.info MSG_STRINGS.USER_NOT_FOUND,login.uid
        return findOneByid()
      req.session.set 'user',user,->
        cb null,user
        cb = ()-> # set cb to empty function, so don't callback any more
        findOneByid() # still do base logic and set the correct remote main user
  findOneByid()

# __auth_internal_change_permit
# internal_change_permit = (req,uid,permits,cb)->
#   User.hasPermit req,['admin','site_admin'],(user)->
#     if user
#       UserCol.findOne {_id:uid},{readPreference:UserCol.ReadPreferencePrimary},(err,theuser)->
#         if err
#           debug.error err
#           return cb err
#         if Array.isArray permits or 'object' isnt typeof permits
#           return cb MSG_STRINGS.BAD_PARAMETER
#         for p,tf of permits
#           theuser._pmt[p] = if tf then true else false
#         UserCol.updateOne {_id:uid},{$set:{_pmt:theuser._pmt}},{safe:true,upsert:false,new:true, writeConcern: {wtimeout:3000}},(err,nu)->
#           return cb err,nu
#     else
#       return cb MSG_STRINGS.NOT_ENOUGHT_AUTHORITY

# __auth_convert_permits_type
convert_permits_type = (permits,tf)->
  pm = {}
  if Array.isArray permits
    for p in permits
      pm[p] = if tf? then tf else true
  else if 'string' is typeof permits
    pm[permits.toString()] = if tf? then tf else true
  else if 'object' is typeof permits
    for p,torf of permits
      pm[p] = if tf? then tf else torf
  delete pm.admin # nobody can create or remove another admin
  pm

_saveAccessDb2table = (cb)->
  if config.serverBase?.satelliteMode
    cb() if cb
    return
  set = {deny:tblDeny,allow:tblAllow,def:tblDefault,mdls:tblModules}
  SysData.updateOne {_id:sysdataId},{$set:set},{upsert:true},(err)->
    throw err if err
    cb() if cb
_timer = null
_delaySaveAccess = ->
  clear = ->
    if _timer
      helpers.clearWait _timer
      _timer = null
  clear()
  _timer = helpers.wait 1000, ->
    clear()
    _saveAccessDb2table()

# TODO: move to batch? if server restart < 1 day this will not work
resetWeeklyShareCounter = () ->
  if (new Date()).getDay() is 4
    UserCol.updateMany {wsc:{$exists:1}},{$set:{wsc:0}},{multi:1},(err)->
      if err then debug.error err,'resetWeeklyShareCounter'

setInterval resetWeeklyShareCounter,24*3600000

gUserLogHis = []
MAX_BATCH_SIZE = 200
bulkUpdateUserLogs = ()->
  # console.log 'invoke bulk----'
  if not gUserLogHis.length
    return
  isDone = false
  while not isDone
    # NOTE: 每秒很多访问，gUserLogHis有可能永不为空，如果剩下的少于10，剩下的在另一个interval执行
    if gUserLogHis.length < 10
      isDone = true
    jobs = []
    for i in [0..MAX_BATCH_SIZE]
      if not log = gUserLogHis[i]
        break
      jobs.push {
        updateOne:{
          filter:log.filter
          update:log.update
          upsert: true
        }
      }
    if not jobs.length
      isDone = true
      return
    User.bulkUpdateUserLogs(jobs)
    # splice(start, deleteCount), because we updated MAX_BATCH_SIZE+1 records(start from 0)
    gUserLogHis.splice(0,MAX_BATCH_SIZE+1)
setInterval bulkUpdateUserLogs,5*1000


_findByQuery = (q,{Coll,projection},cb)->
  col = Coll or UserCol
  param = {}
  param.fields = projection if projection
  col.findOne q,param,cb

_getListByQuery = (q,{Coll,projection,sort,limit,skip},cb)->
  col = Coll or UserCol
  param = {}
  param.fields = projection if projection
  param.limit = limit if limit
  param.skip = skip if skip
  # param. = getFld opt?.f if opt?.f
  param.sort = sort if sort
  return col.findToArray q,param,cb if cb
  return await col.findToArray q,param

# TODO @rain: shall use UserCol.updateOne or findOneAndUpdate in place,
#         1. do not need to make a one line function. It's easier to maintain when in place.
#         2. Also findOneAndUpdate is much slower than updateOne. Many place only need updateOne.
#         3. findOneAndUpdate return different result than updateOne. The original calling side
#              may have trouble to deal with different result
# TODO: need to check all called position and return  type
_updateOne =(q,{data,param = {},Coll},cb) ->
  col = Coll or UserCol
  col.findOneAndUpdate q,data,param,cb

_sortor = (a,b)->
  if a.n < b.n
    return 1
  -1
# @params
# type dateString = 'String'; # '2022/01/22'
# @return
# type return = 'boolean';
_isThisMonth = (n='')->
  now = new Date()
  arr = n.split('/')
  # console.log arr, now.getFullYear(), helpers.zeroPad(now.getMonth()+1,2)
  if (arr[0] is ''+now.getFullYear()) and (arr[1] is helpers.zeroPad(now.getMonth()+1,2))
    return true
  return false
_isThisYear = (n='')->
  now = new Date()
  arr = n.split('/')
  # console.log arr, now.getFullYear(), helpers.zeroPad(now.getMonth()+1,2)
  if (arr[0] is ''+now.getFullYear())
    return true
  return false

class User
  # TODO: shold title and description be here? belogs to UI instead of DB, only status/on/off should be here
  @getNotificationOption:()->
    obj =
      mobile:
        title:'Mobile Push'
        description:'Not receiving notifications?'
        link:'/1.5/settings/diagnosing'
        action:'Diagnose'
        flds:[
          {title:'Instant News'
          tag:'pnNoNews'
          description:'Top news and events'
          status:0
          agent:false},
          {title:'Listing Morning Express'
          tag:'pnNoMLSM'
          description:'A summary of new listings in subscribed cities'
          status:0
          agent:false},
          {title:'Listing Evening Express'
          tag:'pnNoMLSE'
          description:'A summary of new listings in subscribed cities'
          status:0
          agent:false},
          {title:'Saved Searches'
          src:'/1.5/settings/savedSearchModel?d=/1.5/settings/notification&isPopup=1'
          tag:'pnNoSearch'
          description:'Events based on subscribed search criteria'
          status:0
          agent:false
          number:0
          },
          {title:'Saved Communities'
          tag:'pnNoCmty'
          description:'Events based on subscribed communities'
          status:0
          agent:false},
          {title:'Saved Locations'
          tag:'pnNoLocation'
          description:'Events based on subscribed locations'
          status:0
          agent:false},
          {title:'Saved Homes'
          tag:'pnNoFavs'
          description:'Events based on subscribed properties'
          status:0
          agent:false},
          {title:'Showing Listings'
          tag:'pnNoShowing'
          description:'Events of the listings in your showing lists'
          status:0
          agent:true},
        ]
      email:
        title:'Email'
        emailStatus:
          unsubscribe:
            # cls: 'fa fa-check-circle'
            word: 'Unsubscribe All'
            # color: '#5CB85C'
            action :'set'
          resubscribe:
            # cls: 'fa fa-exclamation-circle'
            word: 'Re-subscribe'
            # color: '#FFCD00'
            action: 'unset'
            description: "You've been unsubscribed from all emails"
          unverified:
            cls: 'fa fa-exclamation-circle'
            word: 'Verify'
            color: '#FFCD00'
            action: 'verify'
            description: 'Your account is unverified'
          blocked:
            cls: 'fa fa-ban'
            word: 'Blocked'
            color: '#666'
            action: 'blocked'
            description: "You've been blocked from all emails"
        link:'/1.5/user/verifyemail'
        flds:[
          {title:'Saved Searches'
          src:'/1.5/settings/savedSearchModel?d=/1.5/settings/notification&isPopup=1'
          tag:'edmNoSearch'
          description:'Events based on subscribed search criteria'
          status:0
          agent:false
          number:0
          },
          {title:'Saved Cities'
          tag:'edmNoStat'
          description:'Market stats of subscribed cities'
          status:0
          agent:false},
          {title:'Saved Communities'
          tag:'edmNoCommunity'
          description:'Events based on subscribed communities'
          status:0
          agent:false},
          {title:'Saved Locations'
          tag:'edmNoLocation'
          description:'Events based on subscribed locations'
          status:0
          agent:false},
          {title:'Saved Homes'
          tag:'edmNoProperty'
          description:'Events based on subscribed properties'
          status:0
          agent:false},
          {title:'Saved Estimates'
          tag:'edmNoEvaluation'
          description:
            'Events based on estimated properties'
          status:0
          agent:false},
          {title:'Saved Precons'
          tag:'edmNoProject'
          description:
            'Events based on subscribed pre-constructions'
          status:0
          agent:false},
          {title:'New Assignments'
          tag:'edmNoAssignment'
          description:'New assignment listings on RealMaster'
          status:0
          agent:true},
          {title:'New Exclusives'
          tag:'edmNoExlisting'
          description:'New exclusive listings on RealMaster'
          status:0
          agent:true},
          {title:'Posts For You'
          tag:'edmNoForum'
          description:'Events on posts I commented and hot posts'
          status:0
          agent:false},
          {title:'Announcements'
          tag:'edmNoNews'
          description:'Product updates and announcements from RealMaster'
          status:0
          agent:false}
        ]
    return obj
  @NOTIFICATION_DIAGNOSING:
    system:
      title:'System services',
      success:'Google Play installed',
      failure:'Google Play not installed'
    notification:
      title:'Notification setting',
      success:'On',
      failure:'Off',
      onFail:'Fix Now',
    language:
      title:'Language setting',
      success:'',
      failure:'Please select a language.',
      onFail:'Change',
      failSrc:'/1.5/settings'
    message:
      title:'Push message test',
      success:'Success',
      #failure:'Failed! After receiving your report, we will check the problem.',
      failure:'Failed. Try to kill & restart App.'
      onFail:'Report',

  @NOTIFICATION_PN_FIELDS:['pnNoMLSM','pnNoMLSE','pnNoNews',\
    'pnNoCmty','pnNoFavs','pnNoLocation','pnNoShowing','pnNoSearch']

  @NOTIFICATION_EMAIL_FIELDS:['edmNoEvent', 'edmNoNews',\
    'edmNoReport','edmNoProperty','edmNoCommunity','edmNoAssignment','edmNoExlisting',\
    'edmNoForum','edmNoProject','edmNoStat','edmNoEvaluation',\
    'edmNoSearch','edmNoLocation']

  @REMOTESERVERS: {}
    #real411app:{host:'app.real411.ca',path:'/rauth',method:'POST'}
    #onemsapp:{host:'app.1ms.ca',path:'/rauth',method:'POST'}
    #test:{host:'test.bbb',port:8080,path:'/rauth',method:'POST'}

  @THIRDPARTYOPS:
    google: 'https://www.google.com/accounts/o8/id'
    yahoo: 'https://me.yahoo.com'

  @PUBLIC_FIELDS:
    _id:1
    id:1
    fn:1
    nm_zh:1
    nm_en:1
    cpny_zh:1
    cpny_en:1
    ln:1
    nm:1
    avt:1
    mbl:1
    eml:1
    web:1
    sgn:1
    wx:1
    itr:1
    wxgrp:1
    cpny:1
    roles:1
    qrcd:1
    grpqrcd:1
    wurl:1
    fburl:1
    twturl:1
    cpny_pstn:1
    addr:1
    tel:1
    fax:1
    exlang:1
    fas:1,
    fornm:1
    sas:1
    category:1
    cpmnm:1
    forBlk:1
    splang:1
    blkCht:1
    stars:1
    defaultHomeTab:1

  @CRM_FIELDS:
    _id:1
    nm:1
    eml:1
    tel:1
    mbl:1
    wx:1
    avt:1
    lts:1
    roles:1
    mt:1
    crmID:1

  @SHOWING_AGENT_FIELDS:
    _id: 1
    mbl:1
    eml:1
    avt:1
    cpny_en:1
    cpny_zh:1
    nm:1
    nm_en:1
    nm_zh:1

  @WESITE_FIELDS:
    _id:1
    fn:1
    nm_zh:1
    nm_en:1
    cpny_zh:1
    cpny_en:1
    ln:1
    nm:1
    avt:1
    mbl:1
    hmbl:1
    eml:1
    wx:1
    qrcd:1
    wxgrp:1
    grpqrcd:1
    itr:1
    cpny:1
    cpny_pstn:1
    cpny_wb:1
    web:1
    addr:1
    tel:1
    fax:1
    exlang:1
    category:1
    sas:1
    rid:1
    aid:1
    bid:1
    roles:1
    splang:1
    ddf_rid:1
    stars:1

  @EXCLUSIVE_FIELDS:
    nm:1
    eml:1
    roles:1
    _id:1
    fn:1
    ln:1
    nm_en:1
    nm_zh:1
    cpny_zh:1
    cpny_en:1
    avt:1
    cpny:1
    mbl:1
    tel:1

  @NOTIFY_FIELDS:
    _id:1
    pn:1
    mbl:1
    pnn:1
    mbln:1
    eml:1
    emln:1
    locale:1
    roles:1

  @MEMBER_FIELDS:
    _id: 1
    eml: 1
    mbl: 1
    pn:  1
    nm_en: 1
    nm_zh: 1
    avt: 1
    lts: 1
    wx:1

  @FOLLOWING_FIELDS:
    flwng:1
    mbl:1
    tel:1
    eml:1
    fn:1
    ln:1
    nm:1
    avt:1
    id:1
    nm_zh:1
    nm_en:1

  @CATEGORY_PROVIDER_FIELDS:
    shr:1
    category:1
    wx:1
    cpny_en:1
    cpny_zh:1
    nm_zh:1
    nm_en:1
    fn:1
    ln:1
    nm:1
    _id:1
    cpny:1
    cpny_pstn:1
    avt:1
    roles:1
    itr:1
    mbl:1
    eml:1
    sas:1
    lic:1
    splang:1
    stars:1
    actpts:1
    lts:1

  @RECOMMEND_PROVIDER_FIELDS:
    lts:1
    wx:1
    cpny_en:1
    cpny_zh:1
    nm_zh:1
    nm_en:1
    fn:1
    ln:1
    nm:1
    _id:1
    cpny:1
    cpny_pstn:1
    avt:1
    roles:1
    itr:1
    mbl:1
    eml:1
    sas:1
    lic:1
    splang:1
    stars:1

  @DOWNLOAD_FIELDS:
    mblalt:1
    _id:1
    avt:1
    sgn:1
    nm:1
    fn:1
    ln:1
    nm_zh:1
    nm_en:1
    cpny:1
    cpny_zh:1
    cpny_en:1
    cpny_pstn:1
    addr:1
    tel:1
    fax:1
    wx:1
    id:1
    mbl:1
    eml:1
    web:1
    qrcd:1
    roles:1
    flwng:1

  @BASIC_INFO_FIELDS:
    nm:1
    eml:1
    roles:1
    _id:1
    fn:1
    ln:1
    nm_en:1
    nm_zh:1
    cpny_zh:1
    cpny_en:1
    avt:1
    cpny:1
    mbl:1
    tel:1
    stars:1

  @AGENT_FIELDS:
    eml:1
    avt:1
    fn:1
    ln:1
    nm:1
    roles:1
    nm_en:1
    nm_zh:1
    wxavt:1
    pic:1

  @LISTING_OWNER_FIELDS:
    nm:1
    eml:1
    roles:1
    fn:1
    ln:1
    nm_en:1
    nm_zh:1
    avt:1
    mbl:1
    tel:1
    cpny_zh:1
    cpny_en:1
    cpny:1

  @SPLANG:
    'English':'EN'
    'Mandarin':'普'
    'Cantonese':'粤'

  @AVATAR_FIELDS:
    _id: 1
    eml: 1
    nm:1
    nm_en: 1
    nm_zh: 1
    avt: 1
    lts: 1
    wxavt:1

  @USER_LOG_TYPES:
    EDIT_PROFILE: 'editprofile'
    EDM: 'edm'
    FORUM: 'forum'
    PROJECT: 'project'
    PROPERTY: 'property'
    RM_LISTING: 'rmlisting'
    SAVED_SEARCH: 'savedsearch'
    TIMESTAMP: 'timestamp'

  @SOURCE_TYPE_REAR_MATCH:
    Homes:'Saved'
    Showings:'Showing'
    Searches:'Saved Search'
    Communities:'Community'
    Locations:['Location','Building']

  @FORM_INPUT_FIELDS = Object.assign {} , User.PUBLIC_FIELDS,
   {
     hasWechat:1, flwng:1, flwngRm:1, roles:1, fubId:1, \
     fubEml:1, fubAgentId:1, fubMbl:1
   }

  @findById:(id,{projection,Coll},cb)->
    if id instanceof Auth.ObjectId
      q = {_id:id}
    else if ('string' is typeof id) and /^[0-9A-F]{24}$/i.test id
      q = {$or:[{_id:new ObjectId(id)},{id:id}]}
    else #if /^U/.test id
      unless isValidId(id)
        debug.error 'findById,Error: Invalid id type: ',id
        # 有些地方可能传了空的id，期望返回一个空的user，直接返回错误可能会导致问题
        return cb(null,{})#MSG_STRINGS.BAD_PARAMETER
      q = {id:id}
    _findByQuery q,{Coll,projection},(err,user)->
      return cb(err) if err
      if user?.avt
        user.avt = libPropertyImage.replaceRM2REImagePath user.avt
      cb(null,user)
  
  @findByIdAsync =  functionHelper.addAsyncSupport User.findById

  @findByEml:(eml,{projection},cb)->
    _findByQuery {eml},{projection},cb

  #FubEml should be unique.
  @findByFubEml:(fubEml,{projection},cb)->
    _findByQuery {fubEml},{projection},cb

  @findByFubId:(fubId,{projection},cb)->
    _getListByQuery {fubId},{projection},(err,users)->
      return cb err if err
      return cb null unless users.length
      for user in users
        user.eml = User.getEmail user
      return cb null, users
  
  #fubAgentId should be unique.
  @findByFubAgentId:(fubAgentId,{projection},cb)->
    _findByQuery {fubAgentId},{projection},(err,user)->
      return cb err if err
      return cb null unless user
      user.eml = User.getEmail user
      return cb null, user

  @findByForumName:(fornm,cb)->
    _findByQuery {fornm},{},cb

  @findByEmlAsync =  functionHelper.addAsyncSupport User.findByEml
  @findByFubIdAsync =  functionHelper.addAsyncSupport User.findByFubId
  @findByFubAgentIdAsync =  functionHelper.addAsyncSupport User.findByFubAgentId

  @updateForumName:({req,user,fornm},cb)->
    User.updateAndChangeSession {req,user,data:{fornm:fornm,mt:new Date()}},cb

  @blockChatOrCommentByDevId:({req,user,id,pn,forum},cb)->
    _id = null
    if id or forum
      _id = user?.devId
      if not _id
        return cb 'user no devId'
    if pn
      _id = user?.pn
      if not _id
        return cb 'user no pn'
    return cb 'err no _id' if not _id
    db = PnToken
    if id or forum
      db = DevUniqID
    act = 'blockChat'
    # TODO: user pn db do not remove old record; pool+user.pn,when send compare
    set = {blkCht:1}
    if forum
      set = {forBlk:1}
      act = 'blockCmnt'
    db.updateOne {_id:_id},{$set:set}, (err,ret)->
      AdminOpLogs.insertOne {act, ts:new Date(),id:_id, target:user.eml}
      cb err,ret?.result?.n or ret.insertedId
    # User.updateAndChangeSession {req,user,data:{fornm:fornm,mt:new Date()}},cb
  ## @return [true/false,'reason']
  @isForumCommentBlocked:({user},cb)->
    # forBlk
    try
      verified = await User.checkVerify user._id
    catch err
      return cb [false]
    return cb [true, MSG_STRINGS.EMAIL_NOT_VERIFIED] if not verified?.emlV
    User.isUserBlockedByType {user,userBlkField:'forBlk'},cb
  @isChatBlocked:({user},cb)->
    User.isUserBlockedByType {user,userBlkField:'blkCht'},cb
  @isUserBlockedByType:({user,userBlkField='blkCht'},cb)->
    if user?[userBlkField]
      return cb [true,'Account ban']
    pn = user?.pn
    devId = user?.devId
    if not (pn or devId)
      return cb [false]
    checks = []
    checks.push {tp:'pn',_id:pn} if pn
    checks.push {tp:'devId',_id:devId} if devId
    fields = {}
    fields[userBlkField] = 1
    doOne = ()->
      job = checks.pop()
      unless job
        return cb [false]
      if job.tp is 'pn'
        db = PnToken
      else if job.tp is 'devId'
        db = DevUniqID
      db.findOne {_id:job._id},{fields:fields},(err,ret)->
        if ret?[userBlkField]
          checks = []
          return cb [true,job.tp+' (only one type will be disp here)']
        else
          doOne()
    doOne()

  @findByCrmId:(crmid,{projection},cb)->
    q = {crmID:{$in:[new ObjectId(crmid)]}}
    _findByQuery q,{projection},cb

  @findEmlByAppleId:(appleId,cb)->
    _findByQuery {appleId:appleId},{projection:{eml:1}},cb

  @findByTskDnAndId:(id,tskDn,cb)->
    opt =
      Coll : UserProfile
      projection:{_id:1} #只需要知道是否有该字段
    q =
      _id:id
      tskDn:tskDn
    _findByQuery q,opt,cb

  @findByUid:(id,{projection},cb)->
    q = User.getMongoIDQuery id
    _findByQuery q,{projection},cb

  @findProfileById:(id,{projection},cb)->
    opt =
      Coll : UserProfile
      projection:projection
    User.findById id,opt,cb

  @findProfileByIdAsync =  functionHelper.addAsyncSupport User.findProfileById


  @getList:(q,{projection,sort,limit,skip},cb)->
    _getListByQuery q,{projection,sort,limit,skip},cb

  # op:
  #   t/to: 'all'/uid/email or [uid/email]
  #   locale: language locale string or []
  #   g/group: to group
  #   f/from: name or {uid:,nm:,eml:}
  #   m: message (may html)
  #   l/url:
  #   n: priority default 0
  #   mbl: set to true, if need to send sms, default false
  #   eml: set to true, if need to send email, default false
  # locale, pnNoNews,pnNoNews
  @getPushNotifyUsers:(op)->
    q = {}
    q2 = null
    if op.q and ('object' is typeof op.q)
      for k,v of op.q
        q[k] = v
    if to = op.t or op.to
      if 'string' is typeof to
        if 'all' isnt to
          q.$or = [{_id:to},{eml:to}]
      else if Array.isArray to
        q.$or = [{_id:$in:to},{eml:$in:to}]
    # need pn
    if not q.pn?
      q.pn = $ne:null
    if op.swch
      q2 = {}
      q2[op.swch] = $ne:1
      uids = await UserProfile.findToArray q2,{projection:{_id:1}}
      if uids.length
        uids = (u._id for u in uids)
        q._id = $in:uids
    return UserCol.findToArray q,{projection:{_id:1,pn:1,locale:1,pnBadge:1}}


  @getForumBlockList:(selectedUID,cb)->
    q = {forBlk:true,_id:{$ne:new ObjectId(selectedUID)}}
    User.getList q,{projection:{_id:1,avt:1,fornm:1,forBlk:1}},cb

  @getMemberListByEml:(emails,cb)->
    q = eml:{$in:emails},roles:{$in:['vip','vip_plus','vip_alliance']}
    User.getList q, {porjection:User.MEMBER_FIELDS},cb

  @getFollowingList:(id,cb)->
    User.getList {'flwng.uid':id},{projection:User.FOLLOWING_FIELDS,sort:{'flwng.ts':-1},limit:100},cb

  # realtorVerify
  @getListByName:({uid,name,role},cb)->
    _tryParse = (mbl)->
      try
        if (mbl = mbl.replace(/[^0-9]/g,''))?.length > 0
          if pmbl = parseInt(mbl)
            return pmbl
      catch e
        # do nothing
      return null
    if uid
      query = {_id:uid, tstAc:{$exists:false}}
    else
      q = []
      if (arr = name.split(' '))?.length > 1
        reversedName = arr.reverse().join(' ')
        nmReg1 = helpers.str2reg(name,true)
        nmReg2 = helpers.str2reg(reversedName,true)
        q = [{nm_en:nmReg1},{nm_en:nmReg2},{nm_zh:nmReg1},{nm_zh:nmReg2},{nm:nmReg1},{nm:nmReg2}]
      else
        nameReg = helpers.str2reg(name,true)
        q = [{eml:nameReg},{nm_en:nameReg},{nm_zh:nameReg},{nm:nameReg}, {mbl:nameReg}]
      if num = _tryParse(name)
        q.push {mbl:num}
      query = { $or:q, tstAc:{$exists:false}}
    query.roles = role if role
    fields = { nm:1, nm_zh:1, nm_en:1, _id:1, cpny:1, cpny_zh:1,cpny_en:1,\
      cpny_pstn:1, avt:1, mbl:1, eml:1,splang:1 ,roles:1}
    #appendCityToQuery(query, req)
    sort = {lts:-1}
    User.getList query, {limit:20 ,fields, sort} ,cb

  @getCategoryList:({body,tp,page,limit},cb)->
    skip = (page or 0) * (limit or 20)
    sub = body?.sub
    # q =
    #   'category.id': tp,
    #   tstAc:{$exists:false},
    #   'roles':{$eq:'merchant',$ne:'_dev'}
    #   $or: [
    #     {roles:{$in:['vip_plus','vip','vip_alliance']}}
    #     {$and:[{roles:{$nin:['_dev','vip_plus','vip','vip_alliance']}},{stars:{$gte:4}}]}
    #   ]
    q =
      'category.id': tp,
      'roles':{$all:['merchant','vip_alliance']},
      tstAc:{$exists:false},
      # lts:   {$gt: new Date(Date.now() - (21 * 24 * 3600000))} # within 21 days
      $and:  [{roles:{$in:['vip_plus','vip','vip_alliance'],$not:{$in:['_dev']}}}]
    q['category.sub'] = {$in: sub } if sub?.length
    if prov = cityHelper.getProvAbbrName body?.prov
      q['sas.prov'] = prov
    city = body.city
    qOpt =
      projection: User.CATEGORY_PROVIDER_FIELDS
      sort: {'lts':-1}
      limit: limit
      skip: skip
    getNonVipMerchant = (totalVipCount,vips)->
      # q.lts =  {$gt: new Date(Date.now() - (14 * 24 * 3600000))} # within 7 days
      q.$and = [{roles:{$nin:['_dev','vip_plus','vip','vip_alliance']}}]
      q.roles={$in:['merchant']}
      q.stars={$gte:4}
      if vips.length > 0 #有vip的情况下减去已获取vip个数
        qOpt.limit= (limit - vips.length)
      else
        qOpt.skip= (skip - totalVipCount)
      User.getList q,qOpt,(err,users)->
        if err
          debug.error err
          return cb err
        userlist = vips.concat users
        UserCol.countDocuments q,(err,nonVipCount)->
          libUser.scoreUserList userlist,{merchant:1,city}
          users = userlist.sort libUser.sortUsers
          totalCount = totalVipCount + nonVipCount
          for u in users
            debug.debug u.nm_zh,' => ',u._score
          cb err,users,totalCount
    UserCol.countDocuments q,(err,totalVipCount)->
      # vip已经全部返回, skip=0 for page=0
      if totalVipCount < skip
        return getNonVipMerchant totalVipCount,[]
      User.getList q,qOpt,(err,vips)->
        if err
          debug.error err
          return cb err
        # vips ?= []
        # if vips.length >= limit
        #   return cb null,users,count
        getNonVipMerchant totalVipCount,vips

  @getRcmdProvider:({limit,city,prov,role},cb)->
    q =
      roles: role or 'realtor'
      tstAc: {$exists:false}
      # avt:   {$exists:true}
      $or:[{nm_en: {$exists:true}}, {nm_zh: {$exists:true}}]
      lts:   {$gt: new Date(Date.now() - (21 * 24 * 3600000))} # within 21 days
      $and:  [{roles:{$in:['vip_plus','vip','vip_alliance'],$not:{$in:['_dev']}}}]
    q['sas.city'] = city if city
    qOpt =
      fields: User.RECOMMEND_PROVIDER_FIELDS
      sort: {'vip_mt':-1}
      limit: (limit or 50) + 200
    User.getList q,qOpt,(err,list)->
      if err
        debug.error err,'getRcmdProvider,q:',q
        return cb err
      list?=[]
      sorted = list.sort libUser.sortUsers
      if sorted?.length >= limit
        return cb null,sorted.slice(0,(limit or 50))
      q.lts =  {$gt: new Date(Date.now() - (14 * 24 * 3600000))} # within 7 days
      q.$and = [{roles:{$nin:['_dev','vip_plus','vip','vip_alliance']}}]
      qOpt.sort= {'lts':-1}
      qOpt.limit= (limit - sorted.length)
      User.getList q,qOpt,(err,elist)->
        if err
          debug.error err,'getRcmdProvider,q:',q
          return cb(err)
        elist?=[]
        cb null,sorted.concat(elist)

  @getListByIds:(ids,{isDeveloperMode,Coll,projection,sort,limit,skip},cb)->
    q = {_id:{$in:ids}}
    if isDeveloperMode
      q.roles = '_dev'
    return _getListByQuery q,{Coll,projection,sort,limit,skip},cb if cb
    return await _getListByQuery q,{Coll,projection,sort,limit,skip}

  @getListByIdsAsync:(ids,{projection})->
    return null unless ids
    query = {_id:{$in:ids}}
    return await UserCol.findToArray query, {fields:projection}

  @getListByEmls:(eml,{Coll,projection,sort,limit,skip},cb)->
    _getListByQuery {eml:{$in:eml}},{Coll,projection,sort,limit,skip},cb

  @getAllPn:(cb)->
    UserCol.findToArray {pn:{$exists:true}}, {pn:1},cb

  # [status/realtorVerify] return list of user hasRole role
  @getListByRole:(role,cb)->
    unless role?
      debug.error "getListByRole, Error: Invalid role type:#{role}"
      return cb null,[]
    UserCol.findToArray {roles:role}, {fields:@PUBLIC_FIELDS},cb

  @updateOneById:(id,{update,param = {},Coll},cb)->
    unless isValidId(id)
      debug.error 'updateOneById, Error: Invalid Id type:'+id
    _updateOne {_id:id},{Coll,data:update,param},cb

  @updateOneByEml:(eml,{update,param = {},Coll},cb)->
    _updateOne {eml:eml},{Coll,data:update,param},cb

  @setLastLocation:(id,{loc,LOC_LIMIT},cb)->
    opt =
      Coll : UserProfile
      param:{upsert:true}
      data : {$set:{lastLoc:loc}}
    _updateOne {_id:id},opt,cb
  @getLastLocation:(id)->
    loc = await UserProfile.findOne {_id:id},{lastLoc:1}
    return loc

  @setTskDn:(id,{tskDn},cb)->
    opt =
      Coll : UserProfile
      param:{upsert:true}
    if tskDn
      opt.data = {$addToSet:{tskDn}}
      opt.param?.upsert = true
    else
      opt.data = {$unset:{tskDn:1}}
    _updateOne {_id:id},opt,cb

  # TODO:user_email在线上没有问题时deprecate setEdmNoByEmls
  @setEdmNoByEmls:(emails,reason,cb)->
    if reason
      update = $set:{edmNo:reason}
    else
      update = $unset:{edmNo:1}
    if not cb
      cb = (err,ret)->
        debug.error err,'setEdmNoByEmls,update',update if err
    UserCol.updateMany {eml:{$in:emails}},update,{multi:true},cb

  @setForumBlockById : (id,forBlk,cb)->
    update = $set:{forBlk}
    User.updateOneById id,{update},cb

  @findShowedTerm:(uid,{devType})->
    projection = {}
    field = if ('app' isnt devType) then 'showedTermWeb' else 'showedTerm'
    projection[field] = 1
    profile = await User.findProfileByIdAsync uid,{projection}
    return profile?[field]

  #if showedTerm = false, check user_profile; cb result
  #if showed before in profile, cb result
  # update find user showedTerm
  @updateUserShowedTerm:(uid,{showedTerm,devType},cb)->
    projection = {}
    field = if ('app' isnt devType) then 'showedTermWeb' else 'showedTerm'
    projection[field] = 1
    User.findProfileById uid,{projection},(err, profile)->
      # if not profile
      #   debug.error 'User Exists but Profile not! _id: '+uid
      #   return cb true
      profile ?= {}
      if (not showedTerm) or profile[field]
        return cb profile[field]
      User.setShowedTermById uid,field,(err,ret)->
        if err
          debug.error err,'updateUserShowedTerm,id:',uid
        return cb true

  @setShowedTermById : (id,field,cb)->
    set = {}
    set[field] = true
    opt =
      update : {$set:set}
      param : {upsert:false}
      Coll : UserProfile
    User.updateOneById id,opt,cb

  @findFavoriteMap:(id,cb)->
    opt =
      Coll : UserProfile
    User.findById id,opt,cb

  @setFavoriteGroup : (id,{nm,MAX_GROUPS,clnt,cNm},cb)->
    User.findProfileById id,{}, (err, ret)->
      return gFindError err,{f:'setFavoriteGroup',id},cb if err
      ret ?= {}
      set = {}
      if not ret.favMap?
        set = {'favMap.0':{v:'Default'}}
      # else if ret.favMap? and Object.keys(ret.favMap)?.length > MAX_GROUPS
      #   return gFindError "Max #{MAX_GROUPS} groups",{f:'setFavoriteGroup',info:'groups: '+Object.keys(ret.favMap)?.length},cb
      cntr = parseInt(ret.favMap?.cntr or 0)+1
      time = new Date()
      val = {v:nm.substr(0,20),mt:time,ts:time}
      if clnt
        val.clnt = clnt
      if cNm
        val.cNm = cNm
      set['favMap.'+cntr] = val
      set['favMap.cntr'] = cntr
      opt =
        update :
          $set:set
        Coll : UserProfile
        param :
          returnDocument:'after'
          upsert:true
      User.updateOneById id,opt,cb
  # TODO: remove group in properties db?
  @deleteFavoriteGroup : ({id,nm},cb)->
    User.findProfileById id,{}, (err, ret)->
      return error(err.toString()) if err
      ret ?= {}
      unset = {}
      unset['favMap.'+nm] = 1
      opt =
        update :
          $unset :unset
        Coll : UserProfile
        param :
          returnDocument:'after'
          upsert:false
      User.updateOneById id,opt,cb

  @putFavoriteGroup : ({id,nm,newV,clnt,cNm},cb)->
    # debug.debug '+++++',id,typeof id,id instanceof Auth.ObjectId
    User.findProfileById id,{}, (err, ret)->
      return gFindError err,{f:'putFavoriteGroup',id},cb if err
      ret ?= {}
      set = {}
      val = {v:newV,mt:new Date()}
      if clnt
        val.clnt = clnt
      if cNm
        val.cNm = cNm
      set['favMap.'+nm] = val
      opt =
        update :
          $set:set
        Coll : UserProfile
        param :
          returnDocument:'after'
          upsert:false
      User.updateOneById id,opt,cb

  @pullSavedAddress : (id,{params},cb)->
    addr = {addr: params.addr, ts: new Date()}
    addr.lat = params.lat if params.lat
    addr.lng = params.lng if params.lng
    opt =
      update :$pull:
        savedAddr:
          addr: addr.addr
      Coll : UserProfile
      param : writeConcern : w : 1
    User.updateOneById id,opt,cb

  @addSavedAddress : (id,{params},cb)->
    addr = {addr: params.addr, ts: new Date()}
    addr.lat = params.lat if params.lat
    addr.lng = params.lng if params.lng
    opt =
      update :$push:
        savedAddr:
          $each:[addr]
          $slice: -100
      Coll : UserProfile
      param :
        upsert: true
        writeConcern : w : 1
    User.updateOneById id,opt,cb

  @promote2Market : ({req,uv,user,to},cb)->
    set = {}
    for nm in ['fn','ln','mbl','eml','cpny','ck','cn','itr','qq','wx', 'web', 'tl','fnm']
      # use "58.fn" to update, because other program may change "58.uts" at the same time
      set["#{to}.#{nm}"] = uv[nm] if uv[nm]
    set["#{to}.mt"] = new Date() #update mt
    debug.error 'promote2Market: no user' unless user?._id
    User.updateOneById user._id, {update:{$set:set}}, (err, ret)->
      #update user
      if err
        debug.error err,'promote2Market,id:',user._id
        return cb err
      update2 = {}
      for fld in ['fn','ln','mbl','cpny','itr', 'qq', 'wx', 'web']
        unless user[fld]
          update2[fld] = uv[fld]
      User.updateAndChangeSession {req,data:update2,user},cb

  @updateEdmOptions : (id, {update},cb)->
    set={}
    unset={}
    data={}
    for k,val of update
      if val is 0
        unset[k] = 1
      else if /edm/.test(k)
        set[k] = 1
      else
        continue
    if Object.keys(set)?.length > 0
      data.$set = set
    if Object.keys(unset)?.length > 0
      data.$unset = unset
    UserProfile.updateOne {_id:id},data,{upsert:true},cb
  #TODO: turn of all 的時候timeout
  @updateSubscription : ({isExuser,body={},id},cb)->
    update = {}
    for i in User.NOTIFICATION_EMAIL_FIELDS
      if body.colseAll
        update[i] = 1
      else
        v = body[i]
        unless (v is undefined) or (v is null)
          update[i] = if v then 1 else 0 #don't unset this fields, because we want to know who clicked the unsub
    opt =
      uid: new ObjectId(id),
      tp: 'editprofile'
      edmOption:update
    opt.edmOption.ts = new Date()
    profileUpdateSavedSearch=(callback)->
      User.findProfileById id,{projection:{savedSearch:1}},(err, profile)->
        return callback err if err
        return callback null unless profile?.savedSearch
        for p in profile.savedSearch
          p.sbscb = false
        UserProfile.updateOne {_id:id},{$set:{savedSearch:profile.savedSearch}},(err)->
          return callback err if err
          return callback null
    User.logHistory opt,(err)->
      if err
        return cb err
      if isExuser is 'true'
        UsersExtra.updateOne {_id:id}, update, (err,nUser)->
          return gFindError err,{f:'updateSubscription UsersExtra.updateOne',id},cb if err
          return cb null unless body.colseAll
          profileUpdateSavedSearch (err)->
            return gFindError err,{f:'updateSubscription UsersExtra profileUpdateSavedSearch',id},cb if err
            cb null
      else
        User.updateEdmOptions id,{update},(err,nUser)->
          return gFindError err,{f:'updateSubscription updateEdmOptions',id},cb if err
          return cb null unless body.colseAll
          profileUpdateSavedSearch (err)->
            return gFindError err,{f:'updateSubscription profileUpdateSavedSearch',id},cb if err
            cb null
  # [transaction] inc user balance after add transaction
  @incBalance: (eml,{balance,amount},cb)->
    update = {$inc:{balance:amount}}
    User.updateOneByEml eml,{update},cb

# ========== 01_user.coffee
  # @updateByIds:(id,{update,opts={}},cb)->
  #   UserCol.updateOne id,update,opts,(err,user)->
  #     if err then debug.error err
  #     cb user
  # dlpage forum propdetail rm2propdetail
  # userIDQuery
  @getMongoIDQuery:(id,cb)->
    if id instanceof Auth.ObjectId
      return {_id:id}
    if ('string' is typeof id) and /^[a-f0-9]{24}$/.test id
      return {$or:[{_id:new ObjectId(id)},{id:id}]}
    return {id:id}

  # 03_msg oauth/index chat settings webRM/edm
  # webRM/propserver webrm/wecard
  # getUserEmail
  @getEmail:(user)->
    return null unless user?.eml
    eml = if Array.isArray(user.eml) then user.eml[0] else user.eml
    eml = eml.toString().toLowerCase()
    return eml

  # [htmltoimg wesite]
  # 替换字段供前端使用 (解释隐性字段)
  # processUserInfo
  @getProcessedInfo:({lang, user, noSas})->
    return {} unless user
    # always create an copy. Don't change original object, when we are not sure if we shall change it
    rUser = Object.assign {},user
    rUser.vip = 1 if User.accessAllowed('vipUser',user)
    rUser.realtor = 1 if libUser.hasRole('realtor',user)
    rUser.evtAdmin = 1 if User.accessAllowed('eventAdmin',user)
    delete rUser.roles
    delete rUser.sas if noSas
    rUser.fnm = libUser.fullNameOrNickname(lang,user)
    if firstLastName = libUser.getFirstLastName(rUser.fnm)
      rUser.fn = firstLastName.fn
      rUser.ln = firstLastName.ln
    rUser.cpny = libUser.userCpny(lang,user)
    rUser.eml = user.eml[0] if Array.isArray user.eml
    for i in ['avt','qrcd','grpqrcd']
      rUser[i] = libPropertyImage.replaceRM2REImagePath rUser[i] if rUser[i]
    for i in ['web', 'wurl', 'fburl', 'twturl']
      if (url = user[i]) and (not /^http/.test url)
        rUser[i] = 'http://' + user[i]
    rUser

  # 外部未使用
  @sendPN:({user,msg,url},cb)->
    unless user?.pn?.length > 8
      return gFindError MSG_STRINGS.NO_PUSH_NOTIFY_ID,{f:'sendPN',eml:user?.eml,id:user?._id,info:'pn: '+user?.pn},cb
    pn =
      to: user.pn
      from: 'RealMaster' #TODO: translate
      msg: msg
    if url
      pn.url = url
    # debug.log pn
    try
      response = await pushNotifyModel.send pn
      debug.info 'pushNotify sendPN',response
    catch error
      return gFindError err,{f:'sendPN pushNotify',info:'pn: '+user.pn},cb if err
    cb()

  # 外部未使用
  @_sendPNorSMS:(user,msg,cb)->
    if user.pn
      # send notification
      User.sendPN {user,msg},cb
    else if user.mbl
      sendSMS user,msg,cb
    else
      cb new Error(MSG_STRINGS.NO_METHOD_TO_SEND)


  # [sysadmin usercrm forum yellowpage search realtorverify
  # rm0mylisting hcat settings userfile x_index/index callback58]
  # Update one or multiple field of a user
  # updateUser
  @updateAndChangeSessionAsync =  functionHelper.addAsyncSupport User.updateAndChangeSession
  @updateAndChangeSession:({req,user,data},cb)->
    if not user? # When user not provided seperately
      user = req.session.get 'user'
    return gFindError MSG_STRINGS.USER_NOT_FOUND,{f:'updateAndChangeSession no user'},cb unless user
    _updateUser = ->
      delete data._id
      delete data.eml
      delete data.src
      delete data.site # reserved keywords
      delete data.lts
      delete data.id
      unset = {}
      # for key of data
      #   if data.hasOwnProperty(key)
      #  data[key] = data[key].replace(/\'/g," ")   #for '
      #  data[key] = data[key].replace(/\"/g," ")   #for "
      #  data[key] = data[key].replace(/\{/g," ")   #for {
      #  data[key] = data[key].replace(/\}/g," ")   #for }
      update = {}
      profile = {}
      for key,val of data
        if key in ['$inc', '$push','$addToSet','$pull']
          update[key] = val
          delete data[key]
          delete val['roles'] if key is '$push'
          continue
        if val or val is 0 # has something to set
          # id:'' will delete id
          # if user changes id field,
          # 1. check if user already changed, if changed ignore, and disable that in settings
          # 2. check for conflicts, if conflicts, send error
          # 3. modify all id field in wecard collection?
          if key is 'id'
            unless ('string' is typeof val) and (/^[a-zA-Z0-9\-]+$/.test val)
              return gFindError new Error('Wecard ID: Only a-z A-Z and -'),{f:'updateAndChangeSession'},cb
            if val?.length  < 5
              return gFindError new Error('ID too short, at leaset 5 chars!'),{f:'updateAndChangeSession'},cb
          if 'string' is typeof val
            data[key] = val.replace(/\</g,'&lt;').replace(/\>/g,'&gt;')
          if /^\$/.test key
            return gFindError new Error("Bad key #{key}"),{f:'updateAndChangeSession'},cb
          if key in ['verify','mblvts','pnMLSM','pnMLSE','pnLM','pnNews',\
          'pnWPRpt','edmNoProperty','edmNoCommunity','edmNoForum',\
          'edmNoProject','edmNoStat','edmNoSearch','edmNoSearch','edmNoLocation','edmNoAssignment','edmNoEvaluation','edmNoExlisting',\
          'edmNoNews','58ui','58token','58rtkn','58exp']
            profile[key] = val
            delete data[key]
        else
          if key is 'id'
            return gFindError new Error('ID too short, at leaset 5 chars!'),{f:'updateAndChangeSession'},cb
          delete data[key]
          unset[key] = 1
      if Object.keys(data)?.length > 0
        update.$set = data
      if Object.keys(unset)?.length > 0
        update.$unset = unset
      if Object.keys(update)?.length = 0 and Object.keys(profile)?.length = 0
        return cb null,user
      # debug.debug 'session and col update is',update
      UserCol.findOneAndUpdate {_id:user._id},update ,{returnDocument:'after',upsert:false},(err,r)->
        if err
          if err.code is 11000
            return gFindError MSG_STRINGS.ID_BEEN_USED,{f:'updateAndChangeSession findOneAndUpdate',id:user._id},cb
          else
            return gFindError err,{f:'updateAndChangeSession findOneAndUpdate',id:user._id},cb
        _changeSession = ->
          nuser = r.value
          if user is req.session.get('user') # update current user
            req.session.set 'user',nuser,->
              # tmpKey = Object.keys(data)[0]
              # debug.debug '+++++session user is set',update,nuser[tmpKey],req.session.get('user')[tmpKey],req.session.id()
              cb null,nuser
          else
            cb null,nuser
        if Object.keys(profile)?.length > 0
          UserProfile.updateOne {_id:user._id},{$set:profile},{upsert:true,returnDocument:'after'},_changeSession
        else
          _changeSession()
    if data.roles
      # delete data.roles
      roles = data.roles
      delete data.roles
      User.updateRoles {req,user,data:roles},(err,u2)->
        return gFindError err,{f:'updateAndChangeSession updateRoles',id:user._id,eml:user.eml},cb if err
        user = u2
        _updateUser()
    else
      _updateUser()

  # [sysadmin]
  # updateUserSetRoles
  @updateRolesAsync =  functionHelper.addAsyncSupport User.updateRoles
  @updateRoles:({req,user,data},cb)->
    if not user?
      user = req.session.get('user')
    roles = data
    return gFindError MSG_STRINGS.USER_NOT_FOUND,{f:'updateRoles',id:user._id,eml:user.eml},cb unless user
    user.roles ?= []
    return cb null,user unless roles?
    # not work if roles is string
    if (Array.isArray roles) or (not 'object' is typeof roles)
      roles = {add:roles}
    changed = false
    update = $set:{}
    updateVipmt = (r)-> update.$set.vip_mt = new Date() if /vip/i.test r

    if roles.add
      # debug.log roles.add
      if Array.isArray(roles.add)
        for r in roles.add
          if user.roles.indexOf(r.toString()) < 0
            user.roles.push r.toString()
            changed = true
            updateVipmt r
      else if 'string' is typeof roles.add
        if user.roles.indexOf(roles.add) < 0
          user.roles.push roles.add
          changed = true
          updateVipmt roles.add
      else
        return gFindError MSG_STRINGS.BAD_PARAMETER,{f:'updateRoles',id:user._id,eml:user.eml,info:'add'},cb
    if roles.del
      if Array.isArray(roles.del)
        for r in roles.del
          if (p = user.roles.indexOf r.toString()) >= 0
            user.roles.splice p,1
            changed = true
      else if 'string' is typeof roles.del
        if (roles.del is 'all')
          user.roles = []
          changed = true
        else if (p = user.roles.indexOf roles.del) >= 0
          user.roles.splice p,1
          changed = true
      else
        return gFindError MSG_STRINGS.BAD_PARAMETER,{f:'updateRoles',id:user._id,eml:user.eml,info:'delete'},cb
    return cb null,user unless changed
    update.$set.roles = user.roles
    if user.flwng? and (user.roles.indexOf('realtor') >= 0) # when realtor, unset flwng
      update.$unset = {flwng:1}
    UserCol.findOneAndUpdate {_id:user._id},update ,{upsert:false,returnDocument:'after'},(err,r)->
      if err
        return gFindError err,{f:'updateRoles findOneAndUpdate',id:user._id,eml:user.eml},cb
      nuser = r.value
      if user is req.session.get 'user'
        req.session.set 'user',nuser,->
          cb null,nuser
      else
        cb null,nuser

  # [realtorverify userpost]
  # need to add support when no user in session
  # followUser
  @followSomeone : ({req,user,buzUserID},cb)->
    # add to flwng
    #unless user = req.session.get('user')
    #  return gFindError new Error "No User"

    unless buzUserID
      return gFindError MSG_STRINGS.NO_FOLLOWED_USER,{f:'followSomeone'},cb
    q = {id:buzUserID}
    if ('string' is typeof buzUserID) and (/^[a-f0-9]{24}$/.test buzUserID)
      q = {_id:buzUserID}
    UserCol.findOneAndUpdate q,{$inc:{rmb:-30,wsc:-10}},(err,buzU)->
      # change buz rmb
      return gFindError MSG_STRINGS.USER_NOT_FOUND,{f:'followSomeone',id:user._id,eml:user.eml,info:buzU},cb unless buzU = buzU.value
      tmp = {}
      tmp.uid = buzU._id.toString()
      tmp.ts = new Date()
      if flwng = user.flwng
        flwng.push tmp
        update = flwng:flwng
      else
        update = flwng:[tmp]
      User._sendPNorSMS buzU,"#{user.nm} followed you.",(err)->
        if err then debug.error err,'followSomeone,id:',user._id,'buzUserID',buzUserID
      User.updateAndChangeSession {req,user,data:update},cb

  # forum 03_propfunctions propdetail realtorverify
  # [rm2propdetail wecard x_index/index]
  # getUserPublicInfo/getUserPrivateInfo
  # NOTE:  dont use return and cb at same time
  @findPublicInfo : ({lang,
      user,
      id,
      noSas,
      roles}, cb)->
    if user?._id
      ret = {}
      for k, v of @PUBLIC_FIELDS
        ret[k] = user[k]
      # opt = getProcessUserInfoOpt req
      ret = User.getProcessedInfo {lang, user:ret}
      return cb null,ret
    if id
      q = User.getMongoIDQuery id
    else
      return cb null,{}
    if roles
      q.roles = roles
    UserCol.findOne q, {fields:@PUBLIC_FIELDS}, (err,user)->
      return gFindError err.toString(),{f:'findPublicInfo',info:util.inspect q},cb if err
      return cb(null,{}) unless user
      # user ?= {}
      # opt = getProcessUserInfoOpt opt,user
      user = User.getProcessedInfo {lang, user, noSas}
      return cb(null, user)
      
  @findPublicInfoAsync : ({lang,
    user,
    id,
    noSas,
  roles}, cb)->
    if user?._id
      ret = {}
      for k, v of @PUBLIC_FIELDS
        ret[k] = user[k]
      # opt = getProcessUserInfoOpt req
      ret = User.getProcessedInfo {lang, user:ret}
      return ret
    if id
      q = User.getMongoIDQuery id
    else
      return {}
    if roles
      q.roles = roles
    user = await UserCol.findOne q, {fields:@PUBLIC_FIELDS}
    return {} unless user
    user = User.getProcessedInfo {lang, user, noSas}
    return user

  # [x_apishare]
  # updateUserActivity
  @incActivity : ({req, user, action},cb)->
    switch action
      when 'share'
        actpts = 1
        wsc = 1
        rmb = 5
      when 'newBlog'
        actpts = 1
        wsc = 1
        rmb = 0
      when 'follow'
        actpts = 1
        rmb = 0
      when 'favNews'
        actpts = 1
        rmb = 0
      when 'favProp'
        actpts = 1
        rmb = 0
      when 'sponsed'
        actpts = 2
        rmb = 0
      else
        return cb null
    userUpdate = {$inc:{actpts:actpts, rmb:rmb, wsc:wsc}}
    User.updateAndChangeSession {req,user,data:userUpdate},cb

  #download in img server
  # authThirdParty,bindWechat,getWechatAuth登录/注册时添加用户头像
  # uploadUserAvt
  @uploadAvatar :({req,user},cb)->
    unless user?._id
      debug.info 'No user yet'
      return cb()
    if /realmaster/.test user.avt
      return cb()
    User.reserveUserFile {user,fileNum:0},(err,nUserFile,fileNames)->
      if err
        req.logger?.log err
        return cb()
      db = {Users:UserCol,UserFile}
      postThirdPartyImg user,nUserFile,db,(err,nuser)->
        if err
          req.logger?.log err
          return cb()
        # NOTE: minor error, 404, 403 instead of db error
        if 'string' is typeof nuser #and/Error/.test nuser
          debug.error nuser,'uploadAvatar postThirdPartyImg,id',user._id
          return cb()
        # updateUser req,user,{avt:nuser.avt},(err,nuser)->
        # user.avt has updated, update session user avt
        if (suser = req.session.get('user')) and (suser._id?.toString() is nuser?._id?.toString())
          return libUser.updateProfileStars {User:UserCol,Login:Auth,id:user._id},(err,stars) ->
            nuser.stars = stars
            req.session.set 'user',nuser,()->
              return cb()
          # debug.log 'updated\t'+user.eml+'\t'+nuser.avt
        cb()

  # ---------------- wechet 部分
  # [wechatauth scheme]
  # wechatGetUserInfo
  # NOTE: max timeout 40s, may exceed server timeout
  @getInfoFromWechat : ({code,appId,secret},cb)->
    url = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=#{appId}&secret=#{secret}&code=#{code}&grant_type=authorization_code"
    opt =
      protocol: 'https'
      timeout: 20000
    debug.verbose "wechatGetUserInfo: start #{new Date()}"
    helpers.http_get url,opt,(err,ret)->
      debug.verbose "wechatGetUserInfo: got token #{new Date()}"
      return gFindError err,{f:'getInfoFromWechat start'},cb if err
      try
        json = JSON.parse ret
      catch e
        debug.error 'getInfoFromWechat Error WeChat Return:',ret
        return cb e #resp.redirect '/wechatauth/web?err=2'
      if json.errcode
        debug.error "getInfoFromWechat Error:",json.errmsg
        return cb json #resp.redirect '/wechatauth/web?err=3'
      url = "https://api.weixin.qq.com/sns/userinfo?access_token=#{json.access_token}&openid=#{json.openid}"
      debug.verbose "wechatGetUserInfo: to get userinfo #{new Date()}"
      # TODO: timeouit error
      # ERROR:cmate3.coffee,model/010_user.coffee,2021-12-03T13:26:11.654
      # model/010_user.coffee gFindError 404 Error: Timeout
      helpers.http_get url,opt,(err,ret)->
        debug.verbose "wechatGetUserInfo: got userinfo #{new Date()}"
        return gFindError err,{f:'getInfoFromWechat got userinfo'},cb if err #resp.redirect '/wechatauth/web?err=11'
        try
          json = JSON.parse ret
        catch e
          debug.error "getInfoFromWechat Error WeChat Return: ",ret
          return cb e #resp.redirect '/wechatauth/web?err=12'
        if json.errcode
          debug.error "getInfoFromWechat Error:",json.errmsg
          return cb json #resp.redirect '/wechatauth/web?err=13'
        debug.info json
        cb null,json

  # [wechatauth]
  # wechatEnsureWechatUser
  @ensureInfoFromWechat: ({wxuser},cb)->
    ###
    { openid: 'oyQyduJxHQfAPvzAwpFdzloQyM1k',
    nickname: 'Nick Name ',
    sex: 1,
    language: 'zh_CN',
    city: '',
    province: '',
    country: 'CA',
    headimgurl: 'http://wx.qlogo.cn/mmopen/sTJptKvBQLIrGdo2tvPEWERgPay8NUicRa8mAniby58Mp0OILtRheK8nGPbKjrZweqxjzaNOf9UVhkvz9Eib8icMXlkuVB0Cmv3j/0',
    privilege: [],
    unionid: 'oSWWZt_Yua6F68JgIS5J9j8hL5hA---' }
    ###
    return gFindError MSG_STRINGS.NO_WECHAT_USER_ID,{f:'ensureInfoFromWechat'},cb unless wxuser?.unionid or wxuser?.openid
    query =
      $or:[
        {wxAppOID:wxuser.openid},
        {wxMobOID:wxuser.openid},
        {wxuid:wxuser.unionid}
      ]
    UserCol.findOne query,(err,user)->
    # UserCol.findOne {wxuid:wxuser.unionid},(err,user)->
      if err then debug.error err,'ensureInfoFromWechat, wxuid',wxuser.unionid
      if user?.pn # has user already
        return User.sendPN {user,msg:MSG_STRINGS.WECHAT_AUTH_PERFORMED},cb
      # insert into wechat_user
      WechatUser.updateOne {_id:wxuser.unionid},{$set:wxuser},{upsert:true},cb

  # [scheme wechatauth]
  # can only bind current user
  # 使用wechatApp.AppID
  # wechatAuthUser
  @getWechatAuth : ({req,wxuser},cb)->
    funcName = 'getWechatAuth'
    return gFindError MSG_STRINGS.NO_WECHAT_USER_ID,{f:funcName},cb unless wxuser?.unionid or wxuser?.openid
    # user logged in, bind wechat account
    if user = req?.session.get('user')
      # debug.log '+++++session user, return bind'
      # wxuser添加移动应用OpenId判断
      wxuser.IsMob = true
      return User.bindWechat {req,user,wxuser},cb
    # not logged in
    query =
      $or:[
        {wxAppOID:wxuser.openid},
        {wxMobOID:wxuser.openid},
        {wxuid:wxuser.unionid}
      ]
    UserCol.findOne query,(err,user)->
    # UserCol.findOne {wxuid:wxuser.unionid},(err,user)->
      return gFindError err,{f:funcName,info:'wxuser.unionid: '+wxuser.unionid},cb if err
      # debug.log '+++++dbuser',user
      # if not user
      #   debug.error 'no user find'
      if user and wxuser.headimgurl?.length > 10
        user.wxavt = wxuser.headimgurl
        if (not user?.avt)
          user.avt = wxuser.headimgurl
      User.uploadAvatar {req,user},->
        if eml = user?.eml
          # has user, try login
          eml = eml[0] if Array.isArray eml
          Auth.findOne {_id:eml.toLowerCase()},{fields:{emlV:1},readPreference:Auth.ReadPreferencePrimary},(err,authRet)->
            return gFindError err,{f:funcName+' isEmailVerified',eml},cb if err
            return cb null,{eml,extraInfo:MSG_STRINGS.EMAIL_VERIFY} if false is authRet.emlV
            return User.loginOnlyById {req,eml,fromMethod:'getWechatAuth'},(err,nuser,login)->
              return gFindError err,{f:funcName+' loginOnlyById',eml},cb if err
              User.appAfterLogin {req,user:nuser,eml},(err)->
                return gFindError err,{f:funcName+' appAfterLogin',eml},cb if err
                return cb null,nuser if user.wxuid is wxuser.unionid
                User.updateAndChangeSession {req,user:nuser,data:{wxuid:wxuser.unionid}},(err)->
                  return gFindError err,{f:funcName+' update wxuser.unionid',eml},cb if err
                  return cb null,nuser
        # no user, ensureWechatUser
        else
          WechatUser.findOneAndUpdate {_id:wxuser.unionid},{$set:wxuser},{upsert:true,returnDocument:'after'},(err,ret)->
            # ask login or input email/phone
            return gFindError err,{f:funcName+' findOneAndUpdate',info:'WechatUser: '+wxuser.unionid},cb if err
            nwxuser = ret?.value
            cb null,{needUser:true,wxuser:nwxuser}

  @formatInfoFromWechat :(user,wxuser)->
    data = {}
    # 添加AppID，OpenID
    if wxuser.IsMob
      data.wxMobOID = wxuser.openid
    if wxuser.IsApp
      data.wxAppOID = wxuser.openid
    # avtor
    if wxuser.headimgurl?.length > 10
      data.wxavt = wxuser.headimgurl
      if (not user?.avt)
        data.avt = wxuser.headimgurl
    # unionid
    data.wxuid = wxuser.unionid
    # nickname
    if (not user?.fn) and (not user?.ln) # user probably not setup names yet
      data.nm = wxuser.nickname
    else
      data.wxnm = wxuser.nickname
    # sex
    if not user?.sex?
      switch wxuser.sex
        when 1 then data.sex = 'Male'
        when 2 then data.sex = 'Female'
        else data.sex = 'None'
    # lang
    if (not user?.locale?) and wxuser.lang
      data.locale = wxuser.lang.toLowerCase().replace('_','-')
    return data

  # 外部未使用
  # wechatBindUser
  @bindWechat : ({req,user,wxuser,notDeleteWechat},cb)->
    data = User.formatInfoFromWechat user,wxuser
    query =
      $or:[
        {wxAppOID:wxuser.openid},
        {wxMobOID:wxuser.openid},
        {wxuid:wxuser.unionid}
      ]
    UserCol.updateMany query,{$unset:{wxuid:1,wxAppOID:1,wxMobOID:1}},{multi:1},(err)->
    # UserCol.updateMany {wxuid:wxuser.unionid},{$unset:{wxuid:1}},{multi:1},(err)->
      return gFindError err,{f:'bindWechat',eml:user.eml,id:user._id},cb if err
      User.updateAndChangeSession {req,user,data},(err,nuser)->
        return gFindError err,{f:'bindWechat',eml:user.eml,id:user._id},cb if err
        # flwng
        User.uploadAvatar {req,user:nuser}, ->
          if wxuser.flwng
            User.followSomeone {req,user:nuser,buzUserID:wxuser.flwng},(err,nnuser)->
              cb err,nnuser
          else
            cb null,nuser
          return if wxuser.isFromMiniApp
          # User._sendPNorSMS user,"Bound with Wechat Acnt #{wxuser.nickname}",->  20221226 allen 提出不需要发短信
          return if notDeleteWechat
          WechatUser.deleteMany {_id:wxuser._id},(err)->
            debug.error err,'wechatUser remove ',wxuser._id if err

  @bindWechatWithoutLogin : ({req,eml,wxuid},cb)->
    fnName='bindWechatWithoutLogin'
    UserCol.findOne {eml:eml},{readPreference:UserCol.ReadPreferencePrimary},(err,user)->
      return gFindError err,{f:fnName,info:'wechatUid: '+wxuid},cb if err
      if not user? then return cb new Error("Unknown user eml #{eml}")
      WechatUser.findOne {_id:wxuid},{readPreference:UserCol.ReadPreferencePrimary},(err,wxuser)->
        return gFindError err,{f:fnName,info:'wechatUid: '+wxuid},cb if err
        if not wxuser? then return cb new Error("Unknown Wechat user id #{wxuid}")
        wxuser.IsMob = true
        User.bindWechat {req,user,wxuser,notDeleteWechat:true},cb

  # revoke user wehcat
  # revokeWechat
  @revokeWechat : ({OpenID,IsMob},cb)->
    q = {}
    update = {}
    if IsMob
      # 移动应用授权撤回
      q.wxMobOID = OpenID
      update['$unset'] = {wxMobOID:1}
    else
      # 微信应用撤回授权
      q.wxAppOID = OpenID
      update['$unset'] = {wxAppOID:1}
    UserCol.findOne q,(err,user)->
      return gFindError err,{f:'revokeWechat-findUser',info:'OpenID: '+OpenID},cb if err
      # 如果用户先在app中删除账户,然后在微信取消授权时会找不到user
      if not user?
        debug.warn 'revokeWechat no user, query:',q
        return cb null
      unless user?.wxMobOID and user?.wxAppOID
        update['$set'] = {hasWechat:0}
        update['$unset'].wxuid = 1
      UserCol.updateOne q,update,(err)->
        return gFindError err,{f:'revokeWechat',info:'OpenID: '+OpenID},cb if err
        debug.info 'Revoke wechat by',user.eml,user._id
        AdminOpLogs.insertOne {act:'revoke-wechat', ts:new Date(),uid:user._id,target:user.eml,op:update}
        return cb null


  # [userpost]
  # create/update a user
  # 使用wechatAppId
  # wechatBindDBUser
  @bindWechatNeedLogin : ({req,wxuid},cb)->
    WechatUser.findOne {_id:wxuid},{readPreference:UserCol.ReadPreferencePrimary},(err,wxuser)->
      return gFindError err,{f:'bindWechatNeedLogin',info:'wechatUid: '+wxuid},cb if err
      if not wxuser? then return cb new Error("Unknown Wechat user id #{wxuid}")
      if user = req.session.get('user')
        debug.info wxuser
        # wxuser添加OpenId判断
        wxuser.IsApp = true
        return User.bindWechat {req,user,wxuser},cb
      else
        return gFindError MSG_STRINGS.NEED_LOGIN,{f:'bindWechatNeedLogin'},cb

  # [callback58]
  # wbGetUserInfo
  @getInfoFrom58 : ({code,redirect_uri,wbClientId,wbSecret},cb)->
    #url = "http://openapi.58.com/oauth2/access_token"
    unless code and redirect_uri and wbClientId and wbSecret
      return gFindError MSG_STRINGS.NO_ENOUGH_PARAMETERS,{f:'getInfoFrom58'},cb
    opt =
      protocol: 'http'
      host: 'openapi.58.com'
      path: '/oauth2/access_token'
      timeout: 20000
    data =
      code: code
      grant_type: 'authorization_code'
      redirect_uri: redirect_uri
      time_sign: time_sign or Date.now()
      client_id: wbClientId
    data.client_secret = crypto.createHash('md5').update(param.wbSecret + 'openapi.58.com' + data.time_sign).digest('hex')
    debug.info data
    helpers.http_post opt,data,(err,ret)->
      return gFindError err,{f:'getInfoFrom58'},cb if err
      try
        json = JSON.parse ret
      catch e
        debug.error "getInfoFrom58 Error 58 Return:#{ret}"
        return cb e #resp.redirect '/wechatauth/web?err=2'
      if json.errcode
        debug.error "getInfoFrom58 Error: #{json.errmsg}"
        return cb json #resp.redirect '/wechatauth/web?err=3'
      cb null,json

  # [admin  sysadmin]
  @switchUser: ({req,newUser},cb)->
    ###
    switch current user to another user account.
    Without newUser, account will switch back to original user
    callback: {ok:0/1,err:error message}
    ###
    newUser = null if newUser is undefined
    # check user/realUser
    return cb {ok:0,err:'Unknown User'} unless user = (req.session.get('realUser') or req.session.get('user'))
    return cb {ok:0,err:'No Auth'} unless User.accessAllowed 'userAdmin',user
    # check if return to original user
    if not newUser?
      if not (realUser = req.session.get('realUser'))
        return cb {ok:0, err: 'No Real User'}
      req.session.set 'user',realUser,->
        req.session.set 'realUser',null,->
          cb {ok:1}
      return null
    # find newUser
    q = {eml:newUser}
    if ('string' isnt typeof newUser) or ((not helpers.isEmail(newUser)) and (helpers.isObjectIDString(newUser?.toString())))
      q = {_id:newUser}
    UserCol.findOne q,(err,nUser)->
      if err
        debug.error err,'switchUser',q
        return cb {ok:0,err:err}
      return cb {ok:0,err:MSG_STRINGS.USER_NOT_FOUND} unless nUser
      if nUser.roles?.indexOf('_admin') >= 0
        return cb {ok:1,err:MSG_STRINGS.NO_AUTHORIZED}
      # do not overwrite realUser
      unless req.session.get('realUser')
        req.session.set('realUser',user)
      req.session.set 'user',nUser,->
        cb {ok:1}

  # ---------------- Third Party 部分

  # [oauth]
  # authThirdPartyUser
  @authThirdParty: ({req, uData,isThirdPartyAuth}, cb) ->
    unless uData
      errMsg = req.l10n MSG_STRINGS.BAD_PARAMETER
      debug.error errMsg,'authThirdParty'
      return cb errMsg,null,{}
    checkByThirdId = (uData)->
      return unless uData.uTp
      query = {}
      switch uData.uTp
        when 'google'
          query.googleId = uData.id
        when 'facebook'
          query.facebookId = uData.id
        when 'apple'
          query.appleId = uData.id
        else # must have uTp
          return
      if not uData.id # must have id, not empty or null
        return
      thirdIdUser = await UserCol.findOne query
      if not thirdIdUser
        return
      eml = User.getEmail thirdIdUser
      authRet = await Auth.findOne {_id:eml},{fields:{emlV:1},readPreference:Auth.ReadPreferencePrimary}
      return {thirdIdUser,eml,authRet}

    email = uData.email?.toLowerCase()
    unless email and helpers.isEmail(email)
    # keep 有可能是第三方问题
      # 判断是否己经有id验证
      try
        ret = await checkByThirdId uData
      catch err
        debug.error err, 'uData:',uData
        return cb MSG_STRINGS.DB_ERROR, null,{}
      if not ret?.thirdIdUser?._id?
        errMsg = MSG_STRINGS.EMAIL_MISSING
        debug.warn errMsg, 'authThirdParty',uData,'isThirdPartyAuth',isThirdPartyAuth,'会要求用户提供email绑定'
        return cb errMsg, null,{}
      return cb MSG_STRINGS.EMAIL_VERIFY,{eml:ret.eml},{} if false is ret?.authRet?.emlV
      return User.oauthLoginUser {req, uData:{eml:ret.eml},uid:ret.thirdIdUser._id}, cb

    User.hasLoginId email, (err, existingUser) ->
      data = libOauth.genUserFromThirdPartyObj uData
      unless data
        errMsg = req.l10n MSG_STRINGS.BAD_PARAMETER
        debug.error errMsg,'auth_has_login',email
        return cb errMsg,null,{}
      # debug.debug 'existingUser',existingUser
      unless existingUser
        if isThirdPartyAuth
          # email用户不存在时，查找thirdId用户
          try
            ret = await checkByThirdId uData
          catch err
            debug.error err, 'uData:',uData
            return cb MSG_STRINGS.DB_ERROR, null,{}
          if ret?.thirdIdUser
            # thirdId获取的用户email与user.email不一致,可能为proxy email
            return cb MSG_STRINGS.EMAIL_VERIFY,{eml:ret.eml},{} if false is ret?.authRet?.emlV
            return User.oauthLoginUser {req, uData:{eml:ret.eml},uid:ret.thirdIdUser._id}, cb
          else if isProxyEmail email
            # email用户与thirdId用户都不存在，第三方获取邮箱为proxy email，需要用户重新填写正式邮箱
            errMsg = MSG_STRINGS.EMAIL_MISSING
            debug.error 'proxy email can not register,authThirdParty',uData,'email:',email
            return cb errMsg, null,{}
          else
            # no user and not proxy email, create one; and create login, signin automatically
            return User.oauthCreateUser {req, uData:data,email,isThirdPartyAuth}, cb
        else
          return cb(MSG_STRINGS.USER_NOT_FOUND,null,{})
      if not existingUser?.emlV
        try
          # verified and reset password
          # 存在用户但未验证,第三方注册时需要重置密码并发送邮件
          pass = User.genPass {seed:new Date(),length:8}
          await User.emailVerified {eml:email,pass}
        catch err
          debug.error err,'authThirdParty, eml:',email
      User.oauthLoginUser {req, uData:data,uid:existingUser.uid, pass}, cb

  # return {err, loginedUser, {sendMail:true/false}}
  @oauthLoginUser: ({req, uData,uid, pass}, cb)->
    unless uData
      errMsg = req.l10n MSG_STRINGS.BAD_PARAMETER
      debug.error 'oauthLoginUser',errMsg
      return cb errMsg,null,{}
    email = uData.eml or uData.email
    UserCol.findOne {_id:new ObjectId(uid)},(err,existingUser)->
      if err
        debug.error err,'oauthLoginUser,uid',uid
        return cb err, null,{}
      unless existingUser
        errMsg = "#{email} #{req.l10n(MSG_STRINGS.USER_NOT_FOUND)}"
        debug.error 'oauthLoginUser',errMsg
        return cb errMsg, null,{}
      update = null
      if (uData.uTp is 'google') and (not existingUser.googleId)
        update = googleId:uData.googleId
      else if (uData.uTp is 'facebook') and (not existingUser.facebookId)
        update = facebookId:uData.facebookId
      else if (uData.uTp is 'apple') and (not existingUser.appleId)
        update = appleId:uData.appleId
      _oauthLoginUser = (req, user, cb) ->
        eml = User.getEmail user
        return User.loginOnlyById {req, eml,fromMethod:'oauthLoginUser:'+uData.uTp+':'+uData.id}, (err, loginedUser) ->
          User.appAfterLogin {req,user,eml},(err)->
            return cb(err,loginedUser)
      # send notification email when user bind google/facebook
      done = (err,updatedUser)->
        _oauthLoginUser req,updatedUser, (err, loginedUser)->
          hasOauthId = (existingUser.googleId or existingUser.facebookId or existingUser.appleId)
          if hasOauthId
            ret = {}
          else if pass
            ret = {pass:pass}
          else
            ret = {sendMail:true}
          return cb err,loginedUser,ret
      unless update
        return done(null,existingUser)
      # bind user with googleId/facebookId
      return User.updateAndChangeSession {req,user:existingUser,data:update},done

  # isThirdPartyAuth:是否第三方注册/登录，第三方注册时邮箱默认验证通过
  @oauthCreateUser: ({req, uData, email,isThirdPartyAuth}, cb) ->
    User.ensureUser {req,data:uData,isThirdPartyAuth},(err,user)->
      if err
        debug.error err,'oauthCreateUser,email',email
        return cb err, null,{}
      done =()->
        # email = getUserEmail user
        User.loginNEnsureLogin {req,eml:email},(err,user,ret)->
          if err
            debug.error err,'oauthCreateUser,email',email
            return cb err,null,{}
          unless ret
            debug.error  "oauthCreateUser, Not able to find login with email: #{email}"
            return cb MSG_STRINGS.USER_NOT_FOUND,null,{}
          {pass} = ret
          User.appAfterLogin {req,user,eml:email},(err)->
            if err
              debug.error err,'oauthCreateUser,email',email
              return cb err, null,{}
            cb null,user,{pass}
      return done() unless user.pic
      User.uploadAvatar {req,user},->
        done()

  # [userpost]
  # ensureUser
  # isThirdPartyAuth:是否第三方注册/登录，第三方注册时邮箱默认验证通过
  @ensureUser:({req,data,isThirdPartyAuth},cb)->
    if data.email
      data.eml = data.email
      delete data.email
    data.eml = data.eml.toLowerCase()
    # NOTE: will replace @ to .   e.g. <EMAIL> -> /aaa.aa.com/i -> /xxxaaa.aa.co/
    # may have conflict users
    regEml = helpers.str2reg(data.eml,false,true)
    UserCol.findOne {eml:regEml},(err,user)->
      return gFindError err,{f:'ensureUser',eml:regEml},cb if err
      if user?._id then return cb null,user
      #in download page user may pass nm in
      unless data.nm
        nickName = data.eml.substring(0, data.eml.indexOf('@'))
        nickName += ''+ Math.floor(Math.random() * (8999 + 1)) + 1
      else
        nickName = data.nm
      createUniqueUserID (err,uid)->
        #todo Debug
        #debug.log '##############' + uid
        return gFindError err,{f:'ensureUser createUniqueUserID',id:uid},cb if err
        src = req.getUA() or 'browser'
        if apsv = req.cookies?.apsv
          src += ":#{apsv}"
        else
          src += ':' + data.src if data.src #data.src = 'download' if from download page
        u = {eml:[data.eml],nm:nickName,src:src,id:uid,ts:new Date()}
        u.wxuid = data.wxid if data.wxid
        u.mbl = data.mbl if data.mbl
        u.fn = data.fn if data.fn
        u.ln = data.ln if data.ln
        if data.fn and data.ln
          if helpers.hasChinese u.fn or helpers.hasChinese u.ln
            u.nm_zh = "#{u.ln}#{u.fn}"
          else
            u.nm_en = "#{u.fn} #{u.ln}"
        #set current weekday for edm group
        u.edmGrp = (new Date()).getDay()
        if locale = req.locale() # save locale for new user
          u.locale = locale
          switch locale
            when 'zh'
              u.splang = ['Cantonese']
            when 'zh-cn'
              u.splang = ['Mandarin']
            when 'en'
              u.splang = ['English']
            when 'kr'
              u.splang = ['korean']
        # 第三方注册，邮箱默认验证通过,绑定第三方id
        if isThirdPartyAuth
          u.emlV = data.eml
          u.googleId = data.googleId if data.googleId
          u.facebookId = data.facebookId if data.facebookId
          u.appleId = data.appleId if data.appleId
        UserCol.insertOne u,{safe:true},(err,ret)->
          # console.log '+++++users:',ret
          _id = ret.insertedId
          u._id = _id
          cb err,u #users?.ops?[0]

  # [userpost]
  @ensureLogin: ({req,data,user},cb)->
    User.hasLoginId data.eml,(err,has)->
      return gFindError err,{f:'ensureLogin',eml:data.eml},cb if err
      if has then return cb null,has
      debug.error 'ensureLogin: no user' unless user?._id
      User.createLogin data.eml,data.password,user._id,{needVerify:1},(err)->
        cb err,false

  @registerUser: (req,data)->
    if data.email
      data.eml = data.email
      delete data.email
    data.eml = data.eml.toLowerCase()
    regEml = helpers.str2reg(data.eml,false,true)

    {transactionOptions,session} = GETTRANSACTION 'chome'
    err = null
    u = null
    hadLogin = null
    try
      session.startTransaction(transactionOptions)

      user = await UserCol.findOne {eml:regEml}
      if user
        debug.warn "register an existing user,email:#{data.eml}"
        u = user
      else
        unless data.nm
          nickName = data.eml.substring(0, data.eml.indexOf('@'))
          nickName += ''+ Math.floor(Math.random() * (8999 + 1)) + 1
        else
          nickName = data.nm
        sysResult = await SysData.findOneAndUpdate {_id:'UserUniqueID'},{$inc:{lastID:1}},{upsert:true,returnDocument:'after',session}
        unless sysResult?.lastErrorObject.updatedExisting
          throw new Error MSG_STRINGS.CANT_GET_USERUNIQUEID_RECORD
          # return cb MSG_STRINGS.CANT_GET_USERUNIQUEID_RECORD
        # TODO:确认使用地方，可以考虑给uuid
        uid = 'U' + helpers.randomCode(7) + sysResult.value.lastID
        src = req.getUA() or 'browser'
        if apsv = req.cookies?.apsv
          src += ":#{apsv}"
        else
          src += ':' + data.src if data.src #data.src = 'download' if from download page
        u = {eml:[data.eml],nm:nickName,src:src,id:uid,ts:new Date()}
        if data.uTp
          uData = libOauth.getLastOauthUserData(req)
          unless uData
            return throw new Error MSG_STRINGS.CAN_NOT_FIND_USER
          uData = libOauth.genUserFromThirdPartyObj uData
          delete uData.uTp
          u = Object.assign uData,u
        u.wxuid = (data.wxid or data.wxuid) if (data.wxid or data.wxuid)
        u.mbl = data.mbl if data.mbl
        u.fn = data.fn if data.fn
        u.ln = data.ln if data.ln
        if data.fn and data.ln
          if helpers.hasChinese u.fn or helpers.hasChinese u.ln
            u.nm_zh = "#{u.ln}#{u.fn}"
          else
            u.nm_en = "#{u.fn} #{u.ln}"
        #set current weekday for edm group
        u.edmGrp = (new Date()).getDay()
        if locale = req.locale() # save locale for new user
          u.locale = locale
          switch locale
            when 'zh'
              u.splang = ['Cantonese']
            when 'zh-cn'
              u.splang = ['Mandarin']
            when 'en'
              u.splang = ['English']
            when 'kr'
              u.splang = ['korean']
        u._exp24h = new Date(new Date().getTime() + (3600000 * 24)) # 新注册用户24小时内没有验证邮箱，删除用户
        user = await UserCol.insertOne u,{safe:true,session}
        u._id = user.insertedId
      authResult = await Auth.findOne {_id:data.eml.toLowerCase()},{readPreference:Auth.ReadPreferencePrimary}
      if not authResult
        login = {}
        login._id = data.eml.toLowerCase()
        login.uid = u._id
        login['method'] = 'sha512'
        login['secret'] = '*.*.*'
        login['passwd'] = crypto.createHash(login['method']).update(data.password).update(login['secret']).digest('hex')
        login['emlV'] = false
        login._exp24h = new Date(new Date().getTime() + (3600000 * 24)) # 新注册用户24小时内没有验证邮箱，删除用户
        await Auth.insertOne login,{safe:true, writeConcern: {wtimeout:3000},session}

      await session.commitTransaction()
    catch error
      err = error
      debug.error error,'register user data:',data
      await session.abortTransaction()
    finally
      await session.endSession()
    if err
      throw err
    return u

  # ---------------- roles 部分
  @isRealtor:(user)-> user?.roles?.indexOf('realtor') >= 0
  @isVip:(user)-> user?.roles?.indexOf('vip_plus') >= 0
# ==========  auth.coffee
  # 外部未使用
  @createUniqueUserID : createUniqueUserID
  # 外部未使用
  # createLogin
  @createLogin: (id,pass,uid,opts,cb)->
    if not ('string' is typeof(id) and id?.length > 3)
      return gFindError MSG_STRINGS.BAD_LOGIN_ID,{f:'createLogin',id},cb
    if not cb?
      cb = opts
      opts = {}
    if 'function' isnt typeof cb
      throw new Error(MSG_STRINGS.NOT_A_VAILD_CB)
    if not uid?
      throw new Error('UID must be defined')
    login = {}
    login._id = id.toLowerCase()
    login.uid = uid
    Auth.findOne {_id:login._id},{readPreference:Auth.ReadPreferencePrimary},(err,existing)->
      if existing? and existing.uid isnt login.uid
        return cb new Error('Duplicated login id')
      login['method'] = opts?.method or 'sha512'
      login['secret'] = opts?.secret or '*.*.*'
      login['passwd'] = crypto.createHash(login['method']).update(pass).update(login['secret']).digest('hex')
      if opts?.needVerify # force login to check emlV
        login['emlV'] = false
      else
        # 第三方注册，存在邮箱默认已验证
        login['emlV'] = new Date()
      Auth.insertOne login,{safe:true, writeConcern: {wtimeout:3000}},(err)->
        cb err,{pass:pass}
    null

  # 外部未使用
  # _auth_gen_unikey
  @authGenUnikey: (lid, isApp, cb)->
    return cb new Error(MSG_STRINGS.BAD_LOGIN_ID) unless lid? and (('string' is typeof lid) or (lid instanceof Auth.ObjectId))
    lid = lid.toLowerCase()
    Auth.findOne {_id:lid},{readPreference:Auth.ReadPreferencePrimary}, (err,login)->
      return gFindError err,{f:'authGenUnikey',eml:lid},cb if err
      return gFindError MSG_STRINGS.UNKNOWN_LOGIN_ID,{f:'authGenUnikey',eml:lid},cb if not login?
      key = null
      done = ->
        if isApp
          update  = {$set:{unikey:key}}
        else
          update = {$addToSet:{wunikey:key}}
        Auth.updateOne {_id:lid},update,{writeConcern: {wtimeout:3000}},(err)->
          cb err,key
      gen_unikey = ->
        key = nanoid()
        query  = if isApp then {unikey:key} else {wunikey:key}
        Auth.findOne query,{readPreference:Auth.ReadPreferencePrimary},(err,one)->
          if one? then return process.nextTick gen_unikey
          done()
      gen_unikey()

  # login by unique, cb(err,user)
  # _auth_by_unikey
  @authByUnikey : (req,key,cb)->
    isApp = req.getDevType() is 'app'
    return cb new Error(MSG_STRINGS.BAD_LOGIN_ID) unless key? and ('string' is typeof key)
    query  = if isApp then {unikey:key} else {wunikey:key}
    findByUnikey = -> # the base logic
      Auth.findOne query,{readPreference:Auth.ReadPreferencePrimary},(err,login)->
        return cb err,{f:'authByUnikey',info:util.inspect query} if err
        return cb() if not login?
        login_login {req,login,fromMethod:'authByUnikey'},cb
    if config.serverBase?.chinaMode
      Auth.findOne query,{readPreference:Auth.ReadPreferenceNearest},(err,login)-> # try local db first
        if err
          debug.error err,'authByUnikey,query',util.inspect query
          return findByUnikey()
        if not login?
          # debug.debug 'Not find unikey',key
          return findByUnikey()
        login_login {req,login,fromMethod:'authByUnikey'},cb
    else
      findByUnikey()

  # remove unique key for login id. cb(err)
  # _auth_del_unikey
  @auth_del_unikey: (unikey, isApp, cb)->
    return cb new Error(MSG_STRINGS.BAD_LOGIN_ID) unless unikey? and ('string' is typeof unikey)
    query  = if isApp then {unikey:unikey} else {wunikey:unikey}
    Auth.findOne query,{readPreference:Auth.ReadPreferencePrimary},(err,login)->
      return cb new Error(MSG_STRINGS.NO_LOGIN_INFO) if not login?
      if isApp
        update = {$unset:{unikey:true}}
      else
        update = {$pull:{wunikey:unikey}}
      Auth.updateOne {_id:login._id},update,{safe:true, writeConcern: {wtimeout:3000}},(err)->
        cb err

  #外部未使用
  # _auth_auth_rtoken
  @authRtoken:(rtoken2,cb)->
    if 'string' is typeof rtoken2 and rtoken2?.length > 10 and 'function' is typeof cb
      try
        ll = parseInt rtoken2.slice(-2)
        lid = rtoken2.substr(0,ll)
        rtoken1 = rtoken2.slice(ll,-2)
        RToken.findOneAndDelete {_id:lid,rtoken:rtoken1,ts:$gt:(Date.now() - 60000)},(err,r)->
          return gFindError err,{f:'authRtoken',id:lid},cb if err
          debug.warn 'authRtoken',r.value?._id
          return gFindError MSG_STRINGS.NO_MATCHED_TOKEN,{f:'authRtoken'},cb if not (rt = r.value)?.lid
          return cb null,{lid:rt.lid,extra:rt.extra}
      catch e
        return gFindError e,{f:'authRtoken'},cb
    else
      return gFindError MSG_STRINGS.NO_TOKEN,{f:'authRtoken'},cb

  # [home homepageserver newcondos/index propdetail webproplist proplist]
  # _auth_auth
  @auth:({req,resp,url},cb)->
    # TODO: support jwt
    if user = req.session?.get('user')
      if 'string' is typeof user._id
        user._id = new ObjectId(user._id)
      if not user.uuid
        uuid = req?.cookies?.uuid
        try
          await @bindUid {user,uuid}
          user.uuid = uuid
          req.session.set 'user',user,->
        catch err
          debug.error err
      return cb user
    done = (err,user)->
      if (not err) and user then return cb user
      #if err then debug.log err
      if url?
        req.session?.set 'after_login_url',req.url,->
        resp.redirect url
      else
        return cb(null)
    # getPK = (req)->
    # # get pk object {id:login-id,pk:pk}
    #   if (ap = req.cookies?[PK_COOKIE]) and (ap.length < 100) and ((p = ap.indexOf(':')) > 5)
    #     id = ap.substr(0,p)
    #     pk = ap.substr(p+1)
    #     ret = {id:id,pk:pk}
    #     req._auth_pk = ret
    #     return ret
    #   null
    # if cki = getPK req
    #   return Auth.findOne {_id:cki.id,'pks.pk':cki.pk},{readPreference:Auth.ReadPreferencePrimary},(err,login)->
    #     if err or not login?
    #       debug.error err
    #       return done err
    #     # return login_login {req,login},done
    # done()
    return done() unless k = req.cookies['wk']
    User.authByUnikey req,k,(err,user)->
      if err
        debug.error err,'auth User.authByUnikey'
        return done(err)
      return done new Error(MSG_STRINGS.USER_NOT_LOGIN) unless user
      return done(null,user)
  # [login]
  # _auth_login
  @authLogin:({req, u,p}, cb)->
    if user = req.session?.get('user')
      return cb(null,user)
    if not (u and p)
      return failedOnce {req},->
        return cb MSG_STRINGS.NO_LOGIN_ID_OR_PWD,null,'EMAIL_PASSWORD_VERIFY'
    Auth.findOne {_id:u.toLowerCase()},{readPreference:Auth.ReadPreferencePrimary}, (err,login)->
      if err
        debug.error "authLogin Auth.findOne #{err}"
        return cb err
      unless login?
        return failedOnce {req},->
          return cb MSG_STRINGS.CANT_FIND_LOGIN_ID,null,'EMAIL_PASSWORD_VERIFY'
      if false is login.emlV
        return cb MSG_STRINGS.EMAIL_NOT_VERIFIED,null,MSG_STRINGS.EMAIL_VERIFY
      pp = crypto.createHash(login.method).update(p.toString()).update(login.secret).digest('hex')
      if login.passwd isnt pp
        return failedOnce {req,login},(failed)->
          return cb MSG_STRINGS.ID_OR_PWD_NOT_MATCH,null,failed
      login_login {req, login,fromMethod:'authLogin'}, cb
  # [login]
  # _auth_logout
  @authLogout:(req,cb)->
    if req.session?.id()
      req.resp.clearCookie PK_COOKIE
      return req.session.destroy cb
    cb()

  # login with a remote token
  # _auth_rtoken_login
  @authRtokenLogin :(req,token,cb)->
    User.authRtoken token,(err,info)->
      return gFindError err,{f:'authRtokenLogin',info:'token: '+util.inspect token},cb if err
      return gFindError MSG_STRINGS.RLOGIN_ERROR,{f:'authRtokenLogin'},cb unless lid = info?.lid or info?.eml
      if Array.isArray lid
        lid = lid[0]
      Auth.findOne {_id:lid.toLowerCase()},{readPreference:Auth.ReadPreferencePrimary},(err,login)->
        return gFindError err,{f:'authRtokenLogin',id:lid},cb if err
        return gFindError MSG_STRINGS.NO_LOGIN_INFO,{f:'authRtokenLogin',id:lid},cb unless login
        login_login {req,login,fromMethod:'authRtokenLogin'},cb

  # [userpost]
  # genPass
  @genPass:({seed,length})->
    if not seed?
      seed = new Date()
    length = length or 10
    if 'string' isnt typeof seed
      seed = seed?.toString()
    if length < 6 then length = 6
    time = '' + Date.now()
    pass = crypto.createHash('md5').update(seed).update(time).digest('hex')
    if length > pass?.length then length = pass.length
    pass.substr(0,length)

  # 外部未使用
  # _auth_has_login_id
  @hasLoginId:(id,cb)->
    if not ('string' is typeof(id) and id?.length > 3)
      return gFindError MSG_STRINGS.BAD_LOGIN_ID,{f:'hasLoginId',id},cb
    #let oauth login to check if emlV need updated
    projection = {fields:{_id:1,emlV:1,uid:1},readPreference:Auth.ReadPreferencePrimary}
    Auth.findOne {_id:id.toLowerCase()},projection,(err,one)->
      return gFindError err,{f:'hasLoginId',id},cb if err
      if one
        return cb null,one
      cb null,false

  # [userpost]
  # _auth_login_only_by_id
  @loginOnlyById:({req,eml,fromMethod},cb)->
    return gFindError MSG_STRINGS.BAD_LOGIN_ID,{f:'loginOnlyById',eml},cb unless eml? and (('string' is typeof eml) or (eml instanceof Auth.ObjectId))
    Auth.findOne {_id:eml.toLowerCase()}, {readPreference:Auth.ReadPreferencePrimary},(err,login)->
      return gFindError err,{f:'loginOnlyById',eml},cb if err
      return gFindError MSG_STRINGS.CANT_FIND_LOGIN_ID,{f:'loginOnlyById',eml},cb if not login?
      login_login {req,login,fromMethod:('loginOnlyById:'+fromMethod)},cb

  # 外部未使用
  # _auth_login_n_ensure_login
  @loginNEnsureLogin:({req,eml},cb)->
    return cb new Error(MSG_STRINGS.BAD_LOGIN_ID) unless eml? and (('string' is typeof eml) or (eml instanceof Auth.ObjectId))
    Auth.findOne {_id:eml.toLowerCase()},{readPreference:Auth.ReadPreferencePrimary}, (err,login)->
      findUserInfo = ()->
        UserCol.findOne {eml:eml.toLowerCase()},{readPreference:UserCol.ReadPreferencePrimary},(err,user)->
          return gFindError err,{f:'loginNEnsureLogin',eml},cb if err
          return gFindError MSG_STRINGS.NO_USER_FOR_THE_EMAIL,{f:'loginNEnsureLogin',eml},cb if not user?
          return User.createDummyLogin {eml:eml,_id:user._id},(err,ret)->
            User.loginOnlyById {req,eml,fromMethod:'loginNEnsureLogin'},(err,user)->
              debug.error err,'loginNEnsureLogin,eml',eml if err
              cb err,user,ret
      if err
        debug.error err,'loginNEnsureLogin,eml',eml
        return findUserInfo()
      if not login?
        debug.info MSG_STRINGS.NO_LOGIN_INFO,'loginNEnsureLogin,eml',eml
        return findUserInfo()
        # create login id
      login_login {req,login,fromMethod:'loginNEnsureLogin'},cb

  # 暂时未使用
  # _login_get_history
  @loginGetHistory : ({eml,dm},cb)->
    if not dm? or not eml? then return cb null
    Auth.findOne {_id:eml.toLowerCase(),'his.dm':dm},{readPreference:Auth.ReadPreferencePrimary},(err,auth)->
      return gFindError err,{f:'loginGetHistory',eml},cb if err
      return gFindError MSG_STRINGS.CANT_FIND_LOGIN_ID,{f:'loginGetHistory',eml},cb if not auth?
      his = []
      for r in auth.his
        if r.dm is dm
          his.push {ip:r.ip,dt:r.dt,openid:r.openid}
      cb null,his

  # [home]
  # _auth_gen_rtoken
  @genRtoken : ({user,
  host
  },cb)->
    if not host?
      host = null
    debug.error 'genRtoken: no user' unless user?._id
    if lid = user?._id?.toString()
      rtoken1 = helpers.randomString()
      login_id = User.getCurrentLoginIdOrEmail {user}
      debug.warn 'genRtoken',login_id
      RToken.updateOne {_id:lid},{$set:{rtoken:rtoken1,ts:Date.now(),dt:(new Date()),lid:login_id,extra:{host:host}}},{safe:true,upsert:true, writeConcern: {wtimeout:3000}},(err)->
        return gFindError err,{f:'genRtoken',id:lid},cb if err
        rtoken2 = lid + rtoken1 + helpers.zeroPad(lid.length,2)
        cb null,{rtoken:rtoken2}
    else
      return gFindError MSG_STRINGS.NOT_VAILD_USER_PARAMTER,{f:'genRtoken'},cb

  # For old APP only API 外部未使用
  # _auth_rlogin_local
  @rloginLocal:(req,cb)->
    #if user = req.session?.get('user')
    #  return cb(null,user)
    return gFindError MSG_STRINGS.NO_ACCESS_TOKEN,{f:'rloginLocal'},cb if not (a = req.param('a'))?
    User.authRtoken a,(err,obj)->
      return gFindError err,{f:'rloginLocal authRtoken'},cb if err
      return gFindError MSG_STRINGS.RLOGIN_ERROR,{f:'rloginLocal'},cb if not obj?.lid
      Auth.findOne {_id:obj.lid},{readPreference:Auth.ReadPreferencePrimary},(err,login)->
        return gFindError err,{f:'rloginLocal Auth'},cb if err
        return gFindError MSG_STRINGS.NO_LOGIN_INFO,{f:'rloginLocal',id:obj.lid},cb if not login
        login.remote_return_url = obj.extra?.return_url
        login_login {req,login,fromMethod:'rloginLocal'},cb

  # _auth_rlogin
  @authRlogin : (req,cb)-># TODO: not tested yet
    return gFindError MSG_STRINGS.NO_ACCESS_TOKEN,{f:'authRlogin'},cb if not (a = req.param('a'))?
    d = req.param('d','onemsapp') # req.param('d','real411app')
    #d = 'test'
    return gFindError MSG_STRINGS.NO_REMOTE_SERVER,{f:'authRlogin'},cb if not (opt = @REMOTESERVERS[d])?
    debug.log opt # TODO
    helpers.http_post opt,{a:a,appid:'RLOGIN'},(err,ret)->
      return gFindError err,{f:'authRlogin',info:'a: '+a},cb if err
      debug.log ret
      try
        obj = JSON.parse ret
      catch e
        return gFindError e,{f:'authRlogin'},cb
      return gFindError MSG_STRINGS.RLOGIN_ERROR,{f:'authRlogin'},cb if not obj?.lid?
      Auth.findOne {_id:obj.lid},{readPreference:Auth.ReadPreferencePrimary},(err,login)->
        return gFindError err,{f:'authRlogin Auth',id:obj.lid},cb if err
        return gFindError MSG_STRINGS.NO_LOGIN_INFO,{f:'authRlogin'},cb if not login?
        login.remote_return_url = obj.extra?.return_url
        login_login {req,login,fromMethod:'authRlogin'},cb

  # return null if no session.login_id or user
  # get
  #TODO: logout delete session.login_id
  #   switch user(dont modify pwd) delete session.login_id
  @getCurrentLoginIdOrEmail:({req,user})->
    user ?= req?.session?.get('user')
    login_id = req?.session?.get('login_id') or null
    eml = User.getEmail(user)
    return login_id or eml
  # [userpost]
  # _auth_is_current_password
  @isCurrentPassword:({req,
  p #oldPwd
  },cb)->
    # debug.debug '++++++',req.session.get('user').login_id
    if loginIdOrEml = User.getCurrentLoginIdOrEmail({req})
      Auth.findOne {_id:loginIdOrEml.toString().toLowerCase()},{readPreference:Auth.ReadPreferencePrimary},(err,login)->
        if err
          debug.error err,'isCurrentPassword,eml',loginIdOrEml
          return cb false
        unless login?
          debug.error 'isCurrentPassword no login',loginIdOrEml,login
          return cb false
        pp = crypto.createHash(login?.method).update(p).update(login.secret).digest('hex')
        if pp isnt login.passwd then return cb false
        return cb true
    else
      debug.log req.session?.get('user')
      return cb false

  # [sysadmin userpost]
  # _auth_reset_pass
  @resetPassword:({eml,pwd},cb)->
    unless ('string' is typeof eml)
      return cb 'Invalid Email:'+eml
    eml = eml.toLowerCase()
    Auth.findOne {_id:eml},{readPreference:Auth.ReadPreferencePrimary},(err,login)->
      return gFindError err,{f:'resetPassword',eml},cb if err
      if login
        npass = crypto.createHash(login['method'] or 'sha512').update(pwd).update(login['secret']).digest('hex')
        Auth.updateOne {_id:eml},{$set:{passwd:npass}},{safe:true,upsert:false,writeConcern: {wtimeout:3000}},(err,info)->
          return gFindError err,{f:'resetPassword',eml},cb if err
          cb()
      else
        cb new Error(MSG_STRINGS.NOT_FIND_LOGIN_ID)
  #verifyMblVCode [userPost] refer: realtorVerify
  @verifyMblVCode: ({req,resp,user},cb)->
    body = req?.body or {}
    UserProfile.findOne {_id:user._id},{fields:{verify:1}},(err,profile)->
      return cb err if err
      return cb MSG_STRINGS.NO_PROFILE if not profile?
      if ('' + body.mblvcd) isnt profile.verify?.mblvcd
        return cb('Incorrect verification code')
      # if isNaN(body.rid)
      #   return cb('ID must be Digits')
      if body.tp is 'realtor' and body.rid?.length < 5
        return cb('ID too Short')
      # TODO: compare mbl2v from body
      if isNaN(parseInt(body.mbl2v))
        return cb('Phone Number must be Digits')
      User.mblVerified {req,body,user},(err,user)->
        return cb(err,'Successfully Verified')

  #verifyEmailVCode [userPost] refer: saveEmlVCode
  @verifyEmailVCode: ({body,neml,user},cb)->
    body ?= {}
    eml = if Array.isArray user.eml then user.eml[0] else user.eml
    try
      login = await Auth.findOne {_id:eml}
    catch err
      debug.error err,' email:',eml
      return cb err
    if not login
      return cb(MSG_STRINGS.NOT_FOUND)
    if login?.emlV and (not neml)
      return cb(null,'Already Verified')
    
    try
      profile = await UserProfile.findOne {_id:user._id},{fields:{verify:1}}
    catch err
      debug.error err,'uid:',user._id
      return cb err
    if not profile
      return cb MSG_STRINGS.NO_PROFILE
    if ('' + body.emlvcd) isnt profile.verify?.emlvcd
      return cb('Incorrect verification code')

    try
      await User.emailVerified {eml:user.eml,id:user._id,vCode:profile.verify}
    catch err
      debug.error err,' email:',user.eml,'uid:',user._id
      return cb err
    # TODO:cb->async，目前用到地方较多暂不修改
    libUser.updateProfileStars {User:UserCol,Login:Auth,id:user._id},(err)->
      return cb err if err
      cb err,'Successfully Verified'

  # [userpost]
  @emailVerified : ({eml,id,vCode,pass})->
    debug.info eml
    unless eml = eml?.toString()
      throw new Error(MSG_STRINGS.NO_ID)
    if pass
      set = {
        $set:{
          emlV:new Date(),
          method:'sha512',
          secret:'*.*.*',
          passwd:crypto.createHash('sha512').update(pass).update('*.*.*').digest('hex')
        },
        $unset:{_exp24h:1}
      }
    else
      set = {$set:{emlV:new Date()},$unset:{_exp24h:1}}
    opt = {upert:false,returnDocument:'after'}
    ret = await Auth.findOneAndUpdate {_id:eml.toLowerCase()},set,opt
    if not ret?.value
      throw new Error(MSG_STRINGS.NOT_FOUND)
    if not id
      id = ret?.value?.uid
    await UserCol.updateOne {_id:id},{$set:{emlV:eml.toLowerCase()},$unset:{_exp24h:1}}
    await VcodeCol.deleteOne {tp:VERIFY_EMAIL,id}
    if vCode
      update ={}
      verify = {vCode:vCode}
      verify.emlvcd = null
      verify.emlvts = Date.now()
      verify.vfd = true
      update.verify = verify
      update.eml2v  = null
      update.emlvts = Date.now()
      await UserProfile.updateOne {_id:id},{$set:update},{upsert:true}
    return

  #saveEmlVCode when user auth by email
  @saveEmlVCode : ({user,vcode},cb)->
    user.verify =  user.verify or {}
    user.verify.emlvts = Date.now()
    user.verify.emlvcd = vcode
    #update user table here, eml2v, emlvcd, emlvts

    #check roles and add realtor
    if (not user.roles?)
      user.roles = []

    update = {}
    update.verify = user.verify
    update.eml2v  = true
    update.emlvts = false
    UserProfile.updateOne {_id:user._id} ,{$set:update},{upsert:true},(err,user)->
      # cb to send json success msg
      cb(err)
  # 外部未使用
  @createDummyLogin : (user,cb)->
    id = user.eml
    pass = User.genPass {seed:new Date(),length:8}
    User.createLogin id,pass,user._id,cb

  # [login]
  # _auth_login_2_user
  @login2user:(id)->
    if not ('string' is typeof(id) and id?.length > 3)
      throw new Error(MSG_STRINGS.BAD_LOGIN_ID)
    one = await Auth.findOne {_id:id.toLowerCase()},{readPreference:Auth.ReadPreferencePrimary}
    if not one?.uid
      return null
    user = await UserCol.findOne {_id:one.uid},{readPreference:UserCol.ReadPreferencePrimary}
    return user

  # 外部未使用
  # _auth_has_permit
  @hasPermit : (req,permit,cb)->
    if user = req.session?.get('user')
      if pmt = user._pmt
        if Array.isArray permit
          for p in permit
            if pmt[p] then return cb p,user
        else if (p = permit?.toString()) and pmt[p]
          return cb p,user
        if pmt.admin then return cb 'admin',user # specail case
      return cb false, user
    return cb false

  # [外部未使用]
  # _auth_change_permit
  # NOTE: still in use?
  # @changePermit:(req,uid,permits,cb)->
  #   pm = convert_permits_type permits
  #   internal_change_permit req,uid,pm,cb

  # [外部未使用]
  # _auth_remove_permit
  # NOTE: still in use?
  # @removePermit: (req,uid,permits,cb)->
  #   pm = convert_permits_type permits,false
  #   internal_change_permit req,uid,pm,cb

  # [外部未使用]
  # _auth_add_permit
  # NOTE: still in use?
  # @addPermit: (req,uid,permits,cb)->
  #   pm = convert_permits_type permits,true
  #   internal_change_permit req,uid,pm,cb

  # [外部未使用]
  # _auth_permit
  # NOTE: still in use?
  # @authPermit:(req,resp,url,permits,cb)->
  #   # - this one works like _auth_auth, but will logout user if it does not have required permit
  #   User.hasPermit req,permits,(user,u2)->
  #     if user then return cb user
  #     if u2
  #       return User.authLogout req,->
  #         resp.redirect url
  #         cb()
  #     resp.redirect url
  #     cb()

  # [外部未使用]
  # _auth_openid_to_auth
  @openid2auth:(req,resp,RelyingParty,provider_id,cb)->
    if opurl = @THIRDPARTYOPS[provider_id.toLowerCase()]
      RelyingParty.authenticate opurl,false,(err,authUrl)->
        debug.log opurl
        return gFindError err,{f:'poenid2auth'},cb if err
        if not authUrl? then return cb new Error(MSG_STRINGS.CAN_NOT_FIND_URL)
        return resp.redirect authUrl
    else
      return cb new Error(MSG_STRINGS.UN_SUPPORTED_PROVIDER)

  # [外部未使用]
  # _auth_openid_verify
  @openidVerify:(req,resp,RelyingParty,cb)->
    RelyingParty.verifyAssertion req,cb

  # [外部未使用]
  # _auth_openid_auth
  @openidAuth:(req,resp,RelyingParty,login_url,cb)->
    #same as _auth_openid_auth, but login user and return, don't work with openid extensions
    RelyingParty.verifyAssertion req,(err,result)->
      if err
        debug.error err,'openidAuth'
        return resp.redirect login_url
      else if not result?
        debug.error 'openidAuth: no result'
        return resp.redirect login_url
      else
        req.session.set 'openid',result
        User.loginOnlyById {req,eml:result.email,fromMethod:'openidAuth'},cb

# ==========  01_appauth.coffee
  ###使用的文件
    Home   02socket   Sysadmin   L10n   Transaction   Status   x_ajaxPageData   banner   Cpmads   Dlpage   Eventpage   Headline
    Proptopup   Comments   Contacts   Usercrm   Formmanage   awordpresssync   Forum   News   Group   Scheme   Map   Userfile
    Staging   Yellow   page   03propfunctions   04search   Mapsrachprop   Prop   detail   Prop   prediction   Resources
    Chophouse   Direction   Evaluation   Housecreep   Htmltoimg   Userpost   Mapmixview   Tools   Brokerage   Leads   Realtor
    Realtorverify   Project   rm0myliating   Rm1macket   Rm2propdetail   Mapschprp   Privateschool   Schoolframe   Schoolr
    Showing   Census   Stats   Transit   chat   Settings   wesite   wecardadmin   wecard   xIndex/index   02api3p   xapishare
    coophouses   Payment   Admin   boundarymanage   Callback58   Edm   forum   forumAdStatServer   forumserver   Webrm/index
    inputCriminal   Inputschool   Mylisting   Project   projectserver   propserver   School   Setting
  ###
  @appAuthAsync =  functionHelper.addAsyncSupport User.appAuth
  @appAuth:({req,resp,url},cb)->
    # TODO: postRedirect:true, ignore following test
    # req.method == post && url, debug.error ''
    done = (user)->
      # if user, set req.user return
      if user
        user.avt = libPropertyImage.replaceRM2REImagePath user.avt if user.avt
        req.user = user
        # setup language
        req.setLocale user.locale if user.locale and (user.locale isnt req.locale())
        # TODO: it seems that ck/cn no longer used, shall remove following block.
        #             no need to save ck/cn for every http request
        # NOTE: used when user promote to 58, can select service City, removed on 2020-12-28
        # if user.ck and user.cn
        #   req.session.set 'ck',user.ck
        #   req.session.set 'cn',user.cn,->
        #     cb user
        #   return null
        # else
        return cb user
      # still no user, redirect to url
      if url then return resp.redirect url
      cb null
    # auth_auth, with null
    User.auth {req,resp},(user)->
      return done user if user
      # check cookie 'k', auto sign-in auth_by_unikey
      if k = req.cookies['k']
        User.authByUnikey req,k,(err,user)->
          if err
            debug.error err,'appAuth'
            req.logger?.error err
          done user
      else
        done()

  # [userpost]
  @appLogin:({req,u,p},cb)->
    # _auth_login
    User.authLogin {req,u,p},(err,user,extraInfo)->
      if err
        debug.error err,'appLogin,eml',u unless extraInfo #only log db err
        return cb(err,null,extraInfo)
      if not user?
        debug.error new Error(MSG_STRINGS.NO_USER)
        return cb(MSG_STRINGS.NO_USER,null,extraInfo)
      User.appAfterLogin {req,user,eml:u},cb

  # [userpost]
  @appLogout:(req,cb)->
    # remove 'k';req.user
    isApp = req.getDevType() is 'app'
    ck = if isApp then 'k' else 'wk'
    if k = req.cookies[ck]
      req.resp?.clearCookie ck
      return User.auth_del_unikey k, isApp, (err)->
        if err
          req.logger?.warn err
          debug.error err,'appLogout'
        User.authLogout req,cb
    # _auth_logout
    User.authLogout req,cb

  # [userpost]
  # loginCheck.js from phantom.js test case
  # @removeRM : (cb) ->
  #   UserCol.remove {eml: '<EMAIL>'},{safe:true},(err, res)->
  #     return gFindError err,cb if err
  #     if res.result.n != 1 then return cb null
  #     Auth.remove {_id: '<EMAIL>'},{safe:true},(err, res)->
  #       return gFindError err,cb if err
  #       cb null

  # [userpost]
  # appAuthAfterLogin
  @appAfterLogin:({req,user,eml},cb)->
    isApp = req.getDevType() is 'app'
    ck = if isApp then 'k' else 'wk'
    debug.error 'appAfterLogin: no user' unless user?._id
    User.authGenUnikey eml,isApp,(err,uk)->
      return gFindError err,{f:'appAfterLogin',eml},cb if err
      return gFindError MSG_STRINGS.CAN_NOT_CREATE_KEY,{f:'appAfterLogin',eml,info:"uk:#{uk}"},cb if not uk?
      req.resp.cookie ck,uk,{maxAge:365*24*3600*1000,path:'/'}
      req.user = user
      # setup language
      req.setLocale user.locale if user.locale
      libUser.updateProfileStars {User:UserCol,Login:Auth,id:user._id},(err,stars)->
        user.stars = stars
        cb null,user
  # [home]
  @appRlogin: ({req,token},cb)->
    User.authRtokenLogin req,token,(err,user)->
      debug.info "app_rlogin #{err} #{user}"
      return gFindError err,{f:'appRlogin',eml:user?.eml},cb if err
      return gFindError MSG_STRINGS.AUTH_ERROR,{f:'appRlogin',eml:user?.eml,info:"token:#{token}"},cb if not user?
      if lid = User.getCurrentLoginIdOrEmail {req}
        # (user.login_id or (if Array.isArray(user.eml) then user.eml[0] else user.eml))
        return User.appAfterLogin {req,user,eml:lid},cb
      else
        return cb new Error(MSG_STRINGS.CAN_NOT_FIND_USER)

# ==========  00_userfile
  # callback(err, userfile, reserved-file-names)
  @reserveUserFile : ({user,fileNum},cb)->
    if not fileNum?
      fileNum = 1
    update =
      $set:{
        ts:new Date(),
        base:config.user_file?.wwwbase,
        fldr:FILE_LOCAL_FOLDER_BASE
      }
    if fileNum > 0
      update.$inc = cntr:fileNum
    else
      update.$setOnInsert = cntr:17
    UserFile.findOneAndUpdate {_id:user._id},update,{upsert:true,returnDocument:'after'},(err,r)->
      # user may not have userFile yet, so must get updated Object instead of original
      return gFindError err,{f:'reserveUserFile',id:user._id},cb if err
      return gFindError MSG_STRINGS.CAN_NOT_CREATE_USER_FILE,{f:'reserveUserFile'},cb if not (userFile = r.value)
      fileHelper.ensureUserFolder SysData,UserFile,userFile,(err,nUserFile)->
        return gFindError err,{f:'reserveUserFile'},cb if err
        ret = []
        if fileNum > 0
          if userFile.cntr < 17
            userFile.cntr = 17 + fileNum
            UserFile.updateOne {_id:user._id},{$set:cntr:userFile.cntr},(err)->
              if err then debug.error err,'reserveUserFile,id:',user._id
          index = userFile.cntr
          index -= fileNum
          for i in [index...(index + fileNum)]
            ret.push helpers.number2chars i
        cb null, nUserFile, ret

  # check if image file name used in forum/wecard/userlisting/pre_constrct/prop_notes/condos/equifax
  @checkFileReference:({uFldnm,fileName,uid},cb)->
    #user_listing pic.l
    #project img.l
    #wecard meta.img, seq.m
    #forum thumb, photos
    #prop_notes pic
    #condos floorplan img and img
    #prop_notes_personal pic
    # searchReg = helpers.str2reg("#{uFldnm}/#{fileName}")
    #equifax status:success
    imgPath = "#{uFldnm}/#{fileName}"
    foundInRecords = []
    try
      forums = await ForumCol.findToArray {uid,del:{$ne:true},$or:[{thumb:imgPath},{photos:imgPath}]},{projection:{_id:1,tl:1}}
      if forums?.length > 0
        for forum in forums
          url = libForum.getDetailUrl forum,{isPopup:true}
          foundInRecords.push {col:'forum',tl:forum.tl,url}
      wecards = await Wecard.findToArray {uid,$or:[{'meta.img':imgPath},{'seq.pics':imgPath}]},{projection:{_id:1,meta:1}}
      if wecards?.length > 0
        for wecard in wecards
          url = libWecard.getEditUrl wecard,{isPopup:true}
          foundInRecords.push {col:'wecard',tl:wecard.meta.title,url}
      userlistings = await UserListing.findToArray {uid,'pic.l':imgPath},{projection:{_id:1,tl:1}}
      if userlistings?.length > 0
        for userlisting in userlistings
          url = libRmListing.getDetailUrl userlisting,{isPopup:true}
          foundInRecords.push {col:'userlisting',tl:userlisting.tl,url}
      projects = await ProjectCol.findToArray {uid,'img.l':imgPath},{projection:{_id:1,nm:1,nm_en:1}}
      if projects?.length > 0
        for p in projects
          url = libProject.getEditUrl p,{isPopup:true}
          foundInRecords.push {col:'pre_constrct',tl:p.nm or nm_en,url}
      propNotes = await PropNotesCol.findToArray {uid,pic:{$in:[imgPath]}},{projection:{_id:1,uaddr:1,propIds:1}}
      if propNotes?.length > 0
        for note in propNotes
          url = libPropNotes.getEditUrl note
          foundInRecords.push {col:'propNotes',tl:note.uaddr,url}
      condosQuey = {$or:[{'floorplan.img':imgPath},{'img.l':imgPath}]}
      condosImgs = await CondosCol.findToArray condosQuey,{projection:{_id:1,addr:1,nm:1}}
      if condosImgs?.length > 0
        for condo in condosImgs
          condosTl = "addr:#{condo.addr},name:#{condo.nm}"
          foundInRecords.push {col:'condos',tl:condosTl,url:''}
      propNotesPersonal = await PropNotesPersonalCol.findToArray {uid,pic:{$in:[imgPath]}},{projection:{_id:1,uaddr:1,unt:1,propIds:1}}
      if propNotesPersonal?.length > 0
        for note in propNotesPersonal
          url = libPropNotes.getEditUrl note
          foundInRecords.push {col:'propNotesPersonal',tl:note.uaddr,url}
      equifaxRecord = await EquifaxCol.findOne {uid,status: 'Success',pic:imgPath},{projection:{_id:1,fn:1,ln:1}}
      if equifaxRecord
        url = '/equifax/history'
        foundInRecords.push {col:'equifax',tl:"#{equifaxRecord.fn} #{equifaxRecord.ln}",url}
      return cb(null,foundInRecords) if cb
      return foundInRecords
    catch err
      debug.critical err
      return cb(err) if cb
      return err

# ==========  access.coffee
  @accessAllowed : (module,user)->
    if user
      if (deny = tblDeny[module]) # module deny policy
        if Array.isArray(roles = user.roles)
          for r in roles
            if rr = deny[r]
              return false
      if (allow = tblAllow[module]) # module allow policy
        if Array.isArray (roles = user.roles)
          for r in roles
            if rr = allow[r]
              return true
    # return default module access or true: allow
    if tblDefault[module]?
      return tblDefault[module]
    false

  # programly set the module names, so UI can have a list to show
  # _access_module
  @accessModule:(module,remove,noSave)->
    if (not tblModules[module]?) or remove
      if remove
        delete tblModules[module]
      else
        tblModules[module] = true
      _delaySaveAccess() unless noSave

  # used by program to set up default access policy
  # _access_set_default
  @accessSetDefault : (module,allow,noSave)->
    if not allow?
      delete tblDefault[module]
    else
      tblDefault[module] = allow
    _saveAccessDb2table() unless noSave

  # used by UI to set up role based access table
  # param:
  #   user: must be admin user to change table
  #   roles: string or [string] ;
  #   denyOrAllow: -1 deny, 1 allow, 0 or null, remove from deny and allow table
  # _access_set
  @accesSet : (user,module,roles,denyOrAllow,noSave)->
    if (user.sysr is 'mstr') and ('string' is typeof module) # system role is master
      chgTbl = (r)->
        if not denyOrAllow?
          delete tblDeny[module]?[r]
          delete tblAllow[module]?[r]
        else if denyOrAllow is -1
          (tblDeny[module] ?= {})[r] = true
          delete tblAllow[module]?[r]
        else if denyOrAllow is 1
          delete tblDeny[module]?[r]
          (tblAllow[module] ?= {})[r] = true
        else
          throw new Error("Bad Parameter: denyOrAllow: #{denyOrAllow}")
      if Array.isArray roles
        for role in roles
          chgTbl role
      else if 'string' is typeof roles
        chgTbl roles
      else
        throw new Error("Bad Parameter Type: roles: #{typeof roles}")
      _saveAccessDb2table() unless noSave
      return true
    false

  ###
  # @description 获取前端管理用户角色界面点击Check Account Info按钮需要展示的用户信息
  # @param {object} u - 从数据库获取的用户信息
  # @return {object} uu - 返回前端需要展示的信息
  ###
  @checkUserInfo:(u)->
    uu = {
      id: u._id,
      eml: u.eml,
      # fn:#{u.fn}; ln:#{u.ln},
      name: "nm:#{u.nm or ''}, nm_en:#{u.nm_en or ''}, \
        nm_zh:#{u.nm_zh or ''}, fnm:#{u.fnm or ''}",
      cpmnm: u.cpmnm,
      cpny: "cpny:#{u.cpny or ''}, cpny_en:#{u.cpny_en or ''}, \
        cpny_zh:#{u.cpny_zh or ''}",
      mbl:u.mbl,
      lic:u.lic,
      balance: helpers.round(u.balance,2),
      roles:u.roles?.toString(),
      lts: u.lts,
      fornm: u.fornm
    }
    for k in ['rid','aid','bid','flwng','rst_prov','ddf_rid',\
    'blkCht','devId','pn','edmNo','cpmnm','wxuid','noFltr','edmPriority']
      uu[k] = u[k] if u[k]?
    return uu
  # NOTE:
  @afterChangeEmailOperationsNew : ({eml,neml,deleteThirdParty,ueml,uid},cb)->
    # AdminOpLogs添加log
    eml = User.getEmail {eml}
    eml = eml.toLowerCase()
    neml = neml.toLowerCase()
    {transactionOptions,session} = GETTRANSACTION 'chome'
    err = null
    nChat = ''
    user = null
    try
      session.startTransaction(transactionOptions)

      user = await UserCol.findOne {eml},{session}
      throw new Error(MSG_STRINGS.NO_USER_FOR_THE_EMAIL) unless user

      login = await Auth.findOne {_id:eml},{session}
      throw new Error(MSG_STRINGS.NO_LOGIN_FOR_THE_EMAIL) unless login

      tologin = await Auth.findOne {_id:neml},{session}
      throw new Error(MSG_STRINGS.NEW_EMAIL_ALREADY_EXISTS) if tologin

      update = {$set:{eml:[neml]},$addToSet:{oEml:eml}}
      if deleteThirdParty
        update['$unset'] = {wxuid:1,pic:1,wxavt:1,googleId:1,facebookId:1,appleId:1}
      # update auth
      await UserCol.updateOne {eml},update, {session}

      login._id = neml
      delete login.unikey
      await Auth.insertOne login,{session}
      await Auth.deleteOne {_id:eml}, {session}

      # update chat
      ret = await ChatDb.updateMany {'usrs.0.eml':eml},{$set:{'usrs.0.eml':neml}},{session}
      nChat += ret?.nModified if ret?.nModified
      ret = await ChatDb.updateMany {'usrs.1.eml':eml},{$set:{'usrs.1.eml':neml}},{session}
      nChat += ret?.nModified if ret?.nModified

      # update ad topup
      q = {uid:user._id}#status:'A'
      now = new Date()
      ret = await TopupAgents.updateMany q,{$set:{eml:neml}},{session}
      update = {act:'change email', ts:new Date(), target:eml,neml,ueml,uid}
      await AdminOpLogs.insertOne update,{session}

      await session.commitTransaction()
    catch error
      await session.abortTransaction()
      debug.error error,'afterChangeEmailOperations eml: ',eml,' => neml:',neml
      err = error
    finally
      # NOTE: this will be called regardless of error
      await session.endSession()
    return cb(err,{user,nChat})
  @afterChangeEmailOperations : ({eml,neml,deleteThirdParty,ueml,uid},cb)->
    # AdminOpLogs添加log
    eml = User.getEmail {eml}
    eml = eml.toLowerCase()
    neml = neml.toLowerCase()
    TRANSACTION 'chome',(dbClient, session)->
      localFindError = (err)->
        # session.abortTransaction()
        debug.error err,'afterChangeEmailOperations eml: ',eml,'neml',neml
        cb {err}
      try
        user = await UserCol.findOne {eml},{session}
        return localFindError MSG_STRINGS.NO_USER_FOR_THE_EMAIL unless user
        login = await Auth.findOne {_id:eml},{session}
        return localFindError MSG_STRINGS.NO_LOGIN_FOR_THE_EMAIL unless login
        tologin = await Auth.findOne {_id:neml},{session}
        return localFindError MSG_STRINGS.NEW_EMAIL_ALREADY_EXISTS if tologin
        update = {$set:{eml:[neml]},$addToSet:{oEml:eml}}
        if deleteThirdParty
          update['$unset'] = {wxuid:1,pic:1,wxavt:1,googleId:1,facebookId:1,appleId:1}

        # update auth
        await UserCol.updateOne {eml},update, {session}
        login._id = neml
        delete login.unikey
        await Auth.insertOne login,{session}
        await Auth.deleteOne {_id:eml}, {session}

        # update chat
        nChat = ''
        ret = await ChatDb.updateMany {'usrs.0.eml':eml},{$set:{'usrs.0.eml':neml}},{session}
        nChat += ret?.nModified if ret?.nModified
        ret = await ChatDb.updateMany {'usrs.1.eml':eml},{$set:{'usrs.1.eml':neml}},{session}
        nChat += ret?.nModified if ret?.nModified

        # update ad topup
        q = {uid:user._id}#status:'A'
        now = new Date()
        ret = await TopupAgents.updateMany q,{$set:{eml:neml}},{session}
        update = {act:'change email', ts:new Date(), target:eml,neml,ueml,uid}
        await AdminOpLogs.insertOne update,{session}
        return cb {err:null,user,nChat}
      catch err
        session.abortTransaction()
        return localFindError err

  @deleteUnuseAccount:({eml},cb)->
    eml = eml.toLowerCase()
    UserCol.findOne {eml:eml},(err,user)->
      return gFindError err,{f:'deleteUnuseAccount',eml},cb if err
      return gFindError MSG_STRINGS.NO_USER_FOR_THE_EMAIL,{f:'deleteUnuseAccount',eml},cb unless user
      if user.lts
        debug.info MSG_STRINGS.USER_CANT_DELETE,'deleteUnuseAccount,eml: ',eml
        return cb null
      uid = new ObjectId(user._id)
      Auth.deleteOne {uid:uid},(err)->
        return gFindError err,{f:'deleteUnuseAccount',id:uid},cb if err
        UserCol.deleteOne {_id:uid},cb

  @deleteUser : ({eml,uuid},cb)->
    # user, user_profile, user_log, login, properties, props_part, user_listing, crm, showing, wechat_user, form, form_input, wecard, rm_group
    # UserCol, UserProfile, UserLog, Auth, Properties, PropsPart, UserListing, Crm, Showing, WechatUser, Form, Forminput, Wecard, Group
    # user_del  fn move col原始col
    eml = User.getEmail {eml}
    debug.debug eml
    user = null
    err = null
    localFindError = (err)->
      debug.error err,'deleteUser eml: ',eml
      return cb(err)
    try
      user = await UserCol.findOne {eml:eml}#, (err,user)->
    catch error
      err = error
      return gFindError err,{f:'deleteUser',eml},cb if err
    if not user?
      debug.error MSG_STRINGS.NO_USER_FOR_THE_EMAIL,'deleteUser,eml: ',eml
      return cb MSG_STRINGS.NO_USER_FOR_THE_EMAIL

    uid = new ObjectId(user._id)
    ts = new Date()
    delChromColls = [
      {
        col:Form
        query:{uid}
        fn:'deleteMany'
      },
      {
        col:Forminput
        query:{uid}
        fn:'deleteMany'
      },
      {
        col:Wecard
        query:{uid}
        fn:'deleteMany'
      },
      {
        col:UserLog
        query:{uid}
        fn:'deleteMany'
      },
      {
        col:Showing
        query:{uid}
        fn:'deleteMany'
      },
      {
        col:UserWatchLocationCol
        query:{uid}
        fn:'deleteMany'
      },
      {
        col:UserListing
        query:{uid}
        fn:'deleteMany'
      },
      {
        col:Crm
        query:{uid}
        fn:'deleteMany'
      },
      {
        col:Group
        query:{members:{$elemMatch:{uid:uid}}}
        opt:{$pull:{members:{uid:uid}}}
        fn:'updateMany'
      },
      {
        col:UserCol
        query:{flwng:{$elemMatch:{uid:uid}}}
        opt:{$pull:{flwng:{uid:uid}}}
        fn:'updateMany'
      },
      {
        col:PropNotesPersonalCol
        query:{uid}
        fn:'deleteMany'
      },
      {
        col:UserProfile
        query:{_id:uid}
        fn:'deleteOne'
      },
      {
        col:Auth
        query:{_id:eml}
        fn:'deleteOne'
      },
      {
        col:UserCol
        query:{_id:uid}
        fn:'deleteOne'
      },
      {
        # 删除用户vcode记录
        col:VcodeCol
        query:{id:uid,tp:VERIFY_EMAIL}
        fn:'deleteOne'
      },
    ]
    delVowColls = [
      {
        col:Properties
        query:{uid}
        fn:'deleteMany'
      },
      {
        col:PropFav
        query:{uid}
        fn:'deleteMany'
      },
    ]
    if user.wxuid
      delChromColls.unshift {
        col:WechatUser
        query:{_id:user.wxuid}
        fn:'deleteOne'
      }
    dataToDel =
      _id : uid
      colls : []
      ts : ts
      uid : uuid or uid
    deleteOne = (coll,session) ->
      col = coll.col
      fn = coll.fn
      query = coll.query
      data = await col.findToArray query,{session}
      if data.length >= 1
        if opt = coll.opt
          await col[fn] query,opt,{session}
        else
          await col[fn] query,{session}
        if fn isnt 'updateMany'
          collData =
            data : data
            col : col.collname
          dataToDel.colls.push collData
    
    try
      chomeTransaction = GETTRANSACTION 'chome'
      chomeTransaction.session.startTransaction(chomeTransaction.transactionOptions)
      vowTransaction = GETTRANSACTION 'vow'
      vowTransaction.session.startTransaction(vowTransaction.transactionOptions)

      for coll in delChromColls
        await deleteOne coll,chomeTransaction.session
      await UserDel.insertOne dataToDel,{session:chomeTransaction.session}
      if uuid
        await AdminOpLogs.insertOne {act:'deleteUser', ts, deleteU:uid,uid:uuid},{session:chomeTransaction.session}
      # NOTE: start vow transactions
      for coll in delVowColls
        await deleteOne coll,vowTransaction.session
      # NOTE: for same transaction, cannot abort after commit
      await vowTransaction.session.commitTransaction()
      await chomeTransaction.session.commitTransaction()
    catch error
      err = error
      debug.error error,'Require Admin operations! user email:',eml,' uid:',uuid
      await vowTransaction.session.abortTransaction()
      await chomeTransaction.session.abortTransaction()
    finally
      await chomeTransaction.session.endSession()
      await vowTransaction.session.endSession()
    # console.log '+++++',user
    return localFindError err if err
    return cb(null)

      # TRANSACTION 'chome',(chomeDbClient, chomeSession)->
      #   innerTransactionError = null
      #   try
      #     await TRANSACTION 'vow',(vowDbClient, vowSession)->
      #       try
      #         for coll in delVowColls
      #           await deleteOne coll,vowSession
      #       catch err
      #         await vowSession.abortTransaction()
      #         innerTransactionError = err
      #     # check if inner trnsaction is successful
      #     if innerTransactionError
      #       # await chomeSession.abortTransaction()
      #       return localFindError innerTransactionError
      #     for coll in delChromColls
      #       await deleteOne coll,chomeSession
      #     await UserDel.insertOne dataToDel,{session:chomeSession}
      #     if uuid
      #       await AdminOpLogs.insertOne {act:'deleteUser', ts, deleteU:uid,uid:uuid},{session:chomeSession}
      #     return cb null
      #   catch err
      #     await chomeSession.abortTransaction()
      #     return localFindError err

  ###
    Update User's push-notification token, location info (optional)
    user: if null, will create anonymous user
    pnloc:  "(ios|android):TOKEN[;LAT,LNG]"
  ###
  @updatePnLocation : ({req,user,pnloc})->
    throw new Error(MSG_STRINGS.BAD_PARAMETER) unless pnloc?.length > 10 and ((0 is pnloc.indexOf 'ios') or (0 is pnloc.indexOf 'android'))
    updateUserPn = (set)->
      if user?._id
        # 在删除PnToken之前，先检查pnBadge
        pnToken = await PnToken.findOne {_id: set.pn}
        
        # 如果存在pnBadge，将其添加到set中
        if pnToken?.pnBadge?
          set.pnBadge = pnToken.pnBadge
        
        # 更新用户信息
        await User.updateAndChangeSessionAsync {req, data:set, user}
        # 删除PnToken
        await PnToken.deleteMany {_id:set.pn}

      else if set.pn
        set.lts = new Date()
        set.ip = req.remoteIP()
        set.lang = req.locale()
        set.cip = req.isChinaIP()
        pn = set.pn
        delete set.pn
        await PnToken.updateOne {_id:pn}, {$set:set, $setOnInsert:{ts:new Date()}}, {upsert:true}
      else
        debug.error 'updatePnLocation,No user._id, no push token!'
        throw new Error(MSG_STRINGS.BAD_PARAMETER)
    [pn,loc] = pnloc.split ';'
    pn_user = await UserCol.findOne {pn:pn}
    set = pn:pn

    if loc
      [set.lat,set.lng] = (parseFloat v for v in loc.split(','))
  
    if not pn_user? # no pn_user or is this user, update current user
      return await updateUserPn set
    else if (pn_user._id?.toString() is user?._id?.toString())
      if loc
        return await updateUserPn set
      else
        return null
    else # pn_user isn't user
      if (not pn_user.eml?) and user
        # remove pn_user, update current user
        try
          await UserCol.deleteOne {_id:pn_user._id}, {safe:true}
        catch err
          debug.error err,'updatePnLocation,_id',pn_user._id
        return await updateUserPn set
      else # pn_user is not current user
        # remove pn from old pn_user
        try
          await UserCol.updateOne {_id:pn_user._id}, {$unset:{pn:1}}, {safe:true}
        catch err
          debug.error err,'updatePnLocation,_id',pn_user._id
        # update user pn.
        return await updateUserPn set

  # TODO:user_email在线上没有问题时deprecate softBounceOne
  @softBounceOne : (email,cb)->
    UserCol.findOneAndUpdate {eml:email},{$inc:{edmNoBounce:1}},{projection:{edmNoBounce:1},returnDocument:'after'},(err,u2)->
      if err
        debug.error err,'sofeBounceOne,eml:',email
      if u2?.edmNoBounce > 2
        User.setEdmNoByEmls [email],"Bounced #{u2.edmNoBounce}",cb

  @updateEdmStatus : (emails,detail)->
    UserCol.findToArray {eml:{$in:emails}},{fields:{_id:1}},(err,users)->
      if err
        return debug.error err,'updateEdmStatus,emls:',emails
      if users? and (Array.isArray users)
        ids = (u._id for u in users)
        UserProfile.updateMany {_id:{$in:ids}},{$push:{edm:{$each:[detail],$slice:-100}}},{multi:true,upsert:true},(err)->
          debug.error err,'updateEdmStatus,ids:',ids if err

  @getForumHistories:(id,cb)->
    q =
      uid:id
      tp:{$in:['forum','forumhist']}
    q.uid = {$in:id} if 'array'is typeof id
    UserLog.findToArray q,{sort:{'ts':-1},limit:100},cb
  @logForumHistory : (uid, {postid}, cb) ->
    opt = {
      uid: new ObjectId(uid),
      id: postid,
      tp: 'forum',
      isBulk: true
    }
    User.logHistory opt,cb
    # set =
    #   id: postid
    #   ts: new Date()
    #   uid: new ObjectId(id)
    #   tp:'forum'
    #   exp:new Date(Date.now()+31536000*1000)
    # UserLog.insertOne set,{upsert: true},cb

  # [sysadmin]
  @getEdmHistories:(id,cb)->
    q =
      logid:id
      tp:'edm'
    q.logid = {$in:id} if 'array'is typeof id
    UserLog.findToArray q,{sort:{'opents':-1},limit:100},cb

  @logEdmHistory = (uid, {emlid,logid},cb)->
    unless helpers.isObjectIDString(uid)
      return cb(MSG_STRINGS.BAD_PARAMETER)
    opt = {
      uid: new ObjectId(uid),
      id: emlid,
      tp: 'edm',
      logid
    }
    User.logHistory opt,cb
    # set =
    #   id: emlid
    #   opents: new Date()
    #   logid: logid
    #   uid: new ObjectId(id)
    #   tp:'edm'
    #   exp:new Date(Date.now()+31536000*1000)
    # UserLog.insertOne set,{upsert: true},cb

  @getProjectHistories:(id,cb)->
    q =
      uid:id
      tp:'project'
    q.uid = {$in:id} if 'array'is typeof id
    UserLog.findToArray q,{sort:{'ts':-1},limit:100},cb
  @logProjectHistory = (uid, {projId},cb)->
    opt = {
      uid: new ObjectId(uid),
      id: projId,
      tp: 'project'
      isBulk: true
    }
    User.logHistory opt,cb
    # set =
    #   id: new ObjectId(projId)
    #   ts: new Date()
    #   uid: new ObjectId(id)
    #   tp:'project'
    #   exp:new Date(Date.now()+31536000*1000)
    # UserLog.insertOne set,{upsert: true},cb

  @getViewedHistories:({id,page,limit},cb)->
    skip = (page or 0) * (limit or 20)
    q =
      uid:id
      tp:{$in:['property','rmlisting']}
    # q.uid = {$in:id} if 'array'is typeof id
    UserLog.findToArray q,{fields:{id:1},sort:{'mt':-1},limit,skip},cb

  @logRMListingHistory : (uid, ml_num)->
    opt = {
      uid: new ObjectId(uid),
      id: ml_num,
      tp: 'rmlisting'
      isBulk: true
    }
    User.logHistory opt

  @logPropertiesHistory : (uid, ml_num)->
    opt = {
      uid: new ObjectId(uid),
      id: ml_num,
      tp: 'property'
      isBulk: true
    }
    User.logHistory opt
    # set =
    #   id: ml_num
    #   ts: new Date()
    #   uid: new ObjectId(id)
    #   tp:'property'
    #   exp:new Date(Date.now()+31536000*1000)
    # UserLog.insertOne set,{upsert: true},(err)->
    #   debug.error err.toString() if err
    # return

  @logHistory:({uid,tp,id,logid,edmOption,isBulk},cb)->
    date = new Date()
    exp = libUser.getUserLogExpByTp tp
    set = {
      mt: date,
      exp
    }
    if logid
      set.logid = logid
    update =
      $set: set
      $push: {tss: { $each: [date], $slice: -10 }}
      $inc: {cnt:1}
    if edmOption
      update.$addToSet = edmOption:edmOption
    if isBulk #does not support cb and abuse
      gUserLogHis.push {filter:{uid,tp,id},update}
      return
    User.upsertUserLog {uid,tp,id,update},cb
  # bulk update records, ignore result and not invoke cb, useful for db logs
  @bulkUpdateUserLogs: (arrUpdates=[])->
    # console.log arrUpdates
    if not arrUpdates.length
      return
    try
      UserLog.bulkWrite arrUpdates
    catch err
      # NOTE: bulk update for non-important logs, if error no need to retry or restore
      debug.error err
  @upsertUserLog:({uid,tp,id,_id,update},cb)->
    UserLog.findOneAndUpdate {uid,tp,id},update,{fields:{cnt:1},upsert:true},(err,ret)->
      if err
        debug.error 'UserLog.findOneAndUpdate', err, ret
        return cb() if cb
      oldCnt = ret?.value?.cnt
      if oldCnt < 100
        cb() if cb
        return
        # TODO log abuse
      update = {
        $set: {"abuse.#{tp}.ts":new Date(),"abuse.#{tp}.th":100},
        $inc: {"abuse.#{tp}.cnt": 1}
      }
      UserProfile.updateOne {_id:uid},update,{upsert:true},(err,ret)->
        if err
          debug.error 'upsertUserLog UserProfile.updateOne',err
        cb(err) if cb

  @logEdmProperties:({cmty,uid,newProps,soldProps,changedProps,search},cb)->
    getPropsToSaveEdm = (edmProps)->
      ps = {}
      return null unless edmProps?.length
      for p in edmProps
        his = p.his
        lastHis = his[his.length-1] if his
        ps[p._id] = {
          status:p.status,
          lst: p.lst
        }
        if lastHis?.s is 'Pc'
          ps[p._id].pc = lastHis.c
          ps[p._id].n = lastHis.n
          ps[p._id].o = lastHis.o
      return ps
    edmSavedSearch = {}
    hasRecords = false
    if search
      edmSavedSearch.ts = search.ts
      edmSavedSearch.edmTs = new Date()
      edmSavedSearch.nm = search.nm
      edmSavedSearch.isFavCmty = search.isFavCmty
    if newProps?.length
      edmSavedSearch.new = getPropsToSaveEdm(newProps)
      hasRecords = true
    if soldProps?.length
      edmSavedSearch.off = getPropsToSaveEdm(soldProps)
      hasRecords = true
    if changedProps?.length
      edmSavedSearch.chgd = getPropsToSaveEdm(changedProps)
      hasRecords = true
    return unless hasRecords
    q =
      id: parseInt helpers.dateFormat(new Date(), 'YYYYMMDD')
      uid: new ObjectId(uid)
      tp: 'savedSearch'
    set =
      hasNewChange: true
      exp: libUser.getUserLogExpByTp 'savedSearch'
    update =
      #$set:q USE this will cause update faild but insert new record
      $push: edm: edmSavedSearch
      $set: set
    UserLog.findOneAndUpdate q, update, {upsert:true},cb

  @findListingsByEdmts:({savedSearch,user,edmts,type,edmTp,limit},cb)->
    query = {uid:user._id,ts:{$gt:edmts},tp:'propChg'}
    if edmTp
      if Array.isArray(edmTp) and edmTp.length
        query['src.k'] = {$in:edmTp}
      else if typeof edmTp is 'string'
        query['src.k'] = edmTp
    UserLog.findToArray query,{limit},(err,edmLogs)->
      if err
        debug.error 'findListingsByEdmts',err
        debug.error query
        return cb(err)
      props = []
      for log in edmLogs
        sourcesMap = {}
        for src in log.src
          #put source in to map {Community:['C1','C2']}
          if src?.k
            srcValue = src.v
            srcValue = [srcValue] if (typeof srcValue is 'string')
            srcKey = src?.k
            srcKey = 'Saved Home' if srcKey is 'Saved' #change name Saved to Saved Home for display
            if sourcesMap[srcKey]
              sourcesMap[srcKey] = sourcesMap[srcKey].concat(srcValue)
            else
              sourcesMap[srcKey] = srcValue
        sources = []
        for k,v of sourcesMap
          sources.push {k,v:Array.from(new Set(v))}
        props.push {_id:log.id,sources}
      cb(null,props)
  
  @findPropChangeFeed:({date,uid,tag,l10n,updateViewStatus})->
    projection = {sort:{date:-1}}
    query = {uid,tp:'propChg'}
    if tag isnt 'All'
      if k = @SOURCE_TYPE_REAR_MATCH[tag]
        if tag is 'Locations'
          query['src.k'] = {$in:k}
        else
          query['src.k'] = k
    if date
      if Array.isArray date
        dateQuery = Object.assign {}, query, {date:{$lt:date[date.length - 1]}}
      else
        query.date = date
    try
      if dateQuery or (not query.date)
        nextFeedQuery = dateQuery or query
        nextFeedLog = await UserLog.findOne nextFeedQuery,{sort:{date:-1}}
        return null unless nextFeedLog
        query.date = nextFeedLog.date
      propChangeFeeds = await UserLog.findToArray query,projection
      if updateViewStatus
        updateQuery = {uid,tp:'propChg',hasNewChange: true}
        await UserLog.updateMany updateQuery,{$set:{hasNewChange: false}}
      edmLog = {edm:{}}
      savedSearches = {}
      getSavedSearchNm = (log)->
        return '' unless log.src
        for src in log.src
          if src?.k is SOURCE_TYPE.SAVED_SEARCH
            if src?.v and src?.v[0]?.nm
              return src?.v[0]?.nm
            else
              return src?.v
        return ''
      formatedLogSrc = (src)->
        return null unless src
        ret = []
        srcObj = {}
        for s in src
          if s?.k
            srcObj[s.k] ?= []
            if s.k is SOURCE_TYPE.SAVED_SEARCH
              if s?.v and s?.v[0]?.nm
                srcObj[s.k] = srcObj[s.k].concat s.v
              else if s.v.length
                srcObj[s.k].push s.v
            else
              srcObj[s.k] = s.v
        for tag in Object.values(SOURCE_TYPE)
          if srcObj[tag]?.length
            transK = l10n tag
            if tag is SOURCE_TYPE.FAV
              transK = l10n tag,'favorite'
            ret.push {k:tag,v:srcObj[tag],transK}
        return ret
      for log in propChangeFeeds
        # {id,edm:{id:{pnSrc:}}}
        edmLog.id = log.date unless edmLog.id
        edmLog.edm[log.id] = {pnSrc:formatedLogSrc(log.src)}
      return edmLog
    catch err
      debug.error 'findPropChangeFeed', err
      throw err

  # NOTE: changeType,clnt not logged?
  @logPropChangeStatus:({propId,uid,changeType,src,pc,lst,status,clnt})->
    ###
    prop change log from watch prop and push notify
    {
      id:propId,
      uid,
      status:Price Change/new on market new/sold/chg,
      pc: for price change
      lst: for status change
      src:array of [{key:value}]Showing/Fav/View/Cmty,
      exp,
      ts,
      tp:property
    }
    ###
    isSavedSearch = (kvSrc, sources) ->
      if not kvSrc
        debug.error 'No k in src', kvSrc, sources
      return (kvSrc?.k is SOURCE_TYPE.SAVED_SEARCH)

    date = parseInt helpers.dateFormat(new Date(), 'YYYYMMDD')
    query = {
      date,
      uid,
      id:propId
    }
    update = {}
    set = {
      # date
      ts: new Date(),
      # uid,
      # id:propId,
      tp: 'propChg',
      hasNewChange:true,
      exp: libUser.getUserLogExpByTp 'pushFeeds',
      noDelBefore: new Date(Date.now()+TIME_MS_7_DAYS)
    }
    if status
      set.status = status
    if pc
      set.pc = pc
    if lst
      set.lst = lst
    # saved search单独处理,不能直接addToSet
    if not (srcIsSavedSearch = isSavedSearch(src[0], src))
      addToSet = {src:{$each:src}}
    # set has no src, if addToSet = {}, src will be null
    # if src[0].k is SOURCE_TYPE.SAVED_SEARCH
    #   addToSet = {}
    update['$set'] = set
    update['$addToSet'] = addToSet if addToSet

    try
      ret = await UserLog.findOneAndUpdate query, update, {upsert:true,returnDocument:'after'}
      srcHasSavedSearch = false
      value = ret.value
      # src不是savedSearch的情况下没有src
      if not (value?.src or srcIsSavedSearch)
        debug.error 'No src for user_log',update,src
      if value.src
        for s in value.src
          if isSavedSearch(s, value.src)
            srcHasSavedSearch = true
      #saved search单独处理，需检查更新后的数据是否存在 saved search
      if srcIsSavedSearch
        if srcHasSavedSearch
          query['src.k'] = SOURCE_TYPE.SAVED_SEARCH
          addToSet = 'src.$.v':src[0].v[0]
        else
          addToSet = {src:{$each:src}}
        await UserLog.updateOne query, {$addToSet:addToSet}
      updatedExisting = ret?.lastErrorObject?.updatedExisting
      if updatedExisting
        return {updatedExisting}
      #if insert new
      userProfileQuery = {_id:uid}
      userProfile = await UserProfile.findOne userProfileQuery,{projection:{propLogCnt:1}}
      # propLogCnt is estimate of how many logs
      # TODO: call every 10 mins, no need to run on every prop change
      if userProfile?.propLogCnt > MAX_PROP_LOG_COUNT
        userLogQuery = {uid,tp:'propChg'}
        # get real user log count
        userLogCount = await UserLog.countDocuments userLogQuery
        if userLogCount > MAX_PROP_LOG_COUNT
          # if user log count > MAX_PROP_LOG_COUNT, keep the last MAX_PROP_LOG_COUNT records
          numberToDelete = userLogCount - MAX_PROP_LOG_COUNT
          # noDelBefore: a timestamp that marks this record cannot be deleted before this date
          toDelRecords = await UserLog.findToArray userLogQuery,{fields:{_id:1,_mt:1},sort:{_mt:1},skip:numberToDelete,limit:1}
          if mt = toDelRecords?[0]?._mt
            debug.error "User #{uid} has more than #{MAX_PROP_LOG_COUNT} user_log prop changes"
            # delete old logs from db through timestamp
            await UserLog.deleteMany {
              _mt:{$lt:mt},
              uid,
              tp:'propChg',
              noDelBefore:{$lt:new Date()}
            }
            await UserProfile.updateOne userProfileQuery,{$set:{propLogCnt:0}}
      else
        await UserProfile.updateOne userProfileQuery,{$inc:{propLogCnt:1}}
      return {updatedExisting}
    catch err
      debug.error 'logPropChangeStatus', err
      debug.error 'AddToSet: ', addToSet
      debug.error 'Set: ', set
      throw err

  @findEdmLastUpdateTs:(uid,cb)->
    return cb null,{} unless uid
    fields = {hasNewChange:1,ts:1}
    query = {uid,tp:'propChg'}
    UserLog.findOne query, {fields,sort:{date:-1,ts:-1}},(err,logs)->
      if err
        debug.error 'findEdmLastUpdateTs',err
        return cb err,null
      return cb null,{} unless logs
      returnLog =
        ts:logs.ts
        hasNewChange:logs.hasNewChange or false
      cb null,returnLog

  @addCRMID : ({req,uid,crmid},cb)->
    User.findById uid,{},(err,crmMatchedUser)->
      if err
        debug.error err,'addCRMID,uid:',uid
        return cb "Can not find user #{uid}",null
      #update user with crmID
      update = $addToSet:{crmID:crmid}
      User.updateAndChangeSession {req,user:crmMatchedUser,data:update},cb

  # [sysAdmin]
  @updateSwitchUsers:({req,realUser,switchUsers},cb)->
    User.updateAndChangeSession {req,user:realUser,data:{switchUsers:switchUsers}},cb

  @updateDGids:({req,dGids},cb)->
    objectGids = []
    if dGids
      for g in dGids
        objectGids.push new ObjectId(g)
    update = {dGids:objectGids}
    User.updateAndChangeSession {req,data:update},cb

  @updateSpeakLanguage:({req,splang},cb)->
    User.updateAndChangeSession {req,data:{splang,mt:new Date()}},cb

  @updateCategory:({req,categories=[],sas},cb)->
    categoriesList = []
    mainCate = libUser.getYellowpageMainCate()
    subCate = libUser.getYellowpageSub()
    for obj in categories
      category =  {id:'', sub:[]}
      # NOTE: isValid id
      category.id = obj.id if obj.id in mainCate
      if obj.sub
        for sub in obj.sub
          # NOTE: is valid sub
          category.sub.push sub if sub in subCate
      # debug.debug category
      categoriesList.push category if category.id and category.sub?.length>0

    update = {category: categoriesList}
    update.mt = new Date()
    update.sas = (if Array.isArray(sas) then sas else []).slice(0,10)
    User.updateAndChangeSession {req,data:update},cb
  
  @findSavedSearch:(id,cb)->
    User.findProfileById id,{projection:{savedSearch:1}}, (err, profile) ->
      if err
        debug.error 'findSavedSearch',err
        return cb(err,null)
      savedSearch = profile?.savedSearch or []
      cb null,savedSearch
  
  @findFavCmtys:(id,cb)->
    try
      query = {uid:id,tp:'cmty'}
      cmtys = await UserWatchLocationCol.findToArray query, {fields:{uaddr:1,propType:1,watchEvent:1}}
      favCmtys={}
      for cmty in cmtys
        obj = {}
        obj.propType = cmty.propType if cmty.propType
        obj.range = cmty.range if cmty.range
        obj.watchEvent = cmty.watchEvent if cmty.watchEvent
        favCmtys[cmty.uaddr] = {watching:obj}
      return cb(null,favCmtys)
    catch err
      debug.error 'findFavCmtys',id, err
      cb(err)
  
  @isCmtyFavedByUser:({cmtyId,uid},cb)->
    UserProfile.findOne {_id:uid,'favCmtys.nm':cmtyId},{fields:{favCmtys:1}},(err,userProfile)->
      return cb(err) if err
      found = if userProfile?.favCmtys?.length > 0 then true else false
      cb null,found

  @addSavedSearch : (id,{q,readable},cb)->
    delete q.page #ignore page for savedsearch
    opt =
      Coll : UserProfile
      param:{upsert:true}
      update : $push:{savedSearch:{
      $each:[{k:q, r:readable, ts:new Date(),sbscb:false}],
      $slice:-USER_SAVE_SEARCH_LIMIT
      }}
    User.updateOneById id,opt,cb

  @updateSavedSearch : (id,{ts,nm,sbscb,clnt,lang})->
    userP = await UserProfile.findOne {_id:id},{projection:{savedSearch:1}}
    if not userP
      throw new Error(MSG_STRINGS.NOT_FOUND)

    ts = new Date(ts) or new Date()
    finalName = nm
    if userP.savedSearch?.length
      # 检查重名,对重名自动添加后缀
      finalName = libUser.checkSavedSearchAndRenameDupName {arr:userP.savedSearch,name:nm,ts}

    # q.id = 'fail remove flag'
    update =
      $set:
        'savedSearch.$.nm':finalName
        'savedSearch.$.sbscb':sbscb
    update.$set['savedSearch.$.clnt']=clnt if clnt
    update.$set['savedSearch.$.lang']=lang if lang
    if clnt is null
      update.$unset={}
      update.$unset['savedSearch.$.clnt']=1
      update.$unset['savedSearch.$.lang']=1
    # return error('No Data') unless ts #or q
    await UserProfile.updateOne {_id:id,'savedSearch.ts':ts}, update
    isDuplicatedName = if finalName is nm then false else true
    return {isDuplicatedName,nm:finalName}

  # NOTE: 根据权限检查sbscb的savedsearch数量,判断能否开启订阅
  @checkSbscbByRole : ({uid,isVip})->
    if isVip
      return true
    sbscbCnt = 0
    userProfile = await UserProfile.findOne {_id:uid},{projection:{savedSearch:1}}
    for savedSearch in userProfile.savedSearch
      if savedSearch.sbscb is true
        sbscbCnt++
    if sbscbCnt < LIMIT_SAVEDSEARCH_SBS
      return true
    return false

  # NOTE: update saved search from native map save btn
  @updateSavedSearchKandReadable : (id,{k,r,ts,idx},cb)->
    ts = new Date(ts) or new Date()
    # q.id = 'fail remove flag'
    if not Array.isArray r
      return cb MSG_STRINGS.BAD_PARAMETER
    if (typeof k) is not 'object'
      return cb MSG_STRINGS.BAD_PARAMETER
    if not (Object.keys(k).length and r.length)
      return cb MSG_STRINGS.BAD_PARAMETER
    if k?.bnds and not helpers.isValidPolygonBnds(k.bnds)
      return cb MSG_STRINGS.BAD_PARAMETER
    if k?.bnds?.length
    # 将string转为number,es查询需要使用number类型
      k.bnds = k.bnds.map(Number)
    update =
      $set:
        'savedSearch.$.k':k
        'savedSearch.$.r':r
    # return error('No Data') unless ts #or q
    UserProfile.updateOne {_id:id,'savedSearch.ts':ts}, update,cb

  @deleteSavedSearch : (id,{ts},cb)->
    opt =
      Coll : UserProfile
      update : {$pull:{'savedSearch':{ts:ts}}}
      param : {}
    User.updateOneById id,opt,cb

  @addFavorite:(uid,{tp,
      id,# 传入需要保存的id
      },cb)->
    add ={}
    if /^[a-zA-Z0-9\-]+$/.test tp
      add['fav'+tp] = id
    update = $addToSet: add
    UserProfile.updateOne {_id:uid}, {$addToSet: add},{upsert:true},cb
    # User.updateOneById uid, {update,param:{upsert:true},Coll:UserProfile},cb

  @deleteFavorite:(uid,{tp,id},cb)->
    pull ={}
    if /^[a-zA-Z0-9\-]+$/.test tp
      pull['fav'+tp] = id
    update = $pull:pull
    UserProfile.updateOne {_id:uid}, {$pull:pull},{upsert:true},cb
    # User.updateOneById uid, {update,param:{upsert:true},Coll:UserProfile},cb

  @getFavoriteProvidersByName:(name,cb)->
    _tryParse = (mbl)->
      try
        if (mbl = mbl.replace(/[^0-9]/g,''))?.length > 0
          if pmbl = parseInt(mbl)
            return pmbl
      catch e
      # do nothing
      return null
    q = []
    if (arr = name.split(' '))?.length > 1
      reversedName = arr.reverse().join(' ')
      nmReg1 = helpers.str2reg(name,true)
      nmReg2 = helpers.str2reg(reversedName,true)
      q = [{nm_en:nmReg1},{nm_en:nmReg2},{nm_zh:nmReg1},{nm_zh:nmReg2},{nm:nmReg1},{nm:nmReg2}]
    else
      nameReg = helpers.str2reg(name,true)
      q = [{eml:nameReg},{nm_en:nameReg},{nm_zh:nameReg},{nm:nameReg}, {mbl:nameReg},{eml:nameReg}]
    if num = _tryParse(name)
      q.push {mbl:num}
    query = { $or:q,  tstAc:{$exists:false}, roles:'merchant'}

    qOpt = {limit:20 ,projection:User.CATEGORY_PROVIDER_FIELDS}
    User.getList query,qOpt,cb

  @logUserHasWechat:({has,user,req},cb)->
    # TODO:need this?
    # if user.wxavt and not has
    #   return cb null
    has = if has then 1 else 0
    User.updateAndChangeSession {req,data:{hasWechat:has}},(err,ret)->
      if err
        debug.error err,'matchUserDeviceId,uid:',user?._id
      return cb(err,ret) if cb

  # NOTE: id could be user input, hackable
  @matchUserDeviceId:({req,id,user},cb)->
    # UserCol.updateOne {_id:user._id}, {$set:{devId:id}},(err,ret)->
    debug.error 'matchUserDeviceId: no user' unless user?._id
    User.updateAndChangeSession {req,data:{devId:id}},(err,ret)->
      if err
        debug.error err,'matchUserDeviceId,uid:',user?._id
      DevUniqID.updateOne {_id:id}, {$set:{uid:user._id}},{upsert:true},(err,ret)->
        if err
          debug.error err,'matchUserDeviceId,uid:',user?._id
        return cb(err,ret) if cb

  # NOTE: useragent set by native, hard to change
  @logUserDevUniqID:({id,user,ip,ua,cookie={}},cb)->
    return unless id
    # debug.error 'logUserDevUniqID: no user' unless user?._id
    id += ''
    formatedCookie = Object.assign {},cookie
    for k,v of formatedCookie
      if /\.|\$|\\/.test k
        newKey = k.replace('\\', '\\\\').replace('\$', '\\u0024').replace('.', '\\u002e')
        formatedCookie[newKey] = v
        delete formatedCookie[k]
    set = {id,mt:new Date(),ip,ua,cookie:formatedCookie}
    # debug.debug '+++++++',set
    setOnInsert = {ts:new Date()}
    DevUniqID.updateOne {_id:id},{$set:set, $setOnInsert:setOnInsert},{upsert:true},(err,ret)->
      if err
        debug.error err,'logUserDevUniqID, DevUniqID, uid:',user?._id
        return cb(err) if cb
      if user
        UserCol.updateOne {_id:user._id}, {$set:{devId:id}},(err,ret)->
          if err
            debug.error err,'logUserDevUniqID, UserCol, uid:',user?._id
          return cb(err,ret) if cb

  @getMyFavoriteProviders:({req,resp,faved,page=0,limit=20},cb)->
    if faved
      faved = faved.split(',')
      return cb null,[] unless faved?.length
      q = {_id:{$in:faved}}
      User.getListByIds faved,{projection:User.CATEGORY_PROVIDER_FIELDS},cb
    else
      User.appAuth {req,resp}, (user)->
        return cb MSG_STRINGS.BAD_PARAMETER,null unless user
        User.findProfileById user._id,{projection:{favProviders:1}}, (err, profile) ->
          return gFindError err,{f:'getMyFavoriteProviders',id:user._id},cb if err
          faved = profile?.favProviders
          return cb null,[] unless faved?.length
          faved = faved.slice page * limit, (page + 1) * limit
          q = {_id:{$in:faved}}
          User.getListByIds faved,{projection:User.CATEGORY_PROVIDER_FIELDS},cb

  # NOTE: for prov-admin
  @switchAdminAction:({b,u,isSameProv,isVip,uid,ueml},cb)->
    #添加权限判断和action判断
    userAct = null
    roleAct = {}
    switch b.act
      when 'check'
        userInfo = User.checkUserInfo u
        return cb {userInfo}
      when 'make-vip','make-vip-plus','make-vip-alliance'
        prov = u.rst_prov
        role = 'vip'
        if b.act is 'make-vip-plus'
          role = 'vip_plus'
        else if b.act is 'make-vip-alliance'
          role = 'vip_alliance'
        return cb {err:MSG_STRINGS.NO_PROV} unless prov
        unless Array.isArray prov
          prov = [prov]
        userAct = {
          roles:[role]
          rst_prov:prov
        }
      when 'revoke-vip'
        if isVip and not isSameProv
          return cb {err:MSG_STRINGS.DIFF_PROV}
        roleAct.del = ['vip','vip_plus','vip_alliance','vip_client']
    [method,data] = if userAct then [User.updateAndChangeSession,userAct] else [User.updateRoles,roleAct]
    AdminOpLogs.insertOne {act:b.act, ts:new Date(),uid,target:b.eml,ueml,op:data}
    cb {method,data}
  # NOTE: inreal admin available commands
  # @switchInRealAdminActionAsync =  functionHelper.addAsyncSupport User.switchInRealAdminAction
  @switchInRealAdminAction:({b,u,uid,ueml})->
    #添加权限判断和action判断
    # TODO: DRY with switchAdminAction and switchAction
    userAct = null
    roleAct = {}
    switch b.act
      when 'check'
        userInfo = User.checkUserInfo u
        return {userInfo}
      when 'make-vip','make-vip-plus','make-vip-alliance'
        role = 'vip'
        if b.act is 'make-vip-plus'
          role = 'vip_plus'
        else if b.act is 'make-vip-alliance'
          role = 'vip_alliance'
        userAct = {
          roles:[role]
        }
      when 'revoke-vip'
        roleAct.del = ['vip','vip_plus','vip_alliance','vip_client']
    [method,data] = if userAct then ['updateAndChangeSession',userAct] else ['updateRoles',roleAct]
    AdminOpLogs.insertOne {act:b.act, ts:new Date(),uid,target:b.eml,ueml,op:data}
    return {method,data}
  # [sysAdmin]
  @switchActionAsync =  functionHelper.addAsyncSupport User.switchAction
  @switchAction:({b,u,uid,ueml},cb)->
    ueml = ueml.toLowerCase()
    #添加权限判断和action判断
    userAct = null
    roleAct = {}
    switch b.act
      when 'check'
        userInfo = User.checkUserInfo u
        if not (u.devId or u.pn)
          return cb null,{userInfo}
        return User.isChatBlocked {user:u},(ret)->
          userInfo.isChatBlocked = ret[0]
          userInfo.whichBlock = ret[1]
          User.isForumCommentBlocked {user:u},(ret)->
            userInfo.isForumCommentBlocked = ret[0]
            userInfo.whichForumCommentBlock = ret[1]
            return cb null,{userInfo}
        break
      when 'no-edm'
        userAct = {edmNo:true}
      when 'add-edm'
        userAct = {edmNo:null}
      when 'set-id'
        unless (aid = b.id)?.length >= 4
          return cb MSG_STRINGS.NO_TREB_ID
        unless /^TRB/.test aid
          aid = 'TRB' + aid
        userAct = {aid:aid}
      when 'set-rid'
        unless (rid = b.id)?.length >= 4
          return cb MSG_STRINGS.NO_TREB_ID
        unless /^DDF/.test rid
          rid = 'DDF' + rid
        userAct = {ddf_rid:rid}
      when 'set-lic'
        unless (lic = b.lic)?.length >= 4
          return cb MSG_STRINGS.NO_LIC
        # unless /^LIC/.test lic
        #   lic = 'TRB' + lic
        userAct = {lic:lic}
      when 'set-cpmnm'
        unless (cpmnm = b.cpmnm)?.length >= 1
          return cb MSG_STRINGS.NO_CPM_NAME
        # unless /^LIC/.test lic
        #   lic = 'TRB' + lic
        userAct = {cpmnm:cpmnm}
      when 'add-crmNoAssign'
        roleAct.add = '_crmNoAssign'
      when 'revoke-crmNoAssign'
        roleAct.del = '_crmNoAssign'
      when 'add-crmAdmin'
        roleAct.add = '_crmAdmin'
      when 'revoke-crmAdmin'
        roleAct.del = '_crmAdmin'
      when 'make-create-project'
        roleAct.add = '_create_proj'
      when 'revoke-create-project'
        roleAct.del = '_create_proj'
      when 'make-topup'
        roleAct.add = '_topup'
      when 'revoke-topup'
        roleAct.del = '_topup'
      when 'make-market'
        roleAct.add = '_market'
      when 'revoke-market'
        roleAct.del = '_market'
      when 'make-realtor'
        roleAct.add = 'realtor'
      when 'make-vip'
        roleAct.add = 'vip'
      when 'make-ana-admin'
        roleAct.add = '_userStats'
      when 'make-cpm-admin'
        roleAct.add = '_cpm'
      when 'revoke-cpm-admin'
        roleAct.del = ['_cpm']
      when 'revoke-ana-admin'
        roleAct.del = ['_userStats']
      when 'make-vip-plus'
        roleAct.add = 'vip_plus'
      when 'make-treb'
        roleAct.add = '_treb'
      when 'make-treb2'
        roleAct.add = '_treb2'
      when 'make-vip-alliance'
        roleAct.add = 'vip_alliance'
      when 'make-vip-c'
        roleAct.add = 'vip_client'
      when 'make-app-editor'
        roleAct.add = '_appEditor'
      when 'revoke-vip'
        roleAct.del = ['vip','vip_plus','vip_alliance','vip_client']
      when 'revoke-treb'
        roleAct.del = ['_treb']
      when 'revoke-treb2'
        roleAct.del = ['_treb2']
      when 'revoke-realtor'
        roleAct.del = ['realtor']
      when 'revoke-app-editor'
        roleAct.del = ['_appEditor']
      when 'revoke-roles-all'
        roleAct.del = 'all'
      when 'make-forum-admin'
        roleAct.add = '_forum'
      when 'make-news-admin'
        roleAct.add = '_news'
      when 'make-mluser'
        roleAct.add = 'MLUser'
      when 'revoke-mluser'
        roleAct.del = 'MLUser'
      when 'revoke-forum-admin'
        roleAct.del = '_forum'
      when 'revoke-news-admin'
        roleAct.del = '_news'
      when 'make-forum-commentator'
        roleAct.add = 'commentator'
      when 'revoke-forum-commentator'
        roleAct.del = 'commentator'
      when 'make-form-organizer'
        roleAct.add = 'organizer'
      when 'revoke-form-organizer'
        roleAct.del = 'organizer'
      when 'make-app-splash-admin'
        roleAct.add = '_splashAdmin'
      when 'revoke-app-splash-admin'
        roleAct.del = '_splashAdmin'
      when 'remove-following'
        userAct = {flwng:null}
      when 'remove-wxuid'
        userAct = {wxuid:null,wxAppOID:null,wxMobOID:null,wxavt:null}
      # when 'admin-op-hist'
      #   AdminOpLogs.findToArray {},{sort:{_id:-1}},(err,list)->
      #     list ?= []
      #     if err
      #       debug.error err
      #       return cb {err}
      #     cb {logList:list}
      #   return
      when 'rm-rst-prov'
        userAct = {rst_prov:null}
      when 'prov-admin','rst-prov'
        # debug.debug '++++',b,b.prov
        # prov = b['prov[]']
        prov = cityHelper.getProvAbbrName b.prov
        return cb MSG_STRINGS.NO_PROV unless prov
        unless Array.isArray prov
          prov = [prov]
        userAct = {
          rst_prov:prov
        }
        if b.act is 'prov-admin'
          userAct.roles = ['_provAdmin']
      when 'add-noFltr'
        userAct = {noFltr:true}
      when 'remove-noFltr'
        userAct = {noFltr:null}
      when 'block-chat'
        userAct = {blkCht:true}
      when 'unblock-chat'
        userAct = {blkCht:null}
      when 'make-stigmatized-admin'
        roleAct.add = '_stigmaAdmin'
      when 'revoke-stigmatized-admin'
        roleAct.del = '_stigmaAdmin'
      when 'make-geo-admin'
        roleAct.add = '_geo'
      when 'revoke-geo-admin'
        roleAct.del = '_geo'
      when 'make-agreement-admin'
        roleAct.add = '_agreementAdmin'
      when 'revoke-agreement-admin'
        roleAct.del = '_agreementAdmin'
      when 'make-assign-admin'
        roleAct.add = '_assignAdmin'
      when 'revoke-assign-admin'
        roleAct.del = '_assignAdmin'
      when 'make-inrealAdmin-admin'
        roleAct.add = '_inrealAdmin'
      when 'revoke-inrealAdmin-admin'
        roleAct.del = '_inrealAdmin'
      when 'make-note-admin'
        roleAct.add = '_noteAdmin'
      when 'revoke-note-admin'
        roleAct.del = '_noteAdmin'
      when 'make-token-admin'
        roleAct.add = '_tokenAdmin'
      when 'revoke-token-admin'
        roleAct.del = '_tokenAdmin'
      when 'make-showing-admin'
        roleAct.add = '_showingAdmin'
      when 'revoke-showing-admin'
        roleAct.del = '_showingAdmin'
      when 'make-claim-admin'
        roleAct.add = '_claimAdmin'
      when 'revoke-claim-admin'
        roleAct.del = '_claimAdmin'
      when 'make-verify-realtor'
        if libUser.hasRole 'merchant',u
          return cb 'Merchant can not verify realtor'
        userAct = {waitingVerifyAgent: true}
      when 'revoke-verify-realtor'
        userAct = {waitingVerifyAgent: false}
    [method,data] = if userAct then ['updateAndChangeSession',userAct] else ['updateRoles',roleAct]
    AdminOpLogs.insertOne {act:b.act, ts:new Date(),uid,target:b.eml,ueml,op:data}
    cb null,{method,data}

  @updateAgentId:({req,aid,bid,user},cb)->
    User.updateAndChangeSession {req,user,data:{aid:aid,bid:bid}},cb

  @deleteFollowing:(req,cb)->
    return User.updateAndChangeSession {req,data:{flwng:null}},cb

  @deleteRoles:(req,cb)->
    return User.updateAndChangeSession {req,data:{roles:null}},cb

  @deleteWXuid:(req,cb)->
    return User.updateAndChangeSession {req,data:{wxuid:null}},cb

  @saveMblVerificationCode:({id,req,body={}},cb)->
    data =
      verify : body.verify
      mbl2v  : body.mbl2v
    # UserProfile.updateOne {_id:id},{$set:data},{upsert:true},cb
    User.updateAndChangeSession {req,data},cb


  @mblVerified:({req,body={},user},cb)->
    data = {}
    data.rid = body.rid
    data.lid = body.lid if body.lid
    data.mbl = body.mbl2v
    data.mbl2v = null
    data.mblvcd = null
    data.verify ?= {}
    data.verify.mblvcd = body.mbl2v
    data.verify.mblvts = Date.now()
    data.mblvts = Date.now()
    data.verify.vfd = true
    data.roles = user.roles?.slice() # make a copy, don't use refer. otherwise change data.roles will change the user object too.
    # force set cpny and addr
    data.pstn = body.pstn if body.pstn
    data.cpny_en = body.cpny_en if  body.cpny_en
    data.cpny_zh = body.cpny_zh if  body.cpny_zh
    data.addr = body.addr
    data.tel = body.tel if body.tel
    data.fax = body.fax if body.fax
    data.cpny_wb = body.cpny_wb if body.cony_wb
    data.sas = (if Array.isArray(body.sas) then body.sas else []).slice(0,10)
    role = 'realtor'
    if body.tp in ['realtor', 'merchant']
      role = body.tp
    if body.tel
      data.tel = body.tel
    #add roles + 'realtor'
    if user.roles? and Array.isArray user.roles
      if user.roles.indexOf(role) < 0
        data.roles.push(role)
    else
      data.roles = [role]
    if body.tp is'visitor' #普通客户验证手机号不更新权限
      delete data.roles
    if body.tp is 'realtor'
      data.waitingVerifyAgent = false
    User.updateAndChangeSession {req,data},cb

  @updateMobel:({req,mbl},cb)->
    User.updateAndChangeSession {req,data:{mbl:mbl}},cb

  @updateLanguage:({req,lang},cb)->
    User.updateAndChangeSession {req,data:{locale:lang}},cb

  @updateCity:({req,newCity},cb)->
    User.updateAndChangeSession {req,data:{city:newCity}},cb

  @updateImage:({req,tp,avtUrl},cb)->
    avtUrl = libPropertyImage.removeHostFromUrl avtUrl
    data = {}
    switch tp
      when 'avator'
        data = {avt:avtUrl}
      when 'qrcd'
        data = {qrcd:avtUrl}
      when 'grpqrcd'
        data = {grpqrcd:avtUrl}
      else
        data = {avt:avtUrl}
    User.updateAndChangeSession {req,data},(err,user)->
      return cb err unless tp is 'avator'
      debug.error 'updateImage: no user' unless user?._id
      libUser.updateProfileStars {User:UserCol,Login:Auth,id:user._id},(err,stars)->
        user.stars = stars
        cb null,user

  @updatePostNotification:({id,body={}},cb)->
    set={}
    unset={}
    data={}
    for i in User.NOTIFICATION_PN_FIELDS
      # debug.debug body[i]
      if body[i] is 1
        set[i] = 1
      else if body[i] is 0
        unset[i] = 1
    if Object.keys(set)?.length > 0
      data.$set = set
    if Object.keys(unset)?.length > 0
      data.$unset = unset
    # debug.debug data
    UserProfile.updateOne {_id:id},data,{upsert:true},cb

  @addSubscribeCity:({req,city},cb)->
    d = {city:city.o, prov:city.prov, lat:city.lat, lng:city.lng}
    data = {$push:{}}
    data.$push.cities =
      { $each: [d], $slice: -10 }
    User.updateAndChangeSession {req,data},cb

  @unSubscribeCity:({req,city,prov},cb)->
    data = {$pull : {cities:{'city' : city.o,'prov':prov, lat:city.lat, lng:city.lng}}}
    User.updateAndChangeSession {req,data},cb

  @set58 : (id, {wbUser}, cb)->
    opt =
      update : {$set:{'58.58uid':wbUser.uid,'58.58token':wbUser.access_token}}
      Coll:UserProfile
      param:{upsert:true}
    User.updateOneById id,opt,cb

  @update58:({id,wbUser},cb)->
    data =
      '58uid': wbUser.uid
      '58token': wbUser.access_token
      '58rtkn': wbUser.refresh_token
      '58exp': helpers.dateAdd new Date(),{days:parseInt(wbUser.expires_in)}
    opt =
      update:
        $set:update
      Coll:UserProfile
      param:{upsert:true}
    User.updateOneById id,opt,cb

  @updateProfile:({req,user,body},cb)->
    #updateable fields, total 19, not includes rid
    fields = ['nm','id','wx','cpny','cpny_pstn','tel','mbl','hmbl',
    'sgn','sex','web','itr','fax','addr','fn','ln','wurl','cpny_wb','fburl',
    'twturl', 'qq', 'wxgrp', 'wxgrpnm', 'mblalt','nm_en','nm_zh','cpny_zh','cpny_en','splang']
    update = {}
    body?={}
    debug.error 'updateProfile: no user' unless user?._id
    for k in fields
      if (body.hasOwnProperty(k))
        if (user[k] isnt body[k])
          update[k] ?= body[k]
          if 'string' is typeof body[k]
            update[k] = helpers.htmlEscape body[k]
      else
        #delete this field
        update[k] = null
    # user canot update these fields here
    delete update.roles
    delete update.avt
    delete update.qrcd
    delete update.eml
    update.mt = new Date()
    User.updateAndChangeSession {req,data:update,user},(err,user)->
      libUser.updateProfileStars {User:UserCol,Login:Auth,id:user._id},(err,stars)->
        user.stars = stars
        cb null,user

  @editOtherProfile:({user,body={}},cb)->
    fields = ['wpHost','wpUsername','wpPwd','wpSecret']
      # debug.debug user
    update = {}
    for k in fields
      # debug.debug "=================: " + typeof body[k]
      if (body.hasOwnProperty(k))
        if (user[k] isnt body[k])
          update[k] ?= body[k]
          if 'string' is typeof body[k]
            update[k] = helpers.htmlEscape body[k]
      else
        #delete this field
        update[k] = null
    # user canot update these fields here
    update.mt = new Date()
    # debug.debug "------------>"
    # debug.debug update
    # wecard id check in update user
    opt =
      update:
        $set:update
      param:{upsert: true}
      Coll:UserProfile
    User.updateOneById user._id,opt,cb
# group

  # [status]
  @getRegisters = (cb)->
    # UserCol.group ((doc)-> {n: doc._id.getTimestamp().setHours(0,0,0,0).valueOf()}),
    #   {_id:$gt: objectIdWithTimestamp(new Date(Date.now() - 15 * 24 * 3600000))},
    #   {v:0,r:0,f:0},
    #   "function (u,col){col.v++;if(u.roles && (u.roles.indexOf('realtor') >= 0)){col.r++;}else if(u.flwng){col.f++;}}",
    #   (err,results)->
    #     if err then debug.error err
    #     if results
    #       for n in results
    #         n.n = helpers.dateFormat new Date(n.n),'YY/MM/DD'
    #         n.v = "#{n.r}/#{n.f}/#{n.v}"
    #       results.sort _sortor
    #       #debug results
    #       cb results
    #     else
    #       cb []
    UserCol.aggregate ([
      {$match:{_id:$gt: UserCol.objectIdWithTimestamp(new Date(Date.now() - 15 * 24 * 3600000))}},
      {$group:{
        _id: {$dateToString:{format:'%Y/%m/%d',date:'$_id',timezone:'America/Toronto'}} # {year:{$year:'$_id'},month:{$month:'$_id'},day:{$dayOfMonth:'$_id'}},
        v:{$sum:1},
        r:{$sum:{$cond:[{$in:['realtor',{$ifNull:['$roles',[]]}]},1,0]}},
        f:{$sum:{$cond:['$flwng',1,0]}},
        ch:{$sum:{$cond:[{$eq:['$cip',true]},1,0]}},
        web:{$sum:{$cond:[{$eq:['$src','browser']},1,0]}}
        ios:{$sum:{$cond:[{$eq:[{$substr:['$src',0,6]},'iphone']},1,0]}}
        adrd:{$sum:{$cond:[{$eq:[{$substr:['$src',0,7]},'android']},1,0]}}
        }},
      ]),{cursor:{batchSize:0}},(err,results)->
        if err then debug.error err,'getRegisters'
        if results
          for n in results
            n.n = n._id #n._id.year + '/' + d2(n._id.month) + '/' + d2(n._id.day) #helpers.dateFormat new Date(n.n),'YY/MM/DD'
            n.v = "#{n.v-n.ch}.#{n.ch}|#{n.ios}.#{n.adrd}.#{n.web}.#{n.v-n.web-n.ios-n.adrd}|#{n.r}.#{n.f}|#{n.v}"
            delete n._id
          results.sort _sortor
          results.unshift {n:'as',v:'N.Ch|iOS.ANDR.Web.O|R.F|All'}
          # debug.info 'Reg:'
          # debug.info results
          cb results
        else
          cb []

  # [status]
  @getLastAccess = (cb)->
    # thirtyDays = 31 * 24 * 3600000 #15 * 24 * 3600000
    oneYear = 366 * 24 * 3600000
    UserCol.aggregate [
      {$match:{lts:$gt:(new Date(Date.now() - oneYear))}},
      {$group:{
        _id: {year:{$year:'$lts'},month:{$month:'$lts'},day:{$dayOfMonth:'$lts'}},
        c:{$sum:1},
        r:{$sum:{$cond:[{$in:['realtor',{$ifNull:['$roles',[]]}]},1,0]}},
        f:{$sum:{$cond:['$flwng',1,0]}},
        ch:{$sum:{$cond:[{$eq:['$cip',true]},1,0]}},
        web:{$sum:{$cond:[{$eq:['$src','browser']},1,0]}}
        ios:{$sum:{$cond:[{$eq:[{$substr:['$src',0,6]},'iphone']},1,0]}}
        adrd:{$sum:{$cond:[{$eq:[{$substr:['$src',0,7]},'android']},1,0]}}
        }}
      ],{cursor:{batchSize:0}},(err,results)->
        if err then debug.error err,'getLastAccess'
        if results
          emptyAc = ->
            Object.assign {},{c:0,r:0,f:0,ch:0,web:0,ios:0,adrd:0}
          accumulate = (ac,nr)->
            for k,v of ac
              ac[k] = ac[k] + nr[k]
            null
          disp = (n)-> "#{n.c-n.ch}.#{n.ch}|#{n.ios}.#{n.adrd}.#{n.web}.#{n.c-n.web-n.ios-n.adrd}|#{n.r}.#{n.f}|#{n.c}"
          ac7 = emptyAc()
          ac14 = emptyAc()
          ac30 = emptyAc()
          ac365 = emptyAc()
          acThisMonth = emptyAc()
          acThisYear = emptyAc()
          for n,i in results
            n.n = n._id.year + '/' + helpers.zeroPad(n._id.month,2) + '/' + helpers.zeroPad(n._id.day,2) #helpers.dateFormat new Date(n.n),'YY/MM/DD'
            #n.v = '#{n.r}/#{n.f}/#{n.v}'
            n.v = disp n
            delete n._id
          # sort by n.n, which is ts 2022/09/10
          results.sort _sortor
          for n,i in results
            if i < 7
              accumulate ac7,n
            if i < 14
              accumulate ac14,n
            if i < 31
              accumulate ac30,n
            if i < 366
              accumulate ac365,n
            if _isThisMonth(n.n)
              accumulate acThisMonth,n
            if _isThisYear(n.n)
              accumulate acThisYear,n
          results = results.splice(0,7)
          results.unshift {n:'as',v:'N.Ch|iOS.ANDR.Web.O|R.F|All'}
          results.push {n:'7 Days(1 week, inc today, accumulate by date)',v:disp(ac7)}
          results.push {n:'14 Days',v:disp(ac14)}
          results.push {n:'30 Days',v:disp(ac30)}
          results.push {n:'365 Days',v:disp(ac365)}
          results.push {n:'this Month',v:disp(acThisMonth)}
          results.push {n:'this Year',v:disp(acThisYear)}
          debug.info 'last access:'
          debug.info results
          cb results
        else
          cb []

  # [stauts.coffee]
  @getAreas = (cb)->
    lastOneWeek = new Date(Date.now() - 7 * 24 * 3600000)
    UserCol.aggregate [
      {$match:{lts:$gt:(new Date(Date.now() - 365 * 24 * 3600000))}},
      {$facet:{
        byCity:[
          {$group:{
            _id: {prov:'$city.prov',city:'$city.city'},
            v:{$sum:1},
            week:{$sum:{$cond:[{$gt:['$lts',lastOneWeek]},1,0]}},
            }},
          {$sort:{v:-1}}
        ]
        byCip:[
          {$group:{
            _id: '$cip',
            v:{$sum:1},
            week:{$sum:{$cond:[{$gt:['$lts',lastOneWeek]},1,0]}},
            }}
        ]
      }
      }
      ],{cursor:{batchSize:0}},(err,results)->
        if err then debug.error err,'getAreas'
        ret = []
        results = results?[0]
        if results?.byCity
          all = 0
          week = 0
          for n in results.byCity
            n.n = n._id.prov + ':' + n._id.city
            all += n.v
            week += n.week
            n.v = "#{n.week}/#{n.v}"
            delete n._id
            delete n.week
            ret.push n
          ret.unshift {n:'Week/All',v:"#{week}/#{all}"}
        if results?.byCip
          for n in results.byCip
            n.n = if n._id then 'China' else if n._id? then 'NotChina' else 'Unknown'
            n.v = "#{n.week}/#{n.v}"
            delete n._id
            delete n.week
            ret.unshift n
        cb ret

  # [status]
  @getFollowers = (cb)->
    # UserCol.group ((doc)-> {n:doc.flwng[0].uid}),
    #   {flwng:$exists:1},
    #   {v:0},
    #   "function (u,col){col.v++;}",
    #   (err,results)->
    UserCol.aggregate [
      {$match:{flwng:$exists:1}},
      {$unwind:'$flwng'}
      {$group:{
        _id: '$flwng.uid',
        v:{$sum:1},
        }}
      ],{cursor:{batchSize:0}},(err,results)->
        return gFindError err,{f:'getFollowers'},cb if err
        if results
          results.sort (a,b)-> b.v - a.v
          tAgent = 0
          tFollower = 0
          for c in results
            tFollower += c.v
            tAgent++
          #r = results.slice 0,20
          uids = []
          for n in results
            if ('string' is typeof n._id) and (/^[a-zA-Z0-9\-]+$/.test n._id)
              uids.push new UserCol.ObjectId(n._id)
          UserCol.findToArray {_id:$in:uids},{fields:{fn:1,ln:1,nm:1,eml:1,roles:1}},(err,users)->
            return gFindError err,{f:'getFollowers'},cb if err
            users?=[]
            us = {}
            #vipr = []
            for u in users
              id = u._id.toString()
              us[id] = (u.eml + ' ' + (u.nm_zh or u.nm_en or u.nm))
              if u.roles?.indexOf('vip_plus') >= 0
                us[id] += '+V'
                # get vip user from list, and put to vipr
                #if u = r[uids.indexOf(u._id)]
                #  u.n = us[id]
                #  vipr.push u
            #r = r.slice 0,20
            ret = []
            for n,i in results
              if (i < 20) or (us[n._id]?.substr(-2) is '+V')
                n.n = us[n._id]
                delete n._id
                ret.push n
            r = ret
            r.unshift {n:'Followers',v:tFollower}
            r.unshift {n:'Followee',v:tAgent}
            r.unshift {n:'Ratio',v:''+(Math.floor( tFollower / tAgent * 1000) / 1000)}
            #r = r.concat vipr
            cb r
        else
          cb []

  # [status]
  @getRealtors = (cb)->
    ret = []
    UserCol.countDocuments {$or:[{lts:$exists:1},{src:$exists:1}]},(err,c)->
      ret.push {n:'Total (lts or src exists)',v:c} if c
      UserCol.countDocuments {lts:$gte:new Date(Date.now() - 7 * 24 * 3600000)},(err,c)->
        ret.push {n:'1 Week Active(all user, non-dup)',v:c} if c
        UserCol.aggregate [
            {$match:{roles:{$exists:1}}}, #$in:['realtor','mortgage', 'inspector', 'lawyer', 'insurance_agent', 'contractor']}}},
            {$unwind:'$roles'},
            {$group:{_id:'$roles',v:{$sum:1}}},
            {$sort:{v:-1}}
          ],{cursor:{batchSize:0}},(err,rets)->
            return gFindError err,{f:'getRealtors'},cb if err
            rets?=[]
            for r in rets
              rr = {n:r._id,v:r.v}
              rr.url = "/analytics_spevent?r=#{r._id}" if r.v < 300
              ret.push rr
            cb ret

  # [PageDataMethods] in native when getting pagedata, will send loc if has loc
  # NEED_TEST:
  @logPosition = (req,user)->
    if user?._id
      #data is recorded every time user open app
      LOC_LIMIT = -30
      loc = req?.body?.loc
      # NOTE: test isValidLoc
      if not (loc?.latitude and loc.longitude)
        return
      _loc = # never trust user input, create new object instead of modify req.body.loc
        ts: new Date()
        lat: loc.latitude
        lng: loc.longitude
        ip: req.remoteIP()
      if UA = req.UA()
        _loc.ua = UA
      
      User.setLastLocation user._id, {loc:_loc,LOC_LIMIT},(err,ret)->
        debug.error err,'loc,id:',user._id if err
      UserLoc.updateOne {_id:user._id}, {
        $inc: {cnt:1},
        $push:{
          locHis:{
            $each:[_loc],
            $slice:LOC_LIMIT,
          }
        }
        }, {upsert:true}, (err,ret)->
          debug.error err,'logPosition,id:',user._id if err

  @findBySponsorEmail:(speml,cb)->
    _findByQuery {speml},{},cb

  @updateBySponsorEmail:(req,speml,cb)->
    update = {}
    update.roles = ['_wecard']
    update.speml = speml
    User.updateAndChangeSession {req,user:realUser,data:update},cb

  @updatePopup:(uid,{cmd,id},cb)->
    set = {}
    if cmd is 'nomore'
      set['popup.'+id] = {ts:new Date(),st:'nomore'}
    else if cmd is 'view'
      set['popup.'+id] = {ts:new Date(),st:'view'}
    opt =
      update:
        $set:set
      param:{upsert:true}
      Coll:UserProfile
    User.updateOneById uid,opt,cb

  @syncUserBlock : ({user,blkUids,blkCmnts},cb)->
    debug.error 'syncUserBlock: no user' unless user?._id
    UserProfile.findOne {_id:user._id},{fields:{blkUids:1,blkCmnts:1}},(err,userProfile)->
      return cb(err,null) if err
      blkUidsDB = userProfile?.blkUids or {}
      blkCmntsDB = userProfile?.blkCmnts or {}
      blkCmnts ?= {}
      blkUids ?= {}
      if ((blkCmnts?.length is 0) and (blkUids?.length is 0))
        return cb(null,{blkUids:blkUidsDB,blkCmnts:blkCmntsDB})
      newBlkUids = Object.assign blkUids,blkUidsDB
      #blkCmnts {"5f621a0e6ab23f3411963c74":{"16":1,"1":1},"5f64bbee6ab23f3411fc0a54":{"4":1}
      newBlkCmnts = Object.assign {},blkCmntsDB
      for k,v of blkCmnts
        newBlkCmnts[k] = Object.assign v,newBlkCmnts[k]
      set = {blkUids:newBlkUids,blkCmnts:newBlkCmnts}
      update = {$set:set}
      UserProfile.updateOne {_id:user._id},update,{upsert:true},(err,ret)->
        return cb(err,null) if err
        cb(null,set)

  @checkUserRoleBeforeBlock : ({type,uid},cb)->
    return cb() if (type in ['cmnt','post'])
    return cb('Missing uid') unless uid
    UserCol.findOne {_id:uid},{fields:{roles:1}},(err,blkUser)->
      return cb(err) if err
      return cb(MSG_STRINGS.NOT_FOUND) unless blkUser
      if blkUser.roles and User.accessAllowed('forumAdmin',blkUser)
        return cb('User is admin')
      cb()

  @blockComment:(id,{type,forumId,cmntIdx,uid},cb)->
    update = {}
    if 'cmnt' is type
      unless (forumId or cmntIdx)
        return cb req.l10n('Missing forum id or comment idx')
      update['$set'] = {}
      key = "blkCmnts.#{forumId}.#{cmntIdx}"
      update['$set'][key] = 1
      msg = 'Comment was blocked'
    else if 'post' is type
      key = "blkCmnts.#{forumId}.b"
      update['$set'] = {}
      update['$set'][key] = 1
      msg = 'Post was blocked'
    else if 'user' is type
      return cb req.l10n('Missing uid') unless uid
      key = 'blkUids.'+uid
      update['$set'] = {}
      update['$set'][key] = 1
      msg = 'User was blocked'
    User.checkUserRoleBeforeBlock {type,uid},(err)->
      return gFindError err,{f:'blockComment',id,info:'key: '+util.inspect key},cb if err
      UserProfile.updateOne {_id:id},update,{upsert:true},(err)->
        return cb err,msg
# todo pn相关操作
# 发送通知
  @findByQueryForEdm:({q},cb) ->
    User.findForEdm {q,isStream:true}, cb

  @findOneForEdm:({q},cb)->
    User.findForEdm {q,isStream:false}, cb

  @findForEdm:({q,isStream},cb)->
    fields = {avt:1,roles:1,cities:1,city:1, locale:1, splang:1, _id:1, eml:1,nm_zh:1,nm_en:1,nm:1,cip:1}
    projection = {fields}
    method = 'findOne'
    if isStream
      projection.timeout = false
      projection.maxTimeMS = 0
      method = 'find'
    UserCol[method] q, projection, (err, cur) ->
      if err
        debug.error 'findOneForEdm', err
        return cb(err,null)
      cb(null,cur)
  
  @deleteEdmLog:(cb)->
    rmQuery = {ts: {$lt:new Date(Date.now() - 30 * 24 * 3600 * 1000)}}
    EdmLog.deleteMany rmQuery, (err) ->
      if err
        debug.error 'deleteEdmLog', err
      cb(err)
  
  @createEdmLog:({avgs,msg},cb)->
    logid = new EdmLog.ObjectId()
    edmlog = {_id: logid, ts: new Date(), logs: []}
    edmlog.avgs = avgs if avgs
    edmlog.msg = msg if msg
    EdmLog.insertOne edmlog, (err, ret) ->
      if err
        debug.error 'logUserEdm', err
        return cb(err)
      ret.logid = logid
      cb(err,ret)
    
  @updateEdmLogByLogId:({logid,desUserCount,sentUserCount,sentUserSuccessCount},cb)->
    set = {
      $set: {
        endts: new Date(), dct: desUserCount, sct: sentUserCount
      }
    }
    set['$set'].sctSuc = sentUserSuccessCount if sentUserSuccessCount
    EdmLog.updateOne {_id:logid}, set,(err)->
      if err
        debug.error 'updateEdmLogByLogId', err
      cb(err)
  
  @updateEdmLogExpireListing:({logid,user,msg},cb)->
    debug.error 'updateEdmLogExpireListing: no user' unless user?._id
    log = {
      ts:new Date(),
      uid: user._id.toString(),
      eml: user.eml,
      msg
    }
    update = {$push: {logs:log}}
    EdmLog.updateOne {_id:logid}, update,(err)->
      cb(err)
  
  @findUserEdmData:({uid,fields},cb)->
    profileFields = {savedSearch:1,edmNo:1, edmNoEvaluation:1,edmNoExlisting:1,edmNoAssignment:1,\
    edmNoProject:1, edmNoForum:1, edmNoProperty:1,edmNoCommunity:1,\
    edmNoStat:1,edmNoSearch:1,edmNoLocation:1}
    UserProfile.findOne {_id: uid}, {fields:profileFields}, (err, profile) ->
      if err
        debug.error 'findUserEdmData UserProfile.findOne',err
        return cb(err,null)
      UserLog.findOne {_id: uid,tp:'timestamp'}, {fields:fields}, (err, edmtsLogs) ->
        if err
          debug.error 'findUserEdmData UserLog.findOne',err
          return cb(err,null)
        UserLog.aggregate [
          {$match:{uid:uid,tp:{$in:['forum','property','project']}}},
          {$group:{_id:'$tp',id:{ $addToSet:'$id'}}},
          {$project:{id:{  $slice: [ '$id', 100 ] }}},
          # { $sort :  {ts:-1} }
          ], (err, historys) ->
            if err
              debug.error 'findUserEdmData UserLog.aggregate',err
              return cb(err,null)
            cb(null,{profile,edmtsLogs,historys})
    
  @updateUserLog:({uid,avgs,edmType},cb)->
    hasField = (fieldname, isWeekly) ->
      val = avgs.indexOf(fieldname) >= 0 or avgs.indexOf('all') >= 0
      if isWeekly is true
        return val && avgs.indexOf('weekly') >= 0
      if isWeekly is false
        return val && avgs.indexOf('weekly') < 0
      else
        return val
    isWeekly = () ->
      return avgs.indexOf('weekly') >= 0
    set = {tp:'timestamp'}

    for avg in avgs
      if edmType.includes avg
        if avg is 'forum'
          if hasField avg, false
            set.publicforumEdmts = new Date()
            set.groupforumEdmts = new Date()
          if hasField avg, true
            set.publicforumWeeklyEdmts = new Date()
            set.groupforumWeeklyEdmts = new Date()
        else if avg is 'forumw'
          set.publicforumwEdmts = new Date()
          set.groupforumwEdmts = new Date()
        else
          if hasField avg, false
            set[avg+'Edmts'] = new Date()
            set.pnEdmMt = new Date()
          if hasField avg, true
            set[avg+'WeeklyEdmts'] = new Date()
    UserLog.updateOne {_id:uid}, {$set:set},{upsert:true}, (err, ret)->
      if err
        debug.error 'updateUserLog', err
        return cb(err,null)
      cb(null,ret)


  @findListForEdm:(ids,cb)->
    projection = {nm_en:1,nm_zh:1,nm:1,eml:1,roles:1,_id:1,fn:1,ln:1,avt:1,cpny:1,mbl:1,tel:1,edmPriority:1}
    User.getListByIds ids,{projection},(err,ret)->
      if err
        debug.error 'findListForEdm',err
        return cb(err,null)
      cb(null,ret)
  
  @findEvaluationUpdates:({uid,edmts},cb)->
    query = {tp:'evaluation_update', uid, ts:{$gt:edmts}, tracked:true}
    debug.debug JSON.stringify query
    fields = {props:1, uaddr:1, addr:1, city:1,prov:1,cnty:1}
    UserStream.findToArray query, {fields, sort: {ts:-1}, limit:30}, (err, ret) ->
      if err
        debug.error 'findEvaluationUpdates',err
        return cb(err,null)
      cb(null,ret)
  
  @updateEvaluation:({evaluation,props,uid},cb)->
    {addr,city,prov,cnty} = evaluation
    obj = {uaddr:evaluation._id,uid,tp:'evaluation_update',ts:new Date(),props,addr,city,prov,cnty}
    obj.exp_ts= new Date( Date.now() + 3600*24*1000 * 14)

    if evaluation.tracked
      index = evaluation.tracked.find (el)->
        return el.toString() == uid.toString()
      if (index)
        obj.tracked = true
    debug.info "find #{props.length} new/sold properties for #{uid} at #{obj.uaddr}"
    query = {uaddr: evaluation._id, uid, tp:'evaluation_update'}
    UserStream.updateOne query, obj, {upsert:true}, (err, ret)->
      cb(err,ret)
        
  @getEdmSettingsByUserId:(userId, cb)->
    User.findById userId,{projection:{roles:1}},(err,user)->
      return cb err if err
      if not user?
        return cb()
      fields={}
      for fld in User.NOTIFICATION_EMAIL_FIELDS
        fields[fld] = 1
      User.findById userId,{projection:fields, Coll:UserProfile},(err, profile)->
        # debug.debug err, profile
        return cb err if err
        if not profile?
          return cb()
        return cb null, Object.assign user, profile

  @checkEmlIsExists:(neml)->
    tologin = await Auth.findOne {_id:neml}
    return tologin

  @parseU:(req, u)->
    uu = {nm_zh:u.nm_zh,nm_en:u.nm_en,nm:u.nm,_id:u._id,cpny:u.cpny,cpny_pstn:u.cpny_pstn,avt:u.avt,itr:u.itr,mbl:u.mbl}
    uu.eml = if Array.isArray(u.eml) then u.eml[0] else u.eml
    uu.cert = req.isAllowed 'rcmdRealtor',u
    uu.splang = User.parseSplang(u.splang)
    uu.fnm = libUser.fullNameOrNickname(req.locale(),u)
    uu.cpny = libUser.userCpny(req.locale(),u)
    uu.avt = libPropertyImage.replaceRM2REImagePath uu.avt if uu.avt
    uu

  @parseSplang : (splang)->
    splang ?= []
    ret = []
    for i in splang
      ret.push User.SPLANG[i] or i
    return ret.join(' ')

  @formatList:(req, list,favProviders=[], isfaved)->
    retList = []
    return retList unless list?.length > 0
    for u in list
      u.eml = if Array.isArray(u.eml) then u.eml[0] else u.eml
      u.fnm = libUser.fullNameOrNickname(req.locale(),u)
      u.cpny = libUser.userCpny(req.locale(),u)
      u.vip = 1 if u.roles?.indexOf('vip_alliance') > -1
      u.splang = User.parseSplang(u.splang)
      u.cert = req.isAllowed 'rcmdRealtor',u
      u.avt = libPropertyImage.replaceRM2REImagePath u.avt if u.avt
      favindex = favProviders.indexOf(u._id.toString())
      if favindex>=0 or isfaved then u.isfaved = true else u.isfaved = false
      delete u.sas
      delete u.roles
      delete u.cpny_en
      delete u.cpny_zh
      delete u.fn
      delete u.ln
      retList.push u
    return retList

  # find topAgents in a city(subcity)
  @getTopAgents:(req, opt = {}, cb) ->
    if opt.role isnt 'realtor'
      return cb null, []
    # TODO: ignore prov for now, might need if support multi-cnty
    # if prov = opt?.prov
    #   unless /^[a-zA-Z]{2}$/.test prov
    #     prov = provAbbrName(prov)
    # prov:prov
    q = { city: opt.city }  #status:'A'
    now = new Date()
    q.start_ts = { $lte: now }
    q.end_ts = { $gt: now }
    ta05Found = false
    ta1Found = false
    subCity = opt.subCity
    ta05Count = 0
    ta15Count = 0
    TopupAgents.findToArray q, (err, ret) ->
      ret ?= []
      uids = []
      mapObj = {}
      return cb err if err
      return cb null, [] unless ret?.length
      for a in ret
        if a.subCity and (a.subCity isnt subCity)
          continue
        a.uid = a.uid?.toString()
        uids.push a.uid
        a.eml = User.getEmail a
        # emls.push a.eml
        # NOTE: for duplicate ta2/ta1
        if a.subCity and (a.subCity is subCity)
          if a.ta1
            a.ta05 = true #有subcity的ta1
            ta05Count++
          else
            a.ta15 = true # 有subcity的ta2
            ta15Count++
        for fld in ['ta05', 'ta01', 'ta15', 'ta2']
          if mapObj[a.uid]?[fld]
            a[fld] = true
        mapObj[a.uid] = a
      User.getListByIds uids, { projection: User.RECOMMEND_PROVIDER_FIELDS }, \
      (err, providers) ->
        return cb err if err
        providers ?= []
        return cb null, [] unless providers.length
        for a in providers
          a.uid = a._id.toString()
          a.eml = User.getEmail a
          a.ta05 = mapObj[a.uid]?.ta05 if mapObj[a.uid]?.ta05
          a.ta1 = mapObj[a.uid]?.ta1
          if a.ta05
            ta05Found = true
          if a.ta1
            ta1Found = true
          a.ta15 = mapObj[a.uid]?.ta15 if mapObj[a.uid]?.ta15
          a.ta2 = mapObj[a.uid]?.ta2
          a.reco = true
          a.splang = User.parseSplang(a.splang)
          a.fnm = libUser.fullNameOrNickname(req.locale(), a)
          a.cpny = libUser.userCpny(req.locale(), a)
          a.avt = libPropertyImage.replaceRM2REImagePath a.avt if a.avt
          # 只显示一次
          # if a.ta1 and a.ta2
          #   copy = Object.assign({}, a, { ta1: false })
          #   providers.push copy
        # if providers.length isnt uids.length
        #   console.error 'duplicate top agents or user not found!'
        providers.sort((a, b) ->
          unless (a.ta05 or a.ta1 or a.ta15 or a.ta2) or (b.ta05 or b.ta1 or b.ta15 or b.ta2)
            return 0
          if a.ta05 and not b.ta05
            return -1
          if b.ta05 and not a.ta05
            return 1
          if a.ta05 and b.ta05
            return 0
          if a.ta1 and not b.ta1
            return -1
          if b.ta1 and not a.ta1
            return 1
          if a.ta1 and b.ta1
            return 0
          if a.ta15 and not b.ta15
            return -1
          if b.ta15 and not a.ta15
            return 1
          if a.ta15 and b.ta15
            return 0
          if a.ta2 and not b.ta2
            return -1
          if b.ta2 and not a.ta2
            return 1
          if a.ta2 and b.ta2
            return 0
          return 0
        )
        # logArray 1,providers
        ta05 = providers.shift() if ta05Found
        ta1 = providers.shift() if ta1Found
        # ta2first = providers.splice(TOP_AGENT_ROUND,1)
        # TOP_AGENT_ROUND = TOP_AGENT_ROUND%providers.length
        # logArray 2,providers
        # ta2part1 = providers.splice(0,TOP_AGENT_ROUND)
        # providers = providers.concat ta2part1
        if subCity and ta15Count > 0
          ta15List = providers.splice(0, ta15Count)
          ta15List = helpers.shuffle ta15List
        providers = helpers.shuffle providers
        # logArray 3,providers
        if ta15List?.length > 0
          providers = ta15List.concat providers
        providers.unshift ta1 if ta1Found
        providers.unshift ta05 if ta05Found
        # logArray 4,providers
        TOP_AGENT_ROUND = TOP_AGENT_ROUND + 1
        return cb null, providers

  @updateAvtWithDefaultImg:({ids},cb)->
    q = _id:{$in:ids}
    set =
      avt : 'https://realmaster.cn/img/user-icon-placeholder.png'
    UserCol.updateMany q, {$set:set},cb

  @getAvtList:({filter,page,limit},cb)->
    q = avt:{ $exists: true}
    if filter is 'vip'
      q.roles={$in:['vip','vip_plus','vip_alliance']}
    else if filter is 'realtor'
      q.roles='realtor'
    else
      q.roles={$nin:['vip','vip_plus','vip_alliance','realtor']}
    skip = (page or 0) * limit
    User.getList q,{projection:User.AVATAR_FIELDS,sort:{'lts':-1},skip,limit},(err,list)->
      if err
        debug.error 'getAvtList User.getList',err
        return cb err,null
      for u in list
        u.avt = libPropertyImage.replaceRM2REImagePath u.avt
        u.eml = User.getEmail u
      UserCol.countDocuments q,(err,cnt)->
        if err
          debug.error 'getAvtList UserCol.count',err
          return cb err,null
        cb null,{list,cnt}

  @getUsersByIDsAsync:({ids,fields,roles,idKey})->
    if idKey # ids is an object array, use idKey to retrieve id array
      uids = {}
      for u in ids
        uids[u[idKey]] = 1
      ids = Object.keys uids
    query = {_id:{$in:ids}}
    if roles
      if not Array.isArray roles
        roles = [roles]
      query.roles = {$in:roles}
    if not fields?
      fields = User.NOTIFY_FIELDS
    users = await UserCol.findToArray query,{fields}
    pnFields = {}
    for f in User.NOTIFICATION_PN_FIELDS
      pnFields[f] = 1
    for f in User.NOTIFICATION_EMAIL_FIELDS
      pnFields[f] = 1
    userProfiles = await UserProfile.findToArray query,{fields:pnFields}
    pnOfUsers = objectHelpers.arrayToObjectOnKey userProfiles,'_id'
    for user in users
      user = Object.assign user,pnOfUsers[user._id]
    return users

  @getPropFavUsersAsync:(id,roles)->
    q = id:id
    favUids = await PropFav.findToArray q, {fields:{uid:1}}
    if (not favUids) or (favUids.length is 0)
      return []
    return await @getUsersByIDsAsync {
      ids:favUids,
      idKey: 'uid'
      fields:User.NOTIFY_FIELDS,
      roles}

  @getPropViewedUsersAsync: (id,roles,withinHours)->
    query = {id,tp:@USER_LOG_TYPES.PROPERTY}
    if withinHours
      query.mt = {$gt:new Date(Date.now()-withinHours*3600*1000)}
    viewedUids = await UserLog.findToArray query, {fields:{uid:1}}
    if (not viewedUids) or (viewedUids.length is 0)
      return []
    return await @getUsersByIDsAsync {
      ids:viewedUids,
      idKey: 'uid',
      fields:User.NOTIFY_FIELDS,
      roles}

  @getPropFavUsersWithFavNamesAsync:(id,roles)->
    q = id:id
    favUids = await PropFav.findToArray q, {fields:{uid:1,g:1}}
    if (not favUids) or (favUids.length is 0)
      return []
    uidsWithFavGid = {}
    for u in favUids
      if existingUser = uidsWithFavGid[u.uid]
        existingUser.g.push ''+u.g
      else
        uidsWithFavGid[u.uid] = {uid:u.uid,g:[''+u.g]}
    ids = Object.keys uidsWithFavGid
    query = {_id:{$in:ids}}
    # userProfile has no roles
    userFavMaps = await UserProfile.findToArray query, {fields:{favMap:1,pnNoFavs:1,edmNoProperty:1}}
    favMapOfUsers = objectHelpers.arrayToObjectOnKey userFavMaps,'_id'
    # user has roles
    if roles
      if not Array.isArray roles
        roles = [roles]
      query.roles = {$in:roles}
    users = await UserCol.findToArray query,{fields:User.NOTIFY_FIELDS}
    # merge userProfile and user
    for u in users
      u.pnNoFavs = favMapOfUsers[u._id]?.pnNoFavs
      u.edmNoProperty = favMapOfUsers[u._id]?.edmNoProperty
      u.favNames = []
      if favMap = favMapOfUsers[u._id]?.favMap
        for g in uidsWithFavGid[u._id]?.g
          v = {}
          if nm = favMap[g]?.v
            v.nm = nm
            if favMap[g]?.cNm
              v.cNm = favMap[g]?.cNm
              v.nm = nm.split(' ')[0]
            u.favNames.push v
    return users

  @savedSearchCount:({user},cb)->
    unless user
      return cb null, {act:0,tot:0}
    User.findProfileById user._id,{fields:{savedSearch:1}}, (err, ret)->
      return cb(err.toString()) if err
      return cb null, {act:0,tot:0} unless ret?.savedSearch
      tot = ret.savedSearch?.length or 0
      isReach = ret.savedSearch?.length >= USER_SAVE_SEARCH_LIMIT
      act = 0
      for saved in ret.savedSearch
        if saved.sbscb isnt false
          act += 1
      cb null,{tot,act,isReach}

  ###
  @param {object} user
  @param {object} fontEndParam -  {hasWechat}
  if no fontEndParam, hasWechat is true.
  @return {bool} hasWechat
  ###
  @hasWechat: (user,fontEndParam)->
    fontEndParam ?= {hasWechat:true}
    hasWechat = fontEndParam.hasWechat
    if user?.hasWechat?
      # if any is false, ret = false
      hasWechat = hasWechat and user.hasWechat
    hasWechat
  @updateAppMode:({req,user,appmode})->
    return unless user and appmode
    User.updateAndChangeSession {req,data:{appmode},user},(err)->
      debug.error 'updateAppMode',err if err

  ###
    @param {object} userArray
    @param {string} rmAgentId
    flwngRm = {uid,ts}
    set flwngRm of each user in userArray and add history if needed.
  ###
  @setFlwngRmByAgentIdAsync:({userArray,rmAgentId})->
    #check rmAgentId，check with black agent list
    #Todo:命中黑名单时候unset 关联关系，解决经纪辞职，客户转回到admin的情况。是否有风险？
    for user in userArray
      # if same with old one, do not change.
      if user.flwngRm?.uid?.toString() is rmAgentId?.toString()
        continue
      flwngRm = {
        uid:rmAgentId
        ts:new Date()
      }
      update = {$set:{flwngRm}}
      await UserCol.updateOne {_id:user._id}, update
      await AdminOpLogs.insertOne {
        act:'setFlwngRm',
        src: 'fubWebHook',
        ts: new Date(),
        target: user.eml,
        op:flwngRm
      }



  @setFlwngRmByFubIdAsync: ({fubId,rmAgentId})->
    projection = {_id:1, eml:1, flwngRm:1}
    debug.debug 'setFlwngRmByFubIdAsync fubId', fubId
    userArray = await @findUserByFubIdAsync fubId, {projection}
    if not userArray?.length
      debug.error 'No user found for fubid',fubId
    debug.debug 'user',userArray
    await @setFlwngRmByAgentIdAsync {userArray,rmAgentId}


  @findByServiceAreaAsync:(prov, city, {projection})->
    debug.debug 'findByServiceArea projection',projection
    return unless (prov and city)
    return await UserCol.findToArray {
      'serviceArea.prov': prov,
      'serviceArea.city': city,
      'roles':'vip_plus'
    }, {fields:projection}

  ###
  @param {string} fubAgentId
  @param {object} projection
  return agent user
  check role,
  ###
  @findUserByFubAgentIdAsync: (fubAgentId)->
    return null unless fubAgentId
    projection = {_id:1,roles:1}
    agent = await UserCol.findOne {fubAgentId}, {fields:projection}
    debug.debug 'findUserByFubAgentIdAsync',agent
    if not agent
      errorMsg = 'No user found for assignedUserId(fubAgentId): ' + fubAgentId
      debug.error errorMsg
      return null
    return agent


  ###
  @param {string} fubId
  @param {object} projection
  @return [], different users might has same fubId
  ###
  @findUserByFubIdAsync: (fubId, {projection})->
    return null unless fubId
    return await UserCol.findToArray {fubId}, {fields:projection}
  
  #set fubId
  #set flwngRm if assignedUserId is passed.
  @setUserFubInfo: ({ fubResolved, eml, fubId, assignedUserId,uid,src})->
    return null unless eml
    fubId = parseInt fubId
    update = {$set:{fubId}}
    if fubResolved
      update.$set.fubResolved = 1
      update.$unset?={}
      update.$unset.fubToFix = 1

    #检查agent 是否valid
    if assignedUserId
      agent = await User.findUserByFubAgentIdAsync assignedUserId
      if agent?.roles and ('_crmNoAssign' in agent.roles)
        update.$unset?={}
        update.$unset.flwngRm = 1
      else if agent?._id
        update.$set.flwngRm = {
          uid: agent._id
          ts:new Date()
        }
    await UserCol.updateOne {eml}, update
    log = {
      act:'setfubId',
      src: src or 'fubEventApiRes',
      ts: new Date(),
      target: eml,
      op:update.$set
    }
    if update.$unset
      log.unset = update.$unset
    if uid
      log.uid = uid
    await AdminOpLogs.insertOne log
  
  @findUserAndFollowersByEml:(eml,cb)->
    try
      fields = {flwngRm:1,fubAgentId:1,fubEml:1,fubLeadEml:1,fubMbl:1,serviceCity:1,fubId:1,roles:1,eml:1}
      user = await UserCol.findOne {eml},{fields}
      return cb MSG_STRINGS.NO_USER_FOR_THE_EMAIL unless user
      flwngFields = {nm:1,eml:1}
      if flwngId = user.flwngRm?.uid
        user.flwngRm =  await UserCol.findOne {_id:flwngId},{fields:flwngFields}
      if user.roles and ('realtor' in user.roles)
        user.followers = await UserCol.findToArray {'flwngRm.uid':user._id},{fields:flwngFields,sort:{'flwng.ts':-1}}
      return cb null,user
    catch err
      return cb(err)
  
  @getFollowUpCRMUsers:({eml, tp})->
    if eml
      regEml = helpers.str2reg(data.eml,false,true)
      query = {eml:regEml}
    else if tp is 'people'
      query = {fubId:{$gte:0},fubToFix:1,fubResolved:null}
    else
      query = {fubAgentId:{$gte:0}}
    projection = {
      fields:{
        eml:1,nm:1,nm_zh:1,nm_en:1,fubAgentId:1,\
        fubEml:1,fubId:1, fubMbl:1,roles:1,serviceCity:1
        flwngRm:1,fubToFix:1
      }
      sort: {fubAgentId:1}
    }
    users = await UserCol.findToArray query,projection
    if users?.length
      for u in users
        u.eml = User.getEmail u
        u.nm = u.nm or u.nm_zh or u.nm_en
        if flwngId = u.flwngRm?.uid
          u.flwngRm =  await UserCol.findOne {_id:flwngId},{fields:{nm:1,eml:1}}
    return users
  
  @addRMRealtor2User:({eml,realtorEml},cb)->
    try
      realtor = await UserCol.findOne {eml:realtorEml}
      return cb MSG_STRINGS.NO_USER_FOR_THE_EMAIL unless realtor
      update = {$set:{flwngRm:{uid:realtor._id,ts:new Date()}}}
      await UserCol.updateOne {eml},update
      return cb null
    catch err
      return cb err
  
  @removeUserFlwngRm:({eml,realtorEml},cb)->
    try
      # why check realtor? why not remove by id?
      realtor = await UserCol.findOne {eml:realtorEml}
      return cb MSG_STRINGS.NO_USER_FOR_THE_EMAIL unless realtor
      update = {$unset:{'flwngRm':1}}
      await UserCol.updateOne {eml},update
      return cb null
    catch err
      return cb err

  @removeUserFlwngRmByFubId:({fubId})->
    await UserCol.updateMany {fubId}, {$unset:{'flwngRm':1}}
    
  @removeRealtorFollowers:({eml},cb)->
    try
      realtor = await UserCol.findOne {eml}
      return cb MSG_STRINGS.NO_USER_FOR_THE_EMAIL unless realtor
      update = {$unset:{'flwngRm':1}}
      query = {'flwngRm.uid':realtor._id}
      await UserCol.updateMany query,update
      return cb null
    catch err
      return cb err
  
  @assignFollowers2NewRealtor:({eml,realtorEml},cb)->
    try
      realtor = await UserCol.findOne {eml}
      return cb MSG_STRINGS.NO_USER_FOR_THE_EMAIL unless realtor
      newRealtor = await UserCol.findOne {eml:realtorEml}
      return cb MSG_STRINGS.NO_USER_FOR_THE_EMAIL unless newRealtor
      update = {$set:{'flwngRm':{uid:newRealtor._id,ts:new Date()}}}
      query = {'flwngRm.uid':realtor._id}
      await UserCol.updateMany query,update
      return cb null
    catch err
      return cb err

  @setFubToFixFlag:(eml)->
    await UserCol.updateOne {eml},{$set:{fubToFix:1}}

  @setUserFubAgentInfo:({eml,fubLeadEml,fubEml,fubMbl,fubAgentId,serviceCity,uid,src})->
    #uid: op admin uid
    unless eml
      debug.warn 'updateUserFollowup empty eml', eml
      return cb(MSG_STRINGS.BAD_PARAMETER)
    set = {fubLeadEml,fubEml,fubMbl,serviceCity}
    update = {$set:set}
    if fubAgentId?
      fubAgentId = parseInt fubAgentId
      if fubAgentId>=0
        update.$set.fubAgentId = fubAgentId
      else
        update.$unset={fubAgentId:1} #fubAgentId can not be empty as it is unique.

    await UserCol.updateOne {eml},update
    param = {act:'updateFub', ts:new Date(),target:eml,op:set}
    if src
      param.src = src
    if uid
      param.uid = uid
    await AdminOpLogs.insertOne param

  @setUserContactRealtorStatus:({id,crStat})->
    return await UserCol.updateOne {_id:id},{$set:{crStat}}

  @getDetailSavedLog:({id,uid,l10n})->
    # 获取当前房源加入propFav和showing的时间及客户
    # post已检查登录，获取当前房源加入propFav和showing的时间及客户
    logs = []
    uid = new ObjectId(uid)
    if ('string' is typeof id) and /^[0-9A-F]{24}$/i.test id
      id = new ObjectId(id)
    try
      showing = await Showing.findToArray {uid,'props._id':id},{fields:{cNm:1,ts:1,dt:1,dtNum:1}}
      propFav = await PropFav.findToArray {uid,id},{fields:{ts:1,g:1},sort:{ts:-1}}
      profile = await UserProfile.findOne {_id:uid},{fields:{favMap:1}}
    catch err
      debug.error 'getDetailSavedLog',err
      throw err
    showing ?= []
    propFav ?= []
    profile ?= {}
    favMap = profile.favMap or {}
    logs = showing.concat propFav
    if logs.length
      logs.sort (a,b)-> b.ts - a.ts
      for l in logs
        l.updateTs = formatCmntTs(l.ts,l10n)
        if (typeof(l.g) is 'number') and favMap[l.g]
          l.gNm = favMap[l.g].v
          if favMap[l.g].clnt
            l.clnt = favMap[l.g].clnt
            l.cNm = favMap[l.g].cNm
    return logs

  @updateFavmt:({uid,grp},cb)->
    set = {}
    UserProfile.findOne {_id:uid},{fields:{favMap:1}},(err,ret)->
      return gFindError err,{f:'updateFavmt',id:uid},cb if err
      if not ret?.favMap
        set['favMap.'+grp+'.ts'] = new Date()
        set['favMap.'+grp+'.v'] = 'Default'
        set['favMap.cntr'] = 1
      set['favMap.'+grp+'.mt'] = new Date()
      UserProfile.updateOne {_id:uid},{$set:set},(err)->
        return gFindError err,{f:'updateFavmt',id:uid},cb if err
        cb null if cb

  @updateSettings:({req,user,body},cb)->
    fields = ['defaultHomeTab']
    data = {}
    for f in fields
      if body[f] isnt 'undefined'
        data[f] = body[f]
    User.updateAndChangeSession {req,data,user},cb

  @checkEmailAddress:(email)->
    return null if not email
    # 检验email格式
    unless helpers.isEmail(email)
      return 'Bad Email Address'
    # 检验邮箱域名mx
    validRet = await getValidatedEmail email
    if not validRet?.length
      return MSG_STRINGS.INVALID_EMAIL_ADDRESS
    return null

  ###
  # @description 获取邮箱验证和edmno状态
  # @param {ObjectId} uid -  A necessary param
  # @return 返回判断后的结果 {status, showBackdrop}
  ###
  @getEmailStatus:(uid)->
    # 获取用户数据，包含更多字段用于状态判断
    user = await UserCol.findOne {_id:uid},{projection:{emlV:1,edmNo:1,ts:1,googleId:1,facebookId:1,appleId:1}}
    
    # 情况1：如果有user?.edmNo，增加showBackdrop = true
    if user?.edmNo
      if user.edmNo is MSG_STRINGS.REQUEST_EDMNO
        status = 'resubscribe'
      else
        status = 'blocked'
      return {status, showBackdrop: true}

    # 检查邮箱验证状态 - 参考 getUsersForEdm 的 emlVCondition 逻辑
    isEmailValid = false
    if user?.emlV? and (user.emlV isnt false)
      # emlV存在且不为false
      isEmailValid = true
    else if user?.ts? and (user.ts < IGNORE_EMLV_DATE)
      # 在指定日期之前注册的用户不检查emlV
      isEmailValid = true
    else if (not user?.emlV?) and (user?.googleId? or user?.facebookId? or user?.appleId?)
      # 第三方注册用户且emlV不存在的情况
      isEmailValid = true
    
    # 情况2：如果user?.emlV?不存在或者为false，则status = unverified
    if (not user?.emlV?) or (user.emlV is false)
      showBackdrop = if isEmailValid then false else true
      return {status: 'unverified', showBackdrop}
    
    # 其他情况 showBackdrop = false
    return {status: 'unsubscribe', showBackdrop: false}

  ###
  # @description 检查并设置账号的edmno状态
  # @param {ObjectId} uid -  A necessary param
  # @param 'string' action - A necessary param ['set'/'unset']
  # @return 返回查找的结果
  ###
  @setEdmNoStatus:({uid,action})->
    user = await UserCol.findOne {_id:uid},{projection:{edmNo:1}}
    if (action is 'set') and (not user.edmNo)#用户主动设置edmno
      update = {$set:edmNo:MSG_STRINGS.REQUEST_EDMNO}
      msg = 'Unsubscribed'
    else if (action is 'unset') and (user.edmNo is MSG_STRINGS.REQUEST_EDMNO)
      # 用户主动设置的edmno可以unset，其他状态不允许unset
      update = {$unset:edmNo:1}
      msg = 'Subscribed'
    else
      msg = 'Email receiving status cannot be modified.'
    if update
      await UserCol.updateOne {_id:uid},update
    return msg

  ###
  # @description 处理加密信息，获取用户uid和加密时间
  # @param 'string' i -  A necessary param
  # @return 返回处理后，获取到的uid和ts
  ###
  @decodeUserFromParam:(i)->
    SHARE_SECRET = config.share?.secret
    if (Array.isArray i) and i.length > 0
      i = i[0]
    [pass, obj] = checksum.shareDecode(i,SHARE_SECRET)
    # debug.debug pass,obj
    unless pass
      debug.error 'decodeUserFromParam shareDecode not passed for',i
      return {err:'shareDecode not passed'}
    try
      obj = JSON.parse obj
    catch error
      debug.error 'decodeUserFromParam JSON.parse failed',obj
      return {err:'JSON.parse failed'}
    if obj
      uid = obj?.uid
      ts = obj?.ts
    return {uid,ts}

  ###
  # @description 获取dailyNotify通知的用户
  # @return user list
  ###
  @getUsersForDailyNotify:(isMorning)->
    q =
      roles:'_dev'
      pn:{$exists:1}
      $or:[{city:{$exists:1}},{cities:{$exists:1}},{ck:{$exists:1}}]
    delete q.roles unless config.serverBase?.developer_mode
    if isMorning
      q['profile.pnNoMLSM'] = $ne:1
    else
      q['profile.pnNoMLSE'] = $ne:1
    return await UserCol.aggregate [
      {$lookup:{
        from: 'user_profile',
        let: { userid: '$_id' },
        pipeline: [
          {$match: {$expr:{ $eq: [ '$_id',  '$$userid' ] }}},
          {$project:{pnNoLM:1,_id:0,pnNoNews:1,pnNoMLSM:1,pnNoMLSE:1,pnNoWPRpt:1}}
        ],
        as: 'profile'
      }},
      {$match:q},
      {$project:{pn:1,ck:1,cn:1,locale:1,city:1,cities:1}}
    ],{cursor:{batchSize:0}}

  ###
  # @description 更新用户的uuid和b4luuid
  ###
  @bindUid:({user,uuid})->
    return null if not (uuid and user?._id)
    id = user._id
    update = {$addToSet:{b4luuid:uuid}}
    user = await UserCol.findOne {_id:new ObjectId(id)},{uuid:1}
    orgUuid = user?.uuid
    if not orgUuid?
      update.$set = {uuid:uuid}
    await UserCol.updateOne {_id:new ObjectId(id)},update,
    await UUIDCol.updateOne {_id:uuid},{$set:{uid:id}}
    return orgUuid

  ###
  # @description 查询客户是否验证了邮箱和手机
  # NOTE: emlV 字段在user下
  # @return emlV:true/false
      mblV:true/false
      eml: user.Eml
      mbl:user.mbl
  ###
  @checkVerify:(uid)->
    return {} unless uid
    profile = await UserProfile.findOne {_id:uid},{fields:{emlvts:1,mblvts:1}}
    user = await UserCol.findOne {_id:uid},{fields:{eml:1,emlV:1,mbl:1}}
    return {
      emlV:profile?.emlvts? or user?.emlV?
      mblV:profile?.mblvts?
      eml: User.getEmail user
      mbl:user.mbl
    }

  ###
  # @description 删除用户电话及验证信息
  # @param {objectId} uid
  # @returns {object} - 返回删除结果，成功返回null
  ###
  @deletePhone:({req,user})->
    # 检查用户是否有published rmlisting，存在则不能删除联系方式
    rmlisting = await Properties.findToArray {uid:user._id}
    if rmlisting?.length > 0
      return {e:'In use'}
    err = await User.deletePhoneVerify {uid:user._id}
    return err if err
    await User.updateAndChangeSessionAsync {req,user,data:{mbl:null}}
    return null

  ###
  # @description 通过phone number或email查找用户
  # @param {objectId} uid
  # @param {string} eml - 邮箱
  # @param {string} mbl - 手机号
  # @param {boolean} isAdmin - 是否是管理员
  # @returns {object} - 返回删除结果，成功返回null,不能删除返回e
  ###
  @getByEmlOrMbl:({uid,eml,mbl,isAdmin})->
    queryOr = []
    if eml
      queryOr.push {eml:new RegExp(eml,'g')}
    if mbl
      mbl = formatPhoneNumber mbl
      queryOr.push {mbl}
    return await UserCol.findToArray {$or:queryOr},{projection:{eml:1,mbl:1,roles:1}}


  ###
  # @description 删除用户验证信息
  # @param {objectId} uid
  # @param {string} eml - 邮箱
  # @param {string} mbl - 手机号
  # @param {boolean} isAdmin - 是否是管理员
  # @returns {object} - 返回删除结果，成功返回null,不能删除返回e
  ###
  @deletePhoneVerify:({uid,eml,mbl,isAdmin})->
    uid = [uid]
    if (eml or mbl)
      if not isAdmin
        return {e:MSG_STRINGS.NO_AUTHORIZED}
      else
        mbl = formatPhoneNumber mbl if mbl
        users = await UserCol.findToArray {$or:[{eml:eml},{mbl:mbl}]}
        uid = users.map (user)->
          return user._id
    await UserProfile.updateMany {_id:{$in:uid}},{$unset:{mblvts:1,\
    'verify.mblvcd':1, 'verify.mblvts':1, 'verify.vfd':1}}
    return null

  ###
  # @description 添加 Sponsor 用户
  # @param {Object} proj - project detail信息
  # @return 增加了Sponsor 用户信息的project detail信息
  ###
  @injectSponsorUser:(opt, proj)->
    getAllIds = (objFld,proj)->
      ret = {}
      return ret unless Object.keys(objFld) and proj
      for fld in Object.keys(objFld)
        continue unless proj[fld]
        ret[fld]=proj[fld]
      return ret

    isApp = opt.devType is 'app'
    isAdmin = opt.isAllowedMarketAdmin
    isCreateProject = opt.isAllowedCreateProject
    #DIY refactor
    # spuids，spgids， sendTouids，sendTogids
    # get all uid in flooragent，comment，app：spuids，spgids， sendTouids，sendTogids，
    # spuids，spgids， sendTouids，sendTogids，
    # then,  get group info from if spgids sendTogids, web:spgidsWeb sendTogidsWeb
    # in edit page, need to inject all
    # key is proj fld name, val is result fldname after injection

    #sendTouidsWeb,'sendTouids', 'sendToGid','sendTogidsWeb' not used anymore
    appUidFld = {'spuids':'agents'}
    appGidFld =  {'spgids':'sponsorGroups'}
    webUidFld = {'spuidsWeb':'agentsWeb'}
    webGidFld = {'spgidsWeb': 'sponsorGroupsWeb'}

    uidFld = if isApp then appUidFld else webUidFld
    gidFld = []
    if isAdmin or isCreateProject # 普通用户看不到group的信息
      # if is manage page, need all fields
      uidFld = Object.assign {}, appUidFld, webUidFld
      gidFld = Object.assign {}, appGidFld, webGidFld
    # for query
    uidObjects = getAllIds uidFld,proj
    gidObjects = getAllIds gidFld,proj
    uids = []
    gids = []
    for k, v of uidObjects
      uids = uids.concat v
    for k, v of gidObjects
      gids = gids.concat v
    # add floorplan uid , comments uid to uids
    floorplan = proj.floorplan or []
    comment = proj.comment or {}
    tmp = {}
    # floorplan is array
    for p in floorplan
      uids.push new ObjectId(p.uid.toString()) if p.uid
    # comments is saved as object, uid is key
    if Object.keys(comment).length
      for k,v of comment
        uids.push new ObjectId(k.toString())
    unless uids.length
      return proj
    list = await User.getListByIds uids,{}
    unless list?.length
      return proj
    usersMapping = {}
    for u in list
      u._id = u._id.toString()
      eml = if Array.isArray(u.eml) then u.eml[0] else u.eml
      avt = libPropertyImage.replaceRM2REImagePath u.avt
      tmp = {
        _id: u._id,
        mbl:u.mbl,
        eml:eml,
        fnm:libUser.fullNameOrNickname(opt.locale,u),
        itr:u.itr,
        avt,
        cpny_en:u.cpny_en,
        cpny_zh:u.cpny_zh,
        uid:u._id.toString()
      }
      usersMapping[u._id] = tmp
      # add u in different list
      #uidFld = {'spuidsWeb':'agentsWeb', 'spuid':'agent'}
      for id,v of uidFld
        findIndex = uidObjects[id]?.findIndex (el)->
          return el.toString() is u._id.toString()
        if findIndex >=0
          proj[v]?=[]
          proj[v].push tmp
      if comment[u._id]
        proj.comment[u._id] = Object.assign(proj.comment[u._id],tmp)
    #copy agent 用户信息到floor plan 经纪
    if floorplan.length
      for p in proj.floorplan
        p = Object.assign(p, usersMapping[p.uid])
    unless gids?.length
      return proj
    groups = await Group.findToArray {_id: {$in:gids}}, {projection:{nm:1}}
    for g in groups
      for id,v of gidFld
        findIndex = gidObjects[id]?.findIndex (el)->
          return el.toString() is g._id.toString()
        if findIndex >=0
          proj[v]?=[]
          proj[v].push g
    return proj

MODEL 'User',User
