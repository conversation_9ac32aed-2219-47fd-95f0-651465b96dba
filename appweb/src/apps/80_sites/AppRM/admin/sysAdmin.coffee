libProperties = INCLUDE 'libapp.properties'
helpers = INCLUDE 'lib.helpers'
statHelper = INCLUDE 'libapp.stat_helper'
libProxyEmail = INCLUDE 'lib.proxyEmail'
{respError} = INCLUDE 'libapp.responseHelper'

# User = COLLECTION 'chome', 'user'
Login = COLLECTION 'chome', 'login'

Properties = MODEL 'Properties'
UserModel = MODEL 'User'
ForumModel = MODEL 'Forum'
LimiterModel = MODEL 'Limiter'
ProvAndCity = MODEL 'ProvAndCity'
UserEmailModel = MODEL 'UserEmail'
SysDataModel = MODEL 'SysData'
BlockPhoneModel = MODEL 'BlockPhone'

SysData = COLLECTION 'chome','sysdata'
EdmLog = COLLECTION 'chome', 'edmLog'
UserProfileCol = COLLECTION 'chome', 'user_profile'
SignupRecords = COLLECTION 'chome', 'signups'
# Transactions = COLLECTION 'chome', 'transactions'
AdminOpLogs = COLLECTION 'chome', 'admin_op_logs'
PredStat = COLLECTION 'vow','pred_stat'

TransactionModel = MODEL 'Transaction'
GroupModel = MODEL 'Group'

getConfig = DEF 'getConfig'
reloadAllConfig = DEF 'reloadAllConfig'
# updateUser = DEF 'updateUser'
popularCities = DEF 'popularCitiesWithOwnControlData'
loadPopularCities = DEF 'loadPopularCities'
customerServicesStatus = DEF 'customerServicesStatus'
# fnSwitchUser = DEF 'switchUser'
setConfig = DEF 'setConfig'
sendMail = SERVICE 'sendMail'
config = CONFIG(['mailEngine','serverBase','contact'])
debug = DEBUG 'admin'
StatModel = MODEL 'Stat'
# NOTE: aleady created in edm.coffee
# EdmLog.createIndex {'ts': -1}
initCServiceStatus = ()->
  SysData.findOne {_id:'customerServicesStatus'},(err,ret)->
    if err
      console.error err
      return
    if ret?.list?.info?
      customerServicesStatus.list = ret.list

# propCommons = INCLUDE 'libapp.propCommons'
# propConfg = DEF 'propConfg'
# translateConfig = DEF 'translateConfig'
# forumConfig = DEF 'forumConfig'
# initPropConfig = ()->
#   SysData.findOne {_id:'propConfig'},(err,ret)->
#     if err
#       console.error err
#       return
#     ret ?= {}
#     for i in ['showSoldPrice', 'isPaytopAll']
#       propConfg[i] = ret[i] or false
# initPropConfig()

# initForumConfig = ()->
#   SysData.findOne {_id:'forumConfig'},(err,ret)->
#     if err
#       console.error err
#       return
#     if ret?.hotThreshold
#       forumConfig.hotThreshold = ret.hotThreshold
# initForumConfig()

#if not login, findError will rediret to login page.
chkUserAdmin = (req,resp,findError,cb)->
  UserModel.appAuth {req,resp},(user)->
    return findError MSG_STRINGS.NEED_LOGIN unless user
    unless req.isAllowed 'userAdmin'
      # TODO: send notification to admin
      return findError "Unauthorized Operation. Admin Notified."
    cb user


APP '1.5'
APP 'admin',true
GET 'toolsList',(req, resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    unless req.isAllowed 'devGroup'
      return resp.redirect '/1.5/index'
    resp.ckup 'adminToolsListPage',{user:user}

VIEW 'adminToolsListPage',->
  div id:"helpModal", class:"modal", ->
    header class:"bar bar-nav",->
      a class:"icon icon-close pull-right", href:"javascript:;", onclick:'toggleModal("helpModal","close")',->
      h1 class:"title",-> text 'Help'
    div class:"content",->
      div ->
        label -> text '**1'
        p -> text 'remove following' # -> updateUser req, {flwng:null}'
      div ->
        label -> text '**2'
        p -> text 'remove roles' # ->  updateUser req, {roles:null}'
      div ->
        label -> text '**3'
        p -> text 'remove wechat bind' # -> updateUser req, {wxuid:null},->'
      div ->
        label -> text '))1'
        p -> text 'i18n.reload'
      div ->
        label -> text '))3'
        p -> text 'server status'
      div ->
        label -> text '))5'
        p -> text "pay stripe" #resp.redirect '/bill/pay/stripe'"
      div ->
        label -> text '))route'
        p -> text "transit routes" #resp.redirect '/1.5/transit/route'"
      div ->
        label -> text '))edm'
        p -> text "edm admin" #"resp.redirect '/sys/edm'"
      div ->
        label -> text '))ep'
        p -> text "edm report" #resp.redirect '/sys/edm/report'"
      div ->
        label -> text '))8cn/))8ca/))8'
        p -> text "jump to server" #resp.redirect '/sitejump?r=cn/ca/null'"
      div ->
        label -> text '))10'
        p -> text "event analytics" #resp.redirect '/analytics_spevent'"
      div ->
        label -> text ')))'
        p -> text "wechat auth" #resp.redirect '/wechatauth'"
      div ->
        label -> text ':;('
        p -> text "diagnose page" #resp.redirect '/diagnosePage'"
      div ->
        label -> text '**p'
        p -> text "predict list manage page"
      # div ->
      #   label -> text ")$& or ))0(("
      #   p -> text """resp.redirect "http://host?sv=" + req.cookies['apsv']"""

  header class:'bar bar-nav',->
    h1 class:'title', -> text 'Tools'
    a class:'icon fa fa-back pull-left', href:'/1.5/index'
  div class:'content',->
    ul class:"table-view",->
      li class:"table-view-cell media",->
        a class:"navigate-right", onclick:'toggleModal("helpModal")',->
          span class:"media-object pull-left icon fa fa-question-circle-o"
          div class:"media-body",->
            text 'Commands Help'
      # li class:"table-view-cell media",->
      #   a class:"navigate-right", href:'/1.5/topagent/manage',->
      #     span class:"media-object pull-left icon fa fa-top"
      #     div class:"media-body",->
      #       text 'TopAgent Manage'
      li class:"table-view-cell media",->
        a class:"navigate-right", href:'/1.5/cpm/manage?eml='+@user.eml,->
          span class:"media-object pull-left icon fa fa-top"
          div class:"media-body",->
            text 'Impression Ads Manage'


APP 'sys'
#sys/city
GET 'city',(req,resp)->
  chkUserAdmin req,resp,((err)-> resp.redirect '/1.5/index'),(
    (user)-> resp.ckup 'sys-contact-city', {cities:popularCities.list}
  )

# general app configs from db without restarting server
# supported: clear tanslate cache/show sold price/ etc.
# k:key, v:value, name:dispName, checked:if not value

generateConfigList = (req)->
  ret = []
  # ret.push {k:'translateClear', name:'clear translate cache flag', checked:getConfig('translateClear')}
  ret.push {k:'transDDF', name:'translate DDF prop', checked:getConfig('transDDF')}
  ret.push {k:'webRmInfoDisp', name:'website rm info display', checked:getConfig('webRmInfoDisp')}
  # ret.push {k:'showSoldPrice', name:'should show BC sold price to all user', checked:getConfig('showSoldPrice')}
  ret.push {k:'showSoldPriceBtn', name:'should show index sold btn to all user', checked:getConfig('showSoldPriceBtn')}
  # ret.push {k:'showIndexReno', name:'should show index reno card', checked:getConfig('showIndexReno')}
  ret.push {k:'isPaytopAll', name:'should show prop topup (if false only realtor)', checked:getConfig('isPaytopAll')}
  ret.push {k:'showForum', name:'should show forum to all user', checked:getConfig('showForum')}
  ret.push {k:'rltrTopAd', name:'should show ad agents to realtor', checked:getConfig('rltrTopAd')}
  ret.push {k:'useTileOverlay', name:'useTileOverlay for map', checked:req.session.get('useTileOverlay')}
  # ret.push {k:'showBcPropSp', name:'should show BC(prov) Lst/Sold Price to vip', checked:getConfig('showBcPropSp')}
  ret.push {k:'brokerRmrkToVip', name:'should show broker remark all vip plus', checked: getConfig('brokerRmrkToVip')}
  ret
generalConfigRadios = (req)->
  ret = []
  val = req.session.get('useWebMap')
  ret.push {k:'useWebMap',name:'use web map flag(for my self in session)',vals:[
    {k:'true',v:'true',checked:val is 'true'},
    {k:'false',v:'false',checked:val is 'false'},
    {k:'auto',v:'auto',checked:(val is null) or (val is undefined)}
    ]}
  ret
generateInputConfigList = (req)->
  ret = []
  ret.push {k:'vueCacheExpireDate', name:'vueCacheExpireDate', v:getConfig('vueCacheExpireDate'), disabled:true}
  ret.push {k:'tileUrl', name:'tileUrl for map', v:req.session.get('tileUrl')}
  ret.push {k:'forumHotThreshold', name:'Forum Hot Threshold', v:getConfig('forumHotThreshold')}
  ret.push {k:'cpm', name:'cpm price', v:getConfig('cpm')}
  ret.push {k:'cpmCnt', name:'cpm count', v:getConfig('cpmCnt')}
  ret.push {k:'cpmExp', name:'cpm timeout seconds', v:getConfig('cpmExp')}
  ret.push {k:'showingListLimit', name:'showing list limit', v:getConfig('showingListLimit')}
  ret.push {k:'showingPropsLimit', name:'showing props limit', v:getConfig('showingPropsLimit')}
  ret.push {k:'vipShowingUpcoimgLimit', name:'vip showing upcoimg limit', v:getConfig('vipShowingUpcoimgLimit')}
  ret.push {k:'isntVipShowingUpcoimgLimit', name:'isnt vip showing upcoimg limit', v:getConfig('isntVipShowingUpcoimgLimit')}
  ret.push {k:'useMysql', name:'use mysql(0/1)', v:getConfig('useMysql')}
  ret.push {k:'usePostgresql', name:'use Postgresql(0/1)', v:getConfig('usePostgresql')}
  # ret.push {k:'blockUAFields',name:'bad UA fields, reduce results and no map key',v:getConfig('blockUAFields')}
  ret

GET 'generalConfig',(req,resp)->
  cfgs = generateConfigList(req)
  cfginputs = generateInputConfigList(req)
  radios = generalConfigRadios(req)

  chkUserAdmin req,resp,(
    (err)-> resp.redirect '/1.5/index'),((user)->
      resp.ckup 'sys-general-cfgs', {cfgs:cfgs,cfginputs:cfginputs,radios:radios})

UserCityStats = COLLECTION 'chome', 'user_city_stats'
GET 'userStats',(req,resp)->
  chkUserAdmin req,resp,((err)-> resp.redirect '/1.5/index'),
    ((user)->
      UserCityStats.findToArray {}, (err, cityStatList)->
        sortedList = []
        for cityStat in cityStatList
          sortedCityStat = {_id:cityStat._id,totalSale:0,totalLease:0,vc:0}
          delete cityStat._id
          delete cityStat._mt
          for k in Object.keys(cityStat).sort()
            if /SALE/.test k
              sortedCityStat.totalSale += cityStat[k]
            if /LEASE/.test k
              sortedCityStat.totalLease += cityStat[k]
            sortedCityStat[k]=cityStat[k]
          sortedList.push sortedCityStat
        resp.ckup('sys-user-status', {statsList:sortedList})
    )

GET 'cservice',(req,resp)->
  initCServiceStatus()
  chkUserAdmin req,resp,((err)-> resp.redirect '/1.5/index'),((user)-> resp.ckup 'sys-cservice', {services:customerServicesStatus.list})

GET 'user',(req,resp)->
  chkUserAdmin req,resp,(
    (err)->
      if req.isAllowed('provAdmin')
        return resp.ckup 'sys-prov-user-admin'
      resp.redirect '/1.5/index'
    ),
    ((user)->
      param = {}
      param.eml = eml if eml = req.param 'eml'
      param.id  = id  if id = req.param 'id'
      param.popup  = popup  if popup = req.param 'popup'
      resp.ckup 'sys-user-admin',param
    )

# inrealAdmin编辑inreal group成员页面
# /sys/inreal
GET 'inreal',(req,resp)->
  findError = (err)->
    if req.isAllowed('provAdmin')
      return resp.ckup 'sys-prov-user-admin'
    resp.redirect '/1.5/index'
  UserModel.appAuth {req,resp},(user)->
    unless req.isAllowed 'inrealAdmin'
      return findError 'Unauthorized Operation. Admin Notified.'
    param = {}
    param.eml = eml if eml = req.param 'eml'
    param.id  = id  if id = req.param 'id'
    param.popup  = popup  if popup = req.param 'popup'
    resp.ckup 'sys-inreal-admin',param


# GET 'ddf',(req,resp)->
#   chkUserAdmin req,resp,((err)-> resp.redirect '/1.5/index'),((user)-> resp.ckup 'sys-ddf',{ddfShowable:getConfig('DDF_DISPLAY_POLICY')})

GET 'pntest',(req,resp)->
  getEmail = (user)->
    return if Array.isArray(user.eml) then user.eml[0] else user.eml
  postid = ''
  UserModel.appAuth {req,resp},(user)->
    unless req.isAllowed 'devGroup'
      return resp.redirect '/1.5/index'
    ForumModel.findRecommend {},(err,list)->
      postid = list[0]._id if list?.length
      resp.ckup 'sys-pushtest',{
        postid,
        email:getEmail(user),
        token:req.user.pn,
      }

GET 'signupRecords',(req,resp)->
  chkUserAdmin req,resp,((err)-> resp.redirect '/1.5/index'),((user)-> resp.ckup 'sys-signups')

GET 'agentTransactions',(req,resp)->
  curType = req.param('type') or 'transactions'
  types = ['transactions','bookkeeping','billing']
  chkUserAdmin req,resp,((err)->
    resp.redirect '/1.5/index'),((user)->
    resp.ckup 'admin/transactions',{types,curType}
  )

GET 'predictStats',(req,resp)->
  chkUserAdmin req,resp,(
    (err)-> resp.redirect '/1.5/index'),
    ((user)->
      dates = []
      now = new Date()
      # for i in [0..4]
      #   dates.push helpers.dateFormat new Date(now-i*24*3600*1000),'YYYY-MM-DD'
      date = helpers.dateFormat(statHelper.getLastMonth(now),'YYYY-MM-DD')
      q = {'_id.date':{$gt:date}}
      map = {}
      dates = []
      PredStat.findToArray q,{sort:{'_id.date':-1}},(err,ret)->
        # console.log ret
        ret ?= []
        for r in ret
          dates.push(r._id.date) if dates.indexOf(r._id.date) < 0
          key=(''+r._id.prov+r._id.city).replace(/\W/g,'').toLowerCase()
          map[key] ?= {c:r._id.city,p:r._id.prov,l:[],count:r.count}
          map[key].l.push {dt:r._id.date,count:r.count,stdp:r.stdp,diffp:r.diffp,avgsp:r.avgsp,avglp:r.avglp}
        # console.log '++++',map
        list = []
        for k,v of map
          list.push v
        list.sort (a,b)->
          return b.count - a.count
        resp.ckup 'sys-predictStats',{list:list, dates:dates}
  )

GET 'adminOps',(req, resp)->
  chkUserAdmin req,resp,((err)-> resp.redirect '/1.5/index'),((user)-> resp.ckup 'sys-admin-ops')

GET 'switch',(req,resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    user = (req.session.get('realUser') or user)
    unless req.isAllowed 'userAdmin',user
      return resp.redirect '/1.5/index'
    resp.ckup 'sys-user-switch',{user:user}


GET 'edm',(req,resp)->
  findError = (e)-> resp.redirect '/1.5/index'
  edmTitle = {}
  log = {}
  UserModel.appAuth {req,resp},(user)->
    unless req.isAllowed 'devGroup'
      return resp.redirect '/1.5/index'
    SysData.findOne {_id:'edmTitle'},(err,ret)->
      if err
        console.error err
        return
      edmTitle = ret if ret
      EdmLog.findToArray {},{sort:{ ts: -1 }, limit:1},(err, ret) ->
        # console.log ret
        if err
          console.error err
          return
        log = ret[0] if ret?.length
        resp.ckup 'sys-edm-admin', {edmTitle:edmTitle, log:log}

POST 'cservice',(req,resp)->
  findError = (e)-> resp.send {e:e?.toString() or 'Error'}
  chkUserAdmin req,resp,findError,(user)->
    req.body ?= {}
    sname = req.param 'servicename'
    # console.log req.body
    return findError('no service name') unless sname
    checked = req.param('checked') is 'true'
    customerServicesStatus.list[sname] = checked
    SysData.updateOne {_id:'customerServicesStatus'}, customerServicesStatus, {upsert:true}, (err,ret)->
      return findError err if err
      resp.send {ok:1, update:customerServicesStatus}
# 处理session相关配置的辅助函数
handleSessionConfig = ({req, resp, sname, val, checked})->
  cb = ()->
    ret = {ok:1}
    if sname is 'tileUrl'
      ret.val = val
    else
      ret[sname] = val
    resp.send ret
  if sname is 'tileUrl'
    if val?.length
      req.session.set('tileUrl', val, cb)
  else
    val = req.param 'val'
    if sname is 'useTileOverlay'
      val = req.param 'checked'
    if val in ['true', 'false']
      req.session.set(sname, val, cb)
    else
      req.session.set(sname, false, cb)
  return

# 获取配置更新信息的辅助函数
getConfigUpdate = ({sname, checked, val})->
  configs = {
    vueCacheExpireDate: {
      _id: 'translateConfig'
      update: {$set: {vueCacheExpireDate: Date.now()}}
    }
    transDDF: {
      _id: 'propConfig'
      update: {$set: {transDDF: checked}}
    }
    webRmInfoDisp: {
      _id: 'propConfig'
      update: {$set: {webRmInfoDisp: checked}}
    }
    isPaytopAll: {
      _id: 'propConfig'
      update: {$set: {isPaytopAll: checked}}
    }
    rltrTopAd: {
      _id: 'propConfig'
      update: {$set: {rltrTopAd: checked}}
    }
    brokerRmrkToVip: {
      _id: 'propConfig'
      update: {$set: {brokerRmrkToVip: checked}}
    }
    showForum: {
      _id: 'forumConfig'
      update: {$set: {showForum: checked}}
    }
    forumHotThreshold: {
      _id: 'forumConfig'
      update: {$set: {hotThreshold: parseInt(val)}}
    }
    cpm: {
      _id: 'cpmConfig'
      update: {$set: {cpm: parseInt(val)}}
    }
    cpmCnt: {
      _id: 'cpmConfig'
      update: {$set: {cpmCnt: parseInt(val)}}
    }
    cpmExp: {
      _id: 'cpmConfig'
      update: {$set: {cpmExp: parseInt(val)}}
    }
    useMysql: {
      _id: 'sqlConfig'
      update: {$set: {useMysql: parseInt(val)}}
    }
    usePostgresql: {
      _id: 'sqlConfig'
      update: {$set: {usePostgresql: parseInt(val)}}
    }
    blockUAFields: {
      _id: 'propConfig'
      update: {$set: {blockUAFields: val}}
    }
  }
  # else if sname is 'showSoldPrice'
  #   # propConfg.showSoldPrice = checked
  #   _id = 'propConfig'
  #   update = {$set:{showSoldPrice:checked}}
  # else if sname is 'showIndexReno'
  #   _id = 'indexConfig'
  #   update = {$set:{showIndexReno:checked}}
  # else if sname is 'showSoldPriceBtn'
  #   # propConfg.showSoldPrice = checked
  #   _id = 'propConfig'
  #   update = {$set:{showSoldPriceBtn:checked}}
  # else if sname is 'showBcPropSp'
  #   _id = 'propConfig'
  #   update = {$set:{showBcPropSp:checked}}

  # 处理showing相关配置
  if sname in ['showingListLimit','showingPropsLimit','vipShowingUpcoimgLimit','isntVipShowingUpcoimgLimit']
    set = {}
    set[sname] = val
    return {
      _id: 'showingConfig'
      update: {$set: set}
    }
  return configs[sname] or throw new Error('unknown cfg')

#TODO: support prop sold price permission here
POST 'generalConfig',(req,resp)->
  findError = (e)-> resp.send {e:e?.toString() or 'Error'}
  chkUserAdmin req,resp,findError,(user)->
    req.body ?= {}
    sname = req.param 'servicename'
    return findError('no service name') unless sname
    checked = req.param('checked') is 'true'
    val = req.param('value')
    # 处理session相关的配置
    if sname in ['tileUrl', 'useWebMap', 'useTileOverlay']
      return handleSessionConfig {req, resp, sname, val, checked}
    # 获取配置更新信息
    try
      {_id, update} = getConfigUpdate {sname, checked, val}
    catch err
      return findError('unknown cfg')
    # TODO: use setConfig to update field
    # TODO: use setConfig to update field
    # 更新配置
    SysData.updateOne {_id:_id}, update, {upsert:true}, (err,ret)->
      return findError err if err
      # showBcPropSp
      reloadAllConfig()
      if sname is 'forumHotThreshold'
        ForumModel.updateHotThreshold {val},(err,ret)->
          return findError err if err
          resp.send {ok:1, update}
      else
        resp.send {ok:1, update}

#post sys/city
POST 'city',(req,resp)->
  findError = (e)-> resp.send {e:e?.toString() or 'Error'}
  chkUserAdmin req,resp,findError,(user)->
    req.body ?= {}
    prov = req.param 'prov'
    city = req.param 'city'
    # console.log req.body
    tp = req.param('tp') or 'sale'
    return findError('no prov and city') unless prov and city
    # fld = '$unset'
    checked = 0
    if (tmp = req.param 'checked') and tmp is 'true'
      checked = 1
    set = {}
    prov = ProvAndCity.getProvAbbrName(prov)
    set[prov+'.'+libProperties.formatCityName(city)+'.'+tp] = checked
    update = {$set:set}
    SysData.updateOne {_id:'ownCtrlCity'}, update, {upsert:true}, (err,ret)->
      return findError err if err
      loadPopularCities()
      resp.send {ok:1, update:update}

POST 'switch',(req,resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    realUser = (req.session.get('realUser') or user)
    unless req.isAllowed 'userAdmin',realUser
      return resp.send {ok:0,err:"No Auth"}
    body = req.body
    # add
    if body.act is 'add'
      UserModel.findByEml body.eml,{projection:{eml:1,roles:1}},(err,u)->
        if err
          console.error err
          return resp.send {ok:0,err:u.err.toString()}
        unless u
          return resp.send {ok:0,err:"User not found"}
        if u.roles?.indexOf('_admin') >= 0
          return resp.send {ok:0,err:"Not authorized"}
        (switchUsers = realUser.switchUsers or []).push body.eml
        if switchUsers.length > 16
          return resp.send {ok:0,err:"Too many switching users. Limit 16."}
        UserModel.updateSwitchUsers {req,realUser,switchUsers},(err)->
          if err
            console.error err
            return resp.send {ok:0,err:err.toString()}
          return resp.send {ok:1,url:'/sys/switch'}
      return null
    # del
    if (body.act is 'del')
      return resp.send {ok:0,err:"Bad switchUsers"} unless Array.isArray(switchUsers = realUser.switchUsers)
      for eml,i in switchUsers
        if eml is body.eml
          switchUsers.splice i,1
          break
      UserModel.updateSwitchUsers {req,realUser,switchUsers},(err)->
        if err
          console.error err
          return resp.send {ok:0,err:err.toString()}
        return resp.send {ok:1,url:'/sys/switch'}
      return null
    # switch
    if (body.act is 'switch') or (body.act is 'back')
      eml = if body.act is 'back' then null else body.eml
      UserModel.switchUser {req,newUser:eml},(ret)->
        if ret.ok
          ret.url = '/1.5/index'
        resp.send ret
      return null
    resp.send {ok:0,err:"Unknown command #{body.act}"}


# POST 'ddf',(req,resp)->
#   findError = (e)-> resp.send {e:e?.toString() or 'Error'}
#   chkUserAdmin req,resp,findError,(user)->
#     req.body ?= {}
#     hideAll = (req.body.hideAll+'') is 'true'
#     hideEng = (req.body.hideEng+'') is 'true'
#     all = (req.body.all+'') is 'true'
#     value = 'loginOnly'
#     if hideEng
#       value = 'hideEng'
#     if hideAll
#       value = 'hideAll'
#     if all
#       value = 'all'
#     #set = {$set:{ddfShowable:value, mt:new Date()}}
#     #SysData.updateOne {_id:'propConfig'}, set, {upsert:true}, (err,ret)->
#     setConfig 'DDF_DISPLAY_POLICY', value, (err,oldVal)->
#       return findError err if err
#       #updateConfigVals(true)
#       resp.send {ok:1, set:value, ret:ret}

#u1 u2 same rst_prov
isSameProv = (u1, u2)->
  p1 = u1.rst_prov or []
  p2 = u2.rst_prov or []
  p1[0] is p2[0]

#post /sys/provuser
POST 'provuser',(req,resp)->
  findError = (e)-> resp.send {e:e?.toString() or 'Error'}
  unless req.isAllowed 'provAdmin'
    return findError 'Not Allowed'
  # add vip/vip_plus
  UserModel.appAuth {req,resp},(user)->
    unless (b = req.body).eml and b.act
      return findError "Bad Param"
    unless user.rst_prov
      return findError "No Rst_prov, please contact admin"
    UserModel.findByEml b.eml,{},(err,u)->
      if err then return findError err
      unless u
        return resp.send {ok:0,err:'user not found',user:'Not Found'}
      if req.isAllowed('admin', u)
        return findError 'Not authed'
      UserModel.switchAdminAction {b,u,isVip:req.isAllowed('vipUser',u),isSameProv:isSameProv(user,u),uid:user._id,ueml:user.eml.toString()},(ret)->
        return findError ret.err if ret.err
        return resp.send ret.userInfo if ret.userInfo
        ret.method {req,user:u,data:ret.data},(err,nu)->
          if err then return findError err
          resp.send {ok:1,roles:nu?.roles?.toString()}

#post '/sys/user'
POST 'user',(req,resp)->
  findError = (e)-> resp.send {e:e?.toString() or 'Error'}
  chkUserAdmin req,resp,findError,(user)->
    # add vip/vip_plus
    unless (b = req.body).eml and b.act
      return findError "Bad Param"
    fn = if helpers.isEmail b.eml then UserModel.findByEml else UserModel.findById
    fn b.eml,{},(err,u)->
      if err then return findError err
      unless u
        return resp.send {ok:0,err:'user not found',user:'Not Found'}
      UserModel.switchAction {b,u,uid:user._id,ueml:user.eml.toString()},(err,ret)->
        return findError err if err
        return resp.send ret.userInfo if ret.userInfo
        return resp.send {l:ret.logList} if ret.logList
        UserModel[ret.method] {req,user:u,data:ret.data},(err,nu)->
          if err then return findError err
          resp.send {ok:1,roles:nu?.roles?.toString()}
          # after work
          #TODO: set-rid ddf_bid: branch id
          if (b.act is 'set-id') and aid = b.id
            aid = 'TRB' + aid unless /^TRB/.test aid
            Properties.findOneByAgentId aid,(err,listing)->
              if err then console.error err
              return unless bid = listing?.bid
              UserModel.updateAgentId {req,user:u,aid,bid},(err)->
                if err then console.error err
          # change edmNo,record history
          else if b.act in ['no-edm','add-edm']
            if b.act is 'no-edm'
              eventType = 'Complaint'
            else
              eventType = 'Subscription'
            try
              await UserEmailModel.setSuspendAndEmailHistory {emails:[b.eml],eventType,reason:'admin set'}
            catch err
              debug.error 'setSuspendAndEmailHistory error:',err,'eml:',b.eml
            return

#post '/sys/inreal'
###
act: make-vip-plus 加入inreal group并且成为vip_plus
act: make-vip-plus 退出inreal group并且删除vip权限
###
POST 'inreal',(req,resp)->
  findError = (e)->resp.send {e:e?.toString() or 'Error'}
  # 权限校验
  UserModel.appAuth {req,resp},(user)->
    unless req.isAllowed 'inrealAdmin'
      return findError 'Unauthorized Operation. Admin Notified.'
    unless (b = req.body).eml and b.act
      return findError req.l10n MSG_STRINGS.BAD_PARAMETER

    fn = if helpers.isEmail b.eml then UserModel.findByEmlAsync else UserModel.findByIdAsync
    try
      u = await fn b.eml,{}
    catch err
      debug.error err
      return findError err
    unless u
      return findError req.l10n MSG_STRINGS.USER_NOT_FOUND

    # 是经纪才能继续操作
    isRealtor = req.hasRole 'realtor',u
    unless isRealtor
      return findError req.l10n 'The user must be a realtor'

    # 无法操作super admin
    isSuperAdmin = req.hasRole '_admin',u
    if isSuperAdmin
      return findError req.l10n 'The user cant be operated'

    # NOTE: 需求改变，一键加入inrealgroup
    # 检查用户是否已经在inreal group,否则无法操作用户
    # try
    #   groupid = await GroupModel.isInGroup {uid:u._id,groupName:':inReal'}
    # catch err
    #   debug.error err
    #   return findError err
    # if not groupid
    #   return findError req.l10n MSG_STRINGS.USER_NOT_FOUND
    
    # 操作用户权限
    ret = UserModel.switchInRealAdminAction {b,u,uid:user._id,ueml:user.eml.toString()}
    # 直接返回用户信息
    if ret.userInfo
      return resp.send ret.userInfo
    try
      method = ret.method+'Async'
      nu = await UserModel[method] {req,user:u,data:ret.data}
    catch err
      debug.error err
      return findError err

    # 添加用户进入group
    if b.join
      try
        ret = await GroupModel.switchAction {b,groupName:':inReal'}
        method = ret.method
        nu = await GroupModel[method] {user:nu,id:ret.gid,opt:{}}
      catch err
        debug.error err
        return findError err
    resp.send {ok:1,roles:nu?.roles?.toString(),operation:method}

# TODO: chat.coffee, modify user chats
POST 'chgEmail',(req,resp)->
  findError = (e)-> resp.send {e:e?.toString() or 'Error'}
  chkUserAdmin req,resp,findError,(user)->
    # add vip/vip_plus
    unless (b = req.body).eml and b.neml
      return findError MSG_STRINGS.BAD_PARAMETER#'Bad Parameter'
    b.neml = b.neml.toLowerCase()
    unless helpers.isEmail b.neml
      return findError MSG_STRINGS.BAD_PARAMETER#'Bad Parameter'
    # changeemail
    UserModel.afterChangeEmailOperationsNew {eml:b.eml,neml:b.neml,ueml:user.eml,uid:user._id},(err,ret)->
      return findError err if err
      resp.send {ok:1,nChat:ret?.nChat}

POST 'chgPwd',(req,resp)->
  findError = (e)-> resp.send {e:e?.toString() or 'Error'}
  chkUserAdmin req,resp,findError,(user)->
    # add vip/vip_plus
    unless (pwd = (b = req.body).pwd) and (eml = b.eml)
      return findError "Bad Parameter"
    unless pwd.length >=5
      return findError 'Password is too short'
    UserModel.resetPassword {eml,pwd},(err)->
      # return resp.ckup 'forgotPwd',{title:'Retrieve Password',err:err} if err
      return findError(err) if err
      resp.send {ok:1}

POST 'chgFornm',(req,resp)->
  findError = (e)-> resp.send {e:e?.toString() or 'Error'}
  chkUserAdmin req,resp,findError,(userAdmin)->
    # add vip/vip_plus
    unless (fornm = (b = req.body).fornm) and (eml = b.eml)
      return findError "Bad Parameter"
    UserModel.findByEml eml,{}, (err, user) ->
      UserModel.updateForumName {req,user,fornm},(err)->
        return findError(err) if err
        resp.send {ok:1}

# /sys/chgEdmPrioy 修改用户edmPriority字段 (用于assignment发送edm房源排序，edmPriority优先级高的房源会排在前面显示)
POST 'chgEdmPrioy',(req,resp)->
  findError = (e)-> resp.send {e:e?.toString() or 'Error'}
  chkUserAdmin req,resp,findError,(userAdmin)->
    eml = req.body?.eml
    edmPriority = req.body?.edmPriority
    unless eml and edmPriority?
      return findError MSG_STRINGS.BAD_PARAMETER
    parseIntPriority = parseInt(edmPriority)
    if Number.isNaN(parseIntPriority)
      return findError MSG_STRINGS.BAD_PARAMETER
    UserModel.findByEml eml,{}, (err, user) ->
      unless user
        return findError MSG_STRINGS.USER_NOT_FOUND
      UserModel.updateOneById user._id,{update:{$set:{edmPriority: parseIntPriority}}},(err)->
        return findError(err) if err
        resp.send {ok:1}

# /sys/chgAgentLevel 修改用户Level，A，B，C...
POST 'chgAgentLevel',(req,resp)->
  findError = (e)-> respError {clientMsg:req.l10n(e), resp}
  chkUserAdmin req,resp,findError,(userAdmin)->
    eml = req.body?.eml
    level = req.body?.level
    unless eml and level
      return findError MSG_STRINGS.BAD_PARAMETER
    patternTest = /^[A-Z]$/g.test(level)
    unless patternTest
      return findError MSG_STRINGS.BAD_PARAMETER
    UserModel.findByEml eml,{}, (err, user) ->
      unless user
        return findError MSG_STRINGS.USER_NOT_FOUND
      UserModel.updateOneById user._id,{update:{$set:{lvl: level}},Coll: UserProfileCol},(err)->
        return findError(err) if err
        AdminOpLogs.insertOne {act:'change agent level', ts:new Date(),uid:userAdmin._id,target:eml,op:{editLevel: level}}
        resp.send {ok:1}

POST 'blockChat',(req,resp)->
  findError = (e)-> resp.send {e:e?.toString() or 'Error'}
  chkUserAdmin req,resp,findError,(userAdmin)->
    # add vip/vip_plus
    unless (eml = req.body?.eml)
      return findError "Bad Parameter"
    tp = req.param 'tp'
    pn = false
    id = false
    if tp is 'deviceIdBlock'
      id = true
    if tp is 'devicePnBlock'
      pn = true
    if tp is 'deviceIdBlockForum'
      forum = true
    UserModel.findByEml eml,{}, (err, user) ->
      UserModel.blockChatOrCommentByDevId {req,user,id,pn,forum},(err,ret)->
        return findError(err) if err
        resp.send {ok:1,ret}

POST 'chgEdmTl',(req,resp)->
  findError = (e)-> resp.send {e:e?.toString() or 'Error'}
  UserModel.appAuth {req,resp},(user)->
    unless req.isAllowed 'devGroup'
      return resp.send {e:e?.toString() or 'Error'}
    # add vip/vip_plus
    unless tl = (b = req.body).tl
      return findError "Bad Parameter"
    SysData.updateOne {_id:'edmTitle'}, {$set:{tl:tl, ts:new Date()}}, {upsert:true}, (err,ret)->
      return findError err if err
      resp.send {ok:1, set:tl}

POST 'rmTask',(req,resp)->
  findError = (e)-> resp.send {e:e?.toString() or 'Error'}
  chkUserAdmin req,resp,findError,(user)->
    unless eml = req.body?.eml
      return findError "Bad Parameter"
    UserModel.findByEml eml,{},(err,u)->
      if err or not u? then return findError err
      UserModel.setTskDn u._id,{},(err)->
        if err then return findError err
        resp.send {ok:1}

getTargetUser = (req,resp,cb)->
  UserModel.appAuth {req,resp},(me)->
    return cb(MSG_STRINGS.NEED_LOGIN) unless me
    if req.isAllowed 'userAdmin'
      #admin，如果有eml,用eml查到用户
      if eml = req.body?.eml
        findTheLatestPn eml,cb
      else if pn = req.body?.pn # 如果没有eml，有pn，直接使用pn
        return cb null, {pn:pn}
      else # 发给管理员自己。
        return findTheLatestPn me.eml,cb
    else
      #非管理员，如果
      return findTheLatestPn me.eml,(err,user)->
        return cb err if err
        # security risk using client side pn for non admin
        # if pn = req.body?.pn
        #   user.pn ?= pn
        return cb null, user

findTheLatestPn = (eml,cb)->
  UserModel.findByEml eml,{projection:{pn:1,eml:1}},cb


pushNotifyModel = MODEL 'PushNotify'
sendOneMessageV2 = pushNotifyModel.send
# /sys/pntest
# 登陆用户在notification->diagnose页面测试自己的pn
# 管理员Admin command pn test page通过eml或者pn测试
# 如果页面传过来pn，优先使用页面传过来。
POST 'pntest',(req,resp)->
  fnError = (err)->
    resp.send {ok:0, err:err}
  getTargetUser req,resp,(err,user)->
    return fnError err if err
    return fnError 'No user found' unless user
    m = req.body?.m or 'test msg中文内容'
    url = req.body?.url
    title = req.body?.title
    # launchImage = req.body?.launchImage
    timeout = parseInt(req.body?.timeout) or 0
    if req.param 'view'
      return resp.send {ok:1,pn:user.pn,eml:user.eml}
    #等待pushnotify 返回，确认收到了message
    debug.info 'user send pntest',user,m,url
    pn = req.param('pn') or user.pn
    helpers.wait timeout, {from:'RealMaster test',to:pn, msg:m, url, title},sendOneMessageV2    # setTimeout ()->
    #当前的api返回。
    resp.send {ok:1,pn:user.pn,ts:new Date()}

POST 'signups',(req, resp)->
  fnError = (err)->
    resp.send {ok:0, err:err}
  chkUserAdmin req,resp,fnError,(user)->
    page = req.param('page') or 0
    limit = 5
    skip = limit * page
    SignupRecords.findToArray {},{sort:{ts:-1},skip:skip,limit:limit},(err,ret)->
      fnError err.toString() if err
      resp.send {l:ret}

POST 'tatransactions',(req, resp)->
  fnError = (err)->
    resp.send {ok:0, err:err}
  chkUserAdmin req,resp,fnError,(user)->
    page = req.body.page or 0
    type = req.body.type
    eml = req.body.eml
    TransactionModel.findList {page,type,eml},(err,ret)->
      fnError err.toString() if err
      resp.send {l:ret}

POST 'adminops',(req, resp)->
  fnError = (err)->
    resp.send {ok:0, err:err}
  chkUserAdmin req,resp,fnError,(user)->
    page = req.param('page') or 0
    limit = 30
    skip = limit * page
    AdminOpLogs.findToArray {},{sort:{ts:-1},skip:skip,limit:limit},(err,ret)->
      fnError err.toString() if err
      resp.send {l:ret}


POST 'deleteUser',(req, resp)->
  findError = (err)->
    debug.error err
    resp.send {ok:0, err:err}
  chkUserAdmin req,resp,findError,(user)->
    unless eml = req.body?.eml
      return findError MSG_STRINGS.BAD_PARAMETER#'Bad Parameter'
    LimiterModel.isOverLimit {type:LimiterModel.REMOVE_USER,key:req.remoteIP()},(err,isUpperLimit)->
      return findError err if err
      if isUpperLimit
        err = req.l10n 'The maximum number of users deleted today has been reached'
        return findError err
      UserModel.deleteUser {eml:eml,uuid:user._id},(err)->
        return findError err if err
        resp.send {ok:1}

POST 'verifyUser',(req, resp)->
  findError = (err)->
    debug.error err
    resp.send {ok:0, err:err}
  chkUserAdmin req,resp,findError,(user)->
    unless eml = req.body?.eml
      return findError MSG_STRINGS.BAD_PARAMETER
    try
      await UserModel.emailVerified {eml:eml,vCode:'set by admin'}
    catch err
      return findError err if err
    resp.send {ok:1}

VIEW 'sys-user-status',->
  js "/js/jquery-1.10.1.min.js"
  text """<style>
    div.holder{
      word-wrap: break-word;
      padding: 10px;
      background:white;
      margin-top:10px;
      font-size: 15px;
    }
    .type{
      width:50%;
      padding-right:10px;
      display:inline-block;
    }
    .daily{
      font-size:13px;
      color:#777;
      padding-left:10px;
    }
    .city{
      padding-bottom: 5px;
      font-size:17px;
    }
    </style>"""
  header class: 'bar bar-nav', style:" padding-right:0px;", ->
    a class: 'icon fa fa-back pull-left', style:"padding-right:10px; padding-left:10px;", href: '/1.5/index'
    h1 class: 'title', -> text _("RealMaster")
  
  div class: 'content', style:"background-color: rgb(230, 230, 230);",->
    div class:'holder', ->
      div style:'white-space:pre-wrap',->
        'Clicks are from propDetail page, realtor contact page, yellow page and wesite. \n
        Total count does not include click on contact \n
        SALE_EML is from for sale prop detail page \n
        LEASE_EML is from for lease prop detail page \n'

    for c in @statsList
      div class:'holder', ->
        for k,v of c
          if k is '_id'
            div class:'city', ->  text c._id
          else if k is 'tp'
          else if k is 'stat'
            a class:'monthly-stats-label type', id:c._id, -> text 'Monthly Stats'
            div class:"monthly-stats monthly-stats-#{c._id}", ->
              for kk,vv of v
                div class:'daily',->
                  span class:'type',-> text kk
                  span -> text vv
          else
            div ->
              typeStr = k
              if k is 'vc'
                typeStr = 'Total clicked'
              span class:'type', -> text typeStr
              span class:'', -> text v
    coffeejs ->
      $ ->
        $('.monthly-stats-label').on 'click',(e)->
          id = $(e.target).attr('id')
          if $(".monthly-stats-"+id).css('display') is 'none'
            $(".monthly-stats-"+id).css('display','block')
          else
            $(".monthly-stats-"+id).css('display','none')
      null



VIEW 'sys-general-cfgs',->
  js "/js/jquery-1.10.1.min.js"
  text """<style>
    div label{
      font-weight: normal;
      padding: 4px 9px;
    }
    .row {
      border-bottom: 1px solid rgb(230, 230, 230);
      padding: 5px 0 5px 20px;
    }
    </style>"""
  header class: 'bar bar-nav', style:"padding-right:0px;", ->
    a class: 'icon icon-close pull-right nobusy', style:"padding-right:10px; padding-left:10px;", href: '/1.5/index'
    h1 class: 'title', -> text _("RealMaster")
  div class: 'content', style:"background-color: #f1f1f1;",->
    for c in @cfgs
      div class:"row",->
        # span class:'name', -> text c.prov+'-'+c.city
        k = c.k
        inputParams = value:k, id:k
        if c.checked
          inputParams.checked = 1
        input class:'switch', name:k,type:'checkbox',inputParams
        label for:k, -> text c.name
    for c in @radios
      div class:"row",->
        # span class:'name', -> text c.prov+'-'+c.city
        label for:c.k, -> text c.name
        select id:c.k, class:'select',->
          for v in c.vals
            inputParams = value:v.k
            if v.checked
              inputParams.selected = 1
            option inputParams, -> v.v
    for d in @cfginputs
      div class:"row",->
        label ->
          text d.name
        inputParam = style:'width:100px;',value:d.v
        if d.disabled
          inputParam.disabled = true
        input inputParam
        button id:d.k,class:'chginput chgEmail btn',style:'margin-left:10px;',-> "Update"
    # div style:"margin-top:40px",->
    #   input type:'text',id:'nativeKey'
    #   input type:'text',id:'nativeVal'
    #   button id:'setSystemValue',-> "setSystemValue"
    div style:"margin-top:40px",->
      button onclick:"RMSrv.showSystemValue()",-> "Show native config values"
    # div ->
    #   label ->
    #     text _("CPM")
    #   input id:'cpm',name:'cpm',style:'width:100px;',value:@cpm
    #   button id:"chgCpm",class:'chgEmail btn',style:'margin-left:10px;',-> "Update"
    js '/js/sys_general_cfgs.min.js'



VIEW 'sys-admin-ops',->
  js "/js/jquery-1.10.1.min.js"
  text """<style>
  .content{
    overflow-y:scroll;
    background-color: rgb(230, 230, 230);
  }
  .record{
    margin-bottom:10px;
    background:white;
    padding:10px;
    font-size: 15px;
    color: #666;
  }
  label{
    min-width: 90px;
    color:black;
  }
  .idx{
    color:#e03131;
  }
  </style>"""
  header class: 'bar bar-nav', ->
    a class: 'icon icon-close pull-right nobusy', style:"padding-right:10px; padding-left:10px;", href: '/1.5/index'
    h1 class: 'title', -> "Admin ops"
  div class: 'content', ->
    ul class:'table-view'
    button class:"btn btn-block btn-primary", -> text "Get Records"
  js '/js/sys_admin_ops.min.js'


VIEW 'sys-predictStats',->
  js "/js/jquery-1.10.1.min.js"
  text """<style>
.table-view-cell{
  padding: 0;
  height:56px;
  overflow-y:hidden;
}
.table-view-cell > div{
  display:inline-block;
  vertical-align:top;
}
.left .table-view-cell{
  padding:5px 0 5px 10px;
}
.right .table-view-cell > div{
  border-right:1px solid #f1f1f1;
  display: inline-block;
  width: 90px;
  height: 56px;
}
.right .table-view-cell > div > div{
  padding: 2px 5px;
  height:28px;
  text-align: center;
}
.left{
  width:30%;
  font-size:13px;
  color:#888;
  border-right: 1px solid #f1f1f1;
}
.left .city{
  color:black;
  font-size: 14px;
  white-space: nowrap;
  overflow-x: scroll;
  width: 100px;
}
.left, .right{
  display: inline-block;
  vertical-align:top;
}
.right{
  width:70%;
  overflow-x:scroll;
  white-space: nowrap;
}
.gray{
  background:#f1f1f1;
}
  </style>"""
  header class: 'bar bar-nav', ->
    a class: 'icon fa fa-back pull-left', href: '/1.5/index'
    h1 class: 'title', -> "Stats"
  div class: 'content', ->
    div class:'left',->
      ul class:'table-view',->
        li class:'table-view-cell', ->
          div class:'city', -> 'City'
        for s in @list
          li class:'table-view-cell', ->
            div class:'city', -> s.c
            div -> 'count: '+s.count
            if s.avglp
              div -> 'lp: '+parsePrice(s.avglp)
            if s.avgsp
              div -> 'sp: '+parsePrice(s.avgsp)
    div class:'right',->
      width = 100*@dates.length
      ul class:'table-view', style:"width:#{width}px",->
        li class:'table-view-cell dates', ->
          for i in @dates
              div -> i
        parseResult = (r)->
          return (r*100).toFixed(3)+'%'
        parsePrice = (r)->
          return '' unless r
          return (r/1000)+'k'
        for s in @list
          li class:'table-view-cell', ->
            for i in s.l
              div ->
                # div class:'ts',->
                #   text i.dt
                div ->
                  # text 'stdp'
                  text parseResult(i.stdp)
                div class:'gray',->
                  # text 'diffp'
                  text parseResult(i.diffp)
          # div ->
          #   # div class:'ts',->
          #   #   text 'Date'
          #   div ->
          #     text 'Stdp'
          #   div ->
          #     text 'Diffp'
      # button class:"btn btn-block btn-primary", -> text "Get Records"
    coffeejs ->
      $ ->
        page = 0
        # renderList = (l)->
        #   for i,idx in l
        #     className = if i.passed then 'warn' else ''
        #     elem = "<li class='table-view-cell record #{className}'><div class='idx'>#{idx}</div>"
        #     for j in ['_id','city','prov','ueml','prod_desc','pay_tp','ts','operation','opeml','trans_type','amount','start_dt','end_dt']
        #       if i[j]
        #         elem += "<div><label>#{j}</label><span>#{i[j]}</span></div>"
        #     elem += '</li>'
        #     $('.table-view').append(elem)
        # getSignups = ()->
        #   data = {page:page}
        #   $.post '/sys/tatransactions',data,(ret)->
        #     if ret.err
        #       alert(ret.err)
        #       return
        #     page += 1
        #     renderList(ret.l)
        # getSignups()
        $('.btn-primary').on 'click',(e)->
          #alert JSON.stringify data
          getSignups()
      null

# Move to dot template
# VIEW 'sys-agentTransactions',->
#   js "/js/jquery-1.10.1.min.js"
#   text """<style>
#   .content{
#     overflow-y:scroll;
#     background-color: rgb(230, 230, 230);
#   }
#   .record{
#     margin-bottom:10px;
#     background:white;
#     padding:10px;
#     font-size: 15px;
#     color: #666;
#   }
#   label{
#     min-width: 90px;
#     color:black;
#   }
#   .idx{
#     color:#e03131;
#   }
#   </style>"""
#   header class: 'bar bar-nav', ->
#     a class: 'icon icon-close pull-right nobusy', style:"padding-right:10px; padding-left:10px;", href: '/1.5/index'
#     h1 class: 'title', -> "Transactions"
#   div class: 'content', ->
#     div class: 'segmented-control',->
#       for type in @types
#         classNm = 'control-item ' + (if @curType is type.type then 'active' else '')
#         url = "#{type.url}&type=#{type.type}"
#         a class: classNm, href: url, -> text _ type.text
#     ul class:'table-view'
#     button class:"btn btn-block btn-primary", -> text "Get Records"
#     coffeejs ->
#       $ ->
#         page = 0
#         renderList = (l)->
#           for i,idx in l
#             className = if i.passed then 'warn' else ''
#             elem = "<li class='table-view-cell record #{className}'><div class='idx'>#{idx}</div>"
#             for j in ['_id','city','prov','ueml','prod_desc','pay_tp','ts','operation','opeml','trans_type','amount','start_dt','end_dt']
#               if i[j]
#                 elem += "<div><label>#{j}</label><span>#{i[j]}</span></div>"
#             elem += '</li>'
#             $('.table-view').append(elem)
#         getSignups = ()->
#           data = {page:page}
#           $.post '/sys/tatransactions',data,(ret)->
#             if ret.err
#               alert(ret.err)
#               return
#             page += 1
#             renderList(ret.l)
#         getSignups()
#         $('.btn-primary').on 'click',(e)->
#           #alert JSON.stringify data
#           getSignups()
#       null

VIEW 'sys-signups',->
  js "/js/jquery-1.10.1.min.js"
  text """<style>
  .record{
    margin-bottom:10px;
    background:white;
    padding:10px;
  }
  label{
    min-width: 81px;
  }
  .warn{
    color:#ffc107;
  }
  .content{
    overflow-y:scroll;
    background-color: rgb(230, 230, 230);
  }
  </style>"""
  header class: 'bar bar-nav', ->
    a class: 'icon icon-close pull-right nobusy', style:"padding-right:10px; padding-left:10px;", href: '/1.5/index'
    h1 class: 'title', -> "Signups"
  div class: 'content', ->
    button class:"btn btn-block btn-primary", -> text "Get Records"
    ul class:'table-view'
    js '/js/sys_signups.min.js'

VIEW 'sys-pushtest',->
  js "/js/jquery-1.10.1.min.js"
  text """<style></style>"""
  header class: 'bar bar-nav', style:"padding-right:0px;", ->
    a class: 'icon icon-close pull-right nobusy', style:"padding-right:10px; padding-left:10px;", href: '/1.5/index'
    h1 class: 'title', -> text _("RealMaster")
  div class: 'content', style:"background-color: rgb(230, 230, 230); padding-left:20px",->
    div ->
      p -> 'fill email or token, if token filled, will use token and ignore email'
    div class:'line',->
      span class:'col-3',-> 'Email'
      span class:'clear btn btn-primary eml',-> 'Clear'
      input class:'eml-input', name:'eml',type:'email',placeholder:'email',value:@email
    div class:'line',->
      span class:'col-3',-> 'Token'
      span class:'clear btn btn-primary token',-> 'Clear'
      input class:'token-input', name:'token',type:'text',placeholder:'token',value:@token
    div class:'line',->
      div ->
        span class:'fill btn btn-positive',-> 'Fill prop'
        span class:'url',-> '/1.5/prop/detail/inapp?lang=zh-cn&id=TRBC5382555'
      div ->
        span class:'fill btn btn-positive',-> 'Fill setting'
        span class:'url',-> '/1.5/settings?nativefullscreen=1'
      div ->
        span class:'fill btn btn-positive',-> 'Fill post'
        span class:'url',-> "/1.5/forum?postid=#{@postid}"
      div ->
        span class:'fill btn btn-positive',-> 'Fill daily'
        span class:'url',-> "/1.5/mapSearch?mode=list&d=/1.5/index&city=Toronto&prov=On&saletp=sale&cityName=Toronto"
      span class:'col-3',-> 'URL'
      input class:'url-input', name:'url',type:'text',placeholder:'url',value:'/1.5/prop/detail/inapp?lang=zh-cn&id=TRBC5382555'
    div class:'line',->
      span class:'col-3',-> 'title'
      input class:'title-input', name:'title',type:'text',placeholder:'title',value:'pn title'
    # div class:'line',->
    #   span class:'col-3',-> 'image src'
    #   img id:'imageInput',src:'https://realmaster.com/img/listing.png', style:'width:60px;height:60px'
    #   input class:'image-input', name:'image',type:'text',placeholder:'image url',value:'https://realmaster.com/img/listing.png'
    div class:'line',->
      span class:'col-3',-> 'Msg'
      input class:'msg-input', name:'msg',type:'text',placeholder:'msg',value:'test msg中文内容'
    div class:'line',->
      span class:'col-3',-> 'Timeout'
      input class:'time-input', name:'time',type:'number',placeholder:'timeout',value:'3000'
    button class:'send-btn btn btn-primary', -> 'Send Pn'
    button class:'send-btn btn btn-positive view', style:'margin-left:10px',-> 'View'
    js '/js/sys_pushtest.min.js'


VIEW 'sys-cservice',->
  js "/js/jquery-1.10.1.min.js"
  text """<style>
    div label{
      font-weight: normal;
      padding: 4px 9px;
    }</style>"""
  header class: 'bar bar-nav', style:"padding-right:0px;", ->
    a class: 'icon icon-close pull-right nobusy', style:"padding-right:10px; padding-left:10px;", href: '/1.5/index'
    h1 class: 'title', -> text _("RealMaster")
  div class: 'content', style:"background-color: rgb(230, 230, 230); padding-left:20px",->
    for k,v of @services
      div ->
        # span class:'name', -> text c.prov+'-'+c.city
        checkValue = k+'(check means online)'
        inputParams = value:checkValue, id:checkValue
        if v
          inputParams.checked = 1
        input class:'switch', name:'city',type:'checkbox',inputParams
        label for:checkValue, -> text checkValue
    coffeejs ->
      $ ->
        $('.switch').on 'click',(e)->
          $elem = $(e.target)
          data = {servicename:$elem.val(), checked:$elem.prop('checked')}
          #alert JSON.stringify data
          $.post '/sys/cservice',data,(ret)->
            alert JSON.stringify ret
      null

VIEW 'sys-contact-city',->
  js "/js/jquery-1.10.1.min.js"
  text """<style>
    div label{
      font-weight: normal;
      padding: 4px 9px;
    }</style>"""
  header class: 'bar bar-nav', style:"padding-right:0px;", ->
    a class: 'icon icon-close pull-right nobusy', style:"padding-right:10px; padding-left:10px;", href: '/1.5/index'
    h1 class: 'title', -> text _("RealMaster")
  div class: 'content', style:"background-color: rgb(230, 230, 230); padding-left:20px",->
    div style:'color:#e03131; padding: 10px 0 10px 0;',->
      text 'this table sets the value of ownCtrlCity, which is used by isOwnCtrlCityProp(prop,popularCities). '
      text 'howerver, libapp.properties.isRMControlProp(prop) also does this, see RM_CONTROL_CITIES(harde coded). '
      text 'this value is related to RM_CONTROL_CITY, which will affect EVALUATION_BUSINESS_LOGIC'
    for c in @cities
      div ->
        # span class:'name', -> text c.prov+'-'+c.city
        checkValue = c.prov+'-'+c.city
        inputParams = value:checkValue, id:checkValue
        if c.own?.sale
          inputParams.checked = 1
        input class:'city', name:'city',type:'checkbox',tp:'sale',inputParams
        text 'Sale'
        inputParams.id += '-Rent'
        delete inputParams.checked
        if c.own?.rent
          inputParams.checked = 1
        input class:'city', name:'city',type:'checkbox',tp:'rent',inputParams
        text 'Rent'
        label for:checkValue, -> text checkValue
    coffeejs ->
      $ ->
        $('.city').on 'click',(e)->
          $elem = $(this)#$(e.target)
          [prov,city] = $elem.val().split('-')
          tp = $elem.attr('tp')
          data = {city:city, prov:prov, checked:$elem.prop('checked'), tp:tp}
          #alert JSON.stringify data
          $.post '/sys/city',data,(ret)->
            alert JSON.stringify ret
      null

VIEW 'sys-prov-user-admin',->
  text """
  <style>
  .content .action{
    position: relative;
    display: inline-block;
    padding: 6px 8px 7px;
    margin-bottom: 0;
    font-size: 12px;
    font-weight: 400;
    line-height: 1;
    color: #333;
    vertical-align: top;
    cursor: pointer;
    background-color: #5cb85c;
    border: 1px solid #5cb85c;
    border-radius: 3px;
    margin: 5px 10px 5px 10px;
    color: white;
  }
  .content .action.negative{
    background-color: #d9534f;
    border: 1px solid #d9534f;
  }
  .content .action.info{
    background-color: #46b8da;
    border: 1px solid #46b8da;
  }
  .content input{
    height: 35px;
    font-size: 16px;
    padding: 3px 7px;
  }
  </style>
  """
  js "/js/jquery-1.10.1.min.js"
  header class: 'bar bar-nav', style:"padding-right:0px;", ->
    a class: 'icon icon-close pull-right', style:"padding-right:10px; padding-left:10px;", href: '/1.5/index'
    h1 class: 'title', -> text _("RealMaster")
  div class: 'content', style:"background-color: rgb(230, 230, 230);",->
    div ->
      input id:'email',name:'email',style:"width:100%;"
      br()
      button id:"check",class:'action info',-> "Check Account"
      br()
      button id:"make-vip",class:'action',-> "Make VIP"
      button id:"make-vip-plus",class:'action',-> "Make VIP Plus"
      button id:"make-vip-alliance",class:'action',-> "Make VIP Alliance"
      br()
      button id:'revoke-vip',class:'action negative',-> "Revoke VIP/VIP Plus/Alliance/C"
    coffeejs ->
      $ ->
        $('.action').on 'click',(e)->
          act = $(@).attr('id')
          eml = $('#email').val()
          unless eml
            alert('Need email')
            return false
          data = {act:act,eml:eml}
          #alert JSON.stringify data
          $.post '/sys/provuser',data,(ret)->
            alert JSON.stringify ret
      null
VIEW 'sys-user-admin',->
  js "/js/jquery-1.10.1.min.js"
  text """
  <style>
  .content{
    background-color: rgb(230, 230, 230);
    padding-bottom:44px;
  }
  .content .action{
    position: relative;
    display: inline-block;
    padding: 6px 8px 7px;
    margin-bottom: 0;
    font-size: 12px;
    font-weight: 400;
    line-height: 1;
    color: #333;
    vertical-align: top;
    cursor: pointer;
    background-color: #5cb85c;
    border: 1px solid #5cb85c;
    border-radius: 3px;
    margin: 5px 10px 5px 10px;
    color: white;
  }
  .content .action.negative{
    background-color: #d9534f;
    border: 1px solid #d9534f;
  }
  .content .action.info{
    background-color: #46b8da;
    border: 1px solid #46b8da;
  }
  .content input{
    height: 35px;
    font-size: 16px;
    padding: 3px 7px;
  }
  .content .row {
    display:flex;
    justify-content:space-between;
    align-items:center;
  }
  .content .desc{
    font-size: 13px;
    color: #888;
    padding: 10px 0 0 10px;
  }
  #userInfoModal .content p{
    display:inline-block;
    width:calc(100% - 93px);
    padding-left: 10px;
  }
  #userInfoModal .content label{
    width:92px;
    text-align:left;
    font-size: 14px;
    overflow: scroll;
  }
  #userInfoModal .content .row{
    padding: 4px 10px 0 10px;
  }
  </style>
  """
  unless @popup
    header class: 'bar bar-nav', ->
      a class: 'icon icon-close pull-right nobusy', href: '/1.5/index'
      h1 class: 'title', -> text _("RealMaster")
  div id:"userInfoModal", class:"modal", ->
    header class:"bar bar-nav",->
      a class:"icon icon-close pull-right", href:"javascript:;", onclick:'toggleModal("userInfoModal","close")',->
      h1 class:"title",-> text 'User'
    div class:"content",->
      # div ->
      #   label -> text '**1'
      #   p -> text 'remove following' # -> updateUser req, {flwng:null}'
  div class: 'content', ->
    div class:'bar bar-standard',style:'padding: 0;height: 35px;',->
      input id:'email',name:'email',style:"width:100%;",value:(@id or @eml or ''),
    div style:'padding-top:44px', ->
      button id:"check",class:'action info',-> "Check Account Info"
      div class:'row desc', -> 'will not assign this user in fub crm'
      div class: 'row',->
        button id: "add-crmNoAssign", class:'action',-> "Make _crmNoAssign"
        button id: "revoke-crmNoAssign", class:'action negative',-> "Revoke _crmNoAssign"
      div class:'row desc', -> 'can access fub related features'
      div class: 'row',->
        button id: "add-crmAdmin", class:'action',-> "Make _crmAdmin"
        button id: "revoke-crmAdmin", class:'action negative',-> "Revoke _crmAdmin"
      div class:'row desc', -> 'will not check user post content for moderation'
      div class:'row',->
        button id:"add-noFltr", class:'action',-> "Disable content moderation"
        button id:"remove-noFltr", class:'action negative',-> "Enable content moderation"
      div class:'row desc', -> 'user can topListing'
      div class:'row',->
        button id:"make-topup", class:'action',-> "Make topup"
        button id:"revoke-topup", class:'action negative',-> "Revoke topup"
      div class:'row desc', -> 'user can access banner and market releated feat'
      div class:'row',->
        button id:"make-market", class:'action',-> "Make market"
        button id:"revoke-market", class:'action negative',-> "Revoke market"
      div class:'row desc', -> 'user can edit project and make ads of proj'
      div class:'row',->
        button id:"make-create-project",class:'action',-> "Make _create_proj"
        button id:"revoke-create-project",class:'action negative',-> "Revoke _create_proj"
      div class:'row desc', -> 'make user realtor, no need to verify'
      div class:'row',->
        button id:"make-realtor",class:'action',-> "Make Realtor"
        button id:'revoke-realtor',class:'action negative',-> "Revoke Realtor"
      div class:'row desc', -> 'make user waiting verify agent'
      div class:'row',->
        button id:"make-verify-realtor",class:'action',-> "Make Verify Realtor"
        button id:'revoke-verify-realtor',class:'action negative',-> "Revoke Verify Realtor"
      div class:'row desc', -> 'make user vip, can access VIP features'
      div class:'row',->
        div ->
          button id:"make-vip",class:'action',-> "Make VIP"
          button id:"make-vip-plus",class:'action',-> "Make VIP Plus"
          button id:"make-vip-alliance",class:'action',-> "Make VIP Alliance"
          button id:"make-vip-c",class:'action',-> "Make VIPC"
        div ->
          button id:'revoke-vip',class:'action negative',-> "Revoke VIP/VIP Plus/Alliance/C"
      div class:'row desc', -> 'make user treb user, access @me/branch/cpny'
      div class:'row',->
        button id:"make-treb",class:'action',-> "Make TREB"
        button id:"revoke-treb",class:'action negative',-> "Revoke TREB"
      div class:'row desc', -> 'make user treb user, access onm/coopcpny'
      div class:'row',->
        button id:"make-treb2",class:'action',-> "Make TREB-2"
        button id:"revoke-treb2",class:'action negative',-> "Revoke TREB-2"
      div class:'row desc', -> 'make schAdmin/cmtyAdmin can edit school in web'
      div class:'row',->
        button id:"make-app-editor",class:'action',-> "Make App Editor"
        button id:'revoke-app-editor',class:'action negative',-> "Revoke App Editor"
      div class:'row desc', -> 'can access analytics/_userStats'
      div class:'row',->
        button id:"make-ana-admin",class:'action',-> "Make Analytics admin"
        button id:"revoke-ana-admin",class:'action negative',-> "Revoke Analytics admin"
      div class:'row desc', -> 'can access predict feats'
      div class:'row',->
        button id:"make-mluser",class:'action',-> "Make MLUser"
        button id:"revoke-mluser",class:'action negative',-> "Revoke MLUser"
      div class:'row desc', -> 'can access cpm广告 feats, also userAdmin'
      div class:'row',->
        button id:"make-cpm-admin",class:'action',-> "Make CPM admin"
        button id:"revoke-cpm-admin",class:'action negative',-> "Revoke CPM admin"
      div class:'row desc', -> 'can edit inreal group member'
      div class:'row',->
        button id:"make-inrealAdmin-admin",class:'action',-> "Make inreal admin"
        button id:"revoke-inrealAdmin-admin",class:'action negative',-> "Revoke inreal admin"
      div class:'row desc', -> 'set edmNo of user'
      div class:'row',->
        button id:"add-edm",class:'action',-> "Accept EDM"
        button id:"no-edm",class:'action negative',-> "Stop EDM"
      div class:'row desc', -> 'set forumAdmin/webComment of user'
      div class:'row',->
        button id:"make-forum-admin",class:'action',-> "Make forum Admin"
        button id:"revoke-forum-admin",class:'action negative',-> "Revoke forum Admin"
      div class:'row desc', -> 'DEPRECATED! user publish to news via wecard'
      div class:'row',->
        button id:"make-news-admin",class:'action',-> "Make news Admin"
        button id:"revoke-news-admin",class:'action negative',-> "Revoke news Admin"
      div class:'row desc', -> 'access webComment/webBackEnd'
      div class:'row',->
        button id:"make-forum-commentator",class:'action',-> "Make forum commentator"
        button id:"revoke-forum-commentator",class:'action negative',-> "Revoke forum commentator"
      div class:'row desc', -> 'formOrganizer edit WeForm'
      div class:'row',->
        button id:"make-form-organizer",class:'action',-> "Make form organizer"
        button id:"revoke-form-organizer",class:'action negative',-> "Revoke form organizer"
      div class:'row desc', -> 'block chat feat'
      div class:'row',->
        button id:"unblock-chat",class:'action',-> "Unblock chat"
        button id:"block-chat",class:'action negative',-> "Block chat"
      div class:'row',->
        button id:"make-stigmatized-admin",class:'action',-> "Make stigmatized Admin"
        button id:"revoke-stigmatized-admin",class:'action negative',-> "Revoke stigmatized Admin"
      div class:'row',->
        button id:"make-geo-admin",class:'action',-> "Make geo Admin"
        button id:"revoke-geo-admin",class:'action negative',-> "Revoke geo Admin"
      div class:'row desc', -> 'agreementAdmin 判断经济上传协议是否合格'
      div class:'row',->
        button id:"make-agreement-admin",class:'action',-> "Make agreement Admin"
        button id:"revoke-agreement-admin",class:'action negative',-> "Revoke agreement Admin"
      div class:'row desc', -> 'app 开屏广告上传'
      div class:'row',->
        button id:"make-app-splash-admin",class:'action',-> "Make splash Admin"
        button id:"revoke-app-splash-admin",class:'action negative',-> "Revoke splash Admin"
      div class:'row desc', -> 'trusted assignment Admin 真楼花'
      div class:'row',->
        button id:"make-assign-admin",class:'action',-> "Make Assignment Admin"
        button id:"revoke-assign-admin",class:'action negative',-> "Revoke Assignment Admin"
      div class:'row desc', -> '添加笔记admin权限,可以查看他人全部笔记及头像和名称'
      div class:'row',->
        button id:"make-note-admin",class:'action',-> "Make Note Admin"
        button id:"revoke-note-admin",class:'action negative',-> "Revoke Note Admin"
      div class:'row desc', -> '添加token admin权限,可以查看操作token相关内容'
      div class:'row',->
        button id:"make-token-admin",class:'action',-> "Make Token Admin"
        button id:"revoke-token-admin",class:'action negative',-> "Revoke Token Admin"
      div class:'row desc', -> 'showingAdmin 可以查看其他经济showing列表'
      div class:'row',->
        button id:"make-showing-admin",class:'action',-> "Make Showing Admin"
        button id:"revoke-showing-admin",class:'action negative',-> "Revoke Showing Admin"
      div class:'row desc', -> 'claimAdmin 可以查看房大师经济claim列表，删除claim记录'
      div class:'row',->
        button id:"make-claim-admin",class:'action',-> "Make Claim Admin"
        button id:"revoke-claim-admin",class:'action negative',-> "Revoke Claim Admin"
      div class:'row',->
        button id:'revoke-roles-all',class:'action negative',-> "Revoke All Roles"
        button id:'remove-following',class:'action negative',-> "Remove Following Realtor"
        button id:'remove-wxuid',class:'action negative',-> "Remove Wechat"
      div class:'row desc', -> '设置邮箱为已经通过邮件验证的邮箱'
      div class:'row',->
        button id:"verifyEmail",class:'action',-> "Verify Email"
      button id:"clear-tasks",class:'action info',-> "Clear Tasks"
      hr()
      input id:'nemail',name:'nemail',style:'width:100%;',placeholder:"New Email Address(case sensitive，旧的也是)"
      button id:"chgEmail",class:'chgEmail btn',-> "Change Email"
      hr()
      input id:'newPass',name:'newPass',style:'width:100%;',placeholder:"New Password"
      button id:"chgPassword",class:'chgPassword btn',-> "Change Password"
      hr()
      input id:'aid',name:'aid',style:'width:100%;',placeholder:"TREB ID"
      button id:"chgTREBID",class:'btn',-> "Set TREB ID"
      button id:"chgTREBIDRID",class:'btn',-> "DDF MLS ID set DDF Realtor"
      hr()
      input id:'fornm',name:'fornm',style:'width:100%;',placeholder:"Forum NickName"
      button id:"chgFornm",class:'chgFornm btn',-> "Set Forum NickName"
      hr()
      input id:'edmPriority',name:'edmPriority',type:'number',style:'width:100%;',placeholder:"修改用户楼花转让 edm 优先级(越高越靠前)"
      button id:"chgEdmPrioy",class:'chgEdmPrioy btn',-> "Change edm priority"
      hr()
      input id:'agentLevel',name:'agentLevel',style:'width:100%;',placeholder:"修改经纪Level(填写内容为A,B,C...)"
      button id:"chgAgentLevel",class:'chgAgentLevel btn',-> "Change agent level"
      hr()
      # input id:'deviceIdBlock',name:'blkId',style:'width:100%;',placeholder:"Device Id"
      button id:"deviceIdBlock",class:'blkChat btn',-> "Block user chat by device Id"
      button id:"deviceIdBlockForum",class:'blkChat btn',-> "Block user forum comment by device Id"
      button id:"devicePnBlock",class:'blkChat btn disabled',-> "Block user chat by pn"
      hr()
      select id:'provSelect',->
        option value:"ON", -> 'Ontario'
        option value:"BC", -> 'British Columbia'
        option value:"QC", -> 'Quebec'
        option value:"PE", -> 'Prince Edward Island'
        option value:"AB", -> 'Alberta'
        option value:"SK", -> 'Saskatchewan'
        option value:"NS", -> 'Nova Scotia'
        option value:"Other", -> 'Other'
      button id:'provAdmin',class:'btn rst-prov',-> 'Set Prov Admin'
      button id:'rstProv',class:'btn rst-prov',-> 'Set Rst Prov'
      button id:'rmRstProv',class:'btn rst-prov',-> 'Remove Rst Prov'
      hr()
      button id:'admin-op-hist', class:'btn action info',-> 'Check admin ops'
      hr()
      button id:'deleteUser', class:'deleteUser btn',-> 'Delete user information'
    js '/js/sys_user_admin.min.js'

VIEW 'sys-inreal-admin',->
  js "/js/jquery-1.10.1.min.js"
  text """
  <style>
  .content{
    background-color: rgb(230, 230, 230);
    padding-bottom:44px;
  }
  .content .action{
    position: relative;
    display: inline-block;
    padding: 6px 8px 7px;
    margin-bottom: 0;
    font-size: 12px;
    font-weight: 400;
    line-height: 1;
    color: #333;
    vertical-align: top;
    cursor: pointer;
    background-color: #5cb85c;
    border: 1px solid #5cb85c;
    border-radius: 3px;
    margin: 5px 10px 5px 10px;
    color: white;
  }
  .content .action.negative{
    background-color: #d9534f;
    border: 1px solid #d9534f;
  }
  .content .action.info{
    background-color: #46b8da;
    border: 1px solid #46b8da;
  }
  .content input{
    height: 35px;
    font-size: 16px;
    padding: 3px 7px;
  }
  .content .row {
    display:flex;
    justify-content:space-between;
    align-items:center;
  }
  .content .desc{
    font-size: 13px;
    color: #888;
    padding: 10px 0 0 10px;
  }
  #userInfoModal .content p{
    display:inline-block;
    width:calc(100% - 93px);
    padding-left: 10px;
  }
  #userInfoModal .content label{
    width:92px;
    text-align:left;
    font-size: 14px;
    overflow: scroll;
  }
  #userInfoModal .content .row{
    padding: 4px 10px 0 10px;
  }
  </style>
  """
  unless @popup
    header class: 'bar bar-nav', ->
      a class: 'icon icon-close pull-right nobusy', href: '/1.5/index'
      h1 class: 'title', -> text _("RealMaster")
  div id:"userInfoModal", class:"modal", ->
    header class:"bar bar-nav",->
      a class:"icon icon-close pull-right", href:"javascript:;", onclick:'toggleModal("userInfoModal","close")',->
      h1 class:"title",-> text 'User'
    div class:"content",->
      # div ->
      #   label -> text '**1'
      #   p -> text 'remove following' # -> updateUser req, {flwng:null}'
  div class: 'content', ->
    div class:'bar bar-standard',style:'padding: 0;height: 35px;',->
      input id:'email',name:'email',style:"width:100%;",value:(@id or @eml or '')
    div style:'padding-top:44px', ->
      div class:'row desc', -> 'if user not a realtor, can\'t operate'
      button id:"check",class:'action info inreal',-> "Check Account Info"
      div class:'row desc', -> 'make user vip, can access VIP features'
      div class:'row',->
        button id:"make-vip-plus",class:'action inreal',-> "Make vip plus"
        button id:"revoke-vip",class:'action negative inreal',-> "Revoke vip plus"
      div class:'row desc', -> 'make rmgroup user vip, can access VIP features'
      div class:'row',->
        button id:"make-vip-plus",class:'action inreal join',-> "Make vip plus and join group"
        button id:"revoke-vip",class:'action negative inreal join',-> "Revoke vip and leave group"
    js '/js/sys_user_admin.min.js'

# VIEW 'sys-ddf',->
#   js "/js/jquery-1.10.1.min.js"
#   header class: 'bar bar-nav', style:"padding-right:0px;", ->
#     a class: 'icon icon-close pull-right nobusy', style:"padding-right:10px; padding-left:10px;", href: '/1.5/index'
#     h1 class: 'title', -> text _("RealMaster")
#   div class: 'content', style:"background-color: rgb(230, 230, 230); padding-left:20px",->
#     div ->
#       checked = @ddfShowable is 'all'
#       input id:'all',name:'policy',type:'radio',value:'all',checked:checked
#       span -> text "Show All"
#     div ->
#       checked = @ddfShowable is 'hideAll'
#       input id:'hideAll',name:'policy',type:'radio',value:'hideAll',checked:checked
#       span -> text "Hide All"
#     div ->
#       checked = @ddfShowable is 'hideEng'
#       input id:'hideEng',name:'policy',type:'radio',value:'hideEng',checked:checked
#       span -> text "Hide Eng"
#     div ->
#       checked = @ddfShowable is 'loginOnly'
#       input id:'loginOnly',name:'policy',type:'radio',value:'loginOnly',checked:checked
#       span -> text "loginOnly"
#     div ->
#       a class:'btn btn-positive save', href:'javascript:;', -> "Save"
#     coffeejs ->
#       $ ->
#         $('.save').on 'click',(e)->
#           hideAll =  $('#hideAll')[0].checked
#           hideEng =  $('#hideEng')[0].checked
#           loginOnly = $('#loginOnly')[0].checked
#           all = $('#all')[0].checked
#           data = {hideAll:hideAll, hideEng:hideEng, loginOnly:loginOnly, all:all}
#           #alert JSON.stringify data
#           $.post '/sys/ddf',data,(ret)->
#             alert JSON.stringify ret
#       null

VIEW 'sys-user-switch',->
  js "/js/jquery-1.10.1.min.js"
  header class: 'bar bar-nav', style:"padding-right:0px;", ->
    a class: 'icon icon-close pull-right nobusy', style:"padding-right:10px; padding-left:10px;", href: '/1.5/index'
    h1 class: 'title', -> text _("RealMaster")
  div class: 'content', style:"background-color: rgb(230, 230, 230); padding-left:20px",->
    div ->
      text "Please select email to switch:"
    div ->
      select id:'toUser',->
        for u in @user?.switchUsers or []
          option value:u,-> text u
      button id:'switch',class:'action',-> _ "Switch"
      button id:'del',class:'action',-> _ "Delete"
    div ->
      input id:'addUser',name:'eml',style:"width:80%;",placeholder:'email'
      button id:'add',class:'action',-> _ "Add"
    div ->
      button id:'back',class:'action',-> _ "Switch Back"
    coffeejs ->
      $ ->
        $('.action').on 'click',(e)->
          act = $(@).attr('id')
          if act is 'add'
            data = {eml:$('#addUser').val(),act:act}
          else if act is 'del' or act is 'switch'
            data = {eml:$('#toUser').val(),act:act}
          else if act is 'back'
            data = {act:act}
          else
            return alert("Unknown Action")
          $.post '/sys/switch',data,(ret)->
            if ret.ok and ret.url
              return window.location = ret.url
            alert JSON.stringify ret
      null


VIEW 'sys-edm-admin',->
  js "/js/jquery-1.10.1.min.js"
  text """
  <style>
  .content input{
    height: 35px;
    font-size: 16px;
    padding: 3px 7px;
  }
  .report {
    padding: 7px;
  }
  .report span {
    padding-left:10px;
  }
  </style>
  """
  header class: 'bar bar-nav', style:"padding-right:0px;", ->
    a class: 'icon icon-close pull-right nobusy', style:"padding-right:10px; padding-left:10px;", href: '/1.5/index'
    h1 class: 'title', -> text _("RealMaster")
  div class: 'content', style:"background-color: rgb(230, 230, 230);",->
    div ->
      input id:'edmTitle',name:'edmTitle',style:'width:100%;',placeholder:"New Edm Title",value:@edmTitle.tl
      div id:'edmTs', style:"padding-left:10px;",->
        text @edmTitle.ts
      button id:"chgEdmTl",class:'chgEdmTl btn',-> "Change Edm Title"
      hr()
      div class:'report', ->
        label ->
          text _("Last edm report")
        div ->
          span -> text _("dest User")
          span -> text @log.dct
        div ->
          span -> text _("sent User")
          span -> text @log.sct
        if @log.ts
          div ->
            span -> text _("task begin at")
            span -> text @log.ts.toLocaleString()
        if @log.endts
          div ->
            span -> text _("task end at")
            span -> text @log.endts.toLocaleString()
    coffeejs ->
      $ ->
        $('.chgEdmTl').on 'click',(e)->
          tl = $('#edmTitle').val()
          unless tl
            alert('Need new Title')
            return false
          $.post '/sys/chgEdmTl',{tl:tl},(ret)->
            alert JSON.stringify ret
      null

GET 'edm/report',(req,resp) ->
  findError = (e)-> resp.redirect '/1.5/index'
  chkUserAdmin req,resp,findError,(user)->
    EdmLog.findToArray {},{sort:{ ts: -1 }, limit:60, projection:{logs:0}},(err, logs) ->
      # console.log logs
      if err
        console.error err
        return resp.send {ok:0, err:err}
      return resp.send {ok:0, err:'no logs'} unless logs?.length
      logids = logs.map((log) ->
        log._id?.toString()
      )
      UserModel.getEdmHistories logids,(err, profiles) ->
        if err
          console.log err
          return resp.send {ok:0, err:err}
        for log in logs
          log.openUsers = []
          for profile in profiles
            log.openUsers.push(profile.uid) if profile.logid?.toString() == log._id?.toString()
        resp.ckup 'sys-edm-open-report', {logs:logs}


VIEW 'sys-edm-open-report',->
  js "/js/jquery-1.10.1.min.js"
  text """
  <style>
  .report {
    padding: 7px;
  }
  .report span {
    padding-left:10px;
  }
  .edmlog {
    border:1px solid white;
  }
  </style>
  """
  header class: 'bar bar-nav', style:"padding-right:0px;", ->
    a class: 'icon icon-close pull-right nobusy', style:"padding-right:10px; padding-left:10px;", href: '/1.5/index'
    h1 class: 'title', -> text _("RealMaster")
  div class: 'content', style:"background-color: rgb(230, 230, 230);",->
    div ->
      div class:'report', ->
        label ->
          text _("latest edm opened report")
        if @logs
          for log in @logs
            div class:'edmlog',->
              div ->
                span -> text _("send params:")
                span -> text log.avgs
              if log.ts
                div ->
                  span -> text _("task:")
                  span -> text log.ts.toLocaleString()
              if log.endts
                div ->
                  span -> text _("endts:")
                  span -> text log.endts.toLocaleString()
              div ->
                span -> text _("dest User:")
                span -> text log.dct
              div ->
                span -> text _("sent User:")
                span -> text log.sct
              div ->
                span -> text _("opened emails:")
                span -> text log.openUsers.length
              div ->
                span ->
                  a href:'/sys/edm/report/'+log._id,->
                    text _("detail")

GET 'edm/report/:id',(req,resp) ->
  id = req.param 'id'
  findError = (e)-> resp.redirect '/1.5/index'
  chkUserAdmin req,resp,findError,(user)->
    EdmLog.findOne {_id:id},{fields:{logs:0}},(err, log) ->
      UserModel.getEdmHistories id, (err, profiles) ->
        if err
          console.log err
          return resp.send {ok:0, err:err}
        arr = []
        for hist in profiles
        # for hist in profile.edmhist
          if hist.logid?.toString() == id
            ts = new Date(hist.opents)
            key = (ts.getMonth()+1) + '-' + ts.getDate()+ ' ' + ts.getHours()
            arr[key] = {} unless arr[key]
            arr[key].hists = [] unless arr[key].hists
            arr[key].hists.push(hist)
        console.log arr
        resp.ckup 'sys-edm-report-detail', {arr:arr, log:log}

VIEW 'sys-edm-report-detail',->
  js "/js/jquery-1.10.1.min.js"
  text """
  <style>
  .report {
    padding: 7px;
  }
  .report span {
    padding-left:10px;
  }
  .edmlog {
    background-color:white
  }
  </style>
  """
  header class: 'bar bar-nav', style:"padding-right:0px;", ->
    a class: 'icon fa fa-back pull-left nobusy', style:"padding-right:10px; padding-left:10px;", href: '/sys/edm/report'
    a class: 'icon icon-close pull-right nobusy', style:"padding-right:10px; padding-left:10px;", href: '/1.5/index'
    h1 class: 'title', -> text _("RealMaster")
  div class: 'content', style:"background-color: rgb(230, 230, 230);",->
    div ->
      div class:'report', ->
        label ->
          span -> text _("edm opened report","edm")
          if @log.ts
            span -> text @log.ts.toLocaleString()
        div ->
          span -> text _("dest User:")
          span -> text @log.dct
          span -> text _("sent User:")
          span -> text @log.sct
        for key of @arr
          div ->
            span style:"padding-right:10px", -> text key
            span  style: 'padding:0px; width:'+ (@arr[key].hists?.length * 2) + 'px', class:'edmlog', ->
              text @arr[key].hists?.length

getDotHtml = DEF 'getDotHtml'
# add more test email template in future
# rebuildMapping = DEF 'rebuildMapping'
# reloadTemplates = DEF 'reloadTemplates'
# templateFolderPath = appConfig.emailTemplate.path
# nameIndexMapping = {}
# # doReload()
# reloadTemplates templateFolderPath, (err, ret)->
#   if err
#     console.error err
#   nameIndexMapping=rebuildMapping ret if ret
# sys/emailtest
EMAIL_TEMPLATES =
  oauthRegister: 'Oauth Register'
  oauthSignin: 'Oauth Sign in First Time'
  userRegister: 'Email Register'
  resetPassword: 'Reset Password'
APP_HOME = '/1.5/index'
GET 'emailtest',(req, resp) ->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    return resp.redirect APP_HOME unless req.isAllowed 'admin'
    ctx = {templates:EMAIL_TEMPLATES,email:user.eml,engine:config.mailEngine?.mailEngine,engines:sendMail.getEngines()}
     # use theme
    # console.log '+++++',ctx
    resp.ckup 'admin/emailTest',ctx

POST 'emailtest',(req, resp) ->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    return resp.redirect APP_HOME unless req.isAllowed 'admin'
    return resp.redirect APP_HOME unless body = req.body
    {email,template,engine} = body
    langs = body['langs[]']
    ctx = {templates:EMAIL_TEMPLATES,email,engine:config.mailEngine?.mailEngine,engines:sendMail.getEngines()}
    unless langs
      ctx.err = 'Select at lease one language'
      return resp.ckup 'admin/emailTest',ctx
    unless email
      ctx.err = 'Email required'
      return resp.ckup 'admin/emailTest',ctx
    unless template
      ctx.err = 'Please select a template'
      return resp.ckup 'admin/emailTest',ctx
    langs = [langs] unless Array.isArray langs #if langs is 'en'
    sendTestmail = ->
      unless lang = langs.pop()
        ctx.ok = 1
        return resp.ckup 'admin/emailTest',ctx
      req.setLocale lang
      protocolDomain = "#{req.getProtocol()}://#{config.serverBase?.wwwDomain}/#{lang}"
      switch template
        when 'oauthRegister'
          data = {email,pass:'Test Password', link:"#{protocolDomain}/www/login"}
          subject = req.l10n 'RealMaster Registration Confirmation'
        when 'oauthSignin'
          template = 'oauthRegister'
          data = {email, link:"#{protocolDomain}/www/login"}
          subject = req.l10n 'RealMaster Registration Confirmation'
        when 'userRegister'
          data = {email, link: "#{protocolDomain}/verifyEmail/test-vcode"}
          subject = req.l10n 'RealMaster Email Verification'
        when 'resetPassword'
          subject = req.l10n 'Reset Your RealMaster Password'
          data = {link: "#{protocolDomain}/resetPasswd/test-vcode"}
      ret = getDotHtml req, template, data
      if ret.err
        ctx.err = ret.err
        return resp.ckup 'admin/emailTest',ctx
      html = ret.html
      engine = engine or config.mailEngine?.mailEngine or 'SES'
      mail =
        _trans: 'smtp'
        engine: engine
        from: sendMail.getFromEmailByEngine(engine)
        replyTo: "RealMaster Technology Inc.<#{config.contact.defaultRTEmail or '<EMAIL>'}>"
        to: email
        subject: subject
        text: html
        html: html
        isNeedValidate: true
        eventType: 'EmailTest'
      # console.log '+++++++',req.body,engine
      # return
      sendMail.sendMail mail,(err,data)->
        console.error err if err
        sendTestmail()
    sendTestmail()


PROXY_EMAIL_ID = 'proxyEmail'
# /sys/proxyemail
GET 'proxyemail',(req,resp) ->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    return resp.redirect APP_HOME unless req.isAllowed 'admin'
    try
      ret = await SysDataModel.findById PROXY_EMAIL_ID
    catch err
      debug.error err
      return resp.redirect APP_HOME
    list = ret?.list or []
    resp.ckup 'admin/proxyEmail',{list,jsonList:helpers.encodeObj2JsonString(list)}

POST 'proxyemail',(req,resp) ->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    return resp.redirect APP_HOME unless req.isAllowed 'admin'
    return resp.send {ok:0,err:req.l10n(MSG_STRINGS.BAD_PARAMETER)} unless body = req.body
    if body.action is 'reload'
      try
        await libProxyEmail.reload()
        proxyEmails = await SysDataModel.findById PROXY_EMAIL_ID
      catch err
        debug.error err,'body:',body
        return resp.send {ok:0,err:req.l10n(MSG_STRINGS.DB_ERROR)}
      list = proxyEmails?.list or []
    else
      list = body.list or []
      try
        await SysDataModel.updateById PROXY_EMAIL_ID,{list}
        await libProxyEmail.reload()
      catch err
        debug.error err,'body:',body
        return resp.send {ok:0,err:req.l10n(MSG_STRINGS.DB_ERROR)}
    resp.send {ok:1,list}



# admin编辑客户手机验证信息页面
# /sys/phone
GET 'phone',(req,resp)->
  findError = (e)-> resp.redirect '/1.5/index'
  chkUserAdmin req,resp,findError,((user)->
    param = {}
    param.eml = eml if eml = req.param 'eml'
    resp.ckup 'sys-phone',param
  )

# 通过手机号或者邮箱查找用户
POST 'searchUsers',(req,resp)->
  findError = (err)-> respError {clientMsg:req.l10n(err), resp}
  chkUserAdmin req,resp,findError,(userAdmin)->
    unless (eml = (b = req.body).eml) or (mbl = b.mbl)
      return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp}
    try
      users = await UserModel.getByEmlOrMbl {eml,mbl}
    catch err
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), sysErr:err, resp}
    return respError {clientMsg:req.l10n(MSG_STRINGS.USER_NOT_FOUND), resp} unless users.length
    resp.send {ok:1,users}

# 通过手机号或者邮箱删除用户的手机号验证信息
POST 'unverify',(req,resp)->
  findError = (err)-> respError {clientMsg:req.l10n(err), resp}
  chkUserAdmin req,resp,findError,(userAdmin)->
    unless (eml = (b = req.body).eml) or (mbl = b.mbl)
      return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp}
    try
      await UserModel.deletePhoneVerify {eml,mbl,isAdmin:true}
    catch err
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), sysErr:err, resp}
    resp.send {ok:1,msg:req.l10n('Removed verification')}

# block手机号
POST 'blockPhone',(req,resp)->
  findError = (err)-> respError {clientMsg:req.l10n(err), resp}
  chkUserAdmin req,resp,findError,(userAdmin)->
    unless (mbl = (b = req.body).mbl) and b.memo
      return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp}
    try
      await BlockPhoneModel.blockPhone {mbl,m:b.memo,uid:userAdmin._id}
    catch err
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), sysErr:err, resp}
    resp.send {ok:1,msg:req.l10n('Phone number blocked.')}


VIEW 'sys-phone',->
  js "/js/jquery-1.10.1.min.js"
  text """
  <style>
  .content{
    background-color: rgb(230, 230, 230);
    padding-bottom:44px;
  }
  .content .action{
    position: relative;
    display: inline-block;
    padding: 6px 8px 7px;
    margin-bottom: 0;
    font-size: 12px;
    font-weight: 400;
    line-height: 1;
    color: #333;
    vertical-align: top;
    cursor: pointer;
    background-color: #5cb85c;
    border: 1px solid #5cb85c;
    border-radius: 3px;
    margin: 5px 10px 5px 10px;
    color: white;
  }
  .content .action.negative{
    background-color: #d9534f;
    border: 1px solid #d9534f;
  }
  .content .action.info{
    background-color: #46b8da;
    border: 1px solid #46b8da;
  }
  .content input{
    height: 35px;
    font-size: 16px;
    padding: 3px 7px;
  }
  .content .row {
    display:flex;
    justify-content:space-between;
    align-items:center;
  }
  .content .desc{
    font-size: 13px;
    color: #888;
    padding: 10px 0 0 10px;
  }
  #userInfoModal .content p{
    display:inline-block;
    width:calc(100% - 93px);
    padding-left: 10px;
  }
  #userInfoModal .content label{
    width:92px;
    text-align:left;
    font-size: 14px;
    overflow: scroll;
  }
  #userInfoModal .content .row{
    padding: 4px 10px 0 10px;
  }
  .line {
    border-bottom: 1px solid #000;
  }
  </style>
  """
  unless @popup
    header class: 'bar bar-nav', ->
      a class: 'icon icon-close pull-right nobusy', href: '/1.5/index'
      h1 class: 'title', -> text _("Manage phone")
  div id:"userInfoModal", class:"modal", ->
    header class:"bar bar-nav",->
      a class:"icon icon-close pull-right", href:"javascript:;", onclick:'toggleModal("userInfoModal","close")',->
      h1 class:"title",-> text 'User'
    div class:"content",->
  div class: 'content', ->
    div class:'bar bar-standard',style:'height: 0;',->
      div class:'row desc', -> 'Search for target user by email'
      input id:'email',name:'email',style:"width:100%;",value:(@eml or '')
      div class:'row desc', -> 'Search for target users by phone number'
      input type:'number',id:'mbl',name:'mbl',style:"width:100%;",value:(@mbl or '')
    div style:'padding-top:150px', ->
      button id:"check",class:'action info',-> "Check Account Info"
      div class:'row desc', -> 'unverify user phone number'
      div class:'row',->
        button id:"unverify",class:'action',-> "Unverify"
      div class:'row desc', -> 'Reason for block this phone number'
      input type:'text',id:'memo',name:'memo',style:"width: calc(100% - 20px);margin: 0 10px;"
      div class:'row desc', -> 'Must input the phone number for blocking'
      div class:'row',->
        button id:"block",class:'action',-> "Block"
    coffeejs ->
      $ ->
        genUserContent = (ret)->
          $('#userInfoModal .content').text('')
          html = '<div>'
          for u in ret
            for k,v of u
              html += '<div class="row">'
              html += '<label>'+k+'</label>'
              html += '<p>'+v+'</p>'
              html += '</div>'
            html += '<div class="line"></div>'
          html += '</div>'
          $('#userInfoModal .content').html(html)
        $('.action').on 'click',(e)->
          act = $(@).attr('id')
          data = {eml:$('#email').val(),mbl:$('#mbl').val(),act:act}
          if act is 'check'
            url = '/sys/searchUsers'
          else if act is 'unverify'
            url = '/sys/unverify'
          else if act is 'block'
            if $('#mbl').val().length is 0
              return alert('Need to input phone number')
            else if $('#memo').val().length is 0
              return alert('Need to input memo')
            data.memo = $('#memo').val()
            url = '/sys/blockPhone'
          else
            return alert('Unknown Action')
          $.post url,data,(ret)->
            if ret.ok
              if act is 'check'
                genUserContent(ret.users)
                toggleModal('userInfoModal','open')
                return
            alert JSON.stringify ret
      null

# admin编辑客户手机验证信息页面
# /sys/projstat
GET 'projstat',(req,resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    unless req.isAllowed 'marketAdmin',user
      return resp.ckup 'generalError',{err:"Not Authorized"}
    try
      stats = await StatModel.getProjList()
    catch err
      debug.error err
    resp.ckup 'admin/projStat',{stats}

APP '1.5'
#1.5/userfollowup
GET 'userfollowup',(req,resp)->
  tp = req.param 'tp'
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    unless req.isAllowed 'crmAdmin',user
      return resp.ckup 'generalError',{err:"Not Authorized"}
    resp.ckup 'admin/userFollowup',{tp},'_',{}

POST 'getUserfollowupData',(req,resp)->
  UserModel.appAuth {req,resp},(user)->
    unless req.isAllowed 'crmAdmin',user
      return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp}
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless eml = req.body.eml
    UserModel.findUserAndFollowersByEml eml,(err,user)->
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), sysErr:err, resp} if err
      user?.serviceCity ?= []
      resp.send {ok:1,user}

POST 'getFollowUpCRMUsers',(req,resp)->
  UserModel.appAuth {req,resp},(user)->
    unless req.isAllowed 'crmAdmin',user
      return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp}
    eml = req.body.eml
    tp = req.body.tp
    try
      users = await UserModel.getFollowUpCRMUsers {eml,tp}
      resp.send {ok:1,users}
    catch err
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), sysErr:err, resp}
      

POST 'updateUserFollowup',(req,resp)->
  UserModel.appAuth {req,resp},(user)->
    unless req.isAllowed 'crmAdmin',user
      return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp}
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless params = req.body
    params.uid = user?._id
    tp = req.param 'tp'
    try
      if tp is 'agent'
        await UserModel.setUserFubAgentInfo params
      else
        await UserModel.setUserFubInfo params
      resp.send {ok:1}
    catch error
      debug.error error
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), \
      sysErr:error, resp}
