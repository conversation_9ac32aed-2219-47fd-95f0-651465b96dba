Cache = INCLUDE 'lib.cache'
{JT2FT,FT2JT} = INCLUDE 'lib.i18n'
{replaceRM2REImagePath} = INCLUDE 'libapp.propertyImage'

# News = COLLECTION 'chome', 'news'
Forum = COLLECTION 'chome', 'forum'

UserModel = MODEL 'User'

getHeadLinesHome = (req,resp,cb)->
  # newsadmin在国内也可以显示
  if req.isChinaIP() and not req.isAllowed 'newsAdmin' # don't show news for china ip for now
    return cb {ok:0,err:'no headline'}
  UserModel.appAuth {req,resp},(user)->
    if user
      userLang = searchLang = user.locale or 'zh-cn'
      if searchLang is 'zh'
        searchLang = 'zh-cn'
    else
      userLang = searchLang  = req.locale() or 'en'
    # console.log "+++++++++"
    # console.log "lang:          " + lang
    # console.log "req.locale():  " + req.locale()
    # console.log "req._locale:   " + req._locale
    # console.log "cookie:        " + req.cookies['lang']
    # console.log "session:       " + req.session.get 'locale'
    # console.log "=========="
    isRealtor = req.hasRole('realtor')
    getNewsContents = (cb)-> getHeadLines req,searchLang,isRealtor,cb
    k = "#{searchLang}.headline.#{isRealtor}"
    Cache.fetch k, getNewsContents, (err,hlist)->
      if err
        return cb {ok:0, err:err}
      # 首页轮播删除edit profile
      # if user? and (not user.nm_zh?) and (not user.nm_en?)
      #   hlist = hlist.slice 0
      #   hlist.push {tl:req.l10n('Incomplete profile. Click to edit.'), url:'/1.5/settings/editProfile', tp:req.l10n('Tips')}
      for forum in hlist
        forum.tl = JT2FT forum.tl if userLang is 'zh'#conver simplify word to tranditional word
        forum.tl = FT2JT forum.tl if userLang is 'zh-cn'
        forum.tl = processTitleText(forum.tl)
      cb {ok:1,l:hlist}
DEF 'getHeadLinesHome', getHeadLinesHome

APP '1.5'
APP 'news',true

# DEF 'clearHeadLine',(locale)->
#   Cache.destroy locale + '.headline'

getHeadLines = (req,lang,isRealtor,cb)->
  l = []
  query = {sticky:'global', del:{$ne:true}}
  if not isRealtor
    query = Object.assign(query, {realtorOnly:{$exists:false}})
  # non-zh use same headline
  query.lang = 'en' if lang in ['en','kr','jp']
  # console.log query
  Forum.findToArray query, {projection: {cmnts: 0, m:0, fav:0, history:0},sort: {'mt': -1},limit:5},(err,rets)->
    if err
      console.log err
      cb err.toString()
    # console.log rets
    if rets
      for ret in rets
        l.push {
          tl:ret.tl,
          url:'/1.5/forum?postid=' + ret._id ,
          tp:req.l10n 'Headline'
          thumb: replaceRM2REImagePath(ret.thumb)
        }
    cb null, l, 10

# TODO: add new listings and use user_profile
POST 'headline.json',(req,resp)->
  # return resp.send {ok:1,l:[]}
  getHeadLinesHome req,resp,(ret)->
    resp.send ret

processTitleText = (text) ->
  return '' unless text
  
  # 检查是否已经包含处理过的HTML实体
  htmlEntities = ['&amp;', '&lt;', '&gt;', '&quot;', '&#39;']
  hasHtmlEntities = false
  for entity in htmlEntities
    if text.includes(entity)
      hasHtmlEntities = true
      break
  
  # 如果已经包含HTML实体，直接返回
  if hasHtmlEntities
    return text
  
  # 先解码已存在的HTML实体
  decodedText = text.replace /&#(\d+);/g, (match, dec) ->
    String.fromCharCode(dec)
  
  # 处理特殊字符
  specialChars =
    '&': '&amp;'
    '<': '&lt;'
    '>': '&gt;'
    '"': '&quot;'
    "'": '&#39;'
    ':': '：'
    ';': '；'
    ',': '，'
    '.': '。'
    '!': '！'
    '?': '？'
    '(': '（'
    ')': '）'
    '[': '【'
    ']': '】'
    '{': '｛'
    '}': '｝'
    '-': '－'
    '_': '＿'
    '+': '＋'
    '*': '＊'
    '/': '／'
    '\\': '＼'
    '|': '｜'
    '`': '｀'
    '~': '～'
    '@': '＠'
    '#': '＃'
    '$': '＄'
    '%': '％'
    '^': '＾'
    '=': '＝'
    ' ': '　'
  
  decodedText.replace /[^\w\s\u4e00-\u9fa5]/g, (char) ->
    specialChars[char] or char