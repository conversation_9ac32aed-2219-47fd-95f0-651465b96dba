engine = INCLUDE 'libapp.evaluationEngine'
{sprintf,vsprintf} = INCLUDE 'lib.sprintf'
libP = INCLUDE 'libapp.properties'
geolib = INCLUDE 'geolib'
{unifyAddress,formatLng} = INCLUDE 'lib.helpers_string'
Properties = MODEL 'Properties'
libUser = INCLUDE 'libapp.user'
ProvAndCity = MODEL 'ProvAndCity'
propAddress = INCLUDE 'lib.propAddress'
Evaluation = COLLECTION 'chome','evaluation'

UserModel = MODEL 'User'
getWXConfig = DEF 'getWXConfig'
getConfig = DEF 'getConfig'
debugHelper = INCLUDE 'lib.debug'
debug = debugHelper.getDebugger()
getUserCity = DEF 'getUserCity'
getNextTopListings = DEF 'getNextTopListings'
defaultCityToronto = DEF 'defaultCityToronto'
# filterPropSpLstField = DEF 'filterPropSpLstField'
propTranslate = INCLUDE 'libapp.propertiesTranslate'

Evaluation.createIndex {'mt': -1}
config = CONFIG(['share'])

_MAP_PROP_FIELDS = {
  lp:1
  lpr:1
  status:1
  daddr:1
  prov:1
  city:1
  unt:1
  st:1
  cmty:1
  addr:1
  apt_num:1
  bdrms:1
  tbdrms:1
  bthrms:1
  topup_pts:1
  topTs:1
  gr:1
  tgr:1
  sid:1
  ptype:1
  ptype2:1
  lat:1
  lng:1
  tax:1
  taxyr:1
  ddfID:1 #used in listingPicUrls
  phosrc:1 #used in listingPicUrls
  picUrl:1
  # phoUrls:1
  thumbUrl:1
  pho:1
  lst:1 #used in list
  saletp:1
  ts:1
  mt:1
  trbtp:1
  zip:1
  sqft:1
  sqft1:1
  sqft2:1
  sqftEstm:1
  onD:1
  offD:1
  depth:1
  front_ft:1
  lotsz_code:1
  sp:1
  phoP:1
  tnLH:1
  src:1
}

APP '1.5'
APP 'evaluation',true

findError = (msg, resp, err)->
  debug.error err if err
  # ret = genErrRet(msgObj,resp)
  resp.req?.logger?.error err if err
  resp.send {ok:0,e:msg?.toString()}

roundToK = (v)->
  Math.round( v / 1000 ) * 1000

GET 'maxDistRange', (req,resp)->
  type = req.param 'type'
  return findError MSG_STRINGS.BAD_PARAMETER,resp unless type
  return resp.send {ok:1, max_dist_range: engine.distRange[type]}


# 处理单个房源的信息，包括距离计算和属性过滤
processProp = (req, prop, p, dists, weights) ->
  # 如果房源没有距离信息，计算与目标点的距离
  unless prop.dist
    prop.dist = geolib.getDistance(
      {latitude: p.lat, longitude: p.lng},
      {latitude: prop.lat, longitude: prop.lng}
    )
  # 将距离和权重信息添加到数组中
  dists.push {id: prop._id,d: prop.dist}
  weights.push {id: prop._id,weight: prop.weight}
  # 设置过滤选项
  opt =
    hasRoleAdmin:req.hasRole('_admin')
    isAllowedVipUser:req.isAllowed('vipUser')
    apisrc:req.param('apisrc')
    # configShowSoldPrice:getConfig('showSoldPrice')
  libP.filterPropSpLstField opt, prop
  # 获取房源的状态标签和颜色
  {saleTpTag, tagColor} = libP.getSaleTpTagAndTagColorAndLstStr(prop)
  prop.saleTpTag = saleTpTag if saleTpTag
  prop.tagColor = tagColor if tagColor
  # 替换缩略图URL
  libP.thumbUrlReplace(prop, req.isChinaIP(), config.share?.hostNameCn)

# post /1.5/evaluation
POST (req,resp)->
  return findError MSG_STRINGS.BAD_PARAMETER,resp unless p = req.body
  UserModel.appAuth {req,resp},(user)->
    return findError MSG_STRINGS.ACCESS_DENIED,resp unless user
    p.lat = parseFloat p.lat
    p.lng = formatLng p.lng
    p.type = p.tp
    propAddress.formatProvAndCity p
    uaddr = unifyAddress p
    unless uaddr
      debug.error 'invalid evalution uaddr',p
      return findError req.l10n('invalid Address'), resp

    # 获取最大距离范围
    max_dist_range = engine.distRange[p.type]
    if (p.prov is 'BC')
      max_dist_range = max_dist_range * 2
    # 调用评估引擎进行评估
    engine.evaluate p,(err,result)->
      if err
        debug.error 'engine.evaluate err:'+err
        return resp.send {ok:0, e:'Error'}
      # 检查评估结果
      if (not result.sale) and (notresult.rent)
        return resp.send {ok:0, e:req.l10n('No enough data'),last: 12, range:max_dist_range}
      if sale = result.sale
        response =
          f:roundToK(sale.f)  # 最低价格
          t:roundToK(sale.t)  # 最高价格
          p:roundToK(sale.v)  # 预估价格
          aa:sale.aa         # 平均面积
          a:sale.a          # 实际面积
          ver: sale.ver     # 版本号
          range: sale.range or p.range or max_dist_range  # 搜索范围
          last: sale.last or p.last or 12  # 最近几个月的数据  
      # 处理租赁评估结果
      if rent = result.rent
        response = {} unless response
        response.rent=
            f: Math.round rent.f     # 最低租金
            t: Math.round rent.t     # 最高租金
            p: Math.round rent.v     # 预估租金
            aa: rent.aa            # 平均面积
            a: rent.a             # 实际面积
            range: rent.range ||  p.range_r || max_dist_range  # 搜索范围
            last: rent.last || p.last_r || 12  # 最近几个月的数据
      # 如果用户是房产经纪人，添加VIP价格信息
      if sale and (req.hasRole 'realtor')
        response.p1 = if req.isAllowed 'vipRealtor',user then roundToK(sale.v1) else req.l10n('For VIP user only')
      # 设置包含和排除的房源列表
      result.incl  =  result.sale?.used or []
      result.rentalIncl = result.rent?.used or []
      result.excl = p.excl or []
      result.rentalExcl = p.rentalExcl or []
      # 合并所有房源ID
      ids = result.excl.concat(result.rentalExcl)
      response.incl =  result.sale?.usedProps or []
      response.rentalIncl =  result.rent?.usedProps or []
      response.excl = []
      response.rentalExcl = []
      response.max_dist_range = max_dist_range
      # response.uaddr = uaddr
      dists = []
      weights = []
      # 处理包含的销售房源
      for prop in response.incl
        processProp req, prop, p, dists, weights
      # 处理包含的租赁房源
      for prop in response.rentalIncl
        processProp req, prop, p, dists, weights
      
      Properties.findPartialListingsByIDs ids,{projection:_MAP_PROP_FIELDS,sort:{
        sldd:-1, mt:-1
      }},(err,ret)->
        return findError MSG_STRINGS.DB_ERROR,resp, err if err
        doOne = (err)->
          if prop = ret.shift()
            processProp req, prop, p, dists, weights
            if result.excl.indexOf(prop._id)>=0
              response.excl.push prop
            if result.rentalExcl.indexOf(prop._id)>=0
              response.rentalExcl.push prop
            return doOne()
          else
            response.ok = 1
            req.setupL10n() unless req._ab
            response.incl = propTranslate.translate_rmprop_list(req, response.incl)
            response.rentalIncl = propTranslate.translate_rmprop_list(req, response.rentalIncl)
            response.excl = propTranslate.translate_rmprop_list(req, response.excl)
            response.rentalExcl = propTranslate.translate_rmprop_list(req, response.rentalExcl)
            return resp.send response if p.nocalc
            update = {$set:{}}
            update.$set =
              lat: p.lat
              lng: p.lng
              cnty: p.cnty || ''
              prov: p.prov || ''
              city: p.city || ''
              addr: p.addr || ((p.st_num || '') + " " + p.st||'')
              tp: p.tp # type: detached, semi
              mt: new Date()
            update.$set.cmty =  p.cmty if  p.cmty
            update.$set.st =  p.st if  p.st
            update.$set.st_num =  p.st_num if  p.st_num
            update.$set.unt =  p.unt if  p.unt
            ts = new Date()
            histId = user._id.toString()+ts.getTime()
            response.histId = histId
            update.$push =
              hist:
                $each: [
                  _id: histId
                  uid: user._id
                  tp: p.tp
                  bdrms: p.bdrms
                  br_plus: p.br_plus
                  bthrms: p.bthrms
                  gr: p.gr
                  reno:p.reno||3
                  sqft: p.sqft
                  front_ft: p.front_ft,
                  depth: p.depth,
                  lotsz_code: p.lotsz_code,
                  irreg: p.irreg
                  tax: p.tax
                  excl: result.excl,
                  incl: result.incl,
                  excl_r: result.rentalExcl,
                  incl_r: result.rentalIncl,
                  result:response,
                  mlsid: p.mlsid,
                  img: p.thumbUrl,
                  dists: dists,
                  weights: weights,
                  ts: ts
                ]
                $slice:-200
            # update.$set['users.'+user._id] = {avt: user.avt,nm: user.nm}
            update.$set['users.'+user._id] = {nm: user.nm}
            update.$set['users.'+user._id].range = response.range
            update.$set['users.'+user._id].tp = p.tp
            update.$set['users.'+user._id].ts = new Date()

            if user.avt
              update.$set['users.'+user._id].avt = user.avt
            Evaluation.findOneAndUpdate {_id: uaddr}, update, {upsert: true, returnDocument:'after'}, (err, ret)->
              return findError MSG_STRINGS.DB_ERROR,resp, err if err
              evaluate = ret?.value or {}
              response.owner = evaluate.owner or []
              response.histcnt = getHistCnt req, user, evaluate.hist
              response.ok = 1
              if evaluate.tracked
                id = evaluate.tracked.find (e) ->
                  return e.toString() is user._id.toString()
                response.tracked = true if id
              response.uaddr = uaddr
              return resp.send response
        doOne()

POST 'addOwner',(req, resp) ->
  return findError MSG_STRINGS.BAD_PARAMETER,resp unless p = req.body
  uaddr = p.uaddr
  uid = p.uid
  return findError MSG_STRINGS.BAD_PARAMETER,resp unless uaddr and uid
  UserModel.appAuth {req,resp},(user)->
    return findError MSG_STRINGS.ACCESS_DENIED,resp unless user
    Evaluation.updateOne {_id:uaddr}, {$push:{owner:uid}}, (err, ret) ->
      return findError MSG_STRINGS.DB_ERROR,resp, err if err
      return resp.send {ok:1}

POST 'delete', (req, resp) ->
  return findError MSG_STRINGS.BAD_PARAMETER,resp unless p = req.body
  uaddr = p.uaddr
  id = p.id
  uid = p.uid

  return findError MSG_STRINGS.BAD_PARAMETER,resp unless uaddr and (id or uid)
  UserModel.appAuth {req,resp},(user)->
    return findError MSG_STRINGS.ACCESS_DENIED,resp unless user?._id.toString() == uid or req.hasRole '_admin'
    if id
      update = {$pull: {hist: {_id: id}}}
    else # for delete at home, delete all the hist about this prop
      update = {$pull: {hist: {uid: new Evaluation.ObjectId(uid)},tracked: new Evaluation.ObjectId(uid)}}

    Evaluation.findOneAndUpdate {_id:uaddr},update,(err,ret)->
      return findError MSG_STRINGS.DB_ERROR,resp, err if err
      # pull tracked and user when all hist about this user is deleted.
      update = {$pull: {tracked: new Evaluation.ObjectId(uid)}}
      update.$unset = {}
      update.$unset['users.'+uid] = 1
      Evaluation.findOneAndUpdate {_id:uaddr, 'hist.uid':{$ne:user._id}},update,(err,ret)->
        return findError MSG_STRINGS.DB_ERROR,resp, err if err
        return resp.send {ok:1}


# find result used prop details.
POST 'props', (req,resp) ->
  return findError MSG_STRINGS.BAD_PARAMETER,resp unless p = req.body
  req.setupL10n() unless req._ab
  UserModel.appAuth {req,resp},(user)->
    return findError MSG_STRINGS.ACCESS_DENIED,resp unless user or p.share
    ids = req.body.ids
    Properties.findPartialListingsByIDs ids,{projection:_MAP_PROP_FIELDS, sort:{sldd:-1, mt:-1}},(err,ret)->
      return findError MSG_STRINGS.DB_ERROR,resp, err if err
      opt = {hasRoleAdmin:req.hasRole('_admin'),isAllowedVipUser:req.isAllowed('vipUser'),apisrc:req.param('apisrc')}
      for prop in ret
        libP.filterPropSpLstField opt, prop
      items = propTranslate.translate_rmprop_list(req, ret)
      return resp.send {ok:1, props: items}


GET (req,resp)->
  req.setLocale lang if lang = req.param 'lang'
  UserModel.appAuth {req,resp,url:'/1.5/user/login?d=#index'},(user)->
    js = ['/js/vue3.min.js', '/js/estimate/indexPage.js']
    opt = {
      d: req.param('d') or '/1.5/index'
      js: js
      css: ['/apps/estimate.css']
      }
    return resp.ckup "estimate/index",opt,'_',{noAngular:true,noUserModal:1}

GET 'evaluatePage.html', (req,resp)->
  req.setLocale lang if lang = req.param 'lang'
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    return resp.ckup "evaluate-page",{},'_',{noAngular:true,noUserModal:1}

GET 'histPage.html', (req,resp)->
  req.setLocale lang if lang = req.param 'lang'
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    return resp.ckup "evaluate-hist-page",{},'_',{noAngular:true,noUserModal:1}

#todo sort?
POST 'userHist', (req, resp)->
  UserModel.appAuth {req,resp}, (user)->
    return findError MSG_STRINGS.ACCESS_DENIED,resp unless user
    FIELD = {lat:1, tracked:1, lng:1,city:1,prov:1,addr:1,cmty:1,cnty:1, st:1, st_num:1, hist: 1}
    query = {'hist.uid': user._id }
    query.tracked = user._id if req.param('tracked')

    Evaluation.findToArray query,{fields:FIELD,limit:100},(err,ret)->
      return findError MSG_STRINGS.DB_ERROR,resp, err if err
      for prop in ret
        prop.tracked = false unless prop.tracked
        if prop.tracked
          id = prop.tracked.find (e) ->
            return e.toString() == user._id.toString()
          prop.tracked =  if id then true else false

        if prop.hist
          for i in [prop.hist.length-1 .. 0]
            if prop.hist[i]?.uid?.toString() == user._id.toString()
              prop.hist = prop.hist[i]
              break

      ret.sort((prop1, prop2)->
        return 1 unless prop1.hist && prop2.hist
        if new Date(prop1.hist.ts) > new Date(prop2.hist.ts)
          return -1
        else
          return 1
      )
      limit = req.param('limit')
      ret = ret.slice(0, limit) if limit
      return resp.send {ok:1, hist:ret}



POST 'propsByLatLng',(req,resp) ->
  return findError MSG_STRINGS.BAD_PARAMETER,resp unless p = req.body
  UserModel.appAuth {req,resp}, (user)->

    Properties.findNearByListings p,{projection:_MAP_PROP_FIELDS,sort:{ts:-1},limit:51},(err,props)->
    # PropertiesPartial.findToArray query,fields,(err,props) ->
      return findError MSG_STRINGS.DB_ERROR,resp, err if err
      # end = Date.now()
      # debug.log 'LOG: mongodb nearby search ts used: '+(end-start)
      opt = {hasRoleAdmin:req.hasRole('_admin'),isAllowedVipUser:req.isAllowed('vipUser'),apisrc:req.param('apisrc')}
      for prop in props
        libP.filterPropSpLstField opt, prop
      req.setupL10n() unless req._ab
      items = propTranslate.translate_rmprop_list(req, props)
      return resp.send {ok:1, props: items}


getHistCnt = (req, user, hist, opt) ->
  showPredOnWeb = (opt?.dpred) and (req.getDevType() isnt 'app')
  return 0 unless ((hist and user) or showPredOnWeb)
  if (req.isAllowed 'vipRealtor') or (req.isAllowed 'admin') or showPredOnWeb or opt?.dpred
    userids = hist.map((obj)->
      return obj.uid.toString()
    )
    ids = new Set(userids)
    hiscnt = ids?.size
    return hiscnt
  else if user
    hist = hist.filter (his)->
      return his.uid?.toString() == user._id.toString()

    return hist?.length
  0

POST 'hist',(req,resp)->
  return findError MSG_STRINGS.BAD_PARAMETER,resp unless p = req.body
  return findError MSG_STRINGS.BAD_PARAMETER,resp unless p.uaddr
  UserModel.appAuth {req,resp}, (user)->
    return findError MSG_STRINGS.ACCESS_DENIED,resp unless user
    Evaluation.findOne {_id:p.uaddr},{fields:{hist:1, users:1, addr:1, city:1, prov:1}},(err,ret) ->
      return findError MSG_STRINGS.DB_ERROR,resp, err if err
      return findError MSG_STRINGS.NOT_FOUND,resp, err unless ret
      if ret.hist?.length and (!req.isAllowed 'vipRealtor') and (!req.isAllowed 'admin') and (!p.inPredPage)
        ret.hist = ret.hist.filter (his)->
          return his.uid?.toString() == user._id.toString()
      return resp.send {ok:1, evaluate: ret}

# request may from prop listing detail page or from evaluation page
# when from listing detail page, param is uaddr, otherwise, param is detail info
POST 'histcnt',(req,resp)->
  return findError MSG_STRINGS.BAD_PARAMETER,resp unless p = req.body
  #if request has uaddr, use uaddr and format to id first.
  if uaddrParts = propAddress.decodeFromUaddr p.uaddr
    p.cnty = uaddrParts.cnty
    p.prov = uaddrParts.prov
    p.city = uaddrParts.city
    p.addr = uaddrParts.addr
  propAddress.formatProvAndCity p
  UserModel.appAuth {req,resp}, (user)->
    return findError MSG_STRINGS.ACCESS_DENIED,resp unless (user or req.getDevType() isnt 'app')
    uaddr = unifyAddress(p)
    return findError MSG_STRINGS.BAD_PARAMETER,resp if not uaddr
    Evaluation.findOne {_id:uaddr},{fields:{hist:1, users:1, addr:1, city:1, prov:1}},(err,ret) ->
      return findError MSG_STRINGS.DB_ERROR,resp, err if err
      return resp.send {ok:1, histcnt: 0, uaddr:uaddr} unless ret?.hist?.length
      # Properties.findOne {_id:p.pid}, {fields:{_id:1,dpred:1,pred_sp:1}},(err,prop) ->
      Properties.findOneByID p.pid, {projection:{_id:1,dpred:1,pred_sp:1}}, (err,prop)->
        debug.error err if err
        opt = if prop and prop.pred_sp and (prop?.dpred isnt 'N') then {dpred: true}
        histcnt = getHistCnt req, user, ret.hist, opt
        return resp.send {ok:1, uaddr:uaddr, histcnt: histcnt}

POST 'propcnt',(req,resp)->
  return findError MSG_STRINGS.BAD_PARAMETER,resp unless p = req.body
  return findError MSG_STRINGS.BAD_PARAMETER,resp unless p.uaddr or (p.lat and p.lng)
  UserModel.appAuth {req,resp}, (user)->
    return findError MSG_STRINGS.ACCESS_DENIED,resp unless user
    propAddress.formatProvAndCity p
    uaddr = if p.uaddr then p.uaddr else unifyAddress(p)
    if p.lng?
      p.lng = formatLng p.lng
    Properties.countPartialEvalNearbyProp p,{projection:_MAP_PROP_FIELDS},(err,ret)->
      if err
        return findError MSG_STRINGS.DB_ERROR,resp, err
      {propcnt,prop} = ret
      return resp.send {ok:1, uaddr, prop, propcnt}


GET 'result',(req,resp)->
  req.setLocale lang if lang = req.param 'lang'
  uaddr = req.param('uaddr')
  id = req.param('id')
  return findError MSG_STRINGS.BAD_PARAMETER,resp unless uaddr && id
  UserModel.appAuth {req,resp}, (user)->
    Evaluation.findOne {_id:uaddr},{fields:{tracked:1, hist:1, lat:1, lng:1, st:1, cmty:1, addr:1, city:1, prov:1, owner:1}},(err,ret)->
      return findError MSG_STRINGS.DB_ERROR,resp, err if err
      unless ret
        return resp.send {ok:0, e:'not found', url:'/1.5/evaluation/error'}
      hist = ret.hist?.find (e) ->
        return e?._id == id
      return resp.send {ok:0, e:'not found', url:'/1.5/evaluation/error'} unless hist
      if user && ret.tracked
        id = ret.tracked.find (e) ->
          return e.toString() == user._id.toString()
        tracked = true if id
      hiscnt = getHistCnt req, user, ret.hist
      Properties.findOneByUaddr {uaddr},(err,prop)->
        return findError MSG_STRINGS.DB_ERROR,resp, err if err
        _id = prop?._id
        return resp.send {ok:1,tracked:tracked,_id,addr:ret.addr,lat:ret.lat, cmty:ret.cmty,st:ret.st,lng:ret.lng, city: ret.city, prov: ret.prov, hist: hist, histcnt: hiscnt||0, owner:ret.owner||[]}

GET 'error', (req, resp)->
  cfg = {}
  cfg.hasU = 1
  cfg.avt  = "/img/icon_Alert.png"
  cfg.nm   = 'RealMaster'
  cfg.cardUrl  = req.fullUrl('/getapp')
  cfg.noWeCard = 1
  cfg.err = 1
  cfg.noLoading = 1
  if (req.getDevType() is 'app')
    cfg.noGetApp = 1
  cfg.shareInfoText = req.l10n "Can't find the report. May be deleted by the owner."
  return resp.ckup 'NewsMiddleAdPageEn', cfg, 'adPage', {jump:0}

POST 'latestEvaluations', (req, resp) ->
  UserModel.appAuth {req,resp}, (user)->
    return findError MSG_STRINGS.ACCESS_DENIED,resp unless user
    FIELD = {lat:1, tracked:1, lng:1,city:1,prov:1,addr:1,cmty:1,cnty:1, st:1, st_num:1, hist: 1}
    Evaluation.countDocuments {hist:{ $exists: true, $ne: [] }}, (err, cnt) ->
      return findError MSG_STRINGS.DB_ERROR,resp, err if err
      Evaluation.findToArray {hist:{ $exists: true, $ne: [] }, 'hist.uid': {$ne:user._id }},{fields:FIELD,limit:100, sort:{mt:-1}},(err,ret)->
        return findError MSG_STRINGS.DB_ERROR,resp, err if err
        for prop in ret
          if prop.hist
            for i in [prop.hist.length-1 .. 0]
              if prop.hist[i]?.uid?.toString() != user._id.toString()
                prop.hist = prop.hist[i]
                break
        return resp.send {ok:1, ret:ret, cnt: cnt}

POST 'trackedStat', (req, resp) ->
  UserModel.appAuth {req,resp}, (user)->
    return findError MSG_STRINGS.ACCESS_DENIED,resp unless user
    Evaluation.countDocuments {'tracked.0':{$exists:true}}, (err, cnt) ->
      return findError MSG_STRINGS.DB_ERROR,resp, err if err
      Evaluation.aggregate [
        {$unwind:"$tracked"},
        {$match:{'tracked':{$exists:true}}},
        { "$group": {"_id": null,"count": {"$sum": 1}}}
      ],{cursor:{batchSize:0}},(err, ret)->
        return findError MSG_STRINGS.DB_ERROR,resp, err if err
        return resp.send {ok:1, totalTrackedProp:cnt, totalTrackingUsers: ret?[0]?.count}

resultPage = (req, resp, title)->
  cfg = {noAngular:1,noUserModal:1,noref:1}
  getWXConfig req,req.fullUrl(),(err,wxcfg)->
    cfg.wxcfg = wxcfg
    maxImageSize = getConfig('maxImageSize')
    return resp.ckup "evaluate-result",{title:title,maxImageSize},'_',cfg


GET 'result.html',(req,resp) ->
  uaddr = req.param('uaddr')
  req.setLocale lang if lang = req.param 'lang'
  unless uaddr
    return resultPage req, resp
  Evaluation.findOne {_id: uaddr}, {fields: {addr:1, st:1, st_num:1, prov:1}}, (err, ret) ->
    return findError req.l10n(MSG_STRINGS.DB_ERROR), resp,'db', err if err
    if ret
      shareAddress = ret.addr || (ret.st_num||'' + ret.st||'')
      title = sprintf(req.l10n("I estimate %s on RealMaster for...",'evaluation'), shareAddress)
    return resultPage req, resp,title


GET 'comparables.html',(req,resp) ->
  req.setLocale lang if lang = req.param 'lang'
  cfg = {noAngular:1,noUserModal:1,noref:1}
  maxImageSize = getConfig('maxImageSize')
  return resp.ckup "evaluate-comparables",{maxImageSize},'_',cfg

POST 'addTrack',(req, resp) ->
  uaddr = req.param('uaddr')
  tracked = req.param('tracked')
  return findError MSG_STRINGS.BAD_PARAMETER,resp unless typeof tracked is 'boolean'
  UserModel.appAuth {req,resp}, (user)->
    return findError MSG_STRINGS.ACCESS_DENIED,resp unless user
    if tracked == true
      update = {$addToSet:{tracked:user._id}}
      msg = 'Saved'
    else
      update = {$pull:{tracked:user._id}}
      msg = 'Unsaved'
    Evaluation.updateOne {_id: uaddr}, update, (err, ret) ->
      return findError req.l10n(MSG_STRINGS.DB_ERROR), resp,'db', err if err
      return resp.send {ok:1,msg:req.l10n(msg,'favorite')}

GET 'listing.html',(req,resp) ->
  cfg = {noAngular:1,noUserModal:1,noref:1}
  maxImageSize = getConfig('maxImageSize')
  return resp.ckup "evaluate-mls-listing",{maxImageSize},'_',cfg

VIEW "evaluate-comparables",->
  div id:'vueBody',->
    text """<app-evaluation-comparables></app-evaluation-comparables>"""
  coffeejs {
      vars: {
        maxImageSize: @maxImageSize
      }
    },->
    null
  js '/js/entry/commons.js'
  css '/css/block.css'
  js '/js/entry/appEvaluationComparables.js'

VIEW "evaluate-mls-listing",->
  div id:'vueBody',->
    text """<app-evaluation-mls-listing></app-evaluation-mls-listing>"""
  coffeejs {
      vars: {
        maxImageSize: @maxImageSize
      }
    },->
    null
  js '/js/entry/commons.js'
  js '/js/entry/appEvaluationMlsListing.js'

VIEW "evaluate-page",->
  div id:'vueBody',->
    text """<app-evaluation-page></app-evaluation-page>"""
  js '/js/entry/commons.js'
  js '/js/entry/appEvaluationPage.js'
  css '/css/block.css'


VIEW "evaluate-result",->
  div id: "evaluateResult", style: "height: 100%;",->
    text """<app-evaluation-result></app-evaluation-result>"""
  coffeejs {
      vars: {
        maxImageSize: @maxImageSize
      }
    },->
    null
  js '/js/entry/commons.js'
  css '/css/block.css'
  js '/js/entry/appEvaluationResultPage.js'

VIEW "evaluate-hist-page",->
  div id: "evaluateHist", ->
    text """<app-evaluation-hist-page></app-evaluation-hist-page>"""
  js '/js/entry/commons.js'
  js '/js/entry/appEvaluationHistPage.js'


APP '1.5'
APP 'landlord',true
tabs = [
  { url: 'owners', nm: 'For Owner', selected: 'owners' },
  { url: 'sellhome', nm: 'Sell Home', selected: 'sellhome' }
]

GET 'owners',(req,resp)->
  req.setLocale lang if lang = req.param 'lang'
  UserModel.appAuth {req,resp,url:"/1.5/user/login?d=/1.5/landlord/owners"}, (user)->
    Properties.findUserActiveListings {locale:req.locale(), user, page:1, limit:21,promoted:true}, (err,listings)->
      js = ['/js/vue3.min.js', '/js/landlord/ownersPage.js']
      # listings = convertRmListingToTreb(req.locale(), listings)
      for reco in listings
        reco.pic.ml_num = reco.sid or reco.ml_num
        scope = {ml_num:reco.sid}
        reco.thumbUrl ?= libP.convert_rm_imgs(scope, reco.pic, 'reset')[0] or '/img/noPic.png'
        reco.lp = "$#{reco.lp.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")}" if reco.lp
      opt = {
        tabs:tabs
        d: req.param('d') or '/1.5/index'
        js: js
        css: ['/css/apps/landlord.css']
        id:'owners'
        recos:listings
        }
      debug.debug opt.recos
      return resp.ckup "landlord/index",opt,'_',{noAngular:true,noUserModal:1}


GET 'sellhome',(req,resp)->
  req.setLocale lang if lang = req.param 'lang'
  UserModel.appAuth {req,resp,url:"/1.5/user/login?d=/1.5/landlord/sellhome"}, (user)->
    js = ['/js/vue3.min.js', '/js/landlord/sellhomePage.js']
    ctx = {
      tabs:tabs
      d: req.param('d') or '/1.5/index'
      js: js
      css: ['/css/apps/landlord.css']
      id:'sellhome'
      }
    idx = parseInt(req.param('idx')) or 0
    cities = user?.cities or req.session.get('cities') or []
    nCities = cities.slice()
    if curCity = user?.city or curCity = req.session.get('city') or curCity = defaultCityToronto
      nCities.unshift(curCity)
    idx = idx%(nCities.length)
    userCity = getUserCity(req, user, nCities, idx)
    provName = userCity?.prov or 'Ontario'
    recos = getNextTopListings({prov:provName,ptype:'Residential'}) or []
    rcmdTypeMap = {
      new:req.l10n('New Listing'),
      rcmd:req.l10n('Recommend'),
      ad:req.l10n('TOP','advertise'),
      pc:req.l10n('New Price')
    }
    Properties.getMarketRecommend {provName,isAssignAdmin:false}, (err,ret)->
      return error(err) if err
      mrecos = propTranslate.translate_prop_list req, ret.l, rcmdTypeMap
      recos = (recos.concat mrecos) or []
      randomRecos=libP.getrecos {recos,isCip:req.isChinaIP(),shareHostNameCn:config.share?.hostNameCn}
      ctx.toplistings = randomRecos.toplistings
      ctx.exclusives = randomRecos.exclusives
      Properties.getPromotions {req,ctx,opt:['listing','exlisting']},(newCtx)->
        return resp.ckup "landlord/index",newCtx,'_',{noAngular:true,noUserModal:1}
