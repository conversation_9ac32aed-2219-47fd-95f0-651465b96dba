path = require 'path'
fs = require 'fs'
#vueServerRenderer = require 'vue-server-renderer/build.js'
# TODO: deprecate server vue-renderer
{ createBundleRenderer } = require('vue-server-renderer')
serialize = require 'serialize-javascript'
# accessAllowed = DEF '_access_allowed'
SysData = COLLECTION "chome",'sysdata'
{formatUrlStr} = INCLUDE 'lib.helpers_string'
{getShareImg,getShareDesc} = INCLUDE 'libapp.forum'
Forum = COLLECTION 'chome','forum'
ForumModel = MODEL 'Forum'
debugHelper = INCLUDE 'lib.debug'
debug = debugHelper.getDebugger()

config = CONFIG(['media'])
# TODO: deprecate server vue-renderer
serverBundleFilePath = path.join(config.media.path,'web/packs/bundle.webForumList.js')
serverBundleFileCode = fs.readFileSync(serverBundleFilePath, 'utf8')
#bundleRenderer = vueServerRenderer.createBundleRenderer(serverBundleFileCode);
bundleRenderer = createBundleRenderer(serverBundleFileCode)
detailserverBundleFilePath = path.join(config.media.path,'web/packs/bundle.webForumDetail.js')
detailserverBundleFileCode = fs.readFileSync(detailserverBundleFilePath, 'utf8')
#detailsBundleRenderer = vueServerRenderer.createBundleRenderer(detailserverBundleFileCode);
detailsBundleRenderer = createBundleRenderer(detailserverBundleFileCode)
getForumList = DEF 'getForumList'
getForumDetail = DEF 'getForumDetail'
webErrPage = DEF "webErrPage"
getWXConfig = DEF 'getWXConfig'

UserModel = MODEL 'User'

APP 'forum'

GET ':id/:title', (req, resp)->
  getDetails req, resp
GET ':id', (req, resp)->
  getDetails req, resp
#deprecated url
# NOTE：该接口在EDM里还在使用，未来需要使用新forum接口，暂时无法去掉
# TODO: EDM use new link https://www.realmaster.cn/1.5/topics/details?share=1&id=6822113e50d0e89b5d66c5fc&lang=zh-cn&wct=1
GET 'details/:id', (req, resp)->
  getDetails req, resp

getDetails =(req,resp) ->
  req.setLocale lang if lang = req.param 'lang'
  ctx = {webBackEnd:(req.isAllowed 'webBackEnd'), isApp: req.getDevType() is 'app', isCip: req.isChinaIP(), lang:req.locale(), isVipUser:(req.isAllowed 'vipUser'), host: req.host}
  id =req.param 'id'
  return webErrPage req, resp, 'no id', 'NA' unless id=id?.split('.')[0]

  UserModel.auth {req,resp},(user)->
    ctx.isLoggedIn = user?
    UserModel.findPublicInfo {lang:req.locale(),user},(err,sessionUser)->
      ctx.sessionUser= sessionUser
      ctx.forumAdmin = UserModel.accessAllowed 'forumAdmin',user
      req.user = user
      getForumDetail req, resp, id, (err, ret)->
        if err or !ret or  !ret.post then return webErrPage req, resp, err, 'NA'
        
        # 使用通用方法判断是否满足 .cn 域名且是个人帖子的条件
        if await ForumModel.isPersonalPostInCnDomain(req.host, ret.post)
          return webErrPage req, resp, MSG_STRINGS.ACCESS_DENIED
            
        ctx.post = ret.post || []
        ctx.cmnt_userList = ret.users || []
        ctx.authorIsVip = ret.authorIsVip || false
        ctx.relatedPosts = ret.relatedPosts || []
        author  = req.l10n('Realmaster Technology Inc.')
        title = "#{ret.post.tl} | 房大师(Realmaster.com)"
        description = getShareDesc(ret.post.m)
        ctx.title = title
        ctx.isCip = req.isChinaIP()
        ctx.lang = req.locale() || 'en'
        ctx.description = description
        ctx.author = req.l10n('Realmaster Technology Inc.')
        ctx.url = "http://#{req.host}/forum/#{ret.post._id}/#{formatUrlStr ret.post.tl}"
        ctx.images = ret.post.photos||[]
        ctx.post = ret.post
        ctx.addGoogleAd = !ret.post.adTop && !ret.post.adBottom
        shareImage = getShareImg req,ret.post
        ForumModel.findRecommend {similars:ret.post.similars},(err,posts)->
          debug.error 'forumServer getDetails',err if err
          ctx.relatedPosts = posts if posts
          layoutOpt = {title,description,image:shareImage,author,page:'forumDetail'}
          unless req.isWeChat()
            return resp.ckup 'forum/detail',ctx,'layout',layoutOpt
          getWXConfig req,req.fullUrl(),(err,wxcfg)->
            layoutOpt.wxcfg = wxcfg
            resp.ckup 'forum/detail',ctx,'layout',layoutOpt

GET 'list', (req, resp)->
  UserModel.auth {req,resp},(user)->
    # 准备web端论坛页面的上下文数据
    js = ['/js/vue3.min.js','/libs/jquery-1.9.1.min.js','/js/xpull.js',
      '/js/overthrow/overthrow.min.js ','/js/entry/commons.js ',
      '/js/axios.min.js', '/js/forum/indexPage.js']
    ctx =
      d: req.param('d') or '/'
      js: js
      css: ['/css/bootstrap.css', '/css/apps/forum.css','/css/xpull.css','/css/apps/drawers.css']
      page:'news'
      appmode: null  # web端不需要appmode
      lang: req.locale()
      isChinaIP: req.isChinaIP()
      os: if req.isIOSDevice() then 'ios' else 'android'
      isApp: req.getDevType() is 'app'

    # 渲染web端论坛页面
    resp.ckup 'forum/homeResponsive', ctx, 'layout', {noAngular: true, noref: true, form:'forum', vue3:true}

VIEW 'forum-view',->
  # css '/web/css/common.css'
  if @post.wpHosts
    css '/css/apps/wpPost.css'
  css '/css/apps/forum.css'
  js '/js/rmapp.min.js'
  text @html
  text @initialState
  js '/js/lz-string.min.js'
  js '/web/packs/commons.js'
  js '/web/packs/webForumDetail.js'
  # css '/web/css/bootstrap.min.css'
  # js '/js/jquery-2.1.1.min.js'
  # js '/web/packs/bootstrap.min.js'
