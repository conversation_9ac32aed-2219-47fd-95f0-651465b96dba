var path = require('path')
// var CommonsChunkPlugin = require("webpack/lib/optimize/CommonsChunkPlugin");
const webpack = require('webpack')
const VueLoaderPlugin = require('vue-loader/lib/plugin')

var config = {
  assetsRoot: path.resolve(__dirname, '../webroot/public/js/entry'),
  assetsPublicPath: '/js/entry', //not used
  productionSourceMap: true
}
// TODO:// appMapSearch: './coffee4client/entry/appMapSearch.js',
// TODO:// appLuckyDraw: './coffee4client/entry/appLuckyDraw.js',
module.exports = {
  entry: {
    commons:['babel-polyfill'],
    login: './coffee4client/entry/login.js',//web 登录
    // index: './coffee4client/entry/index.js',//web adminindex
    // coophousesMap: './coffee4client/entry/coophousesMap.js',
    commu: './coffee4client/entry/commu.js',//app 1.5/census/commu
    edm: './coffee4client/entry/edm.js',//web edm广告
    appEvaluationPage: './coffee4client/entry/appEvaluationPage.js',//app 估房价 第一步
    chat: './coffee4client/entry/chat.js',//app 和别人对话
    chatlist: './coffee4client/entry/chatlist.js',//app 对话列表
    syschat: './coffee4client/entry/syschat.js',
    propDetailPage: './coffee4client/entry/propDetailPage.js',//* app  propdetail
    // appIndex: './coffee4client/entry/appIndex.js',
    appSettings: './coffee4client/entry/appSettings.js',//app setting
    appLogin: './coffee4client/entry/appLogin.js',//app login
    appListSearch: './coffee4client/entry/appListSearch.js',//!app 搜索
    appSchoolList: './coffee4client/entry/appSchoolList.js',//app 学校列表
    appVerify: './coffee4client/entry/appVerify.js',//权限认证
    appRolesSearch: './coffee4client/entry/appRolesSearch.js',//经济列表
    appPropStats: './coffee4client/entry/appPropStats.js',//市场行情
    // nearbySearch: './coffee4client/entry/nearbySearch.js',
    appAdsManage: './coffee4client/entry/appAdsManage.js',//旧版广告编辑
    appProjectList: './coffee4client/entry/appProjectList.js',//新盘列表
    appTopUpPay: './coffee4client/entry/appTopUpPay.js',//置顶房源联系页面
    // appRMTopUpPay: './coffee4client/entry/appRMTopUpPay.js',
    appProjManage: './coffee4client/entry/appProjManage.js',//楼花编辑页面
    projDetailPage: './coffee4client/entry/projDetailPage.js',//* 楼花详情
    embededSchoolList: './coffee4client/entry/embededSchoolList.js',//! 第三方使用
    appRealtorPage: './coffee4client/entry/appRealtorPage.js',//more 地产经纪
    appPropDetailPage: './coffee4client/entry/appPropDetailPage.js',//* app propdetail
    appCitySelectModal: './coffee4client/entry/appCitySelectModal.js',//城市选择
    appMapSearchNew: './coffee4client/entry/appMapSearchNew.js',//网页版房源列表
    appAdvFilterModalPage: './coffee4client/entry/appAdvFilterModalPage.js',//搜索条件过滤
    appSchoolDetailPage: './coffee4client/entry/appSchoolDetailPage.js',//公校详情
    directionSearch: './coffee4client/entry/directionSearch.js',//通勤/路线规划
    placeSelect: './coffee4client/entry/placeSelect.js',//todo  大学列表？
    appMapMixView: './coffee4client/entry/appMapMixView.js',//街景
    appTopAgents: './coffee4client/entry/appTopAgents.js',//! 项目中未使用
    appPropManage: './coffee4client/entry/appPropManage.js',// 房源编辑页面
    // appEvaluationMap: './coffee4client/entry/appEvaluationMap.js',
    appEvaluationResultPage: './coffee4client/entry/appEvaluationResultPage.js',//app 估房价结果页
    appEvaluationHistPage: './coffee4client/entry/appEvaluationHistPage.js',//app 入口detail 当前房源估价历史
    appEvaluationMlsListing: './coffee4client/entry/appEvaluationMlsListing.js',//app 估价历史，地址附近的房源
    appEvaluationComparables:'./coffee4client/entry/appEvaluationComparables.js',//app 估房价 第二步
    appPropPredictionPage: './coffee4client/entry/appPropPredictionPage.js',//app 估价信息介绍
    // appPropServices:'./coffee4client/entry/appPropServices',
    appCpmAds:'./coffee4client/entry/appCpmAds.js',//cpm管理
    appCpmEdit:'./coffee4client/entry/appCpmEdit.js',//cpm广告编辑
    // forumHome: './coffee4client/entry/forumHome.js',
    forumEdit: './coffee4client/entry/forumEdit.js',//新闻编辑
    forumDetail: './coffee4client/entry/forumDetail.js',//新闻详情
    formList: './coffee4client/entry/formList.js',//admin  表格
    appCpmServices:'./coffee4client/entry/appCpmServices',//广告服务说明
    appCpmAnalytics:'./coffee4client/entry/appCpmAnalytics',//自己添加过的广告列表
    appCpmAnalyticsDetail: './coffee4client/entry/appCpmAnalyticsDetail',//广告详情
    appPropLoc: './coffee4client/entry/appPropLoc.js',//更改房源位置信息
    appAllTopAgents: './coffee4client/entry/appAllTopAgents.js',//置顶过的经济
    appFormInput:'./coffee4client/entry/appFormInput.js',//weform
    appCpmCurrentAd: './coffee4client/entry/appCpmCurrentAd.js',// native prop cpm广告
    appCrm: './coffee4client/entry/appCrm.js',//group成员查看
    appCrmDetail: './coffee4client/entry/appCrmDetail.js',//成员信息详情
    appListingAgent: './coffee4client/entry/appListingAgent.js',//卖家经济
    appYellowpageCategory: './coffee4client/entry/appYellowpageCategory.js',//黄页分类选择
    appWesite: './coffee4client/entry/appWesite.js',//个人微网站信息
    appFeedback: './coffee4client/entry/appFeedback.js',//联系我们
    privateSchoolDetail: './coffee4client/entry/privateSchoolDetail.js',//私校详情
    showingDetail:'./coffee4client/entry/showingDetail.js',//看房详情
    showingList:'./coffee4client/entry/showingList.js',//看房列表
    indexMoreCategory: './coffee4client/entry/IndexMoreCategory.js',
    contactCrm: './coffee4client/entry/contactCrm.js',//经济客户管理
    imageInsert: './coffee4client/entry/imageInsert.js',//上传图片
    universityDetail: './coffee4client/entry/universityDetail.js',//更多页面
    // settingSavedSearchModel: './coffee4client/entry/settingSavedSearchModel.js',
    webMap: './coffee4client/entry/webMap.js',//问题屋/学校地图
  },
  output: {
    path: config.assetsRoot,
    publicPath: config.assetsPublicPath,
    filename: '[name].js',
    chunkFilename: '[id].js'
  },
  // resolve: {
  //   extensions: ['', '.js', '.vue'],
  // },
  resolve: {
    extensions: ['.wasm', '.mjs', '.js', '.json','.vue','*'],
  },
  optimization: {
    namedModules: true, // NamedModulesPlugin()
    splitChunks: { // CommonsChunkPlugin()
        name: 'commons',
        minChunks: 1
    },
    // noEmitOnErrors: false, // NoEmitOnErrorsPlugin
    concatenateModules: true, //ModuleConcatenationPlugin
  },
  plugins: [
      // https://github.com/webpack/docs/wiki/optimization
      // new CommonsChunkPlugin({
      //   name: "commons",
      //   finename: "commons.js"
      // })
      // ,new webpack.optimize.UglifyJsPlugin({
      //   compress: {
      //     warnings: false
      //   }
      // })
      // ,new webpack.DefinePlugin({
      //   'process.env.NODE_ENV': JSON.stringify('production')
      // })
      new VueLoaderPlugin()
  ],
  // vue: {
  //   loaders: {
  //     scss: 'style!sass!css'
  //   }
  // },
  module: {
    rules: [
      {
        test: /\.scss$/,
        loaders: 'vue-style-loader!css-loader!sass-loader'
      },
      {
        test: /\.vue$/,
        loader: 'vue-loader'
      },
      {
        test: /\.pug$/,
        loader: 'pug-plain-loader'
      },
      {
        test: /\.css$/,
        use: [
          'vue-style-loader',
          {
            loader: 'css-loader',
            options: { importLoaders: 1 }
          }
        ]
      },
      {
        // test: /\.js$/,
        // loader: 'babel?presets=es2015',
        // exclude: /node_modules/
        test: /\.m?js$/,
        exclude: /(node_modules|bower_components)/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: ['@babel/preset-env']
          }
        }
      },
      // {
      //   test: /\.jade$/,
      //   loader: 'pug-plain-loader'
      // },
      {
        test: /\.less$/,
        use: [
          'vue-style-loader',
          'css-loader',
          'less-loader'
        ]
      },
      {
        test: /\.sass$/,
        use: [
          'vue-style-loader',
          'css-loader',
          'sass-loader'
        ]
      },
      {
        test: /isIterable/,
        loader: 'imports?Symbol=>false'
      },
      {
         test: /\.coffee$/,
         loader: 'coffee-loader',
         exclude: /node_modules/
      },
      { test: /.(png|gif|woff(2)?|eot|ttf|svg)(\?[a-z0-9=\.]+)?$/, loader: 'url-loader?limit=100000' }
    ]
  }
}
