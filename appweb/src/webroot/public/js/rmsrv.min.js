"use strict";var RMSrv,confirmJump,handleOpenURL,jumpURL,onNotification,onNotificationAPN;RMSrv={getCookie:function(e){var n,t,r,o,i;for(i=e+"=",r=0,o=(t=document.cookie.split(";")).length;r<o;r++){for(n=t[r];" "===n.charAt(0);)n=n.substring(1);if(0===n.indexOf(i))return n.substring(i.length,n.length)}return""},init:function(){var e;return this.geolocation=!1,this.ver=this.getCookie("apsv"),this.ver<"3.2"&&navigator.geolocation&&(this.geolocation=navigator.geolocation),(e=function(e){var n;return!!(n=(e=(e||navigator.userAgent).toLowerCase()).match(/android\s([0-9\.]*)/))&&n[1]})()&&parseFloat(e())<4.4&&(window.oldVerBrowser=!0),this.bindEvents()},getGeoPosition:function(e){var n,t;return null,t=function(n){if(e)return e(n)},n=function(n){return e&&e({err:n}),console.log(n.toString())},(window.LocationServices?window.LocationServices:(null!=RMSrv?RMSrv.geolocation:void 0)?RMSrv.geolocation:navigator.geolocation).getCurrentPosition(t,n)},bindEvents:function(){return document.addEventListener("deviceready",this.onDeviceReady,!1)},onDeviceReady:function(){var e,n;return(e=null!=(n=device.manufacturer)?n.toLowerCase():void 0)&&["xiaomi","huawei"].indexOf(e)>=0&&delete window.LocationServices,RMSrv.setupMenuBtn(),RMSrv.setupBackBtn(),RMSrv.regDevice(),RMSrv._setFileChooser(),RMSrv._getKeyboard(),RMSrv.ready=!0},enableMenuButton:function(){return RMSrv._disableMenuKey=!enabled},setupMenuBtn:function(){return document.addEventListener("menubutton",(function(){if(!RMSrv._disableMenuKey)return window.location="/1.5/settings"}),!1)},enableBackButton:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return RMSrv._disableBackKey=!e},setupBackBtn:function(){return document.addEventListener("backbutton",(function(e){if(!RMSrv._disableBackKey)return document.getElementById("top-page")?(e.preventDefault(),navigator.notification.confirm("Quit?",(function(e){if(1===e)return navigator.app.exitApp()}))):document.getElementById("news-page")?(e.preventDefault(),window.location="/1.5/index"):document.getElementById("wecard-list-page")?(e.preventDefault(),window.location="/1.5/settings"):document.getElementById("dl-share-content")?window.location.href=document.referrer:document.getElementById("srvEle")?(e.preventDefault(),toggleModal("propDetailModal","close")):navigator.app.backHistory()}),!1)},scanQR:function(e){return cordova.plugins.barcodeScanner.scan((function(n){return RMSrv.QRsuccess(n,e)}),RMSrv.QRfailed)},QRsuccess:function(e,n){return e.cancelled?null:setTimeout((function(){return"QR_CODE"!==e.format?RMSrv.dialogAlert("Scan Error. Please try again."):e.text.match(/(https|http):\/\/([a-z0-9\.\-\_]+)/i)?"function"==typeof n?n(e.text):window.location=(n||"/1.5/iframe?u=")+encodeURIComponent(e.text):RMSrv.dialogAlert("Unknown Code : "+e.text)}),10)},QRfailed:function(e){return RMSrv.dialogAlert("Scan Failed: "+e)},isIOS:function(){return/iPhone|iPad|iPod/i.test(navigator.userAgent)},isAndroid:function(){return/Android/i.test(navigator.userAgent)},isWeChat:function(){return/MicroMessenger/i.test(navigator.userAgent)},isBlackBerry:function(){return/BlackBerry/i.test(navigator.userAgent)},appendDomain:function(e){var n;return(n=window.location.href.split("/"))[0]+"//"+n[2]+e},showInBrowser:function(e){if(/^(http|https)/.test(e)||(e=this.appendDomain(e)),RMSrv.ready)return window.open(e,"_system")},openTBrowser:function(e,n){return/^(http|https)/.test(e)||(e=this.appendDomain(e)),cordova.ThemeableBrowser?cordova.ThemeableBrowser.open(e,"_blank",n):window.open(e,"_blank")},openInAppBrowser:function(e){var n,t,r,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"location=false";return r=window.open(encodeURI(e),"_blank",o),t=function(){var e;return r.removeEventListner("loadstop"),r.removeEventListner("loadstart"),e&&(clearInterval(e),e=null),r.close()},n=function(e,n){if(e.url.match("in_app_close")&&t(),n)return r.executeSript({code:"function(){var _ref;(_ref = document.getElementById('goback-link')) != null ? _ref.href = '/in_app_close' : void 0;return true;}.();"})},r.addEventListener("loadstop",(function(e){return n(e,!0)})),r.addEventListener("loadstart",(function(e){return n(e,!1)})),setInterval((function(){return r.executeScript({code:"window.location"},(function(e){if(e.toString().match("/tools"))return t()}))}),2e3)},dialogAlert:function(e){return navigator.notification.alert("string"==typeof e?e:e.message||e.toString())},dialogConfirm:function(e,n,t,r){return r||t&&Array.isArray(t)&&(r=t,t="Confirm"),navigator.notification.confirm(e.toString(),n,t,r)},isDBG:function(){return/i\.realmaster/i.test(window.location.hostname||/app\.test/i.test(window.location.hostname))},fDoc:function(){var e,n,t;if(e=null,t=document.getElementById("iframe"))try{null==(e=t.contentDocument)&&(e=document),e.document&&(e=e.document)}catch(e){n=e,console.log(n)}return e},getMeta:function(e){var n,t,r,o,i;for(o=e.querySelectorAll("meta"),i={title:e.title},n=0,t=o.length;n<t;n++)i[(r=o[n]).getAttribute("name")]=r.getAttribute("content");return i},getShareImage:function(e){var n,t,r,o,i;if(n=e.getElementById("content_div")||e.body)for(t=0,o=(i=n.getElementsByTagName("img")).length;t<o;t++)if(null!=(r=i[t])?r.src:void 0)return r.src;return"https://realmaster.com/img/logo.png"},logoImg:"data:image/png;base64,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",resizeImage:function(e,n,t,r){var o,i,a,l;return i=function(e){return i=null,r(e||RMSrv.logoImg)},o=e.createElement("canvas"),(a=e.createElement("img")).onload=function(){var e,n,r,a,l,u;try{if(u=l=this.width,a=r=this.height,l>r?l>t&&(u=t,a=Math.round(r/l*t)):r>t&&(a=t,u=Math.round(l/r*t)),o.width=u,o.height=a,o.getContext("2d").drawImage(this,0,0,u,a),e=o.toDataURL("image/png"),i)return i(e)}catch(e){if(n=e,RMSrv.isDBG()&&RMSrv.dialogAlert("Error:"+n),i)return i()}},a.onerror=function(e){if(RMSrv.isDBG()&&(alert("resizeImage Error:"+a.src),alert(JSON.stringify(e))),i)return i()},a.setAttribute("crossOrigin","anonymous"),a.crossOrigin="",(l=/^(.*\?v=)\d+$/g.exec(n))?a.src=l[1]+Date.now():"#"===n[n.length-1]?(n=n.substring(0,n.length-1),a.src=n+"?v="+Date.now()):/f\.i\.realmaster.*\.(jpg|png)$/.test(n)||/f\.realmaster.*\.(jpg|png)$/.test(n)?a.src=n+"?v="+Date.now():a.src=n,null},hasWechat:function(e){return RMSrv.onReady((function(){return("undefined"!=typeof Wechat&&null!==Wechat?Wechat.isInstalled:void 0)?Wechat.isInstalled((function(n){return e(n)}),(function(){return e(!1)})):e(!1)}))},wechatAuth:function(){return RMSrv.onReady((function(){return"undefined"!=typeof Wechat&&null!==Wechat?Wechat.auth("snsapi_userinfo",(function(e){var n;if(RMSrv.isAndroid()&&(null!=e&&null!=(n=e.code)?n.length:void 0)>10)return window.location.href="/scheme?code="+e.code}),(function(e){return location.reload(),"undefined"!=typeof console&&null!==console?console.log(e):void 0})):void 0}))},wechatShareError:function(e){switch(null!=e?e.toString():void 0){case"ERR_USER_CANCEL":break;case"ERR_WECHAT_NOT_INSTALLED":return RMSrv.dialogAlert("WeChat Not Installed");case"ERR_UNKNOWN":window.onerror("WeChat Sharing ERR_UNKNOWN",window.location.href,"");break;default:return RMSrv.dialogAlert(e.toString())}},wechatShare:function(e,n,t){var r;return RMSrv.ver>="3.1.0"&&"undefined"!=typeof Wechat&&null!==Wechat?(r={message:{title:n.title||"RealMaster Sharing",description:n.description||"RealMaster App Sharing",thumb:n.image,media:{type:Wechat.Type.WEBPAGE,webpageUrl:n.url.replace("realmaster.com","realmaster.cn")}},scene:t},RMSrv.shared(n,!0),void Wechat.share(r,(function(){return RMSrv.shared(n)}),(function(t){return"发送请求失败"===t?RMSrv.resizeImage(e,n.image,100,(function(e){return r.message.thumb=e,Wechat.share(r,(function(){return RMSrv.shared(n)}),RMSrv.wechatShareError)})):RMSrv.wechatShareError(t)}))):RMSrv.resizeImage(e,n.image,100,(function(e){return r={title:n.title||"RealMaster Sharing",description:n.description||"RealMaster App Sharing",thumbData:e.split(",")[1],url:n.url.replace("realmaster.com","realmaster.cn")},WeChat.share(r,t,(function(){return RMSrv.shared(n)}),RMSrv.wechatShareError)}))},facebookLogin:function(e){return facebookConnectPlugin.login(["public_profile"],e,(function(e){return RMSrv.dialogAlert("Login Error:"+JSON.stringify(e))}))},facebookLoginNShare:function(e,n,t){return RMSrv.facebookLogin((function(){return RMSrv.facebookShare(e,n,t)}))},_fbErrorCounter:0,facebookShare:function(e,n,t){var r;return r={method:t,picture:n.image,link:n.url,caption:n.title||"RealMaster Sharing",description:n.description||"RealMaster App Sharing"},facebookConnectPlugin.showDialog(r,(function(){return RMSrv.shared(n)}),(function(r){return RMSrv._fbErrorCounter++>1?(RMSrv.dialogAlert("Dialog Error:"+JSON.stringify(r)),void(RMSrv._fbErrorCounter=0)):"No active session"===r.toString()?RMSrv.facebookLoginNShare(e,n,t):void 0}))},qrcodeShare:function(e,n,t){var r,o,i;if(null==t&&(t="id_share_qrcode"),"show"===e){if(r=document.getElementById(t))return r.style.display="block",o=function(){var e;return(e=document.getElementById(t+"_holder")).innerHTML="",new QRCode(e,n)},"undefined"!=typeof QRCode&&null!==QRCode?o():((i=document.createElement("script")).type="text/javascript",i.src="/js/qrcode/qrcode.min.js",document.getElementsByTagName("head")[0].appendChild(i),i.onload=o)}else if(r=document.getElementById(t))return r.style.display="none"},showSMB:function(e){var n,t,r,o,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"share-";return RMSrv._shareMask||(RMSrv._shareMask=document.getElementById("backdrop"),null!=(t=RMSrv._shareMask)&&t.addEventListener("click",(function(){return RMSrv.showSMB("hide")}))),"show"===e?(RMSrv._sharePrefix=i,(n=document.getElementById(i+"placeholder"))&&n.appendChild(document.getElementById("shareDialog")),document.body.classList.add("smb-open"),null!=(r=RMSrv._shareMask)&&(r.style.display="block"),RMSrv.shareLang()):"hide"===e?(document.body.classList.remove("smb-open"),null!=(o=RMSrv._shareMask)?o.style.display="none":void 0):void 0},_shareMap:{title:"title",desc:"description",url:"url",image:"image",data:"data",dnurl:"dnurl"},_shareLang:null,shareLang:function(e){var n,t,r,o,i,a,l,u,c,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window.document;return o=document.getElementById("id_share_lang_en"),a=document.getElementById("id_share_lang_zh"),i=document.getElementById("id_share_lang_kr"),r=document.getElementById("id_share_lang_cur"),null!=o&&o.classList.remove("active"),null!=r&&r.classList.remove("active"),null!=a&&a.classList.remove("active"),null!=i&&i.classList.remove("active"),t=null!=r&&null!=(l=r.dataset)?l.lang:void 0,e&&"cur"!==e?"en"===e?null!=o&&o.classList.add("active"):"zh-cn"===e||"zh"===e?null!=a&&a.classList.add("active"):"kr"===e&&null!=i&&i.classList.add("active"):(e=t,null!=r&&r.classList.add("active")),RMSrv._shareMap="en"===e?{"title-en":"title","desc-en":"description",url:"url",image:"image",data:"data",dnurl:"dnurl"}:{title:"title",desc:"description",url:"url",image:"image",data:"data",dnurl:"dnurl"},n=RMSrv._getShareInfo(s,RMSrv.getMeta(s)),null!=(u=document.getElementById("id_share_title"))&&(u.value=n.title),null!=(c=document.getElementById("id_share_desc"))&&(c.value=n.description),RMSrv._shareLang=null!=e&&"cur"!==e?e:null},_getShareInfo:function(e,n){var t,r,o;for(r in t=function(t,r){var o,i;try{if(i=e.getElementById(RMSrv._sharePrefix+t)||e.getElementById("share-"+t)||e.getElementById("alt-"+t))return n[r||t]=i.value||i.textContent}catch(e){return o=e,"undefined"!=typeof console&&null!==console?console.log(o):void 0}},o=RMSrv._shareMap)t(r,o[r]);return null==n.image&&(n.image=RMSrv.getShareImage(e)),null==n.url&&(n.url=e.URL||window.location.href),n},share:function(e){var n,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window.document;switch(n=function(e,n){var r,o,i,a,l,u,c,s,d,g;i=function(e){return i=null,n(e)},r=RMSrv._getShareInfo(t,RMSrv.getMeta(t)),(d=null!=(u=t.getElementById("id_share_title"))?u.value:void 0)&&(r.title=d),(o=null!=(c=t.getElementById("id_share_desc"))?c.value:void 0)&&(r.description=o),RMSrv._shareLang&&((l=/\?.*(lang\=[a-zA-Z\-]+)/.exec(r.url))?r.url=r.url.replace(l[0],l[0].replace(l[1],"lang=".concat(RMSrv._shareLang))):/\?[a-z0-9]+\=/i.test(r.url)?r.url+="&lang="+RMSrv._shareLang:r.url+="?lang="+RMSrv._shareLang,r.data&&(l=/.*(lang\=[a-zA-Z\-]+)/.exec(r.data))&&(r.data=r.data.replace(l[0],l[0].replace(l[1],"lang=".concat(RMSrv._shareLang)))));try{if(!r.data&&!(r.data=null!=(s=document.querySelector("#share-data"))?s.innerHTML:void 0))return i(r);"string"==typeof e&&(r.data+="&channel=".concat(e)),(g=new XMLHttpRequest).open("POST","/1.5/api/rm/shareInfo",!0),g.setRequestHeader("Content-type","application/x-www-form-urlencoded"),g.timeout=8e3,g.ontimeout=function(){return RMSrv.dialogAlert("Timeout! Try again Later")},g.onreadystatechange=function(){var n;if(4===g.readyState){if(200===g.status)if(n=JSON.parse(g.responseText),"undefined"!=typeof console&&null!==console&&console.log(n.url),n.ok)r.url2=r.url,r.url=n.url;else if(n.err)return RMSrv.dialogAlert(n.err);if(i&&"share"!==e)return i(r)}},g.send(r.data)}catch(e){a=e,"undefined"!=typeof console&&null!==console&&console.log(a),i&&i(r)}},e){case"show":case"hide":return RMSrv.showSMB(e);case"lang-en":return RMSrv.shareLang("en",t);case"lang-cur":return RMSrv.shareLang("cur",t);case"lang-zh-cn":return RMSrv.shareLang("zh-cn",t);case"lang-kr":return RMSrv.shareLang("kr",t);case"qr-code":return RMSrv.showSMB("hide"),n(e,(function(e){return RMSrv.qrcodeShare("show",e.url)}));case"qr-code-close":return RMSrv.qrcodeShare("hide");case"wechat-friend":return RMSrv.showSMB("hide"),n(e,(function(e){return RMSrv.wechatShare(t,e,RMSrv.ver>="3.1.0"?0:WeChat.Scene.session||0)}));case"wechat-moment":return RMSrv.showSMB("hide"),n(e,(function(e){return RMSrv.wechatShare(t,e,RMSrv.ver>="3.1.0"?"undefined"!=typeof Wechat&&null!==Wechat?Wechat.Scene.TIMELINE:void 0:WeChat.Scene.timeline)}));case"facebook-feed":return RMSrv.showSMB("hide"),n(e,(function(e){return RMSrv.facebookShare(t,e,"feed")}));default:return RMSrv.showSMB("hide"),n(e,(function(e){return window.plugins.socialsharing.share(e.title||e.description||"Shared with RealMaster App",e.title||e.description||"RealMaster App Sharing",e.image,e.url)}))}},origin:function(){return window.location.origin||window.location.protocol+"//"+window.location.hostname+(window.location.port?":"+window.location.port:"")},shared:function(e,n){var t,r,o,i,a;return t=function(e){return/propDetailPage/.test(e)?e.split("ec=")[1].split("&")[0]:e.split("?")[0].split("/").pop()},i=RMSrv.origin()+"/1.5/user/update?act=share",null!=e.dnurl&&(i=e.dnurl,/^\//.test(e.dnurl)&&(i=RMSrv.origin()+e.dnurl)),e.url&&(r=t(e.url),o=i.indexOf("?")>0?"&":"?",i+=o+"data="+r,n&&(i+="&pre=1")),(a=new XMLHttpRequest).onreadystatechange=function(){var e,n;if(4===a.readyState&&200===a.status)try{if(null!=(n=e=JSON.parse(a.responseText))?n.j:void 0)return window.location.href=e.j}catch(e){e}},a.open("POST",i,!0),a.send()},clearCache:function(){var e;return null!=(e=window.cache)?e.clear((function(){}),(function(e){return console.log(e)})):void 0},setFileChooser:function(e,n){return null==RMSrv._fileChooser&&(RMSrv._fileChooser={}),RMSrv._fileChooser[e]=n,RMSrv._setFileChooser()},_setFileChooser:function(){var e,n,t,r,o,i;if("android"===("undefined"!=typeof device&&null!==device&&null!=(r=device.platform)?r.toLowerCase():void 0)&&0===("undefined"!=typeof device&&null!==device&&null!=(o=device.version)?o.indexOf("4.4.2"):void 0)){for(t in window.requestFileSystem(LocalFileSystem.PERSISTENT,0,(function(e){return RMSrv.fileSystem=e}),(function(e){return RMSrv.dialogAlert(e)})),i=RMSrv._fileChooser)e=i[t],(n=document.getElementById(t))&&n.addEventListener("click",(function(){return filechooser.open({},(function(n){return RMSrv.fileSystem.root.getFile(n.filepath,null,(function(n){return n.file(e,(function(e){return RMSrv.dialogAlert(e)}))}),(function(e){return RMSrv.dialogAlert(e)}))}))}),(function(e){return RMSrv.dialogAlert(e)}));return delete RMSrv._fileChooser}},getKeyboard:function(e){var n;return("undefined"!=typeof cordova&&null!==cordova&&null!=(n=cordova.plugins)?n.Keyboard:void 0)?e(cordova.plugins.Keyboard):(null==RMSrv._cbKeyBoard&&(RMSrv._cbKeyBoard=[]),RMSrv._cbKeyBoard.push(e))},_getKeyboard:function(){var e,n,t,r;if(("undefined"!=typeof cordova&&null!==cordova&&null!=(t=cordova.plugins)?t.Keyboard:void 0)&&RMSrv._cbKeyBoard){for(e=0,n=(r=RMSrv._cbKeyBoard).length;e<n;e++)(0,r[e])(cordova.plugins.Keyboard);return delete RMSrv._cbKeyBoard}},onReady:function(e){var n=this;return this._readyWaitingList?(this._readyWaitingList.push(e),!0):(this._readyWaitingList=[],this._readyWaitingList.push(e),this.getKeyboard((function(){var e,t,r,o;for(t=0,r=(o=n._readyWaitingList).length;t<r;t++)"function"==typeof(e=o[t])&&e();return delete n._readyWaitingList})),!0)},sendToken:function(e,n){var t,r;return t=e+":"+n,this._fnWhenReg||t!==localStorage.pn?(localStorage.pn=t,(r=new XMLHttpRequest).onreadystatechange=function(){if(4===r.readyState&&200===r.status)return"undefined"!=typeof console&&null!==console?console.log(r.responseText):void 0},r.open("POST","/1.5/user/updPn"),r.setRequestHeader("Content-type","application/x-www-form-urlencoded"),r.send("pn="+t),this._notifyReg(t)):this._notifyReg(t)},regDevice:function(){var e,n;if(this.wakenByMsg)return null;try{return n=window.plugins.pushNotification,"android"===device.platform||"Android"===device.platform||"amazon-fireos"===device.platform?n.register((function(){}),(function(e){return RMSrv.dialogAlert(e)}),{senderID:"339283724232",ecb:"onNotification"}):n.register((function(e){return RMSrv.sendToken("ios",e)}),(function(e){return RMSrv.dialogAlert(e)}),{badge:"true",sound:"true",alert:"true",ecb:"onNotificationAPN"})}catch(n){return e=n,RMSrv.dialogAlert("Error: "+e.message)}},_notifyReg:function(e){var n,t,r,o,i;if(this._RegFinished=!0,t=this._fnWhenReg){for(i=[],r=0,o=t.length;r<o;r++){n=t[r];try{i.push(n(e))}catch(e){e}}return i}},whenReg:function(e){return this._RegFinished?e():(null==this._fnWhenReg&&(this._fnWhenReg=[]),this._fnWhenReg.push(e))},getTranslate:function(e,n){var t;return t="https://translate.google.com/#auto/zh-CN/"+encodeURIComponent(e),".text-wrap .translation",RMSrv.getPageContent(t,".text-wrap .translation",{wait:12e3},(function(e){return n(e)}))},getPageContent:function(e,n,t,r){return null==r&&(r=t,t=null),RMSrv.getKeyboard((function(){var o,i,a,l,u,c,s,d,g,f,v,h,p;if(!t.close||!g||"function"!=typeof g.close)return d=cordova.ThemeableBrowser,a={image:"left",imagePressed:"left",align:"left",event:"custClosePressed"},i={image:"back",imagePressed:"back",align:"left",event:"backPressed"},(c=window.isIOS?[a,i]:[i,a]).push({image:"check",imagePressed:"check",align:"right",event:"pagesPressed"}),u={hidden:!0,toolbar:{height:44,color:"#E03131"},customButtons:c,fullscreen:!1},!1===t.hide&&(u.hidden=!1),v=!1,null,g=d.open(e,"_blank",u),f=function(){return g.removeEventListener("custClosePressed",l),g.removeEventListener("backPressed",o),g.removeEventListener("loaderror",h),g.removeEventListener("pagesPressed",p),g.removeEventListener(cordova.ThemeableBrowser.EVT_ERR,h),g.close()},s=function(e){if(!v)return v=!0,f(),r(e)},!0!==t.nostop&&setTimeout((function(){return p()}),(null!=t?t.wait:void 0)||6e3),p=function(){var e;return e="(el = document.querySelector('".concat(n,"'))?el.textContent:''"),"html"!==n&&"html:wechat"!==n||(e="document.documentElement.innerHTML"),setTimeout((function(){return g.executeScript({code:e},(function(e){return Array.isArray(e)&&(e=e[0]),s(e)}))}),1e3)},h=function(e){return"Error: ".concat(e.code+" : "+e.message)},l=function(){return s("Cancelled")},o=function(){return"window.history.back(-1)",g.executeScript({code:"window.history.back(-1)"})},(!0===u.hidden||t.isWechat)&&g.addEventListener("loadstop",p),g.addEventListener("custClosePressed",l),g.addEventListener("backPressed",o),g.addEventListener("pagesPressed",p),g.addEventListener("loaderror",h),g.addEventListener(cordova.ThemeableBrowser.EVT_ERR,h);f()}))}},jumpURL=function(e){return window.location=RMSrv.origin()+"/scheme/jump?u="+encodeURIComponent(e)},confirmJump=function(e,n){var t;return t=function(e){if(2===e)return jumpURL(n)},RMSrv.dialogConfirm(e,t,"Message",["Cancel","Open"])},onNotification=function(e){var n,t,r,o,i,a,l;switch(e.event){case"registered":if(RMSrv.wakenByMsg)return;if(e.regid.length>0)return RMSrv.sendToken("android",e.regid);break;case"message":if(RMSrv.wakenByMsg=!0,navigator.vibrate(1e3),e.foreground){if(l=e.soundname||e.payload.sound,new Media("/android_asset/www/"+l).play(),(null!=(n=e.payload)?n.url:void 0)&&(null!=(t=e.payload)?t.message:void 0))return confirmJump(e.payload.message,e.payload.url)}else if(e.coldstart){if(null!=(r=e.payload)?r.url:void 0)return jumpURL(e.payload.url)}else if((null!=(o=e.payload)?o.url:void 0)&&(null!=(i=e.payload)?i.message:void 0))return confirmJump(e.payload.message,e.payload.url);if((null!=(a=e.payload)?a.message:void 0)&&dialogAlert(e.payload.message),"undefined"!=typeof updateNotification&&null!==updateNotification)return updateNotification(e.payload.msgcnt);break;case"error":return RMSrv.dialogAlert(e.msg);default:return RMSrv.dialogAlert("Unknown Event")}},onNotificationAPN=function(e){var n;return("undefined"!=typeof navigator&&null!==navigator?navigator.vibrate:void 0)&&navigator.vibrate(1e3),e.sound&&new Media(e.sound).play(),e.badge&&(n=window.plugins.pushNotification)&&n.setApplicationIconBadgeNumber((function(){}),RMSrv.dialogAlert,e.badge),e.url&&e.alert?e.foreground?confirmJump(e.alert,e.url):jumpURL(e.url):e.alert?RMSrv.dialogAlert(e.alert):void 0},handleOpenURL=function(e){return setTimeout((function(){var n,t;if((n=e.indexOf("?"))>=0)return t=e.substr(n),window.location=RMSrv.origin()+"/scheme"+t;t=""}),10)},window.onerror=function(e,n,t){var r,o,i;o=e+"\n"+n+"\n"+t,/i\.realmaster/.test(window.location.href)&&alert(o);try{return(i=new XMLHttpRequest).onreadystatechange=function(){if(4===i.readyState&&200===i.status)return"undefined"!=typeof console&&null!==console?console.log(i.responseText):void 0},i.open("POST","/cError"),i.setRequestHeader("Content-type","application/x-www-form-urlencoded"),i.send("m="+encodeURIComponent(o))}catch(e){return r=e,"undefined"!=typeof console&&null!==console?console.log(r):void 0}},RMSrv.init();
