"use strict";function _regeneratorRuntime(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */_regeneratorRuntime=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function u(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,n){return e[t]=n}}function s(e,t,n,r){var i=t&&t.prototype instanceof m?t:m,a=Object.create(i.prototype),l=new _(r||[]);return o(a,"_invoke",{value:C(e,n,l)}),a}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=s;var p="suspendedStart",h="suspendedYield",v="executing",f="completed",g={};function m(){}function S(){}function R(){}var M={};u(M,a,(function(){return this}));var w=Object.getPrototypeOf,y=w&&w(w(B([])));y&&y!==n&&r.call(y,a)&&(M=y);var b=R.prototype=m.prototype=Object.create(M);function A(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function k(e,t){function n(o,i,a,l){var c=d(e[o],e,i);if("throw"!==c.type){var u=c.arg,s=u.value;return s&&"object"==_typeof(s)&&r.call(s,"__await")?t.resolve(s.__await).then((function(e){n("next",e,a,l)}),(function(e){n("throw",e,a,l)})):t.resolve(s).then((function(e){u.value=e,a(u)}),(function(e){return n("throw",e,a,l)}))}l(c.arg)}var i;o(this,"_invoke",{value:function(e,r){function o(){return new t((function(t,o){n(e,r,t,o)}))}return i=i?i.then(o,o):o()}})}function C(t,n,r){var o=p;return function(i,a){if(o===v)throw Error("Generator is already running");if(o===f){if("throw"===i)throw a;return{value:e,done:!0}}for(r.method=i,r.arg=a;;){var l=r.delegate;if(l){var c=I(l,r);if(c){if(c===g)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===p)throw o=f,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=v;var u=d(t,n,r);if("normal"===u.type){if(o=r.done?f:h,u.arg===g)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(o=f,r.method="throw",r.arg=u.arg)}}}function I(t,n){var r=n.method,o=t.iterator[r];if(o===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,I(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),g;var i=d(o,t.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,g;var a=i.arg;return a?a.done?(n[t.resultName]=a.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,g):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,g)}function E(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function L(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function _(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(E,this),this.reset(!0)}function B(t){if(t||""===t){var n=t[a];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,i=function n(){for(;++o<t.length;)if(r.call(t,o))return n.value=t[o],n.done=!1,n;return n.value=e,n.done=!0,n};return i.next=i}}throw new TypeError(_typeof(t)+" is not iterable")}return S.prototype=R,o(b,"constructor",{value:R,configurable:!0}),o(R,"constructor",{value:S,configurable:!0}),S.displayName=u(R,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===S||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,R):(e.__proto__=R,u(e,c,"GeneratorFunction")),e.prototype=Object.create(b),e},t.awrap=function(e){return{__await:e}},A(k.prototype),u(k.prototype,l,(function(){return this})),t.AsyncIterator=k,t.async=function(e,n,r,o,i){void 0===i&&(i=Promise);var a=new k(s(e,n,r,o),i);return t.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},A(b),u(b,c,"Generator"),u(b,a,(function(){return this})),u(b,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=B,_.prototype={constructor:_,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(L),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function o(r,o){return l.type="throw",l.arg=t,n.next=r,o&&(n.method="next",n.arg=e),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],l=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=r.call(a,"catchLoc"),u=r.call(a,"finallyLoc");if(c&&u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,g):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),L(n),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;L(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:B(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),g}},t}function asyncGeneratorStep(e,t,n,r,o,i,a){try{var l=e[i](a),c=l.value}catch(e){return void n(e)}l.done?t(c):Promise.resolve(c).then(r,o)}function _asyncToGenerator(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var i=e.apply(t,n);function a(e){asyncGeneratorStep(i,r,o,a,l,"next",e)}function l(e){asyncGeneratorStep(i,r,o,a,l,"throw",e)}a(void 0)}))}}function _typeof(e){return _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_typeof(e)}var RMSrv2,confirmJump,handleOpenURL,jumpURL,urlOpenMap;urlOpenMap={},handleOpenURL=function(e){var t,n;if("string"==typeof e)return function(e){return!1},t=function(e){return!!/\/s\/\w{10,}/.test(e)||/1\.5\/prop\/detail/.test(e)},n=function(){var n,r;return t(e)?RMSrv.openTBrowser(e):(n=e.indexOf("?"))>=0?(r=e.substr(n),window.location=RMSrv.origin()+"/scheme"+r):/^realmaster\:\/\/qrcode\/act/.test(e)?window.location=RMSrv.origin()+e.substr(12):void(r="")},urlOpenMap[e]&&clearTimeout(urlOpenMap[e]),urlOpenMap[e]=setTimeout(n,100)},RMSrv2={getCookie:function(e){var t,n,r,o,i;for(i=e+"=",r=0,o=(n=document.cookie.split(";")).length;r<o;r++){for(t=n[r];" "===t.charAt(0);)t=t.substring(1);if(0===t.indexOf(i))return t.substring(i.length,t.length)}return""},isWechatPic:function(e){return null==e&&(e=""),/qpic\.cn\/mmbiz/.test(e)},setShareImg:function(){var e,t,n,r,o,i;if(n=document.querySelector("#share-image"),r=document.querySelector("#share-image-uri"),(o=null!=n?n.innerText:void 0)&&!/^data|realmaster|realexpert|^\//.test(o))return/^http/.test(o)||(o=window.location.protocol+o,n.innerText=o),RMSrv.isWechatPic(o)&&RMSrv.isIOS()&&LZString?(e={url:o},t=LZString.compressToEncodedURIComponent(JSON.stringify(e)),i="/1.5/htmltoimg/imgflyer?data=".concat(t,"&responseType=blob"),void(r.innerText=this.appendDomain(i))):null},cError:function(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window.location.href;console.error(e);try{t=JSON.stringify(e)}catch(e){e}return window.onerror(e.toString(),n,t||e.toString())},_actCallbacks:{next:Date.now()},_actListeners:{},_emitEvent:function(e){var t,n,r,o,i;if((n=RMSrv._actListeners[null!=e?e.tp:void 0])&&n.length>0){for(i=[],r=o=n.length-1;o>=0;r=o+=-1)(t=n[r]).fn&&setTimeout((function(){return t.fn(e.p)}),0),t.once?i.push(n.pop()):i.push(void 0);return i}},action:function(e,t){if("string"==typeof e&&(e={tp:e}),"object"!==_typeof(e))throw new Error("RM: wrong action type");return t&&(e.cb=RMSrv._actCallbacks.next,RMSrv._actCallbacks[RMSrv._actCallbacks.next]=t,RMSrv._actCallbacks.next+=1),"wechat.has"===e.tp&&"tp: ".concat(e.tp,"; cb: ").concat(e.cb,"; keys:").concat(Object.keys(RMSrv._actCallbacks)),window.rmCall(JSON.stringify(e))},reaction:function(e){var t,n,r,o,i;try{if((RMSrv.isIOS()||null!=window.ReactNativeWebView)&&(e=decodeURIComponent(e)),"{"!==e.charAt(0))return/:sel:/.test(e)?(r=e.match(/^\:sel\:(.*)$/),i=document.querySelector(r[1]),o={tp:"setBarColor"},i&&(/^:name:/.test(i.textContent)?o.name=i.textContent.substr(6):o.barColor=i.textContent),void window.rmCall(JSON.stringify(o))):void 0;n=JSON.parse(e)}catch(e){return e,void console.error("invalid reaction data")}if(n.cb&&(t=RMSrv._actCallbacks[n.cb]))return setTimeout((function(){return t(n.p)}),0),delete RMSrv._actCallbacks[n.cb];if(n.tp){if("ready"!==n.tp)return RMSrv._emitEvent(n);if(null!=n.next)return RMSrv._actCallbacks.next=n.next}},listen:function(e,t,n){var r,o,i,a,l;for(n||(n=t,t=!1),a=0,l=(i=null!=(r=RMSrv._actListeners)[e]?r[e]:r[e]=[]).length;a<l;a++)if((o=i[a]).fn===n){if(o.once!==t)throw new Error("Conflict listen once flag for ".concat(e));return}return t?i.unshift({fn:n}):i.push({fn:n,once:t})},setFileChooser:function(e,t){},removeListner:function(e,t){var n,r,o,i,a;if(t){for(o=i=0,a=(r=null!=(n=RMSrv._actListeners)[e]?n[e]:n[e]=[]).length;i<a;o=++i)if(r[o].fn===t)return void r.splice(o,1)}else delete RMSrv._actListeners[e]},setCookie:function(){return RMSrv.action({tp:"setCookie",p:document.cookie,domain:document.URL})},showSystemValue:function(){return RMSrv.action("showSystemValue")},rmMessage:function(e){return setTimeout((function(){return RMSrv.reaction(e)}),0)},sendWebReady:function(e){if(RMSrv.androidVersion()&&window.postMessage&&e<4)return window.originalPostMessage?window.postMessage(":cmd::goReady"):null!=window.ReactNativeWebView?window.ReactNativeWebView.postMessage(encodeURIComponent(":cmd::goReady")):setTimeout((function(){return RMSrv.sendWebReady(e+1)}),500)},init:function(){var e,t,n;if(!RMSrv.inited)return RMSrv.inited=!0,RMSrv.listen("pushToken",RMSrv.pushToken),RMSrv.listen("pushNotice",RMSrv.pushNotice),RMSrv.listen("openURL",handleOpenURL),RMSrv.listen("native.keyboardshow",RMSrv.handleKeyboardshow),RMSrv.listen("checkAlive",RMSrv.checkAlive),RMSrv.listen("appStateChange",RMSrv.appStateChangeCb),n=function(){var e;if(e=document.querySelector("#simPopUpModal"))return e.parentNode.removeChild(e)},t=function(e){return/^\{/.test(e)||!/^\#/.test(e)},window.addEventListener("message",(function(e){var r,o;if(e&&e.data&&(e=e.data),/^:ctx/.test(e)&&(e=e.substr(5)),!e.vueDetected&&!/vue-devtools/.test(e.source))return/^:cancel/.test(e)?(n(),void(RMSrv.cb=null)):t(e)?((r=RMSrv.cb)&&(r(e),RMSrv.cb=null),n()):"string"==typeof e&&(o=document.querySelector(e))?iframRmCall(o.innerHTML()):void 0})),document.addEventListener("message",(function(e){return setTimeout((function(){return RMSrv.reaction(e.data)}),0)})),window.rmCall=function(e){return(null!=RMSrv._callStack?RMSrv._callStack:RMSrv._callStack=[]).push(e)},window.iframRmCall=function(e){return"string"!=typeof e&&(e=JSON.stringify(e)),window.parent.postMessage(e,"*")},RMSrv.sendWebReady(1),setTimeout((function(){return RMSrv.goReady()}),800),(e=RMSrv.androidVersion())&&parseFloat(e)<4.4?window.oldVerBrowser=!0:void 0},goReady:function(e){if(!RMSrv.ready)return RMSrv.ready=!0,null!=window.ReactNativeWebView?window.rmCall=function(e){return window.ReactNativeWebView.postMessage(encodeURIComponent(e))}:window.rmCall=function(e){return window.postMessage(e)},setTimeout((function(){var e,t;return t=function(){var e;return(e=function(){var t,n;return(t=null!=(n=RMSrv._whenReady)?n.shift():void 0)?("function"==typeof t&&t(),setTimeout(e,1)):delete RMSrv._whenReady})()},(e=function(){var n,r;return(n=null!=(r=RMSrv._callStack)?r.shift():void 0)?(window.rmCall(n),setTimeout(e,1)):(delete RMSrv._callStack,t())})()}),1)},onReady:function(e){return RMSrv.ready?setTimeout((function(){return e()}),0):(null!=RMSrv._whenReady?RMSrv._whenReady:RMSrv._whenReady=[]).push(e)},onAppStateChange:function(e){return(null!=RMSrv._whenAppStateChange?RMSrv._whenAppStateChange:RMSrv._whenAppStateChange=[]).push(e)},offAppStateChange:function(e){var t,n;if((null!=(n=RMSrv._whenAppStateChange)?n.length:void 0)&&e&&(t=RMSrv._whenAppStateChange.indexOf(e))>-1)return RMSrv._whenAppStateChange.splice(t,1)},enableBackButton:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return RMSrv._disableBackKey=!e},setupBackBtn:function(){return RMSrv.listen("backbutton",(function(e){if(!RMSrv._disableBackKey)return document.getElementById("top-page")?(e.preventDefault(),navigator.notification.confirm("Quit?",(function(e){if(1===e)return navigator.app.exitApp()}))):document.getElementById("news-page")?(e.preventDefault(),window.location="/1.5/index"):document.getElementById("wecard-list-page")?(e.preventDefault(),window.location="/1.5/settings"):document.getElementById("dl-share-content")?window.location.href=document.referrer:document.getElementById("srvEle")?(e.preventDefault(),toggleModal("propDetailModal","close")):navigator.app.backHistory()}),!1)},confirmSettings:function(){var e,t,n,r,o=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return r=o.tip||"To find nearby properties and schools you need to enable location.",n=o.later||"Later",t=o.go||"Go to settings",e=function(e){if(e+""=="2")return RMSrv.openSettings()},RMSrv.dialogConfirm(r,e,"",[n,t])},getGeoPosition:function(e,t){return t||(t=e,e=null),RMSrv.permissions("location","check",(function(n){return"authorized"===n||"granted"===n?RMSrv.action("geoPosition",t):"denied"===n?RMSrv.isIOS()?RMSrv.confirmSettings(e):RMSrv.action("geoPosition",t):RMSrv.permissions("location","request",(function(e){return RMSrv.action("geoPosition",t)}))}))},scanQR:function(e){return RMSrv.action("qrcode",(function(t){return"function"==typeof e?e(t):"string"==typeof e?window.location=(e||"/1.5/iframe?u=")+encodeURIComponent(t):RMSrv.cError("Unknown scanQR parameter type")}))},isIOS:function(){return/iPhone|iPad|iPod/i.test(navigator.userAgent)},isAndroid:function(){return/Android/i.test(navigator.userAgent)},isWeChat:function(){return/MicroMessenger/i.test(navigator.userAgent)},isBlackBerry:function(){return/BlackBerry/i.test(navigator.userAgent)},androidVersion:function(){var e;return!!(e=navigator.userAgent.toLowerCase().match(/android\s([0-9\.]*)/))&&e[1]},appendDomain:function(e){var t;return/^\//.test(e)?(t=window.location.href.split("/"))[0]+"//"+t[2]+e:(/^(http|https)/.test(e)||(e="https://"+e),e)},showInBrowser:function(e,t){return/^(www\.)?realmaster/.test(e)&&(e="https://"+e),/^(http|https)/.test(e)||t||(e=this.appendDomain(e)),RMSrv.action({tp:"openInBrowser",url:e})},setAppLang:function(e){return RMSrv.action({tp:"setAppLang",lang:e})},isCommandAvailable:function(e,t){return RMSrv.action({tp:"isCommandAvailable",cmd:e},t)},getRoutesCounts:function(e){return RMSrv.action({tp:"getRoutesCounts"},e)},setApplicationIconBadgeNumber:function(e){return RMSrv.action({tp:"setApplicationIconBadgeNumber",number:e})},getApplicationIconBadgeNumber:function(e){return RMSrv.action({tp:"getApplicationIconBadgeNumber"},e)},showInMap:function(e){var t;return t={tp:"map",lat:e.lat,lng:e.lng,title:e.title},null!=e.marker&&(t.marker=e.marker),e.zoom&&(t.zoom=e.zoom),e.which&&(t.which=e.which),t.mapTypeId=e.mapTypeId||"standard",RMSrv.action(t)},closeAndRedirect:function(e){return RMSrv.action({tp:"closeAndRedirect",url:e})},closeAndRedirectRoot:function(e){return/^http/.test(e)||(e=this.appendDomain(e)),RMSrv.action({tp:"closeAndRedirectRoot",url:e})},fetch:function(e,t,n){return RMSrv.action({tp:"fetch",opt:t,url:e},n)},coreVer:function(e){var t;return RMSrv.coreVersionStr?e(RMSrv.coreVersionStr):(t=setTimeout((function(){var t;if(e)return t=e,e=null,t(null)}),500),RMSrv.action({tp:"coreVer"},(function(n){var r;if(clearTimeout(t),RMSrv.coreVersionStr=n,e)return r=e,e=null,r(n)})))},downloadImage:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0;return RMSrv.action({tp:"downloadImage",opt:t,url:e},n)},appInstalledChecker:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1?arguments[1]:void 0;return RMSrv.action({tp:"AppInstalledChecker",name:e},t)},calendar:function(e){var t,n,r=arguments.length>2?arguments[2]:void 0;if("authorizationStatus"===e||"authorizeEventStore"===e||"findCalendars"===e||"saveCalendar"===e||"findEventById"===e||"fetchAllEvents"===e||"saveEvent"===e||"removeEvent"===e)return n=function(e){var t;if(r)return t=r,r=null,t(e)},t={tp:"calendar."+e,details:arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}},RMSrv.action(t,n);RMSrv.dialogAlert("Not Supported action: "+e)},permissions:function(){var e,t,n,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"location",o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"check",i=arguments.length>2?arguments[2]:void 0,a=arguments.length>3?arguments[3]:void 0;if(a||(a=i,i=1e3),"request"===o||"check"===o){if("location"===r||"notification"===r||"locationaccuracy"===r||"requestMultipleLocAndNotificationAndroid"===r)return t="permissions."+o+"."+r,"check"===o&&-1!==i&&(n=setTimeout((function(){return e("denied")}),i)),e=function(e){var t;if(a)return clearTimeout(n),t=a,a=null,t(e)},RMSrv.action({tp:t},e);RMSrv.dialogAlert("Not Supported permission type: "+r)}else RMSrv.dialogAlert("Not Supported action: "+o)},openSettings:function(){return RMSrv.action({tp:"permissions.openSettings"})},openSettingsNotification:function(){return RMSrv.action({tp:"permissions.openSettings.notification"})},openSettingsLocation:function(){return RMSrv.action({tp:"permissions.openSettings.location"})},getItemObj:function(e,t,n){return n||(n=t,t=!1),RMSrv.action({tp:"storage.getItemObj",key:e,readCache:t},n)},setItemObj:function(e,t){var n=e.key,r=e.value,o=e.stringify,i=e.store;return RMSrv.action({tp:"storage.setItemObj",key:n,value:r,stringify:o,store:i},t)},setSystemValue:function(e,t){return RMSrv.action({tp:"setSystemValue",key:e,value:t})},removeItemObj:function(e,t){return RMSrv.action({tp:"storage.removeItemObj",key:e},t)},refreshSystemValue:function(e,t){return RMSrv.action({tp:"refreshSystemValue",opt:e},t)},openTBrowser:function(e,t,n){return/^(www\.)?realmaster/.test(e)&&(e="https://"+e),/^(http|https)/.test(e)||(e=this.appendDomain(e)),RMSrv.action({tp:"popup",url:e,cfg:t}),null},openInAppBrowser:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"location=false";return RMSrv.action({tp:"popup",url:e,cfg:t})},dialogAlert:function(e,t){var n;return n={tp:"alert",msg:"string"==typeof e?e:e.message||e.toString()},t&&(n.title=t),RMSrv.action(n)},dialogConfirm:function(e,t,n,r){return r||n&&Array.isArray(n)&&(r=n,n="Confirm"),RMSrv.action({tp:"confirm",title:n,msg:e,btns:r},t)},isDBG:!1,fDoc:function(){var e,t,n;if(e=null,n=document.getElementById("iframe"))try{null==(e=n.contentDocument)&&(e=document),e.document&&(e=e.document)}catch(e){t=e,console.log(t)}return e},getMeta:function(e){var t,n,r,o,i;for(o=e.querySelectorAll("meta"),i={title:e.title},t=0,n=o.length;t<n;t++)i[(r=o[t]).getAttribute("name")]=r.getAttribute("content");return i},getShareImage:function(e){var t,n,r,o,i;if(t=e.getElementById("page-container")||e.body)for(r=0,o=(i=t.getElementsByTagName("img")).length;r<o;r++)if(null!=(n=i[r])?n.src:void 0)return n.src;return this.appendDomain("/img/logo.png")},logoImg:"data:image/png;base64,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",hasWechat:function(e){var t;return window.localStorage.hasWechat&&(t="true"===window.localStorage.hasWechat,e(t),e=null),RMSrv.action("wechat.has",(function(t){if(window.localStorage.hasWechat=t,e)return e(t)}))},exitApp:function(e){return RMSrv.action("exitApp",e)},hasGoogleService:function(e){return RMSrv.action("hasGoogleService",e)},hasPlayServices:function(e){return RMSrv.action("hasPlayServices",e)},getDeviceId:function(e){return RMSrv.action("getDeviceId",e)},getUniqueId:function(e){return RMSrv.action("getUniqueId",e)},getSystemVersion:function(e){return RMSrv.action("getSystemVersion",e)},copyToClipboard:function(e,t){return RMSrv.action({tp:"clipboard.set",p:e.toString()},t)},makePhoneCall:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return e=function(e){return e="1"!==(e=e.replace(/[^0-9]*/g,"")).charAt(0)?"+1"+e:"+"+e}(e),RMSrv.action({tp:"linking",p:{url:"tel:"+e,dest:"tel"}})},wechatAuth:function(e){return RMSrv.action("wechat.auth",(function(t){var n;return((null!=t?t.err:void 0)||(null!=t?t.cancelled:void 0)||-2===(null!=t?t.code:void 0))&&e?e((null!=t?t.err:void 0)||"cancelled"):(null!=t&&null!=(n=t.code)?n.length:void 0)>10?window.location.href="/scheme?code="+t.code:(console.error(t),e?e("Unknown error"):void 0)}))},facebookAuth:function(e,t){return t||(t=e,e={}),RMSrv.action({tp:"facebook.auth",permissions:e.permissions},(function(e){var n;if(((null!=e?e.err:void 0)||(null!=e?e.cancelled:void 0))&&t&&t((null!=e?e.err:void 0)||"cancelled"),(null!=e&&null!=(n=e.accessToken)?n.length:void 0)>10)return window.location.href="/scheme/facebook?code="+e.accessToken}))},appleAuth:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;return t||(t=e,e={}),RMSrv.action({tp:"apple.auth"},(function(e){var n;if(((null!=e?e.err:void 0)||(null!=e?e.cancelled:void 0))&&t&&t((null!=e?e.err:void 0)||"cancelled"),e.token)return(n={}).id_token=e.token,n.nonce=e.nonce,n.fullName=e.fullName,window.location.href="/scheme/apple?token="+encodeURIComponent(JSON.stringify(n))}))},googleAuth:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;return t||(t=e,e={}),RMSrv.action({tp:"google.auth"},(function(e){var n;if(((null!=e?e.err:void 0)||(null!=e?e.cancelled:void 0))&&t&&t((null!=e?e.err:void 0)||"cancelled"),(null!=e&&null!=(n=e.idToken)?n.length:void 0)>10)return window.location.href="/scheme/google?idToken="+e.idToken}))},wechatShareError:function(e){return"ERR_WECHAT_NOT_INSTALLED"===(null!=e?e.toString():void 0)?RMSrv.dialogAlert("WeChat Not Installed"):"object"===_typeof(e)&&-2===e.code?RMSrv.dialogAlertCancel():RMSrv.dialogAlert(JSON.stringify(e))},dialogAlertCancel:function(){var e,t;return e="Cancelled","zh"===(t=RMSrv._shareLang)||"zh-cn"===t?e="已取消":"kr"===RMSrv._shareLang&&(e="취소되었습니다"),RMSrv.dialogAlert(e)},getThumbName:function(e){var t,n;return e?(t=e.split(".")).length<4?e:(t[n=t.length-2]=t[n]+"_",t.join(".")):e},wechatShare:function(e,t,n,r){var o,i,a,l;return l={title:t.title||"RealMaster Sharing",description:t.description||"RealMaster App Sharing",thumb:t.image,url:t.url,tp:n||0,type:r||"news"},/d\dw?\.realmaster/.test(t.url)||(i=window.gShareHostNameCn||"realmaster.cn",l.url=t.url.replace("realmaster.com",i)),RMSrv.isAndroid()&&/\.gif/.test(l.thumb)&&(l.thumb=RMSrv.logoImg),o=function(){return RMSrv.action({tp:"wechat.share",p:l},(function(e){return e.err?RMSrv.wechatShareError(e.err):e.ok?RMSrv.shared(t):void 0}))},RMSrv.isAndroid()&&/(realexpert|realmaster).*\.png/.test(l.thumb)?((a=new Image).src=RMSrv.getThumbName(l.thumb),a.onload=function(){return l.thumb=a.src,o()},void(a.onerror=function(){return o()})):(/^undefined/.test(l.thumb)&&(l.thumb="/img/noPic.png"),RMSrv.shared(t,!0),o())},facebookShare:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return RMSrv.shared(t,!0),RMSrv.action({tp:"facebook.share",content:{contentType:n.type||"link",contentUrl:t.url,imageURL:t.image,contentDescription:(t.title||"")+(t.description||"RealMaster App Sharing")}},(function(e){return e.err?RMSrv.dialogAlert(err.toString()):e.cancelled?RMSrv.dialogAlert("Cancelled"):RMSrv.shared(t)}))},linkingShare:function(e,t){var n,r,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(n={title:t.title||t.description||"Shared with RealMaster App",message:t.title||t.description||"RealMaster App Sharing",url:t.url,dest:o.dest||"sms"},t.title&&(n.message+=" \n"+t.description),t.url&&(n.message+=" URL: "+t.url),r=t[n.dest]||null,RMSrv.shared(t,!0),n.title=encodeURI(n.title),n.message=encodeURI(n.message),"sms"===n.dest)n.url="sms:".concat(r,"?&subject=").concat(n.title,"&body=").concat(n.message);else{if("mailto"!==n.dest)return alert("unknown dest for linking share");n.url="mailto:".concat(r,"?subject=").concat(n.title,"&body=").concat(n.message)}return RMSrv.action({tp:"linking",p:n},(function(e){return e.cancelled?RMSrv.dialogAlert("Cancelled"):e.ok?RMSrv.shared(t):void 0}))},singleShare:function(e,t,n){var r;return r={title:t.title||t.description||"Shared with RealMaster App",message:t.title||t.description||"RealMaster App Sharing",url:t.url,social:"WHATSAPP"},t.title&&(r.message+=" \n"+t.description),t.url&&(r.message+=" URL: "+t.url),RMSrv.shared(t,!0),RMSrv.action({tp:"singleShare",p:r},(function(e){return e.cancelled?RMSrv.dialogAlert("Cancelled"):e.ok?RMSrv.shared(t):void 0}))},socialShare:function(e,t){var n;return n={title:t.title||t.description||"Shared with RealMaster App",message:t.title||t.description||"RealMaster App Sharing",url:t.url},t.title&&(n.message+=" \n"+t.description),t.url&&(n.message+=" URL: "+t.url),RMSrv.isIOS()&&delete n.url,RMSrv.shared(t,!0),RMSrv.action({tp:"share",p:n},(function(e){return e.cancelled?RMSrv.dialogAlert("Cancelled"):e.ok?RMSrv.shared(t):e.err?RMSrv.dialogAlert(e.err):void 0}))},qrcodeShare:function(e,t,n){var r,o,i;if(null==n&&(n="id_share_qrcode"),"show"===e){if(r=document.getElementById(n))return r.style.display="block",o=function(){var e;return(e=document.getElementById(n+"_holder")).innerHTML="",new QRCode(e,t)},"undefined"!=typeof QRCode&&null!==QRCode?o():((i=document.createElement("script")).type="text/javascript",i.src="/js/qrcode/qrcode.min.js",document.getElementsByTagName("head")[0].appendChild(i),i.onload=o)}else if(r=document.getElementById(n))return r.style.display="none"},showSMB:function(e){var t,n,r,o,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"share-";return RMSrv._shareMask||(RMSrv._shareMask=document.getElementById("backdrop"),null!=(n=RMSrv._shareMask)&&n.addEventListener("click",(function(){return RMSrv.showSMB("hide")}))),"show"===e?(RMSrv._sharePrefix=i,(t=document.getElementById(i+"placeholder"))&&t.appendChild(document.getElementById("shareDialog")),document.body.classList.add("smb-open"),null!=(r=RMSrv._shareMask)&&(r.style.display="block"),RMSrv.shareLang()):"hide"===e?(document.body.classList.remove("smb-open"),null!=(o=RMSrv._shareMask)?o.style.display="none":void 0):void 0},_shareMap:{title:"title",desc:"description",url:"url",image:"image",data:"data",sms:"sms",mailto:"mailto",dnurl:"dnurl"},_shareLang:null,shareLang:function(e){var t,n,r,o,i,a,l,c,u,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window.document;return o=document.getElementById("id_share_lang_en"),a=document.getElementById("id_share_lang_zh"),i=document.getElementById("id_share_lang_kr"),r=document.getElementById("id_share_lang_cur"),null!=o&&o.classList.remove("active"),null!=r&&r.classList.remove("active"),null!=a&&a.classList.remove("active"),null!=i&&i.classList.remove("active"),n=null!=r&&null!=(l=r.dataset)?l.lang:void 0,e&&"cur"!==e?"en"===e?null!=o&&o.classList.add("active"):"zh-cn"===e||"zh"===e?null!=a&&a.classList.add("active"):"kr"===e&&null!=i&&i.classList.add("active"):(e=n,null!=r&&r.classList.add("active")),RMSrv._shareMap="en"===e?{"title-en":"title","desc-en":"description",url:"url",image:"image",data:"data",dnurl:"dnurl",sms:"sms",mailto:"mailto"}:{title:"title",desc:"description",url:"url",image:"image",data:"data",dnurl:"dnurl",sms:"sms",mailto:"mailto"},t=RMSrv._getShareInfo(s,RMSrv.getMeta(s)),null!=(c=document.getElementById("id_share_title"))&&(c.value=t.title),null!=(u=document.getElementById("id_share_desc"))&&(u.value=t.description),RMSrv._shareLang=null!=e&&"cur"!==e?e:null},_getShareInfo:function(e,t){var n,r,o;for(r in n=function(n,r){var o,i;try{if("image"===n&&(i=e.getElementById(RMSrv._sharePrefix+n+"-uri"))&&i.textContent||(i=e.getElementById(RMSrv._sharePrefix+n)||e.getElementById("share-"+n)||e.getElementById("alt-"+n)),i)return t[r||n]=i.value||i.textContent}catch(e){return o=e,"undefined"!=typeof console&&null!==console?console.log(o):void 0}},o=RMSrv._shareMap)n(r,o[r]);return null==t.image&&(t.image=RMSrv.getShareImage(e)),null==t.url&&(t.url=e.URL||window.location.href),t},share:function(e,t,n){var r;switch("null"!==t&&null!=t||(t=window.document),r=function(e,n){var r,o,i,a,l,c,u,s,d,p,h,v=e.to,f=e.channel;i=function(e){return i=null,n(e)},r=RMSrv._getShareInfo(t,RMSrv.getMeta(t)),(p=null!=(c=t.getElementById("id_share_title"))?c.value:void 0)&&(r.title=p),(o=null!=(u=t.getElementById("id_share_desc"))?u.value:void 0)&&(r.description=o),RMSrv._shareLang&&((l=r.url.match(/\?.*(lang\=[a-zA-Z\-]+)/))?r.url=r.url.replace(l[0],l[0].replace(l[1],"lang=".concat(RMSrv._shareLang))):/\?[a-z0-9]+\=/i.test(r.url)?r.url+="&lang="+RMSrv._shareLang:r.url+="?lang="+RMSrv._shareLang,(l=null!=(s=r.data)?s.match(/.*(lang\=[a-zA-Z\-]+)/):void 0)&&(r.data=r.data.replace(l[0],l[0].replace(l[1],"lang=".concat(RMSrv._shareLang)))));try{if(!r.data&&!(r.data=null!=(d=document.querySelector("#share-data"))?d.innerHTML:void 0))return i(r);v&&(r.data+="&to=".concat(v)),f&&(r.data+="&channel=".concat(f)),(h=new XMLHttpRequest).open("POST","/1.5/api/rm/shareInfo",!0),h.setRequestHeader("Content-type","application/x-www-form-urlencoded"),h.timeout=8e3,h.ontimeout=function(){return RMSrv.dialogAlert("Timeout! Try again Later")},h.onreadystatechange=function(){var e;if(4===h.readyState){if(200===h.status)if(e=JSON.parse(h.responseText),"undefined"!=typeof console&&null!==console&&console.log(e.url),e.ok)r.url2=r.url,r.url=e.url;else if(e.err)return RMSrv.dialogAlert(e.err);if(i&&"share"!==f)return i(r)}},h.send(r.data)}catch(e){a=e,"undefined"!=typeof console&&null!==console&&console.log(a),i&&i(r)}},e){case"show":case"hide":return RMSrv.setShareImg(),RMSrv.showSMB(e);case"lang-en":return RMSrv.shareLang("en",t);case"lang-cur":return RMSrv.shareLang("cur",t);case"lang-zh-cn":return RMSrv.shareLang("zh-cn",t);case"lang-kr":return RMSrv.shareLang("kr",t);case"qr-code":return RMSrv.showSMB("hide"),r({channel:e},(function(e){return RMSrv.qrcodeShare("show",e.url)}));case"qr-code-close":return RMSrv.qrcodeShare("hide");case"wechat-friend":return RMSrv.showSMB("hide"),r({channel:e},(function(e){return RMSrv.wechatShare(t,e,0)}));case"wechat-moment":return RMSrv.showSMB("hide"),r({channel:e},(function(e){return RMSrv.wechatShare(t,e,1)}));case"wechat-cust":return RMSrv.showSMB("hide"),r({channel:e},(function(e){return RMSrv.wechatShare(t,e,n.tp,n.type)}));case"facebook-feed":return RMSrv.showSMB("hide"),r({channel:e},(function(e){return RMSrv.facebookShare(t,e,n)}));case"linking-share":return RMSrv.showSMB("hide"),r({channel:e},(function(e){return RMSrv.linkingShare(t,e,n)}));case"single-share":return RMSrv.showSMB("hide"),r({channel:e},(function(e){return RMSrv.singleShare(t,e,n)}));default:return RMSrv.showSMB("hide"),r({channel:e},(function(e){return RMSrv.socialShare(t,e)}))}},origin:function(){return window.location.origin||window.location.protocol+"//"+window.location.hostname+(window.location.port?":"+window.location.port:"")},shared:function(e,t){var n,r,o,i,a;return n=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return e.indexOf("ec=")>1&&/propDetailPage|(1\.5\/prop\/detail)/.test(e)?e.split("ec=")[1].split("&")[0]:/projDetailPage|(\/projects\/detail)/.test(e)?"proj:"+e.split("id=")[1].split("&")[0]:/prop\/detail\/inapp/.test(e)?e.split("id=")[1].split("&")[0]:e.split("?")[0].split("/").pop()},i=RMSrv.origin()+"/1.5/user/update?act=share",null!=e.dnurl&&(i=e.dnurl,/^\//.test(e.dnurl)&&(i=RMSrv.origin()+e.dnurl)),e.url&&(r=n(e.url),o=i.indexOf("?")>0?"&":"?",i+=o+"data="+r,t&&(i+="&pre=1")),(a=new XMLHttpRequest).onreadystatechange=function(){var e;if(4===a.readyState&&200===a.status)try{if((null!=(e=JSON.parse(a.responseText))?e.j:void 0)&&(window.location.href=e.j),null!=e?e.r:void 0)return window.rmCall(e.r)}catch(e){e}},a.open("POST",i,!0),a.send()},sendSMS:function(e){return"string"==typeof e&&(e={body:e,recipients:[e]}),RMSrv.action({tp:"sendSMS",p:e})},clearCache:function(){},keyboard:{close:function(){return RMSrv.action("keyboard.dismiss")},disableScroll:function(e){return RMSrv.action({tp:"disableScroll",p:e})},isVisible:!1},getKeyboard:function(e){return setTimeout((function(){return e(RMSrv.keyboard)}),0)},handleKeyboardshow:function(e){var t;if(RMSrv.keyboard.isVisible=e.isShow,window.CustomEvent)return!1===e.isShow?t=new CustomEvent("native.keyboardhide",{detail:{keyboardHeight:e.keyboardHeight}}):e.isShow&&(t=new CustomEvent("native.keyboardshow",{detail:{keyboardHeight:e.keyboardHeight}})),window.dispatchEvent(t)},appStateChangeCb:function(e){var t,n,r,o,i,a;if(null!=(o=RMSrv._whenAppStateChange)?o.length:void 0){for(a=[],t=0,r=(i=RMSrv._whenAppStateChange).length;t<r;t++)"function"==typeof(n=i[t])?a.push(n(e)):a.push(void 0);return a}},checkAlive:function(){return window.rmCall(":ctx:alive")},pushToken:function(e){var t,n,r,o,i,a,l,c;if((null!=e?e.token:void 0)&&(a=e.tp?e.tp+":":"",i="ios"===e.os?e.os+":"+e.token:e.os+":"+a+e.token,localStorage.pn=i,(c=new XMLHttpRequest).onreadystatechange=function(){if(4===c.readyState&&200===c.status)return"undefined"!=typeof console&&null!==console?console.log(c.responseText):void 0},c.open("POST","/1.5/user/updPn"),c.setRequestHeader("Content-type","application/x-www-form-urlencoded"),c.send("pn="+i),RMSrv._RegFinished=!0,n=RMSrv._fnWhenReg)){for(l=[],r=0,o=n.length;r<o;r++){t=n[r];try{l.push(t(i))}catch(e){e}}return l}},pushNotice:function(e){var t,n,r,o,i,a,l,c;return RMSrv.action("vibrate"),(c=e.url||(null!=(o=e.data)?o.url:void 0))?/^:rmpntest/.test(c)?window.pnFunction({ok:1}):(/^http/.test(c)&&(c=(r=document.createElement("a")).pathname+r.search),e.foreground?(l=e.title,"object"===_typeof(t=e.message||(null!=(i=e.data)?i.message:void 0))&&(l=t.title,t=t.body),confirmJump(l,t,c)):jumpURL(c)):(n=(null!=(a=e.data)?a.message:void 0)||e.message)?RMSrv.dialogAlert(n):void 0},whenReg:function(e){return RMSrv._RegFinished?e():(null!=RMSrv._fnWhenReg?RMSrv._fnWhenReg:RMSrv._fnWhenReg=[]).push(e)},getMemoryDetail:function(e){return RMSrv.action({tp:"getMemoryDetail"},e)},getTranslate:function(){var e=_asyncToGenerator(_regeneratorRuntime().mark((function e(t){var n,r,o;return _regeneratorRuntime().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,fetch("/translate",{method:"POST",headers:{"Content-Type":"application/json;charset=UTF-8"},body:JSON.stringify({m:encodeURIComponent(t)})});case 3:if(!(r=e.sent).ok){e.next=16;break}return e.next=7,r.json();case 7:if(!(o=e.sent).ok){e.next=12;break}return e.abrupt("return",o);case 12:return o.err?RMSrv.dialogAlert(o.err):RMSrv.dialogAlert("Please contact admin!"),e.abrupt("return",null);case 14:e.next=18;break;case 16:return RMSrv.dialogAlert("Translation request failed!"),e.abrupt("return",null);case 18:e.next=25;break;case 20:return e.prev=20,e.t0=e.catch(0),n=e.t0,RMSrv.dialogAlert("An error occurred: "+n.message),e.abrupt("return",null);case 25:case"end":return e.stop()}}),e,null,[[0,20]])})));return function(t){return e.apply(this,arguments)}}(),htmlToElement:function(e){var t;return t=document.createElement("div"),e=e.trim(),t.innerHTML=e,console.log(t),t},getPageContentIframe:function(e,t,n,r){var o,i,a,l,c,u,s,d;return/^(http|https)/.test(e)||(e=this.appendDomain(e)),null==r&&(r=n,n={wait:0,hide:!0}),n.colorName="commonBarColor",i=Object.assign(n,{sel:t,tp:"pageContent",url:e}),d=window.innerWidth,c=window.innerHeight-44,u=document.getElementById("the_iframe"),s=function(e){return"string"!=typeof e&&(e=JSON.stringify(e)),u.contentWindow.postMessage(e,"*")},u?(u.src=e,toggleModal("simPopUpModal","open"),void s(t)):(l="",o="",i.transparent&&(c=window.innerHeight,o="background-color: transparent;",i.hide=!0),i.hide||(l='<header class="bar bar-nav">\n  <a class="icon icon-close pull-right" href="javascript:;", onclick=\'toggleModal("simPopUpModal","close")\'> </a>\n  <h1 class="title">'.concat(i.title||"RealMaster","</h1>\n</header>")),a=this.htmlToElement('<div id="simPopUpModal" class="modal active" style="z-index:9999; '.concat(o,'">\n  ').concat(l,'\n  <div class="content" style="').concat(o,'">\n    <iframe src="').concat(e,'" id="the_iframe" style="width:').concat(d,"px; height:").concat(c,'px;border:0;">\n  </div>\n</div>')),document.body.appendChild(a),u=document.getElementById("the_iframe"),this,r&&(RMSrv.cb=r),n.wait?setTimeout((function(){return s(t)}),n.wait):void 0)},getPageContent:function(e,t,n,r){return/^(http|https)/.test(e)||(e=this.appendDomain(e)),null==r&&(r=n,n={wait:0,hide:!0}),n.colorName="commonBarColor",RMSrv.onReady((function(){var o;return o=Object.assign(n,{sel:t,tp:"pageContent",url:e}),RMSrv.action(o,(function(e){return r(e)}))}))}},Window.RMSrv||(window.RMSrv=RMSrv2,RMSrv.init()),jumpURL=function(e){var t;return t=e,e=RMSrv.origin()+"/scheme/jump?u="+encodeURIComponent(e),RMSrv.coreVer((function(n){return null!=n&&n>"5.6"?/\/prop\/detail\/inapp/.test(t)?RMSrv.openInAppBrowser(e):RMSrv.closeAndRedirectRoot(e):window.location=e}))},confirmJump=function(e,t,n){var r;return r=function(e){if(2===e)return jumpURL(n)},RMSrv.dialogConfirm(t,r,e||"Message",["Cancel","Open"])},window._errorSent={},window.onerror=function(e,t,n){var r,o,i,a;if(o=e+"\n"+t+"\n"+n,/(d[0-9]\.realmaster)|(app\.test)/.test(window.location.href)&&alert(o),i=o.replace(/\W/g,"").toUpperCase(),!window._errorSent[i]){window._errorSent[i]=1;try{return(a=new XMLHttpRequest).onreadystatechange=function(){if(4===a.readyState&&200===a.status)return"undefined"!=typeof console&&null!==console?console.log(a.responseText):void 0},a.open("POST","/cError"),a.setRequestHeader("Content-type","application/x-www-form-urlencoded"),a.send("m="+encodeURIComponent(o))}catch(e){return r=e,"undefined"!=typeof console&&null!==console?console.log(r):void 0}}},window.regedRMError=!0;
