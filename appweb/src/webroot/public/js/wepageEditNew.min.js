"use strict";function _typeof(t){return _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_typeof(t)}var dataURItoBlob,getCanvasImage,getS3Config,imageToDataUri,initSummernote,init_map,processFiles,readFile,splitName,uploadFile,indexOf=[].indexOf;initSummernote=function(t){var e,a;return e=$(".summernote"),a=[["del",["delToStart","delToEnd"]],["color",["color"]],["para",["ul","ol","paragraph"]],["fontsize",["fontsize"]],["upLoadImg",["select"]],["insert",["hr","link"]],["style",["style","bold","italic","underline","clear"]],["misc",["undo","redo","codeview"]]],t&&a.splice(4,1),e.summernote({toolbar:a,onFocus:function(){var t;if(window.keyboard&&window.isIOS)return t=parseInt(window.keyboardHeight||0),$("#editorModal .note-editable.panel-body").css("padding-bottom",t+"px")},onBlur:function(){if(window.keyboard&&window.isIOS&&$("#editorModal .note-editable.panel-body").css("padding-bottom","0px"),!window.onEditImgSelect)return e.summernote("saveRange")}})},processFiles=function(t){var e,a;return e=void 0,a=void 0,t&&"undefined"!=typeof FileReader?(a=0,(e=function(){var n;if(n=void 0,a<t.length)return n=t[a++],readFile(n,(function(){return e()}))})()):RMSrv.dialogAlert("Unsuppored browser. Can't process files.")},getS3Config=function(t,e,a){var n;return(n={}).ext="jpg",n.w=t.width,n.h=t.height,n.s=t.size,n.t=a?1:0,$("#loading-bar-spinner").css("display","block"),$.ajax({url:amGloble.getS3Config,data:n,type:"post"}).done((function(t){t.key?($("#loading-bar-spinner").css("display","none"),window.s3config=t,e&&e()):flashMessage("server-error")})).fail((function(){flashMessage("server-error")}))},uploadFile=function(t,e){var a,n,i,r,o,s;return n=void 0,i=void 0,r=void 0,o=void 0,e?(n=t.blob2,i=window.s3config.thumbKey,r=window.s3config.thumbPolicy,o=window.s3config.thumbSignature):(n=t.blob,i=window.s3config.key,r=window.s3config.policy,o=window.s3config.signature),(a=new FormData).append("acl","public-read"),a.append("key",i),a.append("Content-Type",window.s3config.contentType),a.append("policy",r),a.append("x-amz-credential",window.s3config.credential),a.append("x-amz-date",window.s3config.date),a.append("x-amz-signature",o),a.append("x-amz-algorithm","AWS4-HMAC-SHA256"),a.append("file",n,i),s=0,$("#loading-bar-spinner").css("display","block"),$.ajax({url:amGloble.savePicS3,data:a,type:"post",processData:!1,contentType:!1,crossDomain:!0,headers:{Origin:"anonymous","Access-Control-Request-Origin":"anonymous"}}).done((function(){return s=1})).always((function(t){var a;if(s||204===(null!=t?t.status:void 0)){if($("#loading-bar-spinner").css("display","none"),window.isThumbImg)return a="http://"+window.s3config.s3bucket+"/"+window.s3config.key,window.cardMeta.img=a,setTimeout((function(){$("#thumbImg").attr("src",a)}),500),void(window.isThumbImg=!1);window.isFlyer(amGloble.type,window.shSty)?(e||(window.ctClone.bg="http://"+window.s3config.s3bucket+"/"+window.s3config.key),window.setPreviewContents(window.ctClone)):e||$(".summernote").summernote("insertImage","http://"+window.s3config.s3bucket+"/"+window.s3config.key,window.s3config.key)}else flashMessage("server-error"),$.ajax({url:amGloble.uploadFail,data:{},type:"post"})}))},readFile=function(t,e){var a;return a=void 0,/image/i.test(t.type)?((a=new FileReader).onload=function(a){return void 0,$("<img/>").load((function(){var a,n;return void 0,n=getCanvasImage(this,t),flashMessage("img-inserted"),toggleModal("imgSelectModal"),a=n.width>400||n.height>300,getS3Config(n,(function(){if(uploadFile(n,!1),a)return uploadFile(n,!0)}),a),e()})).attr("src",a.target.result)},a.readAsDataURL(t)):(RMSrv.dialogAlert(t.name+" unsupported format : "+t.type),e())},imageToDataUri=function(t,e,a){var n,i;return i=(n=document.createElement("canvas")).getContext("2d"),n.width=e,n.height=a,i.drawImage(t,0,0,e,a),n.toDataURL()},splitName=function(t,e){var a;return void 0,(a=t.lastIndexOf("."))>0?[t.substr(0,a),t.substr(a+1).toLowerCase()]:[t,"."+e.substr(e.lastIndexOf("/")).toLowerCase()]},dataURItoBlob=function(t){var e,a,n,i,r,o;for(void 0,a=void 0,void 0,i=void 0,r=void 0,void 0,a=t.split(",")[0].indexOf("base64")>=0?atob(t.split(",")[1]):unescape(t.split(",")[1]),o=t.split(",")[0].split(":")[1].split(";")[0],e=new ArrayBuffer(a.length),r=new Uint8Array(e),i=0;i<a.length;)r[i]=a.charCodeAt(i),i++;return n=new DataView(e),new Blob([n],{type:o})},getCanvasImage=function(t,e){var a,n,i,r,o,s,l,d,c,m,p,u,g;return 1e3,1e3,680,680,m=128,10,a=void 0,n=void 0,void 0,void 0,void 0,r=void 0,o=void 0,s=void 0,l=void 0,d=void 0,void 0,void 0,u=void 0,g=void 0,d=1,(t.width>1e3||t.height>1e3)&&(u=1e3/t.width,r=1e3/t.height,d=Math.min(u,r)),t.width>=t.height&&t.height>680&&(r=680/t.height)<d&&(d=r),t.width<=t.height&&t.width>680&&(u=680/t.width)<d&&(d=u),(a=document.createElement("canvas")).width=t.width*d,a.height=t.height*d,a.getContext("2d").drawImage(t,0,0,t.width,t.height,0,0,a.width,a.height),c=splitName(e.name,e.type),(o={name:e.name,nm:c[0],ext:c[1],origType:e.type,origSize:e.size,width:a.width,height:a.height,ratio:d}).type="image/jpeg",o.url=a.toDataURL(o.type,.8),o.blob=dataURItoBlob(o.url),o.size=o.blob.size,o.canvas=a,(n=document.createElement("canvas")).width=p=Math.min(128,t.width),n.height=i=Math.min(m,t.height),t.width*i>t.height*p?(g=(t.width-t.height/i*p)/2,l=t.width-2*g,s=t.height):(g=0,l=t.width,s=t.width),n.getContext("2d").drawImage(t,g,0,l,s,0,0,p,i),o.url2=n.toDataURL(o.type,.7),o.blob2=dataURItoBlob(o.url2),o.size2=o.blob2.size,o.canvas2=n,o},init_map=function(){var t,e,a,n,i;if(43.7182412,-79.378058,void 0,a=void 0,n=void 0,void 0,e=void 0,t=$("#meta-addr").val()||"Mississauga, ON, Canada",i={zoom:12,center:new google.maps.LatLng(43.7182412,-79.378058),mapTypeControl:!0,mapTypeControlOptions:{style:google.maps.MapTypeControlStyle.DROPDOWN_MENU},navigationControl:!0,mapTypeId:google.maps.MapTypeId.ROADMAP},a=new google.maps.Map(document.getElementById("id_d_map"),i),window.map=a,e=new google.maps.Geocoder)return e.geocode({address:t},(function(i,r){var o;return o=function(t){return e.geocode({latLng:t},(function(t){return t&&t.length>0?$("#housecard-page-edit-body").find("[data-role=meta-addr]").val(t[0].formatted_address):console.log("Cannot determine address at this location.")}))},r===google.maps.GeocoderStatus.OK?r!==google.maps.GeocoderStatus.ZERO_RESULTS?(a.setCenter(i[0].geometry.location),new google.maps.InfoWindow({content:"<b>"+t+"</b>",size:new google.maps.Size(150,50)}),(n=new google.maps.Marker({position:i[0].geometry.location,map:a,draggable:!0,animation:google.maps.Animation.DROP,title:t,optimized:!1})).addListener("click",(function(){return null!==n.getAnimation()?n.setAnimation(null):n.setAnimation(google.maps.Animation.BOUNCE)})),google.maps.event.addListener(n,"dragend",(function(){return o(n.getPosition())}))):RMSrv.dialogAlert("No results found"):RMSrv.dialogAlert("Geocode was not successful for the following reason: "+r)}))},$((function(){return window._id=null,{id:"housecard-page-edit-body",init:function(){return this._doms={},this._datas={withDl:!0,withSign:!0},this.initDoms(),this.bindEvents(),this.getData(),window.isFlyer=this.isFlyer},initDoms:function(){var t;return t=this.$=$("#"+this.id),$.extend(this._doms,{metaTitle:t.find("[data-role=meta-title]"),metaEditor:t.find("[data-role=meta-editor]"),metaTemplate:t.find("[data-role=meta-template]"),metaDesc:t.find("[data-role=meta-desc]"),metaVc:t.find("[data-role=meta-vc]"),metaAddr:t.find("[data-role=meta-addr]"),cpName:t.find("[data-role=company-name]"),nkPhoto:t.find("[data-role=nick-photo]"),nki:t.find("[data-role=nick-img]"),nknm:t.find("[data-role=nick-nm]"),intr:t.find("[data-role=intr]"),mapUl:t.find("[data-role=map-item-ul]"),ctct:t.find("[data-role=ctct]"),ctctWx:t.find("[data-role=ctct-wx]"),ctctWxQr:t.find("[data-role=ctct-wxqr]"),ctctWxQrW:t.find("[data-role=ctct-wxqr-wrapper]"),ctctGrpQrcd:t.find("[data-role=ctct-grpqrcd]"),ctctGrpQrcdW:t.find("[data-role=ctct-grpqrcd-wrapper]"),ctctTel:t.find("[data-role=ctct-tel]"),ctctTel2:t.find("[data-role=ctct-tel2]"),ctctEml:t.find("[data-role=ctct-eml]"),ctctWeb:t.find("[data-role=ctct-web]"),ctctCpny:t.find("[data-role=ctct-cpny]"),ctctCpny_pstn:t.find("[data-role=ctct-cpny_pstn]"),cpnydtl:t.find("[data-role=cpnydtl]"),cpnydtlFax:t.find("[data-role=cpnydtl-fax]"),cpnydtlTel:t.find("[data-role=cpnydtl-tel]"),cpnydtlTel2:t.find("[data-role=cpnydtl-tel2]"),cpnydtlAds:t.find("[data-role=cpnydtl-ads]"),cpnydtlWeb:t.find("[data-role=cpnydtl-web]"),medLink:t.find("[data-role=media-link]"),propDetailPane:t.find("[data-role=prop-detail-pane]"),propImgPane:t.find("[data-role=prop-img-pane]"),propPrice:t.find("[data-role=prop-lp_price]"),propType:t.find("[data-role=prop-type_own1_out]"),propBr:t.find("[data-role=prop-br]"),propKit:t.find("[data-role=prop-num_kit]"),propPak:t.find("[data-role=prop-gar_spaces]"),propBsmt:t.find("[data-role=prop-bsmt1_out]"),propBath:t.find("[data-role=prop-bath_tot]"),propLot:t.find("[data-role=prop-front_ft]"),propExt:t.find("[data-role=prop-constr1_out]"),propTax:t.find("[data-role=prop-taxes]"),propSqft:t.find("[data-role=prop-sqft]"),propAC:t.find("[data-role=prop-a_c]"),propCVC:t.find("[data-role=prop-central_vac]"),propAge:t.find("[data-role=prop-yr_built]"),propPool:t.find("[data-role=prop-pool]"),propFuel:t.find("[data-role=prop-fuel]"),propRltr:t.find("[data-role=prop-rltr]"),propRemark:t.find("[data-role=prop-ad_text]"),topic:t.find("[data-role=topic]"),topicContent:t.find("[data-role=topic-content]")})},setShareUrl:function(){var t,e,a;if(a=this,t=amGloble.host+"/1.5/wecard/prop/"+amGloble.id+"/"+window._id,e="tp=wecard&uid="+amGloble.id+"&id="+window._id,a._datas.withDl?(t+="?wDl=1",e+="&dl=1",a._datas.withSign&&(t+="&sgn=1")):a._datas.withSign&&(t+="?sgn=1"),a._datas.withSign&&(e+="&sgn=1"),amGloble.lang&&(e+="&lang="+amGloble.lang),window._id&&$("#share-url").text(t),window._id)return $("#share-data").text(e)},bindEvents:function(){var t;return t=this,/android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(navigator.userAgent.toLowerCase())&&(/iPhone|iPad|iPod/i.test(navigator.userAgent.toLowerCase())&&(window.isIOS=!0),RMSrv.getKeyboard((function(t){return window.keyboard=t,window.keyboard.disableScroll(!1)}))),this.$.on("click",".footer-icon-music",(function(){return $(".bgs-mp3").toggle(),"none"===$(".bgs-map").css("display")&&$("#backdrop").toggle(),$(".bgs-map").hide(),t.getMusicLinks(),!1})).on("click","#li-music-close",(function(){var e;return(e=document.getElementById(t.adid))&&(e.pause(),t.adid=null),$(".bgs-map").hide(),$(".bgs-mp3").toggle(),$("#backdrop").toggle(),!1})).on("click","a.btn-sort",(function(e){var a,n,i,r,o,s,l,d,c;return s=e.currentTarget,a=(n=$(s).parents("li")).clone(),i=n.prev(),o=n.index(),l=i.index(),i&&i.length>0&&i.is("li")&&o>0&&((c=t._datas.pageData.card.seq)&&c.length>0&&(r=c[o],d=c[l],c[l]=r,c[o]=d,t._datas.pageData.card.seq=c),i.before(a),n.remove()),!1})).on("click","#thumbImg",(function(){var e;return e=t._datas.pageData,console.log(t),window.isThumbImg=!0,window.cardMeta=e.card.meta,toggleModal("imgSelectModal")})).on("click","#listPageBtn",(function(){if(window.keyboard)return window.keyboard.disableScroll(!1)})).on("click","#showMap",(function(){var t,e,a,n;return window.mapLoaded?(a=window.map,t=$("#meta-addr").val()||"Mississauga, ON, Canada",(e=new google.maps.Geocoder)&&e.geocode({address:t},(function(e,n){var i,r;return n===google.maps.GeocoderStatus.OK?n!==google.maps.GeocoderStatus.ZERO_RESULTS?(a.setCenter(e[0].geometry.location),i=new google.maps.InfoWindow({content:"<b>"+t+"</b>",size:new google.maps.Size(150,50)}),r=new google.maps.Marker({position:e[0].geometry.location,map:a,title:t,optimized:!1}),google.maps.event.addListener(r,"click",(function(){return i.open(a,r)}))):RMSrv.dialogAlert("No results found"):RMSrv.dialogAlert("Geocode was not successful for the following reason: "+n)}))):($("#id_map_outer").css("display","block"),window.mapLoaded=!0,(n=document.createElement("script")).type="text/javascript",n.src=window.gurl+"&callback=init_map",document.body.appendChild(n)),!1})).on("click",".thumb-wrapper img",(function(){var e,a;return $(this).toggleClass("selected"),void 0,e=$(this).prop("alt"),(a=t._datas.userData.selected.indexOf(e))>=0?t._datas.userData.selected.splice(a,1):t._datas.userData.selected.push(e)})).on("click","#toggleImgSelect",(function(){return window.keyboard&&window.keyboard.isVisible&&(window.keyboard.close(),window.onEditImgSelect=!1),window.isThumbImg&&(window.isThumbImg=!1),toggleModal("imgSelectModal")})).on("click","#listUserPics",(function(){return $("#imgSelectPicList").html(""),$.ajax({url:amGloble.getUserFiles,data:{},type:"get"}).done((function(e){var a,n,i;if(1===e.ok)for(n in t._datas.userData=e,null!=(i=t._datas.userData)&&(i.selected=[]),e.pl)a="<span class='thumb-wrapper'><img src='"+(e.base+"/"+(null!=e.pl[n].tA?e.pl[n].tA:e.pl[n].nm))+"' alt='"+n+"'> </span>",$("#imgSelectPicList").append(a);else console.log("Error has happened while getting file list!")})).fail((function(){flashMessage("server-error")})),!0})).on("click","#saveFrame",(function(e){var a,n,i,r,o,s;return a=$(".summernote").summernote("code"),t.isFlyer(t._datas.pageData.card.tp,null!=(o=t._datas.pageData.card.meta)?o.shSty:void 0)&&t._datas.ctClone?(t._datas.ctClone.m=a,t.setPreviewContents(t._datas.ctClone)):(n=e.currentTarget,$(n).parents("li"),s=t._datas.pageData.card.seq,$(s[t.editIndex].m),r=t.editIndex+1,s[t.editIndex].m=a,i=$("#edit-page-contents-ul > li:nth-of-type("+r+")"),$(a).length>1&&(a=$("<div>").html(a)[0].outerHTML),a+=t._doms.ctrlButtons,i.html(a)),t._doms.metaDesc.val()||t._doms.metaDesc.val($(a).text().replace(/\s|\n|\r|\v/g,"").substr(0,50)),window.keyboard&&(window.keyboard.disableScroll(!1),window.keyboard.isVisible&&window.keyboard.close()),$(".summernote").summernote("destroy"),initSummernote(),!1})).on("click","#savePicFrame",(function(){var e,a,n,i,r,o;for(n in r=t._datas.pageData.card.seq,$(r[t.editIndex]),e=t.getContentFromCt(t._datas.ctClone),a=t.editIndex+1,i=t._datas.ctClone)o=i[n],r[t.editIndex][n]=o;return $("#edit-page-contents-ul > li:nth-of-type("+a+") > div:first-child").replaceWith(e),!1})).on("click touchend","#gal-del-btn",(function(t){var e,a;return a=(e=$(t.currentTarget)).parent("div"),e.hide(),$("#gal-del-yes-btn").show(),$("#gal-del-can-btn").show(),a.addClass("active"),!1})).on("click touchend","#gal-del-can-btn",(function(t){var e;return e=$(t.currentTarget).parent("div"),$("#gal-del-btn").show(),$("#gal-del-can-btn").hide(),$("#gal-del-yes-btn").hide(),e.removeClass("active"),!1})).on("click touchend","#gal-del-yes-btn",(function(e){var a,n,i,r,o,s;if(a=$(e.currentTarget).parent("div"),$("#gal-del-btn").show(),$("#gal-del-can-btn").hide(),$("#gal-del-yes-btn").hide(),a.removeClass("active"),(n={}).fldr=t._datas.userData.fldr,n.files=t._datas.userData.selected,!(n.files.length>0))return!1;for(r=0,o=(s=n.files).length;r<o;r++)if(i=s[r],/^[A-Q]{1}$/.test(i.split(".")[0]))return RMSrv.dialogAlert(vars.ERR_PRO_IMG||"Cannot remove profile images!!"),!1;return $.ajax({url:amGloble.deleteFiles,type:"post",contentType:"application/json",dataType:"json",data:JSON.stringify(n)}).done((function(t){1===t.ok?$("#listUserPics")[0].click():RMSrv.dialogAlert(t.err)})).fail((function(){RMSrv.dialogAlert("sever error!, please try again later")})),!1})).on("click","#saveCard",(function(e){var a;return a=t._datas.pageData,$("[data-role=meta-title]").val()?(a.card?(t.savePropCard(a.card),flashMessage("page-saved")):RMSrv.dialogAlert("Error: no card yet!"),e.preventDefault(),e.stopPropagation(),!1):(flashMessage("no-title"),!1)})).on("click","#id_with_dl",(function(){return t._datas.withDl=$(this).children("input")[0].checked,t.setShareUrl()})).on("click","#id_with_sign",(function(){return t._datas.withSign=$(this).children("input")[0].checked,t.setShareUrl()})).on("click","a.btn-delete",(function(e){var a,n,i,r;return n=e.currentTarget,a=$(n).parents("li").index(),r=t._datas.pageData.card.seq,i=$("#edit-page-contents-ul > li:nth-of-type("+(a+1)+")"),r.splice(a,1),i.remove(),!1})).on("click","a.btn-see",(function(e){var a,n,i,r,o;return r=e.currentTarget,i=(n=$(r).parents("li")).index(),o=t._datas.pageData.card.seq,(a=$(o[i].m)).toggleClass("dis"),o[i].m=a[0].outerHTML,n.toggleClass("dis"),!1})).on("click","#shareToNews",(function(){var e,a,n,i,r,o;for(r={},i=(e=t._datas.card).meta,r.wid=e._id,r.tl=e.meta.title,r.desc=e.meta.desc,r.thumb=e.meta.img,r.url=amGloble.host+"/1.5/wecard/prop/",r.logo="true",r.chnl="WePage",r.src="WePage",r.tp=e.meta.tp,r.area="Toronto",r.lang="zh-cn",r.m="",a='<div style="padding-top:20px">',a+="<h2>"+i.title+"</h2>",a+='<br><span style="margin-right:10px;">'+(i.ts?i.ts.split("T")[0]:(new Date).toDateString())+"</span>",a+='<span style="margin-right:10px;"><a onclick="'+("RMSrv.showInBrowser('"+amGloble.host+"/1.5/wesite/"+e.id+"')")+'">'+i.editor+"</a></span>",a+='<span style="margin-right:10px; color:#607fa6">'+i.custvc+"</span>",o=amGloble.emurl+encodeURIComponent(i.addr),i.addr&&(a+='<span style="color: #007aff;" class="fa fa-location-arrow" onclick="RMSrv.showInBrowser(\''+o+"')\" > </span>"),a+="</div>",r.m+=a,n=0;n<=e.seq.length-1;)r.m+=e.seq[n].m,n++;return $.ajax({url:amGloble.publishToNews,data:r,type:"post"}).done((function(t){1===t.ok?(RMSrv.share("hide"),RMSrv.dialogAlert(t.msg)):RMSrv.dialogAlert("Error has happened while publishing!")})).fail((function(){flashMessage("server-error")})),!1})).on("click","#edit-page-contents-ul > li.edit-in-summernote, #edit-page-contents-ul > li.edit-in-summernote > a.edit-in-summernote",(function(e){var a,n,i,r,o,s,l;return o=e.currentTarget,i="li"===$(o).prop("nodeName").toLowerCase()?$(o):$(o).parents("li"),t.editIndex=i.index(),n=$((null!=(s=t._datas.pageData.card.seq[t.editIndex])?s.m:void 0)||""),t._datas.ctClone=void 0,t.isFlyer(t._datas.pageData.card.tp,null!=(l=t._datas.pageData.card.meta)?l.shSty:void 0)&&"user"!==t._datas.pageData.card.seq[t.editIndex]._for?(r=t._datas.pageData.card.seq[t.editIndex],t._datas.ctClone=t.shallowCopy(r),t.setPreviewContents(t._datas.ctClone),toggleModal("frameEditorModal","open"),e.preventDefault(),e.stopPropagation()):(a=n.clone(),$(".summernote").summernote("code",$("<div>").append(a).html()),$(".summernote").summernote("destroy"),initSummernote(),toggleModal("editorModal")),window.keyboard&&window.isIOS&&window.keyboard.disableScroll(!0),!1})).on("click","#insertImage",(function(){var e,a,n,i,r,o,s;if(e=function(e,a){var n;return window.isThumbImg?(window.cardMeta.img=e,$("#thumbImg").attr("src",e),void(window.isThumbImg=!1)):t._datas.pageData.card&&t.isFlyer(t._datas.pageData.card.tp,null!=(n=t._datas.pageData.card.meta)?n.shSty:void 0)?(t._datas.ctClone.bg=e,t.setPreviewContents(t._datas.ctClone)):$(".summernote").summernote("insertImage",e,a)},a=function(){var e;return null!=(e=t._datas.userData)&&(e.selected=[]),$("#imgSelectPicList img").each((function(){return $(this).removeClass("selected")}))},$(".summernote").summernote("restoreRange"),o=$("#imgInputURL").val())e(o,o),$("#imgInputURL").val(""),toggleModal("imgSelectModal");else if(null!=t._datas.userData&&t._datas.userData.selected.length>0){for(s=(n=t._datas.userData).selected,i=0;i<=s.length-1;)e(o=n.base+"/"+n.pl[s[i]].nm,s[i]),i++;a(),toggleModal("imgSelectModal")}else if(r=$("#imgInputFiles").get(0)){if(void 0,"files"in r){if(0===r.files.length)return console.log("Select one or more files."),!1;window.ctClone=t._datas.ctClone,window.setPreviewContents=t.setPreviewContents,window.getContentFromCt=t.getContentFromCt,processFiles(r.files),a(),$("#previewImg").attr("src",""),$("#imgInputFiles").val("")}}else console.log("no files");return window.onEditImgSelect=!1})).on("click","#wepageShareBtn",(function(){return t.setShareUrl()})).on("click","#wepagePreviewBtn",(function(){var t;return t=amGloble.host+"/1.5/wecard/prop/"+amGloble.id+"/"+window._id,window._id?RMSrv.showInBrowser(t):RMSrv.dialogAlert("Not Saved Yet!"),!1})).on("click","#newFrame",(function(e){var a,n,i,r,o;return a=e.currentTarget,$(a).parents("li"),o=t._datas.pageData.card.seq,i=t.isFlyer(t._datas.pageData.card.tp,null!=(r=t._datas.pageData.card.meta)?r.shSty:void 0)?'<div style="font-size: 19px;">Contents</div>':"<div><p class='text-left'>Contents</p></div>",(n={})._for="newFrame",n.m=i,o.push(n),i+=t._doms.ctrlButtons,$('<li class="edit-in-summernote">'+i+"</li>").insertBefore("li.item-add"),!1})).on("click","#pvReplaceImg",(function(){return toggleModal("imgSelectModal"),t.setPreviewContents(t._datas.ctClone),!1})).on("click","#pvRemoveBgImg",(function(){return t._datas.ctClone.bg="",t.setPreviewContents(t._datas.ctClone),!1})).on("click","#pvEditPreviewText",(function(){return $(".summernote").summernote("code",t._datas.ctClone.m),$(".summernote").summernote("destroy"),initSummernote(!0),toggleModal("editorModal"),t.setPreviewContents(t._datas.ctClone),!1})).on("click","#pvShiftTextPosition",(function(){return function(t){var e,a,n;return e=t.pos,n="top:10%;",a="top:45%;","bottom:10%;",t.pos=e===n?a:e===a?"bottom:10%;":n}(t._datas.ctClone),t.setPreviewContents(t._datas.ctClone),!1})).on("click","#pvShiftImgPosition",(function(){return function(t){var e,a;return e=t.bgPos,"top",a="center","bottom",t.bgPos="top"===e?a:e===a?"bottom":"top"}(t._datas.ctClone),t.setPreviewContents(t._datas.ctClone),!1})).on("click","#pvTextBg",(function(){return function(t){var e,a,n;return e=t.tbg,a="background-color: rgba(0, 0, 0, 0.45);",n="background-color: rgba(0, 0, 0, 0.8);","",t.tbg=e===a?n:e===n?"":a}(t._datas.ctClone),t.setPreviewContents(t._datas.ctClone),!1})).on("click","#pvPreviewAnimation",(function(){return function(t){var e,a,n;return a=t.ani,(n=(e=["zoomIn","fadeIn","fadeInUp","flash","slideInUp","slideInDown","slideInLeft","slideInRight"]).indexOf(a))>=0?t.ani=e[(n+1)%e.length]:t.ani=e[0]}(t._datas.ctClone),t.setPreviewContents(t._datas.ctClone),!1})),$("#devWidthSlider").change((function(){return t.setPreviewScale($(this).val())})),$("#bgs-mp3-ul").on("click","li a",(function(){var e,a,n;return e=$(this).hasClass("anm-rotate"),a=$(this).attr("adid"),t.adid&&t.adid!==a&&($("#bgs-mp3-ul").find("[adid="+t.adid+"]").removeClass("anm-rotate"),document.getElementById(t.adid).pause(),t.adid=null),t.adid=a,n=document.getElementById(a),e?($(this).removeClass("anm-rotate"),n.pause(),t.adid=null):($(this).addClass("anm-rotate"),n.play()),!1})).on("click","li span",(function(){var e,a,n,i,r;return t.adid&&($("#bgs-mp3-ul").find("[adid="+t.adid+"]").removeClass("anm-rotate"),document.getElementById(t.adid).pause(),t.adid=null),r=(e=$(this).parents("li")).attr("urls"),i=e.attr("n"),n=e.find("a").attr("adid"),document.getElementById(n).pause(),a={url:r,nm:i},t._datas.pageData.card.music=a,$("#bgs-mp3-ul").parents(".bgs-mp3").hide(),$("#backdrop").hide(),!1}))},isFlyer:function(t,e){return"xmas1"===t||"xmas2"===t||"spring_fest"===t||"flyer"===t||("vt"===e||"vt"===amGloble.shSty)},shallowCopy:function(t){var e,a;if(null===t||"object"!==_typeof(t))return t;for(e in a=t.constructor(),t)t.hasOwnProperty(e)&&(a[e]=t[e]);return a},enableBtns:function(){return $("#wepagePreviewBtn").removeClass("disabled"),$("#wepageShareBtn").removeClass("disabled")},getUrlSrc:function(t){return/^https?:\/\/([^\/]+\.)*weixin\.qq\.com\//i.test(t)?"wechat":/^https?:\/\/([^\/]+\.)*youtube\.com\//i.test(t)?"youtube":"unknown"},getPageContent:function(t,e,a){var n;return n=t||this,RMSrv.getKeyboard((function(){var t,i,r,o,s,l,d,c,m,p,u,g;return c=cordova.ThemeableBrowser,r={image:"left",imagePressed:"left",align:"left",event:"custClosePressed"},i={image:"back",imagePressed:"back",align:"left",event:"backPressed"},(l=window.isIOS?[r,i]:[i,r]).push({image:"check",imagePressed:"check",align:"right",event:"pagesPressed"}),s={toolbar:{height:44,color:"#E03131"},customButtons:l,fullscreen:!1},p=!1,null,m=c.open(e,"_blank",s),d=function(e,n){if(!p)return p=!0,m.removeEventListener("custClosePressed",o),m.removeEventListener("backPressed",t),m.removeEventListener("loaderror",u),m.removeEventListener("pagesPressed",g),m.removeEventListener(cordova.ThemeableBrowser.EVT_ERR,u),m.close(),n?n({body:e}):a({body:e})},g=function(){return m.executeScript({code:"document.documentElement.innerHTML"},(function(t){var e,a;e=t[0];try{return e.match(/<body.*?>([\s\S]*)<\/body>/i)[1].replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,""),d(e)}catch(t){return a=t,console.log(a),d(e)}}))},u=function(t){return"Error: ".concat(t.code+" : "+t.message)},o=function(){return d("Cancelled",(function(){return window.location="/1.5/wecard"}))},t=function(){return"window.history.back(-1)",m.executeScript({code:"window.history.back(-1)"})},"wechat"===n.getUrlSrc(e)&&m.addEventListener("loadstop",g),m.addEventListener("custClosePressed",o),m.addEventListener("backPressed",t),m.addEventListener("pagesPressed",g),m.addEventListener("loaderror",u),m.addEventListener(cordova.ThemeableBrowser.EVT_ERR,u)}))},setPreviewContents:function(t){var e,a,n,i;if(i=this,e=$("#frame-preview"),n=(a=$("#frame-preview")[0]).contentDocument||a.contentWindow.document,i.getContentFromCt||(i.getContentFromCt=window.getContentFromCt),n.body.innerHTML=i.getContentFromCt(t),n.body.style.backgroundColor="black",n.body.style.margin=0,!(e.contents().find("#animateCss").length>0))return"2.6.0",e.contents().find("head").append($("<link/>",{rel:"stylesheet",href:"/css/animate.min.css?ver=2.6.0",type:"text/css",id:"animateCss"}))},getContentFromCt:function(t){return t.m||""===t.m?"user"===t._for?t.m:'<div style="" class="editPrev"> <div style="position: absolute;  z-index: 10;width: 100%;height: 100%;  min-height: 360px; background-size: 100% auto; background-position: center '+(t.bgPos||"center")+"; background-repeat: no-repeat; background-image: url('"+(t.bg||"")+'\'); background-color: black; "> <div class="animated long '+(t.ani||"fadeInUp")+' " style="'+(t.tbg||"")+"width: 100%;  color: white; padding: 10px; box-sizing: border-box; position: absolute; "+(t.pos||"top:10%;")+'">'+t.m+"</div> </div> </div>":"Error: no text"},setPreviewScale:function(t){var e,a,n,i,r,o,s,l,d,c;return l=function(t,e,a,n){var i;return a="scale("+a+")",(i=$("#frame-preview")).css("height",e+"px"),i.css("width",t+"px"),i.css("margin-left",n+"px"),i.css("transform",a),i.css("-webkit-transform",a)},d=function(t){var e,a,n,i,r;for(i=[],e=a=0,n=(r=["fs-tip-sm","fs-tip-md","fs-tip-lg","fs-tip-pd","fs-tip-pc"]).length-1;0<=n?a<=n:a>=n;e=0<=n?++a:--a)e===t?i.push($("#"+r[e]).css("display","block")):i.push($("#"+r[e]).css("display","none"));return i},d(t=parseInt(t)),c=(e=[[320,568],[375,627],[414,736],[708,1024],[1366,768]])[t][0],i=e[t][1],a=$("#frame-preview-wrapper").height(),n=$("#frame-preview-wrapper").width(),s=parseInt(n)/c,o=parseInt(a)/i,r=Math.min(s,o),l(Math.max(c*r,c),Math.max(i*r,i),r,c*r<n?(n-c*r)/2:0)},filterAndGetTitle:function(t,e){var a,n,i,r,o,s,l,d,c,m,p,u,g,h;r=function(t,e){var a,n;return a="",(n=document.createElement("a")).href=t,a=t.split("/")[0]+"//",a+=n.hostname,"domain"!==e&&(a+=n.pathname),a},u=function(t){var e,a,n;try{return!(n=$(t).attr("src"))||(e=r(amGloble.domain,"domain"),n.indexOf("http")>-1||n.indexOf("https")>-1||"data:image"===n.substr(0,10)||("/"===n[0]?$(t).attr("src",e+n):$(t).attr("src",e+"/"+n)),!0)}catch(t){return a=t,"undefined"!=typeof console&&null!==console?console.log(a):void 0}},p={},n=$("<div>").html(e),p.title=n.find("title").text(),p.title&&(p.title=p.title.replace(/realtor\s+force/i,"")),p.desc=n.find("meta[name=description]").attr("content"),o=e;try{n.find("img").each((function(){var e,a;return void 0,void 0,$(this).attr("style")&&$(this).attr("style",""),$(this).attr("onerror")&&$(this).attr("onerror",""),u(this),e=$(this).attr("data-src"),a=$(this).attr("src"),/^(http|https):\/\/mmbiz.qpic.cn/i.test(t)&&/^data:image/.test(a)&&(a=null),e&&!a&&$(this).attr("src",e),$(this).attr("onload")&&$(this).attr("onload",""),$(this).css("max-width","100%"),$(this).css("height","auto")})),n.find("iframe").each((function(){return $(this).attr("onerror")&&$(this).attr("onerror",""),$(this).attr("onload")&&$(this).attr("onload",""),$(this).attr("style","max-width:100%"),$(this).attr("src")?u(this):($(this).remove(),!0)})),n.find("video").each((function(){return $(this).attr("onerror")&&$(this).attr("onerror",""),null==$(this).attr("controls")&&$(this).attr("controls",""),$(this).attr("onloadonloadstart")&&$(this).attr("onloadonloadstart",""),$(this).attr("style","max-width:100%"),u(this)})),n.find("a").each((function(){var t,e,a;if($(this).attr("style")&&$(this).css("position","relative"),a=$(this).attr("href")){if((e=/brjtools\.cn|schoolinfo\.ca|9lms\.com|realsforce\.ca|realtorforce\.ca/i).test(amGloble.domain)&&0!==a.indexOf("http")||e.test(a))return $(this).remove(),!0;if(t=r(amGloble.domain),0!==a.indexOf("http"))return $(this).attr("href",t+"/"+a)}})),o=n.html()}catch(t){i=t,"undefined"!=typeof console&&null!==console&&console.log(i),o+="Error : ".concat(i.message||i.toString())}switch(g=this.getUrlSrc(t),h="",/^(https|http):\/\/youtu\.be\/([a-zA-Z\d-]+)*/i.test(t)&&(h="youtubeApp"),g){case"wechat":for(o=(o=(o=(o=(o=(o=o.replace(/<head[^>]*>[\s\S]*?<\/head>/g,"")).replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/g,"")).replace(/<css[^>]*>[\s\S]*?<\/css>/g,"")).replace(/<style[^>]*>[\s\S]*?<\/style>/g,"")).replace(/<link\b.+href=.*>/g,"")).replace(/<meta\b.+name=.*>/g,""),n=$("<div>").html(o),l=0,d=(c=["#activity-name","js_cmt_mine",".rich_media_title","title",".rich_media_meta_text",".rich_media_meta_nickname","#js_view_source"]).length;l<d;l++)s=c[l],n.find(s).remove();o=n.html();break;case"youtube":o="<div>",o+='<iframe width="100%" height="315" src="https://www.youtube.com/embed/'+("youtubeApp"===h?t.split(".be/")[1]:t.split("watch?v=")[1]),o+='" frameborder="0" allowfullscreen></iframe>',o+="</div>";break;default:if(o=(o=(o=(o=o.replace(/<head[^>]*>[\s\S]*?<\/head>/g,"")).replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/g,"")).replace(/<css[^>]*>[\s\S]*?<\/css>/g,"")).replace(/<style[^>]*>[\s\S]*?<\/style>/g,""),(a=(n=$("<div>").html(o)).find("article"))&&a.length>0&&(null!=(m=a.html())?m.length:void 0)>800)try{n=$("<div>").html(a.wrap("<p>").parent().html())}catch(t){i=t,console.log(i.toString())}n.filter(".header, .menu, .nav, .box-fix-l, .box-fix-r").remove(),n.filter("link, select, input, button, meta, footer, nav, form").remove(),n.find("*").each((function(){var t,e,a,n,r,o,s;if(s=$(this).prop("tagName").toLowerCase(),n=$(this).parent()?$(this).parent().prop("tagName").toLowerCase():"","img"!==s&&"iframe"!==s&&"video"!==s&&"a"!==s){if(r=$(this).attr("role"),o=$(this).attr("class")&&$(this).attr("class").length>0?$(this).attr("class").split(" ")[0]:"",e=$(this).css("display"),t=$(this).text().length,"navigation"===r||"contentinfo"===r||"banner"===r||"search"===r||"none"===e||t<0||"li"===s&&"ul"!==n&&"li"===s&&"ol"!==n||"select"===s||"input"===s||"button"===s||"link"===s||"meta"===s||"footer"===s||"nav"===s||"form"===s||"base"===s||"header"===o||"menu"===o||"nav"===o||"box-fix-l"===o||"box-fix-r"===o)return $(this).remove(),!0;if("a"===s){try{$(this).replaceWith("<span>"+$(this).text()+"<span>")}catch(t){i=t,RMSrv.dialogAlert(i.toString()),console.log(i.toString())}return!0}if((a=$(this).attr("style"))&&$(this).css("position","relative"),a&&$(this).css("height","auto"),a=$(this).attr("style"),$(this).attr("onerror")&&$(this).attr("onerror",""),$(this).attr("onclick")&&$(this).attr("onclick",""),$(this).attr("onload")&&$(this).attr("onload",""),$(this).attr("class")&&$(this).attr("class",""),$(this).attr("id"))return $(this).attr("id","")}})),o="<div style='font-size: 14px;'>"+(o=n.html())+"</div>"}return p.body=o,p},savePropCard:function(t){var e,a,n;return n=this,this._datas.pageData.card.meta.title=this._doms.metaTitle.val(),this._datas.pageData.card.meta.editor=this._doms.metaEditor.val(),this._datas.pageData.card.meta.desc=this._doms.metaDesc.val(),this._datas.pageData.card.meta.custvc=this._doms.metaVc.val(),this._datas.pageData.card.meta.addr=this._doms.metaAddr.val(),this._datas.pageData.card.meta.tp=this._datas.pageData.card.tp,$("#share-title").html(this._doms.metaTitle.val()),$("#share-desc").html(this._doms.metaDesc.val()),a=$("#share-avt").html(),n.isOwnImg(t.meta.img)||RMSrv.ver>="3.1.0"?$("#share-image").html(t.meta.img+"#"):$("#share-image").html(a),(null!=(e=t.meta.img)?e.indexOf("/img/noPic.png"):void 0)>-1&&$("#share-image").html(a),$("#loading-bar-spinner").css("display","block"),$.ajax({url:amGloble.savePropCard,type:"post",contentType:"application/json; charset=utf-8",dataType:"json",data:JSON.stringify(t)}).done((function(t){$("#loading-bar-spinner").css("display","none"),t.success?(n._datas.pageData.card._id=t._id,t._id&&(window._id=t._id),n.enableBtns()):console.log("Save Error")})).fail((function(){flashMessage("server-error")})),null},getData:function(){var t,e,a;if(a=this,(t=function(t,e){var n;return"edit"===e?(n=function(t){var e;return(e=a._datas.pageData=t).success?(window._id=e._id,a.initData()):RMSrv.dialogAlert(e.err)},{fn:$.ajax,cfg:{url:amGloble.getPropCardInfo,type:"get",data:{}},done:n}):"topic"===t?{update:function(){return $("#loading-bar-spinner").css("display","block")},fn:a.getPageContent,data:amGloble.domain,cb:function(t){var e;return $("#loading-bar-spinner").css("display","none"),t?(e=a._datas.pageData=a.filterAndGetTitle(amGloble.domain,t.body),amGloble.newsId?$.ajax({url:amGloble.getNewsTitle,data:{},type:"post"}).done((function(t){t?(e.title=t.tl,e.desc=t.desc,a.initData()):flashMessage("server-error")})).fail((function(){flashMessage("server-error")})):a.initData()):(flashMessage("url-error"),setTimeout((function(){return window.location="/1.5/wecard"}),1e3))}}:"listing"===t?(n=function(t){var e;return t.success?(e=a._datas.pageData=t).card&&JSON.stringify(e.card).length<=2?$.ajax({url:amGloble.getPropDetail,data:{},type:"post"}).done((function(t){t?(a._datas.pageData.prop=t.detail,a.initData()):flashMessage("server-error")})).fail((function(){flashMessage("server-error")})):a.initData():RMSrv.dialogAlert("cannot get listing!")},{update:function(){},fn:$.ajax,cfg:{url:amGloble.getPropCardInfo,type:"get",data:{}},done:n}):indexOf.call(amGloble.templateTypes,t)>=0?{update:function(){},fn:$.ajax,cfg:{url:amGloble.getTemplate,type:"get",data:{type:t}},done:function(t){return t.success?(a._datas.pageData=t.content,a.initData()):flashMessage("server-error")}}:(alert("error! unknown type of data"),RMSrv.dialogAlert("Error: unknown type of data"))}(amGloble.type.toLowerCase(),null!=(e=amGloble.action)?e.toLowerCase():void 0)).update&&t.update(),t.cfg||(t.cfg=t.data),t.done&&t.fn(t.cfg).done(t.done).fail((function(){flashMessage("server-error")})),t.cb)return t.fn(a,t.cfg,t.cb)},getMusicLinks:function(){var t;return t=this,$.ajax({url:amGloble.getMusicList,data:{},type:"get"}).done((function(e){var a,n,i,r;if(e){for(a=[],i=0,r=(n=t._datas.musicList=e).l.length;i<r;)a.push("<li urls="+n.l[i].url+" n="+n.l[i].nm+'><a href="#" adid="musicPlayer'+i+'" class="mp3 icon"><audio id="musicPlayer'+i+'" loop="" src="'+n.l[i].url+'"  style="display:none;position:absolute;z-index:-11"></audio></a><span>'+n.l[i].nm+"</span>"),i++;$("#bgs-mp3-ul").empty(),$("#bgs-mp3-ul").html(a.join(""))}else flashMessage("server-error")})).fail((function(){flashMessage("server-error")}))},isOwnImg:function(t){var e;return!!t&&(e=function(t){return 0===t.indexOf("http")},!e(t)||!!/^(https|http):\/\/([a-zA-Z\d-]+\.)*realmaster\.(com|cn)(\/\w+)+.(jpg|png)/i.test(t))},setMetaImg:function(t,e){var a,n,i,r,o,s;if(s=this,n=function(t){var e,a;return 0===t.indexOf("http")||0===t.indexOf("data:image")||t.length>1500?t:(e="",(a=document.createElement("a")).href=document.URL,e=document.URL.split("/")[0]+"//",(e+=a.hostname)+t)},o=function(t,e){var a,i,r,o;o=function(t){var e,a,n,r;for(n=[],e=0,a=t.length;e<a;e++)r=t[e],/^(http|https):\/\/mmbiz.qpic.cn/i.test(i.src)&&(r.src=r.src.replace("/0?","/320?"),n.push(r));return n};try{a=$("<div>").html($((null!=t?t.trim():void 0)||""))}catch(e){e,t="<div>"+((null!=t?t.trim():void 0)||"")+"</div>",a=$(t)}if(r=$("img",a),i=0,0===r.length)return e.img="/img/noPic.png",e;for(;i<=r.length-1;){if(r[i].height>110&&s.isOwnImg(r[i].src))return e.img=n(r[i].src),e;i++}for(i=0,o(r);i<=r.length-1;){if(r[i].height>140)return e.img=n(r[i].src),e;i++}return e},Array.isArray(t)){for(a=0;a<=t.length-1;){if(s._datas.pageData.card&&s.isFlyer(s._datas.pageData.card.tp,null!=(i=s._datas.pageData.card.meta)?i.shSty:void 0))return e.img=n(t[0].bg),e;if(null!=(r=o(t[a].m,e))?r.img:void 0)return r;if(a===t.length-1)return e.img="/img/noPic.png",e;a++}return e}return o(t,e)},initData:function(){var t,e,a,n,i,r,o,s,l,d,c,m,p,u,g,h,f,v,w,b,y,_,k,C,S;if(w=this,k=(n=this._datas.pageData).user||{},a=n.card||{},h=n.prop||{},u=a.meta||{shSty:"blog"},_=amGloble.type,t=amGloble.action,null==a.tp&&(a.tp=_),a.id=amGloble.id,window._id&&(a._id=window._id),a.meta||(a.meta={shSty:"blog"}),window.shSty=u.shSty,c=function(t,e){var a;return t.nki.attr("src",e.avt||"/img/logo.png"),t.nknm.html((e.fn||"")+" "+(e.ln||"")),t.ctctWx.html(e.wx),e.qrcd&&t.ctctWxQr.attr("src",e.qrcd),e.grpqrcd?t.ctctGrpQrcd.attr("src",e.grpqrcd):t.ctctGrpQrcdW.css("display","none"),t.ctctTel.html(e.mbl),t.ctctEml.html("<a style='color:white' href='mailto:"+e.eml+"?Subject=Hi%20From%20RealMaster'>"+e.eml+"</a>"),a=e.web?e.web.indexOf("http")>-1?e.web:"http://"+e.web:"",t.ctctWeb.html("<a style='color:white' href='"+a+"'>"+(e.web||"")+"  </a>"),t.ctctCpny.html(e.cpny),t.ctctCpny_pstn.html(e.cpny_pstn)},"edit"!==t){if("listing"===_){if(a.ml_num=amGloble.ml_num,JSON.stringify(a).length<=200||!a.seq)for(amGloble.shSty&&(a.meta.shSty=amGloble.shSty),this._doms.propType.html(h.type_own1_out+" "+(h.style||"")+(h.prop_type||"")+(h.bus_type||"")),this._doms.propBr.html(h.br+" + "+h.br_plus),this._doms.propKit.html(h.num_kit+" + "+h.kit_plus),this._doms.propPak.html((h.gar_spaces||"")+" "+(h.gar_type||"")+" "+h.park_spcs),this._doms.propBsmt.html(h.bsmt1_out+" "+(h.bsmt2_out||"")),this._doms.propBath.html(h.bath_tot+" "+(h.bath_details||"")),this._doms.propLot.html((h.front_ft||"")+" x "+(h.depth||"")+" "+(h.lotsz_code||"")+" "+(h.irreg||"")),this._doms.propExt.html(h.constr1_out+" "+h.constr2_out),this._doms.propTax.html(h.taxes+" / "+h.yr),this._doms.propSqft.html(h.sqft),this._doms.propAC.html(h.a_c),this._doms.propCVC.html(h.central_vac),this._doms.propAge.html(h.yr_built),this._doms.propPool.html(h.pool),this._doms.propFuel.html(h.fuel+" "+h.heating),this._doms.propRltr.html(h.rltr),this._doms.propRemark.html(h.ad_text),this._doms.propPrice.html("$"+h.lp_price),c(this._doms,k),r=$('li[data-role="prop-detail-pane"]').html(),v=$('li[data-role="prop-remark-pane"]').html(),S=$('li[data-role="ctct"]').html(),a.uid=k.id,a.music={nm:"basical",url:"/musics/baical.MP3"},a.bkgimg={url:"/wecardBgs/bg2.jpg",nm:"bg2.jpg"},a.seq=[{_for:"detail",m:r},{_for:"remark",m:v}],"blog"!==amGloble.shSty&&a.seq.push({_for:"user",m:S}),i="<div class='des'><p>"+h.addr+" "+h.municipality+" "+h.county+"</p><p>"+h.type_own1_out+" "+h.style+"</p></div>",(l=getTrebPicUrls(h.pic_num,n.ml_num)).length||l.push(window.location.origin+"/img/noPic.png"),s=l.length-1;s>=0;)0===s&&(a.meta.img=l[s]),d="<img src='"+l[s]+"' alt='"+s+"' style='width:100%;'></img>",b="vt"===amGloble.shSty?{_for:"pic",pos:"top:10%;",ani:"fadeInUp",bg:l[s],m:i,bgPos:"center"}:{_for:"pic",m:d+i},a.seq.unshift(b),s--;return n.card=a,n.user=k,this._datas.pageData=n,this.writeHtml()}if("topic"===_)return a={},e=(n=this._datas.pageData).body||"Empty Content",u=a.meta={shSty:"blog"},a.uid=k.id,n.card=a,n.card.meta=w.setMetaImg(e,u),a.tp="topic",u.title=n.title||"",a.music={nm:"basical",url:"/musics/baical.MP3"},a.bkgimg={url:"/wecardBgs/bg2.jpg",nm:"bg2.jpg"},a.seq=[{_for:"topic",m:"<div id='topic-content'>"+e+"</div>"}],this._datas.pageData.card=a,this.writeHtml();if(indexOf.call(amGloble.templateTypes,_)>=0){if(a={},g={},y={shSty:"blog"},"xmas1"===_?g={nm:"xmas1",url:"/musics/We_wish_you_a_merry_Christmas_clip.MP3"}:"xmas2"===_?g={nm:"xmas2",url:"/musics/Jingle_Bells_clip.MP3"}:"spring_fest"===_&&(g={nm:"spring_fest",url:"/musics/spring_fest.MP3"}),(n=this._datas.pageData).card.music=g,e=n.card.seq||[],w.isFlyer(_)){for(y={shSty:"vt"},m=0,p=(f=n.card.seq).length;m<p;m++)"user"===(s=f[m])._for&&(o=!0);o||(c(this._doms,k),C={_for:"user",m:S=$('li[data-role="ctct"]')[0].innerHTML},n.card.seq.push(C))}return u=a.meta=y,n.card.meta=w.setMetaImg(e,u),this.writeHtml()}return console.log("undefined type init data "+_),alert("Error unknown type write data"),RMSrv.dialogAlert("Error unknown type write data")}this.enableBtns(),this.writeHtml()},setMeta:function(t){if(this,this._doms.metaTitle.val((null!=t?t.title:void 0)||this._doms.metaTitle.val()),this._doms.metaEditor.val((null!=t?t.editor:void 0)||this._doms.metaEditor.val()),this._doms.metaDesc.val((null!=t?t.desc:void 0)||this._doms.metaDesc.val()),this._doms.metaVc.val((null!=t?t.custvc:void 0)||this._doms.metaVc.val()),this._doms.metaAddr.val((null!=t?t.addr:void 0)||this._doms.metaAddr.val()),$("#share-title").html(this._doms.metaTitle.val()),$("#share-desc").html(this._doms.metaDesc.val()),this.isOwnImg(t.img)||RMSrv.ver>="3.1.0")return $("#share-image").html(t.img+"#")},writeHtml:function(){var t,e,a,n,i,r,o,s,l,d,c,m,p,u,g,h,f,v,w,b,y,_,k,C,S,x,M,D,I,P,T,L,R;if(this,l=this._datas.pageData,f="<li class='item-add' dataRole='new-frame' style='height: 73px; text-align: center; font-size:27px; padding-top: 20px;'><div><a id='newFrame' class='fa fa-plus-circle' href='#', style='color: #666' /></div></li>",'<a href="#" class="btn-r btn-delete  fa fa-trash" /></a>',"<a href='#' class='btn-r btn-edit fa fa-edit edit-in-summernote' /></a>",'<a href="#" class="btn-r btn-sort  fa fa-chevron-circle-up" /></a>','<a href="#" types= _for class="btn-r btn-see fa fa-eye" /></a>',s='<a href=\'#\' class=\'btn-r btn-edit fa fa-edit edit-in-summernote\' /></a><a href="#" class="btn-r btn-delete  fa fa-trash" /></a><a href="#" class="btn-r btn-sort  fa fa-chevron-circle-up" /></a><a href="#" types= _for class="btn-r btn-see fa fa-eye" /></a>',this._doms.ctrlButtons=s,!l||l.e)return flashMessage("server-error");if(L=amGloble.type.toLowerCase(),n=l.card,R=l.user,"listing"===L&&"create"===amGloble.action){for("",m=[],(I=n.seq)||(I=[]),d=(v=l.prop).lp_price+" For "+v.s_r+" "+v.addr+" "+v.municipality+" "+v.county+" "+v.type_own1_out+" "+v.style,this._doms.metaDesc.html(d),T=v.addr+" "+v.municipality+" ",this._doms.metaTitle.val(T),a=T+", "+(v.municipality_district||"")+", "+(v.county||""),this._doms.metaAddr.val(a),p=0,u=I.length;p<u;)i=I[p],e=(e="vt"===amGloble.shSty?$(this.getContentFromCt(i)):$("<div>"+i.m+"</div>")).wrap("<li class='item-img edit-in-summernote' dataRole='prop-img-pane'><div></div></li>").parent().parent(),"vt"!==amGloble.shSty&&(i.m=null!=(w=e.children("div"))&&null!=(y=w[0])?y.outerHTML:void 0),e.append(s),m.push(e),p++;m.push(f)}else if("topic"===L){if(l.body,m=[],!(I=n.seq))return void(I=[]);for(this.setMeta(n.meta),p=0,u=I.length;p<u;)c='<li class="edit-in-summernote">'+((i=I[p]).m||"")+"</li>",(e=$(c)).append(s),m.push(e),p++;this._doms.topic.append(s),m.push(f),$("#edit-page-contents-ul").html(m),$("#topic-content").css("overflow-x","hidden"),"edit"!==(null!=(_=amGloble.action)?_.toLowerCase():void 0)&&null!=(k=$("#edit-page-contents-ul li:first-child"))&&null!=(C=k[0])&&C.click()}else if(indexOf.call(amGloble.templateTypes,L)>=0||"edit"===(null!=(S=amGloble.action)?S.toLowerCase():void 0)){for(I=n.seq||[],P=null!=(h=n.meta)?h.shSty:void 0,m=[],p=0,u=I.length;p<u;){i=I[p];try{r=$((null!=i&&null!=(x=i.m)?x.trim():void 0)||"<section></section>")}catch(t){t,r=$("<section>"+((null!=i&&null!=(M=i.m)?M.trim():void 0)||"")+"</section>")}"assignment"!==L&&"exlisting"!==L&&"event"!==L||(t=$(i.m)).find("[data-role=tpl-nm]").length&&(t.find("[data-role=tpl-nm]").html((R.fn||"")+" "+(R.ln||"")),t.find("[data-role=tpl-tel]").html(R.mbl),t.find("[data-role=tpl-tel-call]").attr("href","tel:"+R.mbl),i.m=t[0].outerHTML),g=r.hasClass("dis")?'<li class="dis edit-in-summernote"><div>':'<li class="edit-in-summernote"><div>',this.isFlyer(L,P)&&"user"!==i._for?(o=g+(o=this.getContentFromCt(i))+"</div></li>",e=$(o)):(i.m=g+i.m+"</div></li>",e=$(i.m),i.m=null!=(D=e.children("div"))&&null!=(b=D[0])?b.innerHTML:void 0),e.append(s),m.push(e),p++}m.push(f),this.setMeta(h)}else alert("unknown type write html type: "+amGloble.type.toLowerCase()),m=[];return $("#edit-page-contents-ul").html(m),$("#edit-page-contents-ul").css("display","block"),n.meta.img?$("#thumbImg").attr("src",n.meta.img):void 0}}.init()})),$(document).ready((function(){var t;return window.selectTpMap={listing:"Listing",event:"Event",exlisting:"Exclusive Listing",assignment:"Assignment",blog:"Blog",xmas1:"Flyer",xmas2:"Flyer",spring_fest:"Flyer"},t=function(t){return window.keyboardHeight=t.keyboardHeight},function(t){var e;if(t.files&&t.files[0])return(e=new FileReader).onload=function(t){return $("#previewImg").attr("src",t.target.result)},e.readAsDataURL(t.files[0])},window.addEventListener("native.keyboardshow",t),initSummernote()})),function(){var t,e;t=function(t){var e,a;for(e=void 0,a=document.querySelectorAll(".segmented-control .control-item");t&&t!==document;){for(e=a.length;e--;)if(a[e]===t)return t;t=t.parentNode}},e=function(e){var a,n,i,r,o,s,l;if(n=void 0,a=void 0,s=void 0,r="."+(i="active"),(l=t(e.target))&&((n=l.parentNode.querySelector(r))&&n.classList.remove(i),l.classList.add(i),l.hash&&(s=document.querySelector(l.hash)))){for(a=s.parentNode.querySelectorAll(r),o=0;o<a.length;)a[o].classList.remove(i),o++;return s.classList.add(i)}},window.addEventListener("touchend",e),window.addEventListener("click",e)}();
