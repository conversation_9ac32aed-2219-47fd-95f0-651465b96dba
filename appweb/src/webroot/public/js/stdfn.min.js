let _requestStdFn=null;function getStandardFunctionCaller(){if(_requestStdFn)return _requestStdFn;var e=[],t=1,r=null;function n(){let t={},r=e;e=[],r.forEach((e=>{t[e.id]=e.cb})),fetch("/1.5/stdfun",{method:"POST",mode:"cors",cache:"no-cache",credentials:"same-origin",headers:{"Content-Type":"application/json"},redirect:"follow",referrerPolicy:"no-referrer",body:JSON.stringify({q:r})}).then((e=>e.json())).then((e=>{e.r.forEach((e=>{t[e.id](e.err,e.ret)}))})).catch((e=>{console.log(e)}))}function o(o,c,i){e.push({fn:o,p:c,cb:i,id:t++}),clearTimeout(r),r=setTimeout(n,50)}return _requestStdFn=o,o}
