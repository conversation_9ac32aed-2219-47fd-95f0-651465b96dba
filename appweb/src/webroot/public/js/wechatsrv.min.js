"use strict";var RMSrv;(RMSrv={getCookie:function(e){var t,n,r,i,o;for(o=e+"=",r=0,i=(n=document.cookie.split(";")).length;r<i;r++){for(t=n[r];" "===t.charAt(0);)t=t.substring(1);if(0===t.indexOf(o))return t.substring(o.length,t.length)}return""},init:function(){return"undefined"!=typeof wxConfig&&null!==wxConfig&&wx.config(wxConfig),wx.error((function(e){return RMSrv.wxLastError=e,"undefined"!=typeof console&&null!==console?console.log(e):void 0})),wx.ready((function(){return setTimeout((function(){return RMSrv.setUpPreview(),RMSrv.wxBeforeShare()}),300)}))},checkJsApi:function(){return wx.checkJsApi({jsApiList:["updateAppMessageShareData","updateTimelineShareData","previewImage"],success:function(e){return alert(JSON.stringify(e))},fail:function(e){return alert(JSON.stringify(e))},error:function(e){return alert(JSON.stringify(e))}})},isIOS:function(){return/iPhone|iPad|iPod/i.test(navigator.userAgent)},isAndroid:function(){return/Android/i.test(navigator.userAgent)},isWeChat:function(){return/MicroMessenger/i.test(navigator.userAgent)},isBlackBerry:function(){return/BlackBerry/i.test(navigator.userAgent)},appendDomain:function(e){var t;return(t=window.location.href.split("/"))[0]+"//"+t[2]+e},openTBrowser:function(e){return window.location=e,null},showInBrowser:function(e){return window.location=e},getMeta:function(e){var t,n,r,i,o;for(i=e.querySelectorAll("meta"),o={title:e.title},t=0,n=i.length;t<n;t++)o[(r=i[t]).getAttribute("name")]=r.getAttribute("content");return o},getShareImage:function(e){var t,n,r,i,o;if(t=e.getElementById("content_div")||e.body)for(n=0,i=(o=t.getElementsByTagName("img")).length;n<i;n++)if(null!=(r=o[n])?r.src:void 0)return r.src;return"https://realmaster.com/img/logo.png"},setUpPreview:function(){(function(e){var t,n,r,i,o,a;for(r=document.querySelectorAll(e),a=[],t=0,i=r.length;t<i;t++)(null!=(o=(n=r[t]).classList)?o.contains("no-preview"):void 0)||n.clientWidth>200&&n.clientHeight>200&&(n.src&&a.indexOf(n.src)<0&&a.push(n.src),n.addEventListener("click",(function(e){return wx.previewImage({current:e.target.src,urls:a}),!0})))})("img")},getShareInfo:function(e,t){var n,r,i,o,a;for(r in n=function(n,r){var i,o;try{if(o=e.getElementById("wx-"+n)||e.getElementById("share-"+n))return t[r||n]=o.value||o.textContent}catch(e){return i=e,"undefined"!=typeof console&&null!==console?console.log(i):void 0}},i={title:"title",desc:"description",url:"url",image:"image"})n(r,i[r]);return(null!=(o=t.image)?o.length:void 0)>8||(t.image=RMSrv.getShareImage(e)),(null!=(a=t.url)?a.length:void 0)>8||(t.url=e.URL||window.location.href),t},origin:function(){return window.location.origin||window.location.protocol+"//"+window.location.hostname+(window.location.port?":"+window.location.port:"")},wxSuccess:function(){var e,t;return t=new XMLHttpRequest,e=RMSrv.origin()+"/mp/shared?data="+RMSrv.sharedEData,/d\dw?\.realmaster/i.test(window.location.hostname||/app\.test/i.test(window.location.hostname))&&console.log("test server share success"),t.open("POST",e,!0),t.send()},wxCancel:function(){},wxBeforeShare:function(){var e,t,n,r;return/i\.realmaster/i.test(window.location.hostname||/app\.test/i.test(window.location.hostname))&&console.log("appweb wxBeforeShare"),t=function(){r.url&&(RMSrv.sharedEData=r.url.split("?")[0].split("/").pop())},e=window.document,n={title:(r=RMSrv.getShareInfo(e,RMSrv.getMeta(e))).title||"RealMaster Sharing",link:r.url,imgUrl:r.image,desc:r.description||"RealMaster App Sharing",success:RMSrv.wxSuccess,cancel:RMSrv.wxCancel,fail:function(e){return console.error(e)}},t(),wx.updateAppMessageShareData(n),wx.updateTimelineShareData(n)},clearCache:function(){},dialogAlert:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return alert("string"==typeof e?e:e.message||e.toString())},onReady:function(e){return e(),!0}}).init();
