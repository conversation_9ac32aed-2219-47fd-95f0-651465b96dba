
initSummernote = (noImg) ->
  $summernote = $(".summernote")
  buttonList = [
      [ "del", ["delToStart", "delToEnd"]]
      [ "color", [ "color" ] ]
      [ "para", [ "ul", "ol", "paragraph" ] ]
      [ "fontsize", [ "fontsize" ] ]
      [ "upLoadImg", [ "select" ] ]
      [ "insert" , [ "hr", "link" ] ] #,
      [ "style", [ "style", "bold", "italic", "underline", "clear" ] ]
      [ "misc", [ "undo", "redo", "codeview" ] ]
  ]
  if noImg
    buttonList.splice(4, 1)
  $summernote.summernote
    toolbar: buttonList
    onFocus:  ->
      if window.keyboard and window.isIOS
        # var height = $('.note-editable.panel-body > div:first-child').height();
        adjtH = parseInt((window.keyboardHeight or 0))
        $("#editorModal .note-editable.panel-body").css "padding-bottom", adjtH + "px"
    # $('.note-editable.panel-body').scrollTop(parseInt( $('.note-editable.panel-body').scrollTop() + window.keyboardHeight) );
    # window.keyboard.disableScroll(true)
    onBlur:  ->
      $("#editorModal .note-editable.panel-body").css "padding-bottom", "0px"  if window.keyboard and window.isIOS
      $summernote.summernote "saveRange"  unless window.onEditImgSelect

processFiles = (files) ->
  doone = undefined
  i = undefined
  if files and typeof FileReader isnt "undefined"
    i = 0
    doone = ->
      file = undefined
      if i < files.length
        file = files[i++]
        readFile file, ->
          doone()
    doone()
  else
    RMSrv.dialogAlert "Unsuppored browser. Can't process files."
getS3Config = (img, cb, hasThumb) ->
  fd = {}
  fd.ext = "jpg" #img.ext;
  # fd.isThumb = isThumb;
  fd.w = img.width
  fd.h = img.height
  fd.s = img.size
  fd.t = (if hasThumb then 1 else 0)
  $("#loading-bar-spinner").css "display", "block"
  $.ajax(
    url: amGloble.getS3Config
    data: fd
    type: 'post' #default: 'GET' # dataType: 'json' #default: Intelligent Guess
  ).done((ret) ->
    if ret.key
      $("#loading-bar-spinner").css "display", "none"
      window.s3config = ret
      cb() if cb
    else
      flashMessage "server-error"
    return
  ).fail(->
    flashMessage "server-error"
    return
  )

uploadFile = (img, isThumb) ->
  file = undefined
  fileKey = undefined
  policy = undefined
  signature = undefined
  if isThumb
    file = img.blob2
    fileKey = window.s3config.thumbKey
    policy = window.s3config.thumbPolicy
    signature = window.s3config.thumbSignature
  else
    file = img.blob
    fileKey = window.s3config.key
    policy = window.s3config.policy
    signature = window.s3config.signature
  fd = new FormData()
  fd.append "acl", "public-read"
  fd.append "key", fileKey
  fd.append "Content-Type", window.s3config.contentType
  fd.append "policy", policy
  fd.append "x-amz-credential", window.s3config.credential
  fd.append "x-amz-date", window.s3config.date
  fd.append "x-amz-signature", signature

  # fd.append('success_action_redirect',window.s3config.success_action_redirect);
  fd.append "x-amz-algorithm", "AWS4-HMAC-SHA256"
  fd.append "file", file, fileKey
  success = 0
  $("#loading-bar-spinner").css "display", "block"
  $.ajax(
    url: amGloble.savePicS3
    data: fd
    type: 'post'
    processData: false
    contentType: false
    crossDomain: true
    headers: {'Origin': 'anonymous', "Access-Control-Request-Origin": "anonymous"}
  ).done( ->
    success = 1
  ).always((ret ) ->
    if success or ret?.status is 204
      $("#loading-bar-spinner").css "display", "none"
      if window.isThumbImg
        # set thumbImage
        sUrl = "http://" + window.s3config.s3bucket + "/" + window.s3config.key
        window.cardMeta.img = sUrl
        setTimeout (->
          $('#thumbImg').attr('src', sUrl) #if set immediately file might not be found 404
          return
        ), 500
        # console.log that._datas.pageData.card
        window.isThumbImg = false
        return
      if window.isFlyer(amGloble.type, window.shSty)
        window.ctClone.bg = "http://" + window.s3config.s3bucket + "/" + window.s3config.key unless isThumb
        window.setPreviewContents(window.ctClone)
      else
        $(".summernote").summernote "insertImage", "http://" + window.s3config.s3bucket + "/" + window.s3config.key, window.s3config.key  unless isThumb
    else
      flashMessage "server-error"
      $.ajax( url: amGloble.uploadFail, data: {}, type: 'post' )
    return
  )

readFile = (file, callback) ->
  reader = undefined
  if /image/i.test(file.type)
    reader = new FileReader()
    reader.onload = (e) ->
      image = undefined
      image = $("<img/>").load(->
        newimage = undefined
        newimage = getCanvasImage(this, file)
        flashMessage "img-inserted"
        toggleModal "imgSelectModal"
        hasThumb = (newimage.width > 400 or newimage.height > 300)
        getS3Config newimage, (->
          uploadFile newimage, false
          uploadFile newimage, true  if hasThumb
        ), hasThumb
        callback()
      ).attr("src", e.target.result)

    reader.readAsDataURL file
  else
    RMSrv.dialogAlert file.name + " unsupported format : " + file.type
    callback()
imageToDataUri = (img, width, height) ->
  # create an off-screen canvas
  canvas = document.createElement("canvas")
  ctx = canvas.getContext("2d")
  # set its dimension to target size
  canvas.width = width
  canvas.height = height
  # draw source image into the off-screen canvas:
  ctx.drawImage img, 0, 0, width, height
  # encode image to data-uri with base64 version of compressed image
  canvas.toDataURL()
splitName = (name, type) ->
  p = undefined
  if (p = name.lastIndexOf(".")) > 0
    [ name.substr(0, p), name.substr(p + 1).toLowerCase() ]
  else
    [ name, "." + type.substr(type.lastIndexOf("/")).toLowerCase() ]
dataURItoBlob = (dataURI) ->
  ab = undefined
  byteString = undefined
  dataView = undefined
  i = undefined
  ia = undefined
  mimeString = undefined
  if dataURI.split(",")[0].indexOf("base64") >= 0
    byteString = atob(dataURI.split(",")[1])
  else
    byteString = unescape(dataURI.split(",")[1])
  mimeString = dataURI.split(",")[0].split(":")[1].split(";")[0]
  ab = new ArrayBuffer(byteString.length)
  ia = new Uint8Array(ab)
  i = 0
  while i < byteString.length
    ia[i] = byteString.charCodeAt(i)
    i++
  dataView = new DataView(ab)
  new Blob([ dataView ],
    type: mimeString
  )
getCanvasImage = (image, file) ->
  imgLWL = 1000 # for long edge
  imgLHL = 1000 # Long Edge Height Limit
  imgSWL = 680 #for short edge
  imgSHL = 680
  thumbH = thumbW = 128
  thumbNumber = 10
  canvas = undefined
  canvas2 = undefined
  ctx = undefined
  ctx2 = undefined
  h = undefined
  heightRatio = undefined
  img = undefined
  imh = undefined
  imw = undefined
  ratio = undefined
  sname = undefined
  w = undefined
  widthRatio = undefined
  xs = undefined
  ratio = 1
  if (image.width > imgLWL) or (image.height > imgLHL)
    widthRatio = imgLWL / image.width
    heightRatio = imgLHL / image.height
    ratio = Math.min(widthRatio, heightRatio)
  if (image.width >= image.height) and (image.height > imgSHL)
    heightRatio = imgSHL / image.height
    ratio = heightRatio  if heightRatio < ratio
  if (image.width <= image.height) and (image.width > imgSWL)
    widthRatio = imgSWL / image.width
    ratio = widthRatio  if widthRatio < ratio
  canvas = document.createElement("canvas")
  canvas.width = image.width * ratio
  canvas.height = image.height * ratio
  ctx = canvas.getContext("2d")
  ctx.drawImage image, 0, 0, image.width, image.height, 0, 0, canvas.width, canvas.height
  sname = splitName(file.name, file.type)
  img =
    name: file.name
    nm: sname[0]
    ext: sname[1]
    origType: file.type
    origSize: file.size
    width: canvas.width
    height: canvas.height
    ratio: ratio


  # img.type = /png/.test(file.type) ? "image/png" : "image/jpeg"
  img.type = "image/jpeg"
  img.url = canvas.toDataURL(img.type, 0.8)
  img.blob = dataURItoBlob(img.url)
  img.size = img.blob.size
  img.canvas = canvas
  canvas2 = document.createElement("canvas")
  canvas2.width = (w = Math.min(thumbW, image.width))
  canvas2.height = (h = Math.min(thumbH, image.height))
  if (image.width * h) > (image.height * w)
    xs = (image.width - (image.height / h * w)) / 2
    imw = image.width - (xs * 2)
    imh = image.height
  else
    xs = 0
    imw = image.width
    imh = image.width
  ctx2 = canvas2.getContext("2d")
  ctx2.drawImage image, xs, 0, imw, imh, 0, 0, w, h
  img.url2 = canvas2.toDataURL(img.type, 0.7)
  img.blob2 = dataURItoBlob(img.url2)
  img.size2 = img.blob2.size
  img.canvas2 = canvas2
  img
init_map = ->
  lat = 43.7182412
  lng = -79.378058
  ll = undefined
  map = undefined
  marker = undefined
  opts = undefined
  geocoder = undefined
  address = $("#meta-addr").val() or "Mississauga, ON, Canada"
  ll = new google.maps.LatLng(lat, lng)
  opts =
    zoom: 12
    center: ll
    mapTypeControl: true
    mapTypeControlOptions:
      style: google.maps.MapTypeControlStyle.DROPDOWN_MENU
    navigationControl: true
    mapTypeId: google.maps.MapTypeId.ROADMAP

  map = new google.maps.Map(document.getElementById("id_d_map"), opts)
  window.map = map
  geocoder = new google.maps.Geocoder()
  if geocoder
    geocoder.geocode
      address: address
    , (results, status) ->
      geocodePosition = (pos) ->
        geocoder.geocode
          latLng: pos
        , (responses) ->
          if responses and responses.length > 0
            # console.log(responses[0].formatted_address);
            $("#housecard-page-edit-body").find("[data-role=meta-addr]").val responses[0].formatted_address
          else
            console.log "Cannot determine address at this location."
      if status is google.maps.GeocoderStatus.OK
        unless status is google.maps.GeocoderStatus.ZERO_RESULTS
          map.setCenter results[0].geometry.location
          infowindow = new google.maps.InfoWindow(
            content: "<b>" + address + "</b>"
            size: new google.maps.Size(150, 50)
          )
          marker = new google.maps.Marker(
            position: results[0].geometry.location
            map: map
            draggable: true
            animation: google.maps.Animation.DROP
            title: address
            optimized: false
          )
          marker.addListener "click", ->
            if marker.getAnimation() isnt null
              marker.setAnimation null
            else
              marker.setAnimation google.maps.Animation.BOUNCE
          # google.maps.event.addListener(marker, 'click', function() {
          #   infowindow.open(map, marker);
          # })
          google.maps.event.addListener marker, "dragend", ->
            # updateMarkerStatus('Drag ended');
            geocodePosition marker.getPosition()
        else
          RMSrv.dialogAlert "No results found"
      else
        RMSrv.dialogAlert "Geocode was not successful for the following reason: " + status

$ ->
  #that is not controller at :1539, need window._id
  window._id = null
  controller =
    id: "housecard-page-edit-body"
    init: ->
      @_doms = {}
      @_datas =
        withDl: true
        withSign: true
      @initDoms()
      @bindEvents()
      @getData()
      window.isFlyer = @isFlyer

    initDoms: ->
      page = @$ = $("#" + @id)
      $.extend @_doms,
        metaTitle: page.find("[data-role=meta-title]")
        metaEditor: page.find("[data-role=meta-editor]")
        metaTemplate: page.find("[data-role=meta-template]")
        metaDesc: page.find("[data-role=meta-desc]")
        metaVc: page.find("[data-role=meta-vc]")
        metaAddr: page.find("[data-role=meta-addr]")
        cpName: page.find("[data-role=company-name]")
        nkPhoto: page.find("[data-role=nick-photo]")
        nki: page.find("[data-role=nick-img]")
        nknm: page.find("[data-role=nick-nm]")
        intr: page.find("[data-role=intr]")
        mapUl: page.find("[data-role=map-item-ul]")
        ctct: page.find("[data-role=ctct]")
        ctctWx: page.find("[data-role=ctct-wx]")
        ctctWxQr: page.find("[data-role=ctct-wxqr]")
        ctctWxQrW: page.find("[data-role=ctct-wxqr-wrapper]")
        ctctGrpQrcd: page.find("[data-role=ctct-grpqrcd]")
        ctctGrpQrcdW: page.find("[data-role=ctct-grpqrcd-wrapper]")
        ctctTel: page.find("[data-role=ctct-tel]")
        ctctTel2: page.find("[data-role=ctct-tel2]")
        ctctEml: page.find("[data-role=ctct-eml]")
        ctctWeb: page.find("[data-role=ctct-web]")
        ctctCpny: page.find("[data-role=ctct-cpny]")
        ctctCpny_pstn: page.find("[data-role=ctct-cpny_pstn]")
        cpnydtl: page.find("[data-role=cpnydtl]")
        cpnydtlFax: page.find("[data-role=cpnydtl-fax]")
        cpnydtlTel: page.find("[data-role=cpnydtl-tel]")
        cpnydtlTel2: page.find("[data-role=cpnydtl-tel2]")
        cpnydtlAds: page.find("[data-role=cpnydtl-ads]")
        cpnydtlWeb: page.find("[data-role=cpnydtl-web]")
        medLink: page.find("[data-role=media-link]")
        propDetailPane: page.find("[data-role=prop-detail-pane]")
        propImgPane: page.find("[data-role=prop-img-pane]")
        propPrice: page.find("[data-role=prop-lp_price]")
        propType: page.find("[data-role=prop-type_own1_out]")
        propBr: page.find("[data-role=prop-br]")
        propKit: page.find("[data-role=prop-num_kit]")
        propPak: page.find("[data-role=prop-gar_spaces]")
        propBsmt: page.find("[data-role=prop-bsmt1_out]")
        propBath: page.find("[data-role=prop-bath_tot]")
        propLot: page.find("[data-role=prop-front_ft]")
        propExt: page.find("[data-role=prop-constr1_out]")
        propTax: page.find("[data-role=prop-taxes]")
        propSqft: page.find("[data-role=prop-sqft]")
        propAC: page.find("[data-role=prop-a_c]")
        propCVC: page.find("[data-role=prop-central_vac]")
        propAge: page.find("[data-role=prop-yr_built]")
        propPool: page.find("[data-role=prop-pool]")
        propFuel: page.find("[data-role=prop-fuel]")
        propRltr: page.find("[data-role=prop-rltr]")
        propRemark: page.find("[data-role=prop-ad_text]")
        topic: page.find("[data-role=topic]")
        topicContent: page.find("[data-role=topic-content]")

    setShareUrl: ->
      that = this
      linkURL = amGloble.host + "/1.5/wecard/prop/" + amGloble.id + "/" + window._id
      shareData = "tp=wecard" + "&uid=" + amGloble.id + "&id=" + window._id
      if that._datas.withDl
        linkURL += "?wDl=1"
        shareData += "&dl=1"
        linkURL += "&sgn=1"  if that._datas.withSign
      else
        linkURL += "?sgn=1"  if that._datas.withSign
      shareData += "&sgn=1"  if that._datas.withSign
      shareData += "&lang=" + amGloble.lang if amGloble.lang
      $("#share-url").text linkURL  if window._id
      $("#share-data").text shareData  if window._id

    bindEvents: ->
      that = this
      if /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(navigator.userAgent.toLowerCase())
        window.isIOS = true  if /iPhone|iPad|iPod/i.test(navigator.userAgent.toLowerCase())
        RMSrv.getKeyboard (keyboard) -> # only working in APP
          window.keyboard = keyboard
          window.keyboard.disableScroll false

      #page event listeners
      @$.on("click", ".footer-icon-music", ->
        $(".bgs-mp3").toggle()
        $("#backdrop").toggle()  if $(".bgs-map").css("display") is "none"
        $(".bgs-map").hide()
        that.getMusicLinks()
        false
      ).on("click", "#li-music-close", ->
        if myAuto1 = document.getElementById(that.adid)
          myAuto1.pause()
          that.adid = null
        $(".bgs-map").hide()
        $(".bgs-mp3").toggle()
        $("#backdrop").toggle()
        false
      ).on("click", "a.btn-sort", (e) ->
        ele = e.currentTarget
        $pele = $(ele).parents("li")
        $clonePele = $pele.clone()
        $prev = $pele.prev()
        currIndex = $pele.index()
        prevIndex = $prev.index()
        # console.log "cur: " + currIndex + " prev: " + prevIndex
        if $prev and $prev.length > 0 and $prev.is("li") and currIndex > 0
          seq = that._datas.pageData.card.seq
          if seq and seq.length > 0
            curItem = seq[currIndex]
            prevItem = seq[prevIndex]
            seq[prevIndex] = curItem
            seq[currIndex] = prevItem
            that._datas.pageData.card.seq = seq
            # console.log "save-sort:" + JSON.stringify(seq)
          $prev.before $clonePele
          $pele.remove()
        false
      ).on("click", "#thumbImg", ->
        # show image select modal
        data = that._datas.pageData
        console.log that
        # upload file cb in window
        window.isThumbImg = true
        window.cardMeta = data.card.meta
        toggleModal "imgSelectModal"
      ).on("click", "#listPageBtn", ->
        window.keyboard.disableScroll false  if window.keyboard
      ).on("click", "#showMap", ->
        unless window.mapLoaded
          $("#id_map_outer").css "display", "block"
          window.mapLoaded = true
          script = document.createElement("script")
          script.type = "text/javascript"
          script.src = window.gurl + "&callback=init_map"
          document.body.appendChild script
        else
          map = window.map
          address = $("#meta-addr").val() or "Mississauga, ON, Canada"
          geocoder = new google.maps.Geocoder()
          if geocoder
            geocoder.geocode
              address: address
            , (results, status) ->
              if status is google.maps.GeocoderStatus.OK
                unless status is google.maps.GeocoderStatus.ZERO_RESULTS
                  map.setCenter results[0].geometry.location
                  infowindow = new google.maps.InfoWindow(
                    content: "<b>" + address + "</b>"
                    size: new google.maps.Size(150, 50)
                  )
                  marker = new google.maps.Marker(
                    position: results[0].geometry.location
                    map: map
                    title: address
                    optimized: false
                  )
                  google.maps.event.addListener marker, "click", ->
                    infowindow.open map, marker
                else
                  RMSrv.dialogAlert "No results found"
              else
                RMSrv.dialogAlert "Geocode was not successful for the following reason: " + status

        false
      # ).on("click", "#showStyleBlog", (e) ->
      #   if that.isFlyer(that._datas.pageData.card)
      #     return false
      #   ele = e.currentTarget
      #   $ele = $(ele)
      #   isBLGActive = $ele.hasClass("avtive")
      #   unless isBLGActive
      #     $ele.addClass "active"
      #     $("#showStyleVT").removeClass "active"
      #     that._datas.pageData.card.meta.shSty = "blog"  if that._datas.pageData.card
      #   false
      # ).on("click", "#showStyleVT", (e) ->
      #   ele = e.currentTarget
      #   $ele = $(ele)
      #   isVTActive = $ele.hasClass("active")
      #   unless isVTActive
      #     $ele.toggleClass "active"
      #     $("#showStyleBlog").removeClass "active"
      #     that._datas.pageData.card.meta.shSty = "vt"  if that._datas.pageData.card
      #   false
      ).on("click", ".thumb-wrapper img", ->
        $(this).toggleClass "selected"
        index = undefined
        altName = $(this).prop("alt")
        if (index = that._datas.userData.selected.indexOf(altName)) >= 0
          that._datas.userData.selected.splice index, 1
        else
          that._datas.userData.selected.push altName
        # console.log that._datas.userData.selected
      ).on("click", "#toggleImgSelect", ->
        if window.keyboard and window.keyboard.isVisible
          window.keyboard.close()
          window.onEditImgSelect = false
        window.isThumbImg = false if window.isThumbImg
        toggleModal "imgSelectModal"
      ).on("click", "#listUserPics", ->
        $("#imgSelectPicList").html ""
        $.ajax(
          url: amGloble.getUserFiles
          data: {}
          type: 'get' #default: 'GET' # dataType: 'json' #default: Intelligent Guess
        ).done((ret) ->
          if ret.ok is 1
            that._datas.userData = ret
            that._datas.userData?.selected = []
            for k of ret.pl
              imgAddr = ret.base + "/" + ((if ret.pl[k].tA? then ret.pl[k].tA else ret.pl[k].nm))
              imgSpan = "<span class='thumb-wrapper'><img src='" + imgAddr + "' alt='" + k + "'> </span>"
              $("#imgSelectPicList").append imgSpan
          else
            console.log "Error has happened while getting file list!"
          return
        ).fail(->
          flashMessage "server-error"
          return
        )
        true
      ).on("click", "#saveFrame", (e) ->
        editContent = $(".summernote").summernote('code')
        if that.isFlyer(that._datas.pageData.card.tp, that._datas.pageData.card.meta?.shSty) and that._datas.ctClone
          that._datas.ctClone.m = editContent
          that.setPreviewContents(that._datas.ctClone)
        else
          ele = e.currentTarget
          $pele = $(ele).parents("li")
          seq = that._datas.pageData.card.seq
          $editItem = $(seq[that.editIndex].m)
          jqIndex = that.editIndex + 1
          # if $("<div>" + editContent + "</div>").children("div").length > 1
          #   editContent = "<div>" + editContent + "</div>"
          seq[that.editIndex].m = editContent
          items = $("#edit-page-contents-ul > li:nth-of-type(" + jqIndex + ")")
          if $(editContent).length > 1
            editContent = $('<div>').html(editContent)[0].outerHTML
          editContent += that._doms.ctrlButtons
          # hard to decide replace content, reconstruct with ctrlButtons and modify content
          items.html editContent
        unless that._doms.metaDesc.val()
          that._doms.metaDesc.val( $(editContent).text().replace(/\s|\n|\r|\v/g,'').substr(0,50) )
        if window.keyboard
          window.keyboard.disableScroll false
          window.keyboard.close()  if window.keyboard.isVisible
        $(".summernote").summernote('destroy')
        initSummernote()
        # console.log "here"
        false
      ).on("click", "#savePicFrame", ->
        seq = that._datas.pageData.card.seq

        $editItem = $(seq[that.editIndex])
        editContent = that.getContentFromCt( that._datas.ctClone )

        jqIndex = that.editIndex + 1
        for k, v of that._datas.ctClone
          seq[that.editIndex][k]  = v

        items = $("#edit-page-contents-ul > li:nth-of-type(" + jqIndex + ") > div:first-child")
        items.replaceWith editContent

        false
      ).on("click touchend", "#gal-del-btn", (e) ->
        $ele = $(e.currentTarget)
        $pele = $ele.parent('div')
        $ele.hide()
        $("#gal-del-yes-btn").show()
        $("#gal-del-can-btn").show()
        $pele.addClass('active')
        false
      ).on("click touchend", "#gal-del-can-btn", (e) ->
        $ele = $(e.currentTarget)
        $pele = $ele.parent('div')
        $("#gal-del-btn").show()
        $("#gal-del-can-btn").hide()
        $("#gal-del-yes-btn").hide()
        $pele.removeClass('active')
        false
      ).on("click touchend", "#gal-del-yes-btn", (e) ->
        $ele = $(e.currentTarget)
        $pele = $ele.parent('div')
        $("#gal-del-btn").show()
        $("#gal-del-can-btn").hide()
        $("#gal-del-yes-btn").hide()
        $pele.removeClass('active')

        del = {}
        del.fldr = that._datas.userData.fldr
        del.files = that._datas.userData.selected

        # console.log del
        unless del.files.length > 0
          return false

        for i in del.files
          if /^[A-Q]{1}$/.test(i.split('.')[0])
            # alert 'Err:'
            RMSrv.dialogAlert(vars.ERR_PRO_IMG or 'Cannot remove profile images!!')
            return false
        # console.log del
        $.ajax(
          url: amGloble.deleteFiles
          type: 'post' #default: 'GET' # dataType: 'json' #default: Intelligent Guess
          contentType: "application/json"
          dataType: "json"
          data: JSON.stringify(del)
        ).done((ret) ->
          if ret.ok is 1
            $('#listUserPics')[0].click()
          else
            RMSrv.dialogAlert ret.err
          return
        ).fail(->
          RMSrv.dialogAlert "sever error!, please try again later"
          return
        )
        false
      ).on("click", "#saveCard",  (e)->
        data = that._datas.pageData
        unless $("[data-role=meta-title]").val()
          flashMessage "no-title"
          return false
        if data.card
          # console.log data.card
          # alert JSON.stringify data.card
          that.savePropCard data.card
          flashMessage "page-saved"
        else
          RMSrv.dialogAlert "Error: no card yet!"
        e.preventDefault()
        e.stopPropagation()
        false
      ).on("click", "#id_with_dl", ->
        that._datas.withDl = $(this).children("input")[0].checked
        that.setShareUrl()
      ).on("click", "#id_with_sign", ->
        that._datas.withSign = $(this).children("input")[0].checked
        that.setShareUrl()
      ).on("click", "a.btn-delete", (e) ->
        ele = e.currentTarget
        $pele = $(ele).parents("li")
        currIndex = $pele.index()
        seq = that._datas.pageData.card.seq
        jqIndex = currIndex + 1
        items = $("#edit-page-contents-ul > li:nth-of-type(" + jqIndex + ")")
        seq.splice currIndex, 1
        items.remove()
        false
      ).on("click", "a.btn-see", (e) ->
        ele = e.currentTarget
        $pele = $(ele).parents("li")
        currIndex = $pele.index()
        seq = that._datas.pageData.card.seq
        $editItem = $(seq[currIndex].m)
        $editItem.toggleClass "dis"
        seq[currIndex].m = $editItem[0].outerHTML
        $pele.toggleClass "dis"
        false
      ).on("click", "#shareToNews", ->
        newsItem = {}
        data = that._datas
        card = data.card
        meta = card.meta
        newsItem.wid = card._id
        newsItem.tl = card.meta.title
        newsItem.desc = card.meta.desc
        newsItem.thumb = card.meta.img
        newsItem.url = amGloble.host + "/1.5/wecard/prop/"
        newsItem.logo = "true"
        newsItem.chnl = "WePage"
        newsItem.src = "WePage"
        newsItem.tp = card.meta.tp
        newsItem.area = "Toronto"
        newsItem.lang = "zh-cn"
        newsItem.m = ""
        headerStr = "<div style=\"padding-top:20px\">"
        headerStr += "<h2>" + meta.title + "</h2>"
        time = (if meta.ts then meta.ts.split("T")[0] else new Date().toDateString())
        headerStr += "<br>" + "<span style=\"margin-right:10px;\">" + time + "</span>"
        wecardURL = "RMSrv.showInBrowser('" + amGloble.host + "/1.5/wesite/" + card.id + "')"
        headerStr += "<span style=\"margin-right:10px;\"><a onclick=\"" + wecardURL + "\">" + meta.editor + "</a></span>"
        headerStr += "<span style=\"margin-right:10px; color:#607fa6\">" + meta.custvc + "</span>"
        url = amGloble.emurl + encodeURIComponent(meta.addr)
        headerStr += "<span style=\"color: #007aff;\" class=\"fa fa-location-arrow\" onclick=\"RMSrv.showInBrowser('" + url + "')\" > " + "</span>"  if meta.addr
        headerStr += "</div>"
        newsItem.m += headerStr
        i = 0
        while i <= card.seq.length - 1
          newsItem.m += card.seq[i].m
          i++
        $.ajax(
          url: amGloble.publishToNews
          data: newsItem
          type: 'post' #default: 'GET' # dataType: 'json' #default: Intelligent Guess
        ).done((ret) ->
          if ret.ok is 1
            RMSrv.share "hide"
            RMSrv.dialogAlert ret.msg
          else
            RMSrv.dialogAlert "Error has happened while publishing!"
          return
        ).fail(->
          flashMessage "server-error"
          return
        )
        false
      ).on("click", "#edit-page-contents-ul > li.edit-in-summernote, #edit-page-contents-ul > li.edit-in-summernote > a.edit-in-summernote", (e) ->
        # console.log that._datas
        ele = e.currentTarget
        nodeName = $(ele).prop('nodeName').toLowerCase()
        if nodeName is 'li'
          $pele = $(ele)
        else
          $pele = $(ele).parents("li")

        that.editIndex = $pele.index()

        $contentToBeEdited = $(that._datas.pageData.card.seq[that.editIndex]?.m or '')
        that._datas.ctClone = undefined

        if that.isFlyer(that._datas.pageData.card.tp, that._datas.pageData.card.meta?.shSty) and that._datas.pageData.card.seq[that.editIndex]._for isnt 'user'
          ct = that._datas.pageData.card.seq[that.editIndex]
          that._datas.ctClone = that.shallowCopy(ct)
          that.setPreviewContents(that._datas.ctClone)
          toggleModal('frameEditorModal', 'open')
          e.preventDefault()
          e.stopPropagation()
        else
          $cl = $contentToBeEdited.clone()
          $(".summernote").summernote 'code', $('<div>').append($cl).html() #$cl[0].outerHTML
          $(".summernote").summernote('destroy')
          initSummernote()
          toggleModal "editorModal"
        window.keyboard.disableScroll true  if window.keyboard and window.isIOS
        false
      ).on("click", "#insertImage", ->
        _insertImage = (sUrl, sName)->
          if window.isThumbImg
            window.cardMeta.img = sUrl
            $('#thumbImg').attr('src', sUrl)
            # console.log that._datas.pageData.card
            window.isThumbImg = false
            return
          if that._datas.pageData.card and that.isFlyer(that._datas.pageData.card.tp, that._datas.pageData.card.meta?.shSty)
            that._datas.ctClone.bg = sUrl
            that.setPreviewContents(that._datas.ctClone)
          else
            $(".summernote").summernote "insertImage", sUrl, sName
            # new version summernote can set img width
            # $(".summernote").summernote "insertImage", sUrl, ($img) ->
            #   console.log $img
            #   $img.css('width', '100%')
            #   $img.attr('data-name', sName)
            #   return
        clearSelected = ->
          that._datas.userData?.selected = []
          $("#imgSelectPicList img").each( ->
            $(this).removeClass('selected')
            )
        $note = $(".summernote")
        $note.summernote "restoreRange"
        if sUrl = $("#imgInputURL").val()
          _insertImage(sUrl, sUrl)
          $("#imgInputURL").val('') #reset value if insert
          toggleModal "imgSelectModal"
        else if that._datas.userData? and that._datas.userData.selected.length > 0
          data = that._datas.userData
          selected = data.selected
          i = 0
          while i <= selected.length - 1
            sUrl = data.base + "/" + data.pl[selected[i]].nm
            sName = selected[i]
            _insertImage(sUrl, sName)
            i++
          clearSelected()
          toggleModal "imgSelectModal"
        else if input = $("#imgInputFiles").get(0)
          txt = undefined
          if "files" of input
            if input.files.length is 0
              console.log "Select one or more files."
              return false
            else
              window.ctClone = that._datas.ctClone
              window.setPreviewContents = that.setPreviewContents
              window.getContentFromCt   = that.getContentFromCt
              processFiles input.files
              clearSelected()
              $("#previewImg").attr "src", ''
              $("#imgInputFiles").val('')
        else
          console.log "no files"
        window.onEditImgSelect = false
      ).on("click", "#wepageShareBtn", ->
        that.setShareUrl()
      # ).on("click", ".footer-icon-wmrk", ->
      #   return if that._datas.pageData.loading
      #   that._datas.pageData.loading = 1
      #   $("#loading-bar-spinner").css "display", "block"
      #   setTimeout ->
      #     that._datas.pageData.loading = 0
      #     $("#loading-bar-spinner").css "display", "none"
      #   , 2500
      #   if $('.footer-icon-wmrk .fa').hasClass('fa-user-plus')
      #     $('.footer-icon-wmrk .fa').removeClass('fa-user-plus')
      #     $('.footer-icon-wmrk .fa').addClass('fa-check-square-o')
      #     that._datas.pageData.card.meta?.wmk = 1
      #   else
      #     $('.footer-icon-wmrk .fa').addClass('fa-user-plus')
      #     $('.footer-icon-wmrk .fa').removeClass('fa-check-square-o')
      #     that._datas.pageData.card.meta?.wmk = 0
      ).on("click", "#wepagePreviewBtn", ->
        linkURL = amGloble.host + "/1.5/wecard/prop/" + amGloble.id + "/" + window._id
        # console.log linkURL
        if window._id
          RMSrv.showInBrowser linkURL
        else
          RMSrv.dialogAlert "Not Saved Yet!"
        false
      ).on("click", "#newFrame", (e) ->
        ele = e.currentTarget
        $pele = $(ele).parents("li")
        seq = that._datas.pageData.card.seq
        if that.isFlyer(that._datas.pageData.card.tp, that._datas.pageData.card.meta?.shSty)
          newFrame = '<div style="font-size: 19px;">Contents</div>'
        else
          newFrame = "<div><p class='text-left'>Contents</p></div>"
        listItem = {}
        listItem._for = "newFrame"
        listItem.m = newFrame
        seq.push listItem
        newFrame += that._doms.ctrlButtons
        $("<li class=\"edit-in-summernote\">" + newFrame + "</li>").insertBefore "li.item-add"
        false
      ).on("click", "#pvReplaceImg", ->
        toggleModal('imgSelectModal')
        that.setPreviewContents(that._datas.ctClone)
        false
      ).on("click", "#pvRemoveBgImg", ->
        that._datas.ctClone.bg = ''
        that.setPreviewContents(that._datas.ctClone)
        false
      ).on("click", "#pvEditPreviewText", ->
        $(".summernote").summernote 'code', that._datas.ctClone.m
        $(".summernote").summernote('destroy')
        initSummernote(true)

        # window.keyboard.disableScroll true  if window.keyboard and window.isIOS
        toggleModal "editorModal"

        that.setPreviewContents(that._datas.ctClone)
        false
      ).on("click", "#pvShiftTextPosition", ->
        shift = (clone) ->
          cur = clone.pos
          pTop = 'top:10%;'
          pMid = 'top:45%;'
          pBot = 'bottom:10%;'
          if cur is pTop
            clone.pos = pMid
          else if cur is pMid
            clone.pos = pBot
          else
            clone.pos = pTop
          # console.log clone
        shift(that._datas.ctClone)
        # console.log that._datas.ctClone
        that.setPreviewContents(that._datas.ctClone)
        false
      ).on("click", "#pvShiftImgPosition", ->
        shift = (clone) ->
          cur = clone.bgPos
          pTop = 'top'
          pMid = 'center'
          pBot = 'bottom'
          if cur is pTop
            clone.bgPos = pMid
          else if cur is pMid
            clone.bgPos = pBot
          else
            clone.bgPos = pTop
          # console.log clone
        shift(that._datas.ctClone)
        # console.log that._datas.ctClone
        that.setPreviewContents(that._datas.ctClone)
        false
      ).on("click", "#pvTextBg", ->
        shift = (clone) ->
          cur = clone.tbg
          t15 = 'background-color: rgba(0, 0, 0, 0.45);'
          t45 = 'background-color: rgba(0, 0, 0, 0.8);'
          t00 = ''
          if cur is t15
            clone.tbg = t45
          else if cur is t45
            clone.tbg = t00
          else
            clone.tbg = t15
          # console.log clone
        shift(that._datas.ctClone)
        # console.log that._datas.ctClone
        that.setPreviewContents(that._datas.ctClone)
        false
      ).on("click", "#pvPreviewAnimation", ->
        shift = (clone) ->
          cur = clone.ani
          anis = [
            'zoomIn'
            'fadeIn'
            'fadeInUp'
            'flash'
            'slideInUp'
            'slideInDown'
            'slideInLeft'
            'slideInRight'
          ]
          if (index = anis.indexOf(cur)) >=0
            clone.ani = anis[(index+1)%(anis.length)]
          else
            clone.ani = anis[0]

        shift(that._datas.ctClone)
        # console.log that._datas.ctClone
        that.setPreviewContents(that._datas.ctClone)
        false
      )

      # $("#tpSelect").change ->
      #   amGloble.type = $("#tpSelect").val()
      #   amGloble.type = "exlisting"  if amGloble.type.toLowerCase() is "exclusive listing"
      #   that.getData()

      $("#devWidthSlider").change ->
        # console.log $(this).val()
        that.setPreviewScale($(this).val())


      $("#bgs-mp3-ul").on("click", "li a", ->
        has = $(this).hasClass("anm-rotate")
        id = $(this).attr("adid")
        if that.adid and that.adid isnt id
          $("#bgs-mp3-ul").find("[adid=" + that.adid + "]").removeClass "anm-rotate"
          myAuto1 = document.getElementById(that.adid)
          myAuto1.pause()
          that.adid = null
        that.adid = id
        myAuto = document.getElementById(id)
        if has
          $(this).removeClass "anm-rotate"
          myAuto.pause()
          that.adid = null
        else
          $(this).addClass "anm-rotate"
          myAuto.play()
        false
      ).on "click", "li span", ->
        if that.adid
          $("#bgs-mp3-ul").find("[adid=" + that.adid + "]").removeClass "anm-rotate"
          myAuto1 = document.getElementById(that.adid)
          myAuto1.pause()
          that.adid = null
        $li = $(this).parents("li")
        url = $li.attr("urls")
        n = $li.attr("n")
        id = $li.find("a").attr("adid")
        myAuto = document.getElementById(id)
        myAuto.pause()
        data =
          url: url
          nm: n
        # console.log JSON.stringify(data)
        that._datas.pageData.card.music = data
        $("#bgs-mp3-ul").parents(".bgs-mp3").hide()
        $("#backdrop").hide()
        false

    isFlyer: (type, shSty) ->
      if type in ['xmas1', 'xmas2', 'spring_fest', 'flyer']
        return true
      if (shSty is 'vt') or (amGloble.shSty is 'vt')
        return true
      false


    shallowCopy: (obj) ->
      if null is obj or 'object' isnt typeof obj
        return obj
      copy = obj.constructor()
      for attr of obj
        if obj.hasOwnProperty(attr)
          copy[attr] = obj[attr]
      copy

    enableBtns: ->
      $("#wepagePreviewBtn").removeClass "disabled"
      $("#wepageShareBtn").removeClass "disabled"

    getUrlSrc: (url) ->
      return 'wechat'  if /^https?:\/\/([^\/]+\.)*weixin\.qq\.com\//i.test url
      return 'youtube' if /^https?:\/\/([^\/]+\.)*youtube\.com\//i.test url
      'unknown'

    getPageContent: (controller, url, cb) ->
      that = controller or this
      RMSrv.getKeyboard ->
        hiddenBrowser = cordova.ThemeableBrowser
        btnLeft = {
          image: 'left'
          imagePressed: 'left'
          align: 'left'
          event: 'custClosePressed'
        }
        btnB = {
          image: 'back'
          imagePressed: 'back'
          align: 'left'
          event: 'backPressed'
        }
        if window.isIOS
          customButtons = [ btnLeft, btnB ]
        else
          customButtons = [ btnB, btnLeft ]
        customButtons.push {
          image: 'check'
          imagePressed: 'check'
          align: 'right'
          event: 'pagesPressed'

        }
        cfg =
          toolbar:
            height: 44
            color: '#E03131'
          customButtons: customButtons
          fullscreen: false
          # backButtonCanClose: true
        returned = false
        errorBody = null
        ref = hiddenBrowser.open(url, '_blank', cfg)
        done = (body, callback) ->
          return if returned
          returned = true
          ref.removeEventListener 'custClosePressed', cancelImport
          ref.removeEventListener 'backPressed', backPressed
          ref.removeEventListener 'loaderror', whenError
          ref.removeEventListener 'pagesPressed', whenLoadStop
          ref.removeEventListener cordova.ThemeableBrowser.EVT_ERR, whenError
          ref.close()
          if callback
            callback body: body
          else
            cb body: body
        # setTimeout (-> done(errorBody)),60000
        whenLoadStop =  ->
          ref.executeScript {code: 'document.documentElement.innerHTML'}, (values) ->
            body = values[0]
            try
              b1 = body.match(/<body.*?>([\s\S]*)<\/body>/i)[1]
              b2 = b1.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
              return done body #if b2.length > 100
            catch err
              console.log err
              return done body
        whenError = (e) ->
          errorBody = "Error: #{e.code + ' : ' + e.message}"

        cancelImport =  -> # conflict name event will not trigger
          done 'Cancelled', ->
            window.location = '/1.5/wecard'

        backPressed =  ->
          # script = "window.location=" + "'" + url + "'"
          script = "window.history.back(-1)"
          ref.executeScript {code: script}

        # if wechat add load stop
        ref.addEventListener 'loadstop', whenLoadStop if that.getUrlSrc(url) is 'wechat'

        ref.addEventListener 'custClosePressed', cancelImport
        ref.addEventListener 'backPressed', backPressed
        ref.addEventListener 'pagesPressed', whenLoadStop #pagesPressed
        ref.addEventListener 'loaderror', whenError
        ref.addEventListener cordova.ThemeableBrowser.EVT_ERR, whenError

    setPreviewContents: (ct) ->
      that = this
      $frame = $("#frame-preview")
      frame = $("#frame-preview")[0]
      framedoc = frame.contentDocument or frame.contentWindow.document
      # console.log framedoc
      unless that.getContentFromCt
        that.getContentFromCt = window.getContentFromCt
      framedoc.body.innerHTML = that.getContentFromCt(ct)
      framedoc.body.style.backgroundColor = "black"
      framedoc.body.style.margin = 0
      unless $frame.contents().find("#animateCss").length > 0
        # ver = Math.random()
        ver = '2.6.0'
        $head = $frame.contents().find("head")
        $head.append $('<link/>',
          rel: 'stylesheet'
          href: '/css/animate.min.css?ver=' + ver
          type: 'text/css'
          id: "animateCss")

    getContentFromCt: (ct) ->
      # console.log ct
      unless ct.m or ct.m is ""
        return "Error: no text"
      if ct._for is 'user'
        return ct.m
      # ret =
      # '<div>
      #   <div style="
      #     ' + ct.pos + '
      #     position: absolute;
      #     height: 0;
      #     z-index: 10;">
      #     <div class="animated long ' + ct.ani + ' " style="
      #       font-size: 19px;
      #       text-align: center;
      #       width: 100%;
      #       display: block;
      #       color: white;
      #       padding: 10px;
      #       box-sizing: border-box;
      #       -webkit-box-sizing: border-box;
      #       ">
      #       ' + ct.m +
      #       '
      #     </div>
      #   </div>
      #   <div>
      #     <img src="' + ct.bg + '" style="
      #       position: relative;
      #       width: 100%;
      #       height: 100%;
      #       min-height: 450px;
      #   " >
      #   </div>
      # </div>'
      # console.log ct.bg
      ret = '
      <div style="" class="editPrev">
         <div style="position: absolute;  z-index: 10;width: 100%;height: 100%;  min-height: 360px; background-size: 100% auto;
         background-position: center ' + (ct.bgPos or 'center') + ';
         background-repeat: no-repeat;
         background-image: url(\'' + (ct.bg or '') + '\');
         background-color: black;
         ">
            <div class="animated long ' + (ct.ani or 'fadeInUp') + ' " style="' + (ct.tbg or '')  + 'width: 100%;  color: white; padding: 10px; box-sizing: border-box;
              position: absolute; ' + (ct.pos or 'top:10%;') + '">
              ' + ct.m + '
              </div>
         </div>
      </div>
      '
      return ret

    setPreviewScale: (index) ->
      setPreviewCss = (w, h, s, lo) ->
        f = $("#frame-preview")
        s = 'scale(' + s + ')'
        # console.log h + ':' + s
        # lo = 10
        f.css('height', h + 'px')
        f.css('width', w + 'px')
        f.css('margin-left', lo + 'px')
        # f.css('-webkit-transform-origin', lo + 'px 0')
        f.css('transform', s)
        f.css('-webkit-transform', s)


      setPreviewText = (index) ->
        sizes = ['fs-tip-sm', 'fs-tip-md', 'fs-tip-lg', 'fs-tip-pd', 'fs-tip-pc']
        # console.log index
        for i in [0..sizes.length - 1]
          # console.log i is index
          if i is index
            $('#' + sizes[i]).css('display', 'block')
          else
            $('#' + sizes[i]).css('display', 'none')


      index = parseInt(index)
      setPreviewText(index)
      devs = [[320, 568], [375, 627], [414, 736], [708, 1024], [1366, 768]]
      width = devs[index][0]
      height = devs[index][1]
      displayHeight = $("#frame-preview-wrapper").height()
      displayWidth = $("#frame-preview-wrapper").width()
      scaleW = parseInt(displayWidth) / width
      scaleH = parseInt(displayHeight) / height
      scale = Math.min(scaleW, scaleH)
      scaledWidth  = Math.max(width * scale, width)
      scaledHeight = Math.max(height * scale, height)
      leftOffset = if width * scale < displayWidth then ( displayWidth - width * scale ) / 2 else 0
      # leftOffset = 100
      setPreviewCss(scaledWidth, scaledHeight, scale, leftOffset)

    filterAndGetTitle: (url, body) ->
      that = this
      extractDomainAndPath = (url, mode) ->
        domain = ''
        l = document.createElement("a")
        l.href = url
        domain = url.split("/")[0] + "//"
        domain += l.hostname
        if mode isnt 'domain'
          domain += l.pathname
        domain

      rewriteSrc = (that) ->
        try #wechat src is data:base64
          hasSrc = $(that).attr("src")
          return true unless hasSrc
          dp = extractDomainAndPath(amGloble.domain, 'domain')
          unless ((hasSrc.indexOf('http') > -1) or (hasSrc.indexOf('https') > -1) or (hasSrc.substr(0, 10) is 'data:image')) #
            if hasSrc[0] is '/'
              $(that).attr "src", dp + hasSrc
            else
              $(that).attr "src", dp + "/" + hasSrc
          true
        catch err
          console?.log err
          #filtBody += "Error : #{err.message or err.toString()}"

      ret = {}
      $body = $('<div>').html(body)
      ret.title = $body.find("title").text()
      #filter realtor force
      ret.title = ret.title.replace(/realtor\s+force/i, '') if ret.title
      ret.desc = $body.find("meta[name=description]").attr("content")
      filtBody = body


      try
        $body.find("img").each ->
          inline_style = undefined
          on_error = undefined
          inline_style = $(this).attr("style")
          $(this).attr "style", ""  if inline_style

          on_error = $(this).attr("onerror")
          $(this).attr "onerror", ""  if on_error

          rewriteSrc(this)

          data_src = $(this).attr("data-src")
          src = $(this).attr("src")
          #wechat as special rule of write src
          if /^(http|https):\/\/mmbiz.qpic.cn/i.test url
            src = null if /^data:image/.test src
          $(this).attr "src", data_src if data_src and not src

          on_load = $(this).attr("onload")
          $(this).attr "onload", ""  if on_load

          $(this).css "max-width", "100%"
          $(this).css "height", "auto"

        $body.find("iframe").each ->
          on_error = $(this).attr("onerror")
          $(this).attr "onerror", ""  if on_error

          on_load = $(this).attr("onload")
          $(this).attr "onload", ""  if on_load
          $(this).attr "style", "max-width:100%"

          iframe_src = $(this).attr("src")
          unless iframe_src
            $(this).remove()
            # skip this
            return true

          rewriteSrc(this)

        $body.find("video").each ->
          on_error = $(this).attr("onerror")
          $(this).attr "onerror", ""  if on_error

          controls = $(this).attr("controls")
          if not controls?
            $(this).attr "controls", ""

          on_load = $(this).attr("onloadonloadstart")
          $(this).attr "onloadonloadstart", ""  if on_load
          $(this).attr "style", "max-width:100%"

          rewriteSrc(this)

        $body.find("a").each ->
          inline_style = $(this).attr("style")
          $(this).css "position", "relative"  if inline_style
          if hasUrl = $(this).attr("href")
            filter = /brjtools\.cn|schoolinfo\.ca|9lms\.com|realsforce\.ca|realtorforce\.ca/i
            if (filter.test(amGloble.domain) and (hasUrl.indexOf('http') isnt 0)) or (filter.test(hasUrl))
              $(this).remove()
              return true
            dp = extractDomainAndPath(amGloble.domain)
            $(this).attr "href", dp + "/" + hasUrl  unless (hasUrl.indexOf('http') is 0)

        filtBody = $body.html()
      catch err
        console?.log err
        filtBody += "Error : #{err.message or err.toString()}"

      #TODO: toutiao uses article, can ignore rest
      # knownSources = [{wechat:/^(https|http):\/\/([a-zA-Z\d-]+\.)*weixin\.qq\.com/i},
      #                 {toutiao:'/^(https|http):\/\/([a-zA-Z\d-]+\.)*toutiao\.com/i'}]

      src = that.getUrlSrc(url)
      srcType = ""
      if /^(https|http):\/\/youtu\.be\/([a-zA-Z\d-]+)*/i.test url
        srcType = 'youtubeApp'
      switch src
        # embed youtube? <iframe width="560" height="315" src="https://www.youtube.com/embed/fM7xFNIjwJg" frameborder="0" allowfullscreen></iframe>
        when 'wechat'
          filtBody = filtBody.replace(/<head[^>]*>[\s\S]*?<\/head>/g, "")
          #filtBody = filtBody.replace(/<header[^>]*>[\s\S]*?<\/header>/g, "")
          filtBody = filtBody.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/g, "")
          filtBody = filtBody.replace(/<css[^>]*>[\s\S]*?<\/css>/g, "")
          filtBody = filtBody.replace(/<style[^>]*>[\s\S]*?<\/style>/g, "")
          # TODO: update link filter
          filtBody = filtBody.replace(/<link\b.+href=.*>/g, "")
          filtBody = filtBody.replace(/<meta\b.+name=.*>/g, "")
          $body = $('<div>').html(filtBody)
          for i in ["#activity-name", "js_cmt_mine", ".rich_media_title" ,"title", ".rich_media_meta_text", ".rich_media_meta_nickname", "#js_view_source"]   #"#post-user",
            $body.find(i).remove()
          filtBody = $body.html()
          break
        when 'youtube'
          if srcType is 'youtubeApp'
            youtubeID = url.split('.be/')[1]
          else
            youtubeID = url.split('watch?v=')[1]
          filtBody = "<div>"
          filtBody += '<iframe width="100%" height="315" src="https://www.youtube.com/embed/' + youtubeID
          filtBody += '" frameborder="0" allowfullscreen></iframe>'
          filtBody += '</div>'
          break
        else
          filtBody = filtBody.replace(/<head[^>]*>[\s\S]*?<\/head>/g, "")
          filtBody = filtBody.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/g, "")
          filtBody = filtBody.replace(/<css[^>]*>[\s\S]*?<\/css>/g, "")
          filtBody = filtBody.replace(/<style[^>]*>[\s\S]*?<\/style>/g, "")
          # $body = $('<div>').html(filtBody)
          # filtBody = $body.text()
          $body = $('<div>').html(filtBody)
          $article = $body.find('article')
          if $article and ($article.length > 0 ) and ($article.html()?.length  > 800)
            try
              $body = $('<div>').html($article.wrap('<p>').parent().html())
            catch err
              console.log err.toString()

          $body.filter('.header, .menu, .nav, .box-fix-l, .box-fix-r').remove() #(thisClass in ['header', 'menu','nav','box-fix-l', 'box-fix-r'])
          $body.filter('link, select, input, button, meta, footer, nav, form').remove() #or (tn in ['select','input', 'button', 'meta', 'footer', 'nav','form'])

          $body.find("*").each ->
            tn = $(this).prop("tagName").toLowerCase()
            ptn = if $(this).parent() then $(this).parent().prop("tagName").toLowerCase() else ''
            unless tn in ['img' , 'iframe' , 'video' , 'a']
              role = $(this).attr("role")
              thisClass = if $(this).attr('class') then  (if $(this).attr('class').length > 0 then $(this).attr('class').split(' ')[0] else '') else ''
              display = $(this).css('display')
              contentL = $(this).text().length
              #might wrap img in p or div with alt empty
              if (role in ['navigation', 'contentinfo', 'banner', 'search']) or (display in ['none']) or (contentL < 0) or (( (tn is 'li') and ( ptn isnt 'ul') ) and ( (tn is 'li') and ( ptn isnt 'ol') ) ) or (tn in ['select', 'input', 'button', 'link', 'meta', 'footer', 'nav', 'form', 'base']) or (thisClass in ['header', 'menu', 'nav', 'box-fix-l', 'box-fix-r'])
                #may cause prob in loop, better remove before
                $(this).remove()
                return true # continue or skip

              if tn is 'a'
                try
                  $(this).replaceWith('<span>' + $(this).text() + '<span>')
                catch err
                  RMSrv.dialogAlert err.toString()
                  console.log err.toString()
                return true

              inline_style = $(this).attr("style")
              $(this).css "position", "relative"  if inline_style
              $(this).css "height", "auto"  if inline_style
              inline_style = $(this).attr("style")

              on_error = $(this).attr("onerror")
              $(this).attr "onerror", ""  if on_error

              on_click = $(this).attr("onclick")
              $(this).attr "onclick", ""  if on_click

              on_load = $(this).attr("onload")
              $(this).attr "onload", ""  if on_load

              hasClass = $(this).attr("class")
              $(this).attr "class", ""  if hasClass

              hasId = $(this).attr("id")
              $(this).attr "id", ""  if hasId

              # TODO: fix this, remove all attributes but style
              # for i in this.attributes
              #   $(this).removeAttr(i.name)

              #only keep inline style
              #$(this).attr("style", inline_style)

          filtBody = $body.html()
          filtBody = "<div style='font-size: 14px;'>" + filtBody + "</div>"
          break
      ret.body = filtBody
      ret

    savePropCard: (opt) ->
      that = this
      @_datas.pageData.card.meta.title = @_doms.metaTitle.val()
      @_datas.pageData.card.meta.editor = @_doms.metaEditor.val()
      @_datas.pageData.card.meta.desc = @_doms.metaDesc.val()
      @_datas.pageData.card.meta.custvc = @_doms.metaVc.val()
      @_datas.pageData.card.meta.addr = @_doms.metaAddr.val()
      @_datas.pageData.card.meta.tp =  @_datas.pageData.card.tp
      $("#share-title").html @_doms.metaTitle.val()
      $("#share-desc").html @_doms.metaDesc.val()
      # opt.meta = that.setMetaImg(opt.seq, opt.meta)
      share_avt = $("#share-avt").html()
      if that.isOwnImg(opt.meta.img) or (RMSrv.ver >= '3.1.0')
        $("#share-image").html opt.meta.img + '#'
      else
        # has no own pic, reset to user avt
        $("#share-image").html share_avt

      if opt.meta.img?.indexOf('/img/noPic.png') > -1
        # console.log opt.meta.img
        # opt.meta.img = share_avt
        $("#share-image").html share_avt


      $("#loading-bar-spinner").css "display", "block"
      $.ajax(
        url: amGloble.savePropCard
        type: 'post' #default: 'GET' # dataType: 'json' #default: Intelligent Guess
        contentType: "application/json; charset=utf-8"
        dataType: "json"
        data: JSON.stringify(opt)
      ).done((ret) ->
        $("#loading-bar-spinner").css "display", "none"
        if ret.success
          that._datas.pageData.card._id = ret._id
          window._id = ret._id  if ret._id
          that.enableBtns()
        else
          console.log "Save Error"
        return
      ).fail(->
        flashMessage "server-error"
        return
      )
      null

    getData: ->
      that = this
      getDataMap = (type, action) ->
        if action is "edit"
          cb = (ret) ->
            data = that._datas.pageData = ret
            if data.success
              window._id = data._id
              that.initData()
            else
              RMSrv.dialogAlert data.err
          return {
            fn: $.ajax
            cfg: {url: amGloble.getPropCardInfo, type: 'get', data: {}}
            done: cb
          }
        else if type is 'topic'
          return {
            update: ->
              # $("#tpSelect").val "Topic"
              $("#loading-bar-spinner").css "display", "block"
            fn: that.getPageContent
            data: amGloble.domain
            cb: (ret) ->
              $("#loading-bar-spinner").css "display", "none"
              if ret
                data = that._datas.pageData = that.filterAndGetTitle(amGloble.domain, ret.body)
                # $("#tpSelect").prop "disabled", "disabled"
                # $("#tpSelect").css "-webkit-appearance", "none"
                if amGloble.newsId
                  $.ajax(
                    url: amGloble.getNewsTitle
                    data: {}
                    type: 'post' #default: 'GET' # dataType: 'json' #default: Intelligent Guess
                  ).done((pret) ->
                    if pret
                      data.title = pret.tl
                      data.desc = pret.desc
                      that.initData()
                    else
                      flashMessage "server-error"
                    return
                  ).fail(->
                    flashMessage "server-error"
                    return
                  )
                else
                  that.initData()
              else
                flashMessage "url-error"
                setTimeout (->
                  window.location = "/1.5/wecard"
                ), 1000
          }
        else if type is 'listing'
          cb = (ret) ->
            if ret.success
              data = that._datas.pageData = ret
              if data.card and JSON.stringify(data.card).length <= 2
                $.ajax(
                  url: amGloble.getPropDetail
                  data: {}
                  type: 'post' #default: 'GET' # dataType: 'json' #default: Intelligent Guess
                ).done((pret) ->
                  if pret
                    prop = that._datas.pageData.prop = pret.detail
                    that.initData()
                  else
                    flashMessage "server-error"
                  return
                ).fail(->
                  flashMessage "server-error"
                  return
                )
              else
                that.initData()
            else
              RMSrv.dialogAlert "cannot get listing!"
          return {
            update: ->
              # $("#tpSelect").val "Listing"
            fn: $.ajax
            cfg: {url: amGloble.getPropCardInfo, type: 'get', data: {}}
            done: cb
          }
        else if type in amGloble.templateTypes
          return {
            update: ->
              # $("#tpSelect").val window.selectTpMap[type] or "Topic"
            fn: $.ajax
            cfg: {url: amGloble.getTemplate, type: 'get', data: {type: type}}
            done: (ret) ->
              if ret.success
                data = that._datas.pageData = ret.content
                that.initData()
              else
                flashMessage "server-error"
          }
        else
          alert 'error! unknown type of data'
          RMSrv.dialogAlert 'Error: unknown type of data'

      actions = getDataMap(amGloble.type.toLowerCase(), amGloble.action?.toLowerCase())
      actions.update() if actions.update
      actions.cfg = actions.data  unless actions.cfg
      actions.fn(actions.cfg).done(actions.done).fail(-> flashMessage 'server-error';return) if actions.done
      #this = actions in getPageContent
      actions.fn(that, actions.cfg, actions.cb) if actions.cb

    getMusicLinks: ->
      that = this
      $.ajax(
        url: amGloble.getMusicList
        data: {}
        type: 'get' #default: 'GET' # dataType: 'json' #default: Intelligent Guess
      ).done((ret) ->
        if ret
          d = that._datas.musicList = ret
          arrHtml = []
          i = 0
          len = d.l.length
          while i < len
            arrHtml.push "<li urls=" + d.l[i].url + " n=" + d.l[i].nm + "><a href=\"#\" adid=\"musicPlayer" + i + "\" class=\"mp3 icon\"><audio id=\"musicPlayer" + i + "\" loop=\"\" src=\"" + d.l[i].url + "\"  style=\"display:none;position:absolute;z-index:-11\"></audio></a><span>" + d.l[i].nm + "</span>"
            i++
          $("#bgs-mp3-ul").empty()
          $("#bgs-mp3-ul").html arrHtml.join("")
        else
          flashMessage "server-error"
        return
      ).fail(->
        flashMessage "server-error"
        return
      )

    isOwnImg: (src) ->
      #TODO: test can get image
      unless src
        return false
      isAbsolutePath = (src) ->
        return (src.indexOf('http') is 0)
      unless isAbsolutePath(src)
        return true
      # return true if src.indexOf('app.test') > -1 # app.test could be in the src, not necesary to be domainname
      if /^(https|http):\/\/([a-zA-Z\d-]+\.)*realmaster\.(com|cn)(\/\w+)+.(jpg|png)/i.test(src)
        return true
      return false

    setMetaImg: (body, meta) ->
      #if youtube https://i.ytimg.com/vi/XEMMUVEiDno/sddefault.jpg XEMMUVEiDno=id
      that = this
      processSrc = (src) ->
        return src if src.indexOf('http') is 0
        return src if src.indexOf('data:image') is 0
        return src if src.length > 1500
        domain = ''
        l = document.createElement("a")
        l.href = document.URL
        domain = document.URL.split("/")[0] + "//"
        domain += l.hostname
        domain + src
      setImg = (body, meta) ->
        preProcess = (imgs) ->
          ret = []
          for tmpImg in imgs
            if /^(http|https):\/\/mmbiz.qpic.cn/i.test i.src
              tmpImg.src = tmpImg.src.replace('/0?', '/320?')
              ret.push tmpImg
          ret
        try
          $body = $('<div>').html($(body?.trim() or ''))
        catch err
          body = "<div>" + (body?.trim() or '') + "</div>"
          $body = $(body)
        #problemsome
        imgs = $("img", $body)
        i = 0
        if imgs.length is 0
          meta.img = '/img/noPic.png'
          return meta
        #prefer own img
        while i <= imgs.length - 1
          if imgs[i].height > 110 and that.isOwnImg(imgs[i].src)
            meta.img = processSrc imgs[i].src
            return meta
          i++
        i = 0
        #if not our image
        preProcess(imgs) #wechat img give height of 0
        while i <= imgs.length - 1
          # alert imgs[i].src + "::" + imgs[i].height + '::' + imgs[i].clientHeight + '::' + imgs[i].offsetHeight
          if imgs[i].height > 140
            meta.img = processSrc imgs[i].src
            return meta
          i++
        meta
      if Array.isArray(body)
        i = 0
        # find img inside body array
        while i <= body.length - 1
          if that._datas.pageData.card and that.isFlyer(that._datas.pageData.card.tp, that._datas.pageData.card.meta?.shSty)
            meta.img = processSrc body[0].bg
            return meta
          else
            ret = setImg(body[i].m, meta)
            if ret?.img
              return ret
            if i is (body.length - 1) #last frame and no img
              meta.img = '/img/noPic.png'
              return meta
          i++
        meta
      else
        setImg body, meta

    initData: ->
      that = this
      data = @_datas.pageData
      user = data.user or {}
      card = data.card or {}
      prop = data.prop or {}
      meta = card.meta or shSty: "blog"
      type = amGloble.type
      action = amGloble.action
      card.tp ?= type

      card.id = amGloble.id
      card._id = window._id  if window._id
      card.meta = shSty: "blog"  unless card.meta
      window.shSty = meta.shSty
      # $('#previewContent').css('padding-top',$('#frameEditorModal .bar-header-secondary').height() + 44)
      initUserCard = (that, user) ->
        that.nki.attr "src", (user.avt or "/img/logo.png")
        that.nknm.html (user.fn or '') + " " + (user.ln or '')
        that.ctctWx.html user.wx
        # behaviour if no qrcd set here
        if user.qrcd
          that.ctctWxQr.attr('src', user.qrcd)
        # else
        #   that.ctctWxQrW.css('display', 'none')
        if user.grpqrcd
          that.ctctGrpQrcd.attr('src', user.grpqrcd)
        else
          that.ctctGrpQrcdW.css('display', 'none')
          # $('#userQrcd').css('width', '180px')
        that.ctctTel.html user.mbl
        that.ctctEml.html "<a style='color:white' href='mailto:" + user.eml + "?Subject=Hi%20From%20RealMaster'>" + user.eml + "</a>"
        userWeb = if user.web then (if user.web.indexOf('http') > -1 then user.web else ('http://' + user.web)) else ''
        that.ctctWeb.html "<a style='color:white' href='" + userWeb + "'>" + (user.web or '') + "  </a>"
        that.ctctCpny.html user.cpny
        that.ctctCpny_pstn.html user.cpny_pstn
      if action is 'edit'
        @enableBtns()
        @writeHtml()
        return
      else if type is "listing" #TODO:
        card.ml_num = amGloble.ml_num
        if JSON.stringify(card).length <= 200 or not card.seq
          card.meta.shSty = amGloble.shSty if amGloble.shSty
          @_doms.propType.html prop.type_own1_out + " " + (prop.style or "") + (prop.prop_type or "") + (prop.bus_type or "")
          @_doms.propBr.html prop.br + " + " + prop.br_plus
          @_doms.propKit.html prop.num_kit + " + " + prop.kit_plus
          @_doms.propPak.html (prop.gar_spaces or "") + " " + (prop.gar_type or "") + " " + prop.park_spcs
          @_doms.propBsmt.html prop.bsmt1_out + " " + (prop.bsmt2_out or "")
          @_doms.propBath.html prop.bath_tot + " " + (prop.bath_details or "")
          @_doms.propLot.html (prop.front_ft or "") + " x " + (prop.depth or "") + " " + (prop.lotsz_code or "") + " " + (prop.irreg or "")
          @_doms.propExt.html prop.constr1_out + " " + prop.constr2_out
          @_doms.propTax.html prop.taxes + " / " + prop.yr
          @_doms.propSqft.html prop.sqft
          @_doms.propAC.html prop.a_c
          @_doms.propCVC.html prop.central_vac
          @_doms.propAge.html prop.yr_built
          @_doms.propPool.html prop.pool
          @_doms.propFuel.html prop.fuel + " " + prop.heating
          @_doms.propRltr.html prop.rltr
          @_doms.propRemark.html prop.ad_text
          @_doms.propPrice.html '$' + prop.lp_price
          initUserCard(@_doms, user)
          detailHtml = $("li[data-role=\"prop-detail-pane\"]").html()
          remarkHtml = $("li[data-role=\"prop-remark-pane\"]").html()
          userHtml = $("li[data-role=\"ctct\"]").html()
          card.uid = user.id
          card.music =
            nm: "basical"
            url: "/musics/baical.MP3"
          card.bkgimg =
            url: "/wecardBgs/bg2.jpg"
            nm: "bg2.jpg"
          card.seq = [
            _for: "detail"
            m: detailHtml
          ,
            _for: "remark"
            m: remarkHtml
           ]
          if amGloble.shSty isnt 'blog'
            card.seq.push
              _for: "user"
              m: userHtml
          #+ prop.lp_price + " For " + prop.s_r
          descp = "<div class='des'>" + "<p>" + prop.addr + " " + prop.municipality + " " + prop.county + "</p><p>" + prop.type_own1_out + " " + prop.style + "</p></div>"
          imgArray = getTrebPicUrls(prop.pic_num, data.ml_num)
          imgArray.push window.location.origin + "/img/noPic.png" unless imgArray.length
          i = imgArray.length - 1
          while i >= 0
            card.meta.img = imgArray[i]  if i is 0
            imgStr = "<img src='" + imgArray[i] + "' alt='" + i + "' style='width:100%;'></img>"
            if amGloble.shSty is 'vt'
              tmp =
                _for: 'pic'
                pos:'top:10%;'
                ani:'fadeInUp'
                bg:imgArray[i]
                m: descp
                bgPos: 'center'
            else
              tmp =
                _for: "pic"
                m: imgStr + descp
            card.seq.unshift tmp
            i--
        data.card = card
        data.user = user
        @_datas.pageData = data
        @writeHtml()
      else if type is "topic"
        data = @_datas.pageData
        card = {}
        body = data.body or "Empty Content"
        meta = card.meta = shSty: "blog"
        card.uid = user.id
        data.card = card
        data.card.meta = that.setMetaImg(body, meta)
        card.tp = "topic"
        # meta.desc = data.desc or ("Shared  By " + (user.fn or 'RealMaster') + (user.ln or ''))
        meta.title = data.title or ""
        card.music =
          nm: "basical"
          url: "/musics/baical.MP3"
        card.bkgimg =
          url: "/wecardBgs/bg2.jpg"
          nm: "bg2.jpg"
        # body = $(body).wrap('<p>').parent().html()
        card.seq = [
          _for: "topic"
          m: "<div id='topic-content'>" + body + "</div>"
         ]
        @_datas.pageData.card = card
        @writeHtml()
      else if type in amGloble.templateTypes
        data = @_datas.pageData
        card = {}
        musicTmp = {}
        tmp2 = shSty: "blog"
        if type is 'xmas1'
          musicTmp =
            nm: "xmas1"
            url: "/musics/We_wish_you_a_merry_Christmas_clip.MP3"
        else if type is 'xmas2'
          musicTmp =
            nm: "xmas2"
            url: "/musics/Jingle_Bells_clip.MP3"
        else if type is 'spring_fest'
          musicTmp =
            nm: "spring_fest"
            url: "/musics/spring_fest.MP3"
        data.card.music = musicTmp
        body = data.card.seq or []
        if that.isFlyer(type)
          tmp2 =
            shSty: "vt"
          for i in data.card.seq
            if i._for is 'user'
              hasUserFrame = true
          unless hasUserFrame
            initUserCard(@_doms, user)
            userHtml = $("li[data-role=\"ctct\"]")[0].innerHTML
            userFrame =
              _for: "user"
              m: userHtml
            data.card.seq.push userFrame
        meta = card.meta = tmp2
        data.card.meta = that.setMetaImg(body, meta)
        # meta.desc = data.desc or ((vars.sharedBy or "Shared By ") + (user.fn or 'RealMaster') + ' '+ (user.ln or ''))
        # console.log card
        @writeHtml()
      else
        console.log "undefined type init data " + type
        # $("#tpSelect option").each ->
        #   $(this).remove()  if ($(this).val() is "Listing") or ($(this).val() is "Topic")
        alert 'Error unknown type write data'
        RMSrv.dialogAlert 'Error unknown type write data'


    setMeta: (meta) ->
      that = this
      @_doms.metaTitle.val meta?.title or @_doms.metaTitle.val()
      @_doms.metaEditor.val meta?.editor or @_doms.metaEditor.val()
      @_doms.metaDesc.val meta?.desc or @_doms.metaDesc.val()
      @_doms.metaVc.val meta?.custvc or @_doms.metaVc.val()
      @_doms.metaAddr.val meta?.addr or @_doms.metaAddr.val()
      # if meta.wmk
      #   $('.footer-icon-wmrk .fa').removeClass('fa-user-plus')
      #   $('.footer-icon-wmrk .fa').addClass('fa-check-square-o')
      # else
      #   $('.footer-icon-wmrk .fa').addClass('fa-user-plus')
      #   $('.footer-icon-wmrk .fa').removeClass('fa-check-square-o')
      $("#share-title").html @_doms.metaTitle.val()
      $("#share-desc").html @_doms.metaDesc.val()
      if that.isOwnImg(meta.img) or (RMSrv.ver >= '3.1.0')
        $("#share-image").html meta.img + '#'
      # if meta.shSty? and meta.shSty is "vt"
      #   $("#showStyleVT").addClass "active"
      #   $("#showStyleBlog").removeClass "active"

    writeHtml: ->
      that = this
      data = @_datas.pageData
      newFrameBtn = "<li class='item-add' dataRole='new-frame' style='height: 73px; text-align: center; font-size:27px; padding-top: 20px;'><div><a id='newFrame' class='fa fa-plus-circle' href='#', style='color: #666' /></div></li>"
      btnDelete = "<a href=\"#\" class=\"btn-r btn-delete  fa fa-trash\" /></a>"
      btnEdit = "<a href='#' class='btn-r btn-edit fa fa-edit edit-in-summernote' /></a>"
      btnSort = "<a href=\"#\" class=\"btn-r btn-sort  fa fa-chevron-circle-up\" /></a>"
      btnEye = "<a href=\"#\" types= " + "_for" + " class=\"btn-r btn-see fa fa-eye\" /></a>"
      ctrlButtons = btnEdit + btnDelete + btnSort + btnEye
      @_doms.ctrlButtons = ctrlButtons
      if data and not data.e
        type = amGloble.type.toLowerCase()
        card = data.card
        user = data.user
        if type is "listing" and (amGloble.action is "create")
          seq = card.seq
          li = ""
          prop = data.prop
          # arrHtml = [ "<ul class=\"items-ul\">" ]
          htmls = []
          unless seq
            seq = []
          descp = "" + prop.lp_price + " For " + prop.s_r + " " + prop.addr + " " + prop.municipality + " " + prop.county + " " + prop.type_own1_out + " " + prop.style + ""
          @_doms.metaDesc.html descp
          title = "" + prop.addr + " " + prop.municipality + " "
          @_doms.metaTitle.val title
          addr = title + ", " + (prop.municipality_district or "") + ", " + (prop.county or "")
          @_doms.metaAddr.val addr
          i = 0
          len = seq.length
          while i < len
            ct = seq[i]
            if amGloble.shSty is 'vt'
              $htmlObj = $(that.getContentFromCt(ct))
            else
              $htmlObj = $("<div>" + ct.m + "</div>")
            $htmlObj = $htmlObj.wrap("<li class='item-img edit-in-summernote' dataRole='prop-img-pane'><div></div></li>").parent().parent()
            # console.log $htmlObj.html()
            ct.m = $htmlObj.children("div")?[0]?.outerHTML if amGloble.shSty isnt 'vt'
            $htmlObj.append ctrlButtons
            htmls.push $htmlObj
            i++
          htmls.push newFrameBtn
        else if type is "topic"
          body = data.body
          seq = card.seq
          htmls = []
          unless seq
            seq = []
            return
          @setMeta card.meta
          i = 0
          len = seq.length

          while i < len
            ct = seq[i]
            htmlObj = "<li class=\"edit-in-summernote\">" + (ct.m or '') + "</li>"
            $htmlObj = $(htmlObj)
            $htmlObj.append ctrlButtons
            htmls.push $htmlObj
            # ct.m = $htmlObj.children("div")[0].outerHTML
            i++
          @_doms.topic.append ctrlButtons
          htmls.push newFrameBtn
          $("#edit-page-contents-ul").html htmls
          $("#topic-content").css "overflow-x", "hidden"
          #if topic show edit content
          $('#edit-page-contents-ul li:first-child')?[0]?.click() if amGloble.action?.toLowerCase() isnt "edit"
        else if type in amGloble.templateTypes or (amGloble.action?.toLowerCase() is "edit")
          seq = card.seq or []
          meta = card.meta
          shSty = meta?.shSty
          # arrHtml = [ "<ul class=\"items-ul\">" ]
          htmls = []
          i = 0
          len = seq.length
          while i < len
            ct = seq[i]
            try
              ctNode = $(ct?.m?.trim() or '<section></section>') # Rain: is this fix right?
            catch e
              ctNode = $('<section>' + (ct?.m?.trim() or '') + '</section>')
            # console.log ctNode
            if type in ["assignment", "exlisting", "event"] #these template has user info in it
              $ctObj = $(ct.m)
              findR = $ctObj.find("[data-role=tpl-nm]")
              if findR.length
                $ctObj.find("[data-role=tpl-nm]").html (user.fn or "") + " " + (user.ln or "")
                $ctObj.find("[data-role=tpl-tel]").html user.mbl
                $ctObj.find("[data-role=tpl-tel-call]").attr "href", "tel:" + user.mbl
                ct.m = $ctObj[0].outerHTML

            liStr1 = if ctNode.hasClass("dis") then "<li class=\"dis edit-in-summernote\"><div>" else "<li class=\"edit-in-summernote\"><div>"

            if @isFlyer(type, shSty) and ct._for isnt 'user'
              ctmView = this.getContentFromCt(ct)
              ctmView = liStr1 + ctmView + "</div></li>"
              $htmlObj = $(ctmView)
            else
              ct.m = liStr1 + ct.m + "</div></li>"
              $htmlObj = $(ct.m)
              ct.m = $htmlObj.children("div")?[0]?.innerHTML

            $htmlObj.append ctrlButtons
            htmls.push $htmlObj
            i++
          htmls.push newFrameBtn
          @setMeta meta
        else
          alert "unknown type write html type: " + amGloble.type.toLowerCase()
          htmls = []
        # $("#tpSelect").val window.selectTpMap[type] or "Topic"
        $("#edit-page-contents-ul").html htmls
        $("#edit-page-contents-ul").css "display", "block"
        $('#thumbImg').attr('src', card.meta.img) if card.meta.img
      else
        flashMessage "server-error"
  controller.init()

$(document).ready ->
  window.selectTpMap =
    listing: "Listing"
    "event": "Event"
    exlisting: "Exclusive Listing"
    assignment: "Assignment"
    blog: "Blog"
    xmas1: "Flyer"
    xmas2: "Flyer"
    spring_fest: "Flyer"

  keyboardShowHandler = (e) ->
    window.keyboardHeight = e.keyboardHeight
  previewPic = (input) ->
    if input.files and input.files[0]
      reader = new FileReader()
      reader.onload = (e) ->
        $("#previewImg").attr "src", e.target.result
      reader.readAsDataURL input.files[0]
  window.addEventListener "native.keyboardshow", keyboardShowHandler
  # tmpl = $.summernote.renderer.getTemplate()
  # $("#imgInputFiles").change ->
  #   previewPic this
  #
  # $.summernote.addPlugin
  #   name: "upLoadImg"
  #   init: (layoutInfo) ->
  #     $note = layoutInfo.holder()
  #     $note.on "summernote.update", ->
  #       $boldButton = $(this).summernote("toolbar.get", "bold")
  #       $boldButton.toggleClass("active").css color: "red"
  #
  #     $note.on "summernote.blur", ->
  #       $boldButton = $(this).summernote("toolbar.get", "bold")
  #       $boldButton.removeClass("active").css color: "inherit"
  #   buttons:
  #     select: ->
  #       tmpl.iconButton "fa fa-image",
  #         event: "select"
  #         title: "select image"
  #         hide: false
  #   events:
  #     select: (event, editor, layoutInfo) ->
  #       $editable = layoutInfo.editable()
  #       $summernote = $(".summernote")
  #       $summernote.summernote "saveRange"
  #       window.onEditImgSelect = true
  #       $(".summernote").summernote "blur"
  #       window.keyboard.close()  if window.keyboard and window.keyboard.isVisible
  #       toggleModal "imgSelectModal"
  initSummernote()

# ========================================================================
# * Ratchet: segmented-controllers.js v2.0.2
# * http://goratchet.com/components#segmentedControls
# * ========================================================================
not (->
  "use strict"
  getTarget = (target) ->
    i = undefined
    segmentedControls = document.querySelectorAll(".segmented-control .control-item")
    while target and target isnt document
      i = segmentedControls.length
      while i--
        return target  if segmentedControls[i] is target
      target = target.parentNode

  segmentControlHandler = (event) ->
    activeTab = undefined
    activeBodies = undefined
    targetBody = undefined
    targetTab = getTarget(event.target)
    className = "active"
    classSelector = "." + className
    return  unless targetTab
    activeTab = targetTab.parentNode.querySelector(classSelector)
    activeTab.classList.remove className  if activeTab
    targetTab.classList.add className
    return  unless targetTab.hash
    targetBody = document.querySelector(targetTab.hash)
    return  unless targetBody
    activeBodies = targetBody.parentNode.querySelectorAll(classSelector)
    i = 0

    while i < activeBodies.length
      activeBodies[i].classList.remove className
      i++
    targetBody.classList.add className
  # event.preventDefault()
  window.addEventListener "touchend", segmentControlHandler

  # function (e) { if (getTarget(e.target)) {e.preventDefault();} }
  window.addEventListener "click", segmentControlHandler
)()
