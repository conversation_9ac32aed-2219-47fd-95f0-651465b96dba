/***
 * Standard function call support for the client side.
 * @param fnName
 * @param param
 * @param cb
 *
 *
 * Standard function encaurages the client side to
 *  modulize UI parts.
 * Each UI module can call their counterpart server side functions
 *  using a unified fashion.
 * The underlying logic will take care of the grouping/de-grouping of
 *  the calls and responses.
 */
let _requestStdFn = null;
function getStandardFunctionCaller() {
  if (_requestStdFn) {
    return _requestStdFn;
  }
  var _reqQueue = [],
    _reqCount = 1,
    _reqTimeout = null;
  function _doRequest() {
    let allRequests = {},
      thisReqQueue = _reqQueue;
    _reqQueue = []; // reset instance queue for concurrent processing
    thisReqQueue.forEach((p) => {
      allRequests[p.id] = p.cb;
    });
    // console.log(thisReqQueue)
    fetch('/1.5/stdfun', {
      method: 'POST', // GET, POST, PUT, DELETE, etc.
      mode: 'cors', // no-cors, *cors, same-origin
      cache: 'no-cache', // default, no-cache, reload, force-cache, only-if-cached
      credentials: 'same-origin', // include, *same-origin, omit
      headers: {
        'Content-Type': 'application/json',
        // 'Content-Type': 'application/x-www-form-urlencoded',
      },
      redirect: 'follow', // manual, *follow, error
      referrerPolicy: 'no-referrer', // no-referrer, *no-referrer-when-downgrade, origin, origin-when-cross-origin, same-origin, strict-origin, strict-origin-when-cross-origin, unsafe-url
      body: JSON.stringify({ q: thisReqQueue }), // body data type must match "Content-Type" header
    })
      .then((response) => response.json())
      .then((ret) => {
        // console.log(ret);
        ret.r.forEach((r) => {
          allRequests[r.id](r.err, r.ret);
        });
      })
      .catch((err) => {
        console.log(err);
      });
  }
  function requestStdFn(fnName, param, cb) {
    _reqQueue.push({ fn: fnName, p: param, cb: cb, id: _reqCount++ });
    clearTimeout(_reqTimeout);
    _reqTimeout = setTimeout(_doRequest, 50);
  }
  _requestStdFn = requestStdFn;
  return requestStdFn;
}
