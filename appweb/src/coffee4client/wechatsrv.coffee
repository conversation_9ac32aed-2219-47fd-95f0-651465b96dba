RMSrv =
  getCookie: (cname)->
    name = cname + '='
    ca = document.cookie.split(';')
    for c in ca
      while (c.charAt(0) is ' ')
        c = c.substring(1)
      if c.indexOf(name) is 0
        return c.substring(name.length,c.length)
    ''
  init: ->
    # console.log 'initCalled'
    if wxConfig?
      wx.config wxConfig
    wx.error (err)->
      RMSrv.wxLastError = err
      console?.log err
    #   // config信息验证后会执行ready方法，所有接口调用都必须在config接口获得结果之后，
    # config是一个客户端的异步操作，所以如果需要在页面加载时就调用相关接口，
    # 则须把相关接口放在ready函数中调用来确保正确执行。对于用户触发时才调用的接口，则可以直接调用，不需要放在ready函数中。
    wx.ready ->
      # window.addEventListener 'load', (event)->
      setTimeout ()->
        # RMSrv.checkJsApi()
        RMSrv.setUpPreview()
        RMSrv.wxBeforeShare()
      ,300
  checkJsApi:()->
    wx.checkJsApi {
      jsApiList: ['updateAppMessageShareData','updateTimelineShareData','previewImage'],
      success:(r)->
        alert JSON.stringify r
      fail:(r)->
        alert JSON.stringify r
      error:(r)->
        alert JSON.stringify r
    }
  isIOS: -> /iPhone|iPad|iPod/i.test(navigator.userAgent)
  isAndroid: -> /Android/i.test(navigator.userAgent)
  isWeChat: -> /MicroMessenger/i.test(navigator.userAgent)
  isBlackBerry: -> /BlackBerry/i.test(navigator.userAgent)
  appendDomain:(url)->
    location = window.location.href
    arr = location.split("/")
    domain = arr[0] + "//" + arr[2]
    domain + url
  openTBrowser:(url)->
    window.location = url
    null
  showInBrowser: (url)->
    window.location = url
  getMeta: (doc)->
    meta = doc.querySelectorAll 'meta'
    ret = title:doc.title
    for m in meta
      ret[m.getAttribute('name')] = m.getAttribute('content')
    ret
  getShareImage: (doc)->
    if div = (doc.getElementById('content_div') or doc.body)
      for img in div.getElementsByTagName 'img'
        if img?.src
          return img.src
    'https://realmaster.com/img/logo.png'
  setUpPreview: ()->#Mozilla/5.0 (iPhone; CPU iPhone OS 6_1_3 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Mobile/10B329 MicroMessenger/5.0.1
    #init with selector
    _init = (s)->
      imgs = document.querySelectorAll(s)
      urls = []
      for img in imgs
        # 如果img包含no-preview class，跳过处理
        if img.classList?.contains('no-preview')
          continue
        if img.clientWidth > 200 and img.clientHeight > 200
          urls.push img.src if img.src and urls.indexOf(img.src) < 0
          img.addEventListener 'click', (e)->
            wx.previewImage
              current: e.target.src
              urls: urls
            return true
      return
    _init 'img'
    # _init 'img[data-filename]'
    return
  getShareInfo: (doc,share)->
    _getInfo = (key,name)->
      try
        if tmp = (doc.getElementById('wx-' + key) or doc.getElementById('share-' + key))
          share[name or key] = tmp.value or tmp.textContent
      catch e
        console?.log e
    for k,n of {title:'title',desc:'description',url:'url',image:'image'}
      _getInfo k,n
    share.image = RMSrv.getShareImage doc unless share.image?.length > 8
    share.url = (doc.URL or window.location.href) unless share.url?.length > 8
    share
  origin: ->
    window.location.origin or (window.location.protocol + "//" + window.location.hostname + if window.location.port then ':' + window.location.port else '')
  wxSuccess: ->
    # post to server with sharedEData
    xmlhttp = new XMLHttpRequest()
    updateUrl = RMSrv.origin() + '/mp/shared?data=' + RMSrv.sharedEData
    if /d\dw?\.realmaster/i.test window.location.hostname or /app\.test/i.test window.location.hostname
      # alert "wxShared"
      # alert updateUrl
      console.log 'test server share success'
    xmlhttp.open "POST",updateUrl, true
    #xmlhttp.setRequestHeader "Content-type","application/x-www-form-urlencoded"
    xmlhttp.send()
  wxCancel: ->
    # if /d\dw?\.realmaster/i.test window.location.hostname or /app\.test/i.test window.location.hostname
    #  alert "wxCancel"
  wxBeforeShare: ->
    if /i\.realmaster/i.test window.location.hostname or /app\.test/i.test window.location.hostname
      # alert "wxOnShare"
      console.log 'appweb wxBeforeShare'
    getShareId = ()->
      if share.url
        RMSrv.sharedEData = share.url.split('?')[0].split('/').pop() #TODO:
      return
    doc = window.document
    share = RMSrv.getShareInfo doc,RMSrv.getMeta(doc)
    opts =
      title:  share.title or "RealMaster Sharing"
      link: share.url
      imgUrl: share.image
      desc: share.description or "RealMaster App Sharing"
      success: RMSrv.wxSuccess
      cancel: RMSrv.wxCancel
      # error: (e)->console.error e
      fail: (e)->console.error e
      # complete: (r)->console.log r
    # console?.log opts
    getShareId()
    # 请注意，原有的 wx.onMenuShareTimeline、wx.onMenuShareAppMessage、wx.onMenuShareQQ、wx.onMenuShareQZone 接口，即将废弃。
    # 请尽快迁移使用客户端6.7.2及JSSDK 1.4.0以上版本支持的 wx.updateAppMessageShareData、wx.updateTimelineShareData接口。
    # user useing wx<v7.0
    # if wx?.updateAppMessageShareData
    wx.updateAppMessageShareData opts
    wx.updateTimelineShareData opts
    # else
    #   wx.onMenuShareTimeline opts
    #   wx.onMenuShareAppMessage opts
    # wx.onMenuShareQQ opts
    # wx.onMenuShareWeibo opts
  clearCache: ->
  dialogAlert: (msg='')->
    alert if 'string' is typeof msg then msg else msg.message or msg.toString()
  onReady: (cb)->
    cb()
    true
RMSrv.init()
