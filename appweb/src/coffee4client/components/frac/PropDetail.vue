
<template lang="pug">
div#contentWrapper.content(style="background-color: #f1f1f1;", :class="{oldVerBrowser:oldVer<PERSON>rowser,padding44:!noTitleBar}")
  div(v-if='showTermWhenDDF', v-cloak='')
    .backdrop
    .frame
      .iframe-wrapper(:style='{width:computedWith}')
        iframe(src='/terms', frameborder='0')
      .btn-wrapper
        button.btn.btn-positive.btn-block.btn-long(@click='agreeAndRegister()') {{_('I Agree')}}
  wechat-qr-modal(:owner.sync="owner", :qrcd.sync="qrcd", v-if="wId")


  header.bar.bar-nav(style="padding-right:0px;", v-if="!noTitleBar")
    a.icon.icon-close.pull-right.nobusy(
      data-sub='close'
      style="padding-right:10px; padding-left:10px;",
      @click="toggleModal('propDetailModal'),resetSlider();",
      v-if="((dispVar.isApp || btnClose) && !inFrame) || showClose",
      href='javascript:void 0')
    span.changeLang(v-if='fromShare')
      a.icon.pull-right(
        data-sub="change lang"
        href='javascript:void 0',
        v-for='lang in dispVar.languageAbbrObj',
        @click="createLangTemplate(locale)",
        v-show="isActive(lang.k)") {{lang.v}}
    h1.title {{_("RealMaster")}}
  div.bar.hover-bar(v-show="displayHoverBar")
    div.left
      div.price
        span.val(v-show="prop.priceValStrRed")
          //- span(v-if="prop.showSoldPrice && prop.sp && prop.status_en !== 'A'") {{prop.sp | currency '$' 0}}
          //- span(v-if="prop.status_en == 'A'")
          | {{prop.priceValStrRed}}
        span(v-show="prop.lp === 0 || prop.lpr === 0") {{_('To Be Negotiated')}}
      div.exrates(@click="openTBrowser('/1.5/tools/currency?p=' + prop.priceValStrRed.replace(/[\$,A-Za-z]/g,'') + '&nn=1',{nojump:true})",data-sub='exchange',data-query="target:price,place:1")
        div.exrates-wrapper
          div(v-for="(v,k) in exnames", style="height:20px;")
            | {{k}}:
            span  {{ exrates[k] * (prop.lp || prop.lpr) | currency('', 0) }}
    div.right
      div(v-show="!isRMProp() && showNotesIcon != 'personal'",data-sub="history", data-query='place:2',
        @click="showListingHistory()")
        span.ss.ss25.ss-history(:class="{disable: !dispVar.isApp}")
        //- img(src="/img/prop/hist.png", :class="{disable: !dispVar.isApp}")
        //- span.fa.fa-rmprop-history
        div.desc(:class="{disable: !dispVar.isApp}") {{_('History','Listing History')}}
      div(v-show="hasProvAndCity && !showPred",data-sub="estimate", data-query='place:2'
        @click="goEvaluation()")
        span.ss.ss25.ss-estimate(:class="{disable: !dispVar.isApp}")
        //- img(src="/img/prop/calc.png", :class="{disable: !dispVar.isApp}")
        //- span.fa.fa-rmcalc
        div.desc(:class="{disable: !dispVar.isApp}") {{_('Estimate', 'prop')}}
        div.badge {{histcnt}}
      div(v-show="hasProvAndCity && showPred", @click="goPrediction()",data-sub="estimate", data-query='place:2')
        span.ss.ss25.ss-estimate(:class="{disable: !dispVar.isApp}")
        //- img(src="/img/prop/calc.png", :class="{disable: !dispVar.isApp}")
        div.desc(:class="{disable: !dispVar.isApp}") {{_('Estimate','prop')}}
        div.badge(:class="{ai: prop.pred_sp}") {{_('AI')}}
      div(v-show="hasProvAndCity", @click="showNearby('sold')",data-sub="nearby sold", data-query='place:2')
        span.ss.ss25.ss-nearbySold(:class="{disable: !dispVar.isApp}")
        //- img(src="/img/prop/price.png", :class="{disable: !dispVar.isApp}")
        //- span.fa.fa-rmprice
        div.desc(:class="{disable: !dispVar.isApp}")
          span(v-show="isSale") {{_('Nearby Sold')}}
          span(v-show="!isSale") {{_('Nearby Leased')}}
      div(v-show="showNotesIcon == 'personal'", @click="gotoNoteEdit()",data-sub="personal notes", data-query='place:2')
        span.ss.ss25.ss-note
        div.desc {{_('Note')}}
        div.badge(v-show="hasSelfM == 1") {{hasSelfM}}
  div#detail-map-holder
  div.backdrop(v-show="showBackdrop", @click="closeModals()")
  watching-popup(:prop='prop', :watching-type='watchingType',:watch-event='watchEvent',:dispVar='dispVar')
  div.notesBtnPot(v-if="showNotesIcon == 'inReal'")
    button.notesBtn.claim(@click="openClaim()",data-sub="claim",v-if='prop.canClaim || prop.claimed')
      span.fa.fa-sold.noteIcon(:class='{active:prop.claimed}')
      span(v-if='prop.claimed') {{_('Claimed')}}
      span(v-else) {{_('Claim')}}
    button.notesBtn(@click="gotoNoteBrowse",data-sub="notes")
      span.fa.fa-rmmemo.noteIcon(:class='{active:hasSelfM > 0}')
      span(style="font-size:12px")
        | {{hasSelfM}}/{{totalCount}}
  div#listing-hist-modal.modal.modal-45pc.listing-hist-modal
    div.bar.bar-standard.bar-footer
      button.btn.btn-block.btn-long(@click="showListingHistory()") {{_('Cancel')}}
    div.content
      div.card(style="padding-bottom:5px;")
        div.on-off-market
          prop-on-off-market-list(
            :is-app="dispVar.isApp",
            show-all=true, 
            :prop="prop",
            :price-hist="priceHist",
            :his.sync='addrHist',
            :searchById.sync='searchById', 
            :isVip='dispVar.isVipUser',
            :isLoggedIn='dispVar.isLoggedIn',
            showed-all-records=true)
  div.bar.bar-standard.bar-footer.footer-tab(v-if="dispVar.isApp && (!inFrame||showShareIcon)")
    span.pull-left
      a.btn.btn-positive.btn-segment.no-border.brkg(@click="getContactRealtor({page:formPage,prop,hasWechat,src:'app'})",data-sub="book viewing")
        div.brkg-wrapper
          span(v-if="isRMProp()") {{_('Request Info')}}
          span(v-else-if="prop.status != 'A'") {{_('Contact Agent')}}
          span(v-else) {{_('Book Viewing')}}
    //- span.pull-left(v-if="isRMProp() && !inFrame")
    //-   a.btn.btn-positive.btn-segment.no-border.brkg(v-if="!showContactLandlord",@click="contactRealMaster()",:href="this.prop.rmcontact?'#signUpForm':'javascript:;'")
    //-     div.brkg-wrapper
    //-       //- img.agent-avt(:src="computedAgentSrc")
    //-       span {{_('Contact Agent')}}
    //-   a.btn.btn-positive.btn-segment.no-border.brkg(@click="showRMBrkgInfo()", v-if="showContactLandlord")
    //-     div.brkg-wrapper
    //-       span(v-show="prop.ltp !== 'rent'") {{_('Contact Agent')}}
    //-       span(v-show="prop.ltp == 'rent'") {{_('Contact Landlord')}}
    //- span.pull-left(v-if="!isRMProp() && !inFrame && (dispVar.isRealtor || fromMarket)")
    //-   a.btn.btn-positive.btn-segment.no-border.brkg(@click="showBrkgInfo()")
    //-     div.brkg-wrapper
    //-       img.agent-avt(:src="computedAgentSrc", v-if="dispVar.rltrTopAd")
    //-       span {{_('Contact Agent')}}
    //- span.pull-left(v-if="!isRMProp() && !inFrame && !dispVar.isRealtor")
    //-   //- new btn @allen, non-wechat leads to agent
    //-   a.btn.btn-positive.btn-segment.no-border.brkg(v-if="!dispVar.hasFollowedVipRealtor && !hasWechat",@click="contactRealMaster()",:href="this.prop.rmcontact?'#signUpForm':'javascript:;'")
    //-     div.brkg-wrapper
    //-       //- img.agent-avt(:src="computedAgentSrc")
    //-       span {{_('Contact Agent')}}
    //-   a.btn.btn-positive.btn-segment.no-border.brkg(v-if="!dispVar.hasFollowedVipRealtor && hasWechat",@click="findARealtor()",:href="this.prop.rmcontact?'#signUpForm':'javascript:;'")
    //-     div.brkg-wrapper
    //-       img.agent-avt(:src="computedAgentSrc")
    //-       span {{_('Contact Agent')}}
    //-   a.btn.btn-positive.btn-segment.no-border.brkg.followed(@click="showRMBrkgInfo()",v-if="dispVar.hasFollowedVipRealtor && dispVar.userFollowedRltr.mbl")
    //-     div.brkg-wrapper
    //-       img.agent-avt(:src="dispVar.userFollowedRltr.avt || '/img/icon_nophoto.png'")
    //-       span {{dispVar.userFollowedRltr.fnm || (dispVar.userFollowedRltr.fn+' '+dispVar.userFollowedRltr.ln)}}
    a.pull-right.share(v-if="showShareIcon || !inFrame",data-sub="share", href="javascript:;", @click="showSMB()")
      span.icon.sprite16-21.sprite16-1-1
      span.tab-label {{_('Share')}}
    a.pull-right.fav(@click='toggleFav()',data-sub="save", v-if="(showShareIcon || !inFrame) && dispVar.isLoggedIn")
      span.icon.sprite16-21(:class="{'sprite16-2-3':prop.fav,'sprite16-1-3':!prop.fav}")
      span.tab-label {{_('Save','save')}}
    a.pull-right.comment(v-if="!inFrame && dispVar.lang!='en' && !isRMProp()",data-sub="forum",href="javascript:;", @click="propertyComments()")
      span.icon.sprite16-21.sprite16-1-5
        span.comment-number(v-if="cmntNum>-1") {{computedCmntNum}}
      span.tab-label {{_('Forum','forum')}}
    a.pull-right.showing(href="javascript:;",data-sub="showing", @click="toggleShowing()",v-if="(showShowingIcon && dispVar.isRealtor)")
      span.icon.sprite16-21.sprite16-1-8
      span.tab-label {{_('Showing','showing')}}
    a.pull-right.editDetail(:href="'tel:'+prop.mbl",v-if='prop.mbl && prop.marketRmProp',data-sub="call")
      span.icon.sprite16-21.sprite16-3-7
      span.tab-label {{_('Call')}}
  div.slider-container(:style="{ height:computedImgHeight + 'px'}")
    div.slider(v-if="!picUrls || picUrls.length <= 0")
      div.slide-group
        div.slide(:style="{ height:computedImgHeight + 'px'}")
          img.wxImg(src='/img/noPic.png',data-sub="preview img", data-query='place:1', @click='previewPic($event);')
    div.swiper-container#prop-detail-photos(v-show="picUrls.length")
      div.swiper-wrapper
        div.lazy-load.swiper-slide(v-for="(url,$index) in preLoadPicUrls" :key="$index")
          img.lazy.detail-photo.no-preview(
            :id="$index",:src="url",:dataindex="$index",
            data-sub="preview img", data-query='place:1',
            @click="previewPic($event)",
            onerror="hanndleImgUrlError(this)",
            @load="setUpPreview($event, true)")
        div.swiper-slide(v-for="(url,$index) in lazyLoadPicUrls" :key="$index+3")
          lazy-image(
            :src="url",
            :imgclass="'detail-photo no-preview'",
            :load="$event => setUpPreview($event, true)",
            :alt="url",
            :dataindex="$index+3",
            :imgstyle="'width:100%;'",
            data-sub="preview img", data-query='place:1'
            )
    div.onimg-wrapper.left
      div.videoThumb-wrapper(href="javascript:void 0;", v-show="computedShowRelatedVideo", @click="showPropVideo()",data-sub="video", data-query='place:1')
        img#videoThumb(:src="computedShowRelatedVideo",:style="{width:computedVideoWitdhAndHeight[0]+'px',height:computedVideoWitdhAndHeight[1]+'px'}")
        span.fa.ss.ss18.ss-youtube-play
    div.onimg-wrapper.right
      div.intraInfo
        span#picUrlShow.imgidx.tour-url.intraActive(style="margin: 0px 8px 0px 2px;",v-show="picUrls.length > 0",data-sub="preview img", data-query='place:1', @click="previewPic($event)")
          span.red {{imgidx+1}}
          | /{{picUrls.length}}
        a#VTShow.tour-url(href="javascript:void 0;", v-show="prop.vturl",data-sub="virtual tour" @click="openVirtualTour()")
          span {{_('VT')}}
        span#layoutShow.imgidx.tour-url(v-if="showLayoutIcon", @click="openFloorPlan()")
          span {{_('Layout','floorplan')}}
        span#mapShow.imgidx(style="font-size:14px;",@click="openNearbyMap()",data-sub="open map", data-query='place:1')
          span.fa.fa-fw.fa-rmmap
    div.onimg-wrapper.top.right
      div.intraInfo
        span.imgidx(v-show="showRefreshPicButton", @click="refreshPic()",data-sub="refresh img")
          //- NOTE: 现在treb图片无下载，都没有phodl
          span(v-show="prop.src == 'TRB'") {{_('Update Pics')}}
          span(v-show="prop.src !== 'TRB' && prop.phodl") {{_('Update Pics')}}
          span(v-show="prop.src !== 'TRB' && !prop.phodl") {{_('Waiting for Update')}}
        span.imgidx(v-show="showAdminOptionsIcon",@click="showAdminOptions()",data-sub="map", data-query='place:1',style='font-size: 14px;')
          span.fa.fa-gear
  div.assignment-tip(v-show='showRmpropTip')
    span.sprite16-24.sprite16-5-4
    span {{_('The property is not represented by a verified real estate agent, or there might be fraudulent traps. Be cautious and discerning, take risks at your own expense.')}}
  div.prop-main-body
    div.prop-heading-wrapper
      //- isRealGroup can search del prop
      div(v-if="prop.del && (dispVar.isPropAdmin || dispVar.isRealGroup)") Hidden for non-admins/realGroup; del = true
      div(v-if="prop.rmdpho == 'N' && dispVar.isAdmin") Hides pic for non-admins; rmdpho = N
      div.card.user-card(v-if="wId && owner && owner.vip", style="margin:0;")
        div(v-if="owner.mbl", style="padding: 10px 10px;")
          div(style="display: block;  width: 100%; height: 56px;")
            div(style="width:55px; display:inline-block; height: 100%; vertical-align: top;")
              a(href="javascript:void 0")
                img(src="/img/user-icon-placeholder.png", :src="owner.avt", style="height: 55px; width: 55px; border-radius: 50%; border: 1px solid white;")
            div(style="width:calc(100% - 120px); display:inline-block; vertical-align: top;padding: 11px 0 0 8px;")
              div.one-line(style="color: black; font-size: 17px;")
                | {{ownerFnm}}
                span.cert {{_('CERT')}}
                //- img(src="/img/realtor.png", style="display:inline; height: 17px;  width: 17px;  vertical-align: middle; margin-left: 10px;")
              div
                p.one-line(v-if="prop.ltp == 'rent' && !prop.cmstn", style="font-size:11px") {{_('Landlord Rental')}}
                p.one-line(v-else, style="font-size:11px")
                  span(v-show="dispVar.lang == 'en' && owner.cpny_en") {{owner.cpny_en}}
                  span(v-show="dispVar.lang !== 'en' && owner.cpny_zh") {{owner.cpny_zh}}
            div(style="width:65px; display:inline-block; height: 100%; vertical-align: top; padding-top: 15px;  text-align: right;")
              div.btn.btn-positive.btn-outlined.round(@click="showAgentWesite()", style="border-radius:14px; width: 64px;")
                | {{_('Contact')}}
      div(style='margin-bottom:10px')
        div.quick-links
          div(@click="showMapSMB()",data-sub="operate map", data-query='place:1')
            span.ss.ss25.ss-map
            div.desc.iconDescMargin {{_('Map')}}
          //- div(@click="showCommunity({prop,dispVar,title:_('Map')})")
          //-   span.sprite30-25.sprite30-4-8(:class="{disable: !dispVar.isApp}")
          //-   div.desc(:class="{disable: !dispVar.isApp}") {{_('Community')}}
          div(v-if="!isRMProp()", @click="showListingHistory()",data-sub="history", data-query='place:1')
            span.ss.ss25.ss-history(:class="{disable: !dispVar.isApp}")
            div.desc.iconDescMargin(:class="{disable: !dispVar.isApp}") {{_('History','Listing History')}}
          div(v-show="hasProvAndCity && !showPred", @click="goEvaluation()",data-sub="estimate", data-query='place:1')
            span.ss.ss25.ss-estimate(:class="{disable: !dispVar.isApp}")
            div.desc.iconDescMargin(:class="{disable: !dispVar.isApp}") {{_('Estimate','prop')}}
            div.badge {{histcnt}}
          div(v-show="hasProvAndCity && showPred", @click="goPrediction()",data-sub="estimate", data-query='place:1')
            span.ss.ss25.ss-estimate(:class="{disable: !dispVar.isApp}")
            div.desc.iconDescMargin(:class="{disable: !dispVar.isApp}") {{_('Estimate','prop')}}
            div.badge(:class="{ai: prop.pred_sp}") {{_('AI')}}
          div(v-show="hasProvAndCity", @click="showNearby('sold')",data-sub="nearby sold", data-query='place:1')
            span.ss.ss25.ss-nearbySold(:class="{disable: !dispVar.isApp}")
            div.desc.iconDescMargin(:class="{disable: !dispVar.isApp}")
              span(v-show="isSale") {{_('Nearby Sold')}}
              span(v-show="!isSale") {{_('Nearby Leased')}}
          div(v-show="showNotesIcon == 'personal'", @click="gotoNoteEdit()",data-sub="personal notes", data-query='place:1')
            span.ss.ss25.ss-note
            div.desc.iconDescMargin {{_('Note')}}
            div.badge(v-show="hasSelfM == 1") {{hasSelfM}}
        div.match(v-if="prop.pnSrc && prop.pnSrc.length>0 && dispVar.isVipRealtor" @click.stop='popupSavedLogs()',data-sub="saved logs")
          span.fa.fa-radar
          span.pnSrc(v-if="src = prop.pnSrc[0]")
            span.name {{_('Added to')}}&nbsp;
              span(v-if='typeof(src.g) == "number"') {{src.gNm}}
              span(v-else) {{_("Showing")}}@{{src.dt}}
          span.count {{prop.pnSrc.length}}
          span.fa.fa-caret-down
        div.match(v-if="addedShowingLogs && addedShowingLogs.length>0 && dispVar.isRealGroup",style='overflow: auto;',@click='showLogs=!showLogs')
          span(v-if='showLogs')
            span.showingLog(v-for="log in addedShowingLogs",data-sub="colleague agent",  @click.stop='showAgentWesite(log)')
              img(v-show='log.avt', :src="log.avt")
              | {{log.nm}}
          span.link(v-else,data-sub="show colleague") {{_("Colleague Showed")}}
      div.card.Assign(v-if="dispVar.isApp && prop.marketRmProp")
          //- span(style="padding:10px", @click="getContactRealtor({page:'buyTrustedAssign',prop,hasWechat,src:'app'})") {{_('Buy Assignment')}}
          button.AssignBtn(style="padding:10px", @click="getContactRealtor({page:'saleTrustedAssign',prop,hasWechat,src:'app'})",data-sub="assign my count") {{_('Assign My Condos')}}
      div.brief-info
        div.addr
          div.line1(v-if="prop.addr")
            span(v-if="prop.addr") {{prop.origUnt || prop.unt}}  {{prop.addr + ', '}}
            span {{prop.formattedCity ? prop.formattedCity + ',' : ''}} {{prop.prov}} {{prop.zip}} {{prop.cmty? ' - ': ''}}
            span.link(@click='locationClicked("#location")',data-sub="scroll to community")
              span(v-show='prop.cmty',style='padding-right:10px;white-space:nowrap;') {{prop.cmty ? prop.cmty: ''}}
        div.prop-tags(v-if="(prop.tags && prop.tags.length) || (prop.pc && prop.status_en!== 'U')")
          span(v-if='(prop.tags && prop.tags.length)')
            span.prop-tag(v-for="tag in prop.tags") {{_(tag)}}
          span.prop-tag(v-if="prop.pc && prop.status_en!== 'U'")
            span.fa.price-caret(:class="{'fa-caret-up':prop.pc > 0,'fa-caret-down':!(prop.pc > 0)}")
            span {{prop.pc | currency('$',0)}}
        //- dispVar.isVipUser &&
        div.price-wrapper
          div
            div.prop-price-desc
              span.sold(v-if="prop.showSoldRedTag") {{_('Sold')}}
              span.val.price(v-if="prop.priceValStrRed",@click="openCurrencyTool(prop.priceValStrRed)",data-sub="exchange", data-query='target:price,place:1') {{prop.priceValStrRed}}
              img.exchange(src="/img/category-png/rmcat-exchange.png", alt="alt",v-if="showCurrencyIcon()",@click="openCurrencyTool(prop.priceValStrRed)",data-sub="exchange", data-query='target:price,place:1')
            div.prop-price-change(v-if="prop.askingPriceStr")
              span
                span {{_('Listed')}}&nbsp;
                span(:class="{'line':isPropUnavailable}") {{prop.askingPriceStr}}
                //- span.prop-price-per-ft(v-if="prop.pricePerSqft") ${{prop.pricePerSqft}}/sqft
          div.idbtn(@click='copyId()',data-sub="copy id")
            p {{computedShowID}}
              span.copy.fa.fa-copy(v-show='showCopyID')
        div.paddingBottom15
          div.prop-desc
            span(v-if="prop.saleTpTag || (prop.ltp == 'assignment' && prop.status_en == 'U')") {{prop.saleTpTag}}
            span(v-if="prop.rmPropDesc") {{prop.saleTpTag ? '&#183;' : ''}} {{prop.rmPropDesc}}
            span(v-if="prop.lstStr && (prop.lstStr != prop.saleTpTag) && prop.lst != 'Pc'") {{prop.saleTpTag ? '&#183;' : ''}} {{prop.lstStr}}
            span(v-if="!prop.marketRmProp")
              span.bold &#183; {{prop.dom}}
              span.grey {{_('Days on Website','prop')}}
            span.prop-slddt(v-if="prop.slddt") &#183; {{prop.slddt|dotdate}}
            span.prop-updated-mt(v-if="prop.formatedMt") &#183; {{_('Updated')}} {{prop.formatedMt}}
            span.grey(v-if="computedMergedID") &#183; Merged {{computedMergedID}}
          div(v-show="isSold")
            //- div(v-show="!isTrebProp && prop.showSoldPrice && prop.sp", style="font-size: 12px;") {{_('Data from Internet, no guarantee.')}}
            div(v-show="!prop.showSoldPrice", style="font-size: 12px;") {{_('According to regulations, sold price is suppressed here.')}}

          div.exrates(v-show="showExrates && Object.keys(exnames).length",style="margin-bottom:0",data-sub="exchange", data-query='target:price,place:2', @click="openTBrowser('/1.5/tools/currency?p=' + prop.lp + '&nn=1',{nojump:true})")
            div.exrates-wrapper
              div(v-for="(v,k) in exnames", style="height:20px;")
                | {{k}}:
                span  {{ exrates[k] * (prop.lp || prop.lpr) | currency('',0) }}
        div#investAnly.borderTop(v-if="estimateInfo",style="padding: 15px 0 0;")
          estimated-value(:estimate-info="estimateInfo",:from-share="fromShare",:is-share="Boolean(vars && vars.share)",@go-evaluation="goEvaluation")

        div.link.padding15-0.borderTop(@click="openCondoDes()",v-if="showCondoInfo && condoInfo.condoNm")
            div#ellipsisTitle
              span#condoNm.fa.fa-rm-building(style="font-size:13px;font-weight:normal;")
              span#condoNm {{condoInfo.condoNm}}
            div#condoDvlpr(v-if="condoInfo.condoDvlpr") {{_('Developer','building')}}:{{condoInfo.condoDvlpr}}
        div.padding15-0.borderTop
          main-info(:disp-var="dispVar", :prop="prop", :rows='mainInfoRows', :tax-list='taxList', iskeyfactsinfo=true)
        div.price-drop(v-if="prop && prop.uaddr")
          div.link(@click="popupWatch(prop.isBuilding?'building':'location','PC')",:data-sub="prop.isBuilding?'building':'location'+' watch'", data-query='place:1')
            span.fa.fa-rmalert-o(:class="{'fa-rmalert':prop.isWatching}")
            | {{sprintf(_('Sold Alert & %s Watch'), prop.isBuilding?'Building':'Location')}}
          div.link(@click="popupWatch('cmty')",v-if="prop.hasCmtyId",data-sub="community watch", data-query='place:1')
            span.fa.fa-rmalert-o(:class="{'fa-rmalert':prop.isCmtyFav}")
            | {{_('Community Watch')}}
        //- div.link(@click='locationClicked()')
        //-   span(v-show='prop.cmty',style='padding-right:10px;') {{prop.cmty ? prop.cmty: ''}}
        div.similar(v-if='prop.ptype_en=="Residential"')
          div.btn-line
            span(@click="goSimilarProps()",data-sub="similar") {{_('Similar Sold/Leased')}}
            span(v-show='prop.isBuilding' @click='goSimilarProps(true)',data-sub="same building") {{_('Same Building')}}
        div.my-listing(v-if="dispVar.isApp && !prop.marketRmProp && isRMProp()")
          div.err-report
            span {{sprintf(_('List your %s on Realmaster'),_(computedRmType))}}
            span.report-btn(@click="goMylisting()",data-sub="go mylisting") {{_('Post')}}
    div.card.accuracy(v-if="showAllEstimate || showSpEstimate")
      main-info(v-if="showAllEstimate" :prop="allEstimateInfo",:rows='accuracyInfo', iskeyfactsinfo=false)
      div(v-if="showSpEstimate")
        div.soldPriceHeard(@click="toggleSoldPrice")
          span(style="font-weight: bold;") Sold Price Est. Value
          span(style="font-size: 16px;" :class="{'icon icon-plus': !showSoldPriceEst, 'fa fa-rmminus': showSoldPriceEst}")
        main-info(v-show="showSoldPriceEst",:prop="allEstimateInfo",:rows='soldAccuracy', iskeyfactsinfo=false)
    div.content-list
      div.card.admin-card
        div.card-header(style='padding-right: 10px;')
          span {{_('Listing Activity','prop')}}
          //- span.prop-updated-mt.pull-right(v-if="prop.formatedMt") {{_('Updated')}} {{prop.formatedMt}}
        div.oh(v-if="prop.ohz && validOh.length")
          label {{_('Open House','prop')}}
          //- div.oh-msg
          //-   div.sprite16-18.sprite16-5-4
          //-   div.msg {{_('Due to COVID-19,Open House could be cancelled! Verify with your agent before you go.')}}
          div.sp-holder
            div.sp-wrapper(:style="{width:spWrapperWidth}")
              div.sp.day(v-for="oh in validOh", :style="{ width: calculatedSpWidth+'px' }",@click="onOpenHouseClicked(oh)",data-sub="open house")
                div.top
                  div.left {{oh.date}}
                  div.right
                    div.black
                      span {{_(oh.month,'month')}}&nbsp;{{oh.year}}&nbsp;{{_(oh.day)}}
                    div.hour {{oh.time}}
                    div.row-online(@click='toCalendarSelectTime(oh)',data-sub="add OH to calendar")
                      div.add-calendar
                        span.icon.sprite16-18.sprite16-1-2
                        span {{_('Calendar')}}
        div.verified(v-if="isRMProp()",style="padding:0px 10px 3px 10px;margin-top:0px;")
          div.listing-performance
            label(style="padding: 10px 5px 5px 0px;display: inline-block;") {{_('Listing Change','prop')}}
            span.statusTag(v-if="prop.saleTpTag",:class="{'red-bg': prop.tagColor === 'red', 'green-bg': prop.tagColor === 'green'}") {{prop.saleTpTag}}
          div.ver(v-show="prop.ver")
            span.fa.ss.ss16.ss-verify
            span.word {{_('Verified Listing')}}
          div.time
            label.col-5  {{_('Listing Verification')}}
            span.col-7
              span.verified-status(v-if='prop.isV')
                span.fa.fa-check-circle
                span {{_("Verified")}}
              span.verified-status(v-else)
                span.fa.fa-exclamation-circle
                span {{_("To be verified")}}
              span.block(v-show='dispVar.isAgreementAdmin')
                img(:src='prop.agrmntImg',alt='No image',@click='previewAgrmntImg($event);',data-sub="preview img", data-query='place:3')
                span.btn(v-if='prop.isV',@click='toggleVerifyAgreement(false)',data-sub="agreement verify") {{_("Unverify")}}
                span.btn(v-else,@click='toggleVerifyAgreement(true)',data-sub="agreement unverify") {{_("Verify")}}
              span.verified-status.block(v-show='dispVar.isAgreementAdmin')
                input(type="text", v-model="revokeReason",:placeholder="_('Revoke reason')")
                span.btn(@click='revokeProp()',data-sub="agreement revoke") {{_("Revoke")}}
              span.block(v-if='dispVar.isAssignAdmin')
                span {{_("Hide")}}
                span.btn(v-if='prop.private',@click='hideAssignListing(false)',data-sub="assignment unhide") {{_("Unhide")}}
                span.btn(v-else,@click='hideAssignListing(true)',data-sub="assignment hide") {{_("Hide")}}
          div.time(v-if="prop.marketRmProp? dispVar.isAssignAdmin:true")
            label.col-5   {{_('Last updated','rmprop')}}
            span.col-7  {{prop.mt | dotdate}}
          div.time(v-if="prop.ltp=='assignment' && (prop.marketRmProp?dispVar.isAssignAdmin : dispVar.isVipRealtor)",@click="openCurrencyTool(prop.origpr)",data-sub="exchange", data-query='target:price,place:3')
            label.col-5  {{_('Original price')}}
            span.col-7 {{prop.origpr}}
              img.exchange(src="/img/category-png/rmcat-exchange.png", alt="alt")
          div.time(v-if="prop.ltp=='assignment' && (prop.marketRmProp?dispVar.isAssignAdmin : dispVar.isVipRealtor)",@click="openCurrencyTool(prop.dpst)",data-sub="exchange", data-query='target:dpst')
            label.col-5  {{_('Deposit paid','propertyListing')}}
            span.col-7 {{prop.dpst}}
              img.exchange(src="/img/category-png/rmcat-exchange.png", alt="alt")
          div.cmstn(v-show="prop.cmstn && (prop.marketRmProp?dispVar.isAssignAdmin : dispVar.isRealtor)")
            label.col-5  {{_('Commision')}}
            span.col-7 {{prop.cmstn}}
          div.err-report
            span {{_('Is this a phishing listing?')}}
            span.report-btn(@click="reportError()",data-sub="report phishing") {{_('Report')}}
        div.on-off-market(v-else)
          //- label {{_('On Off Market','prop')}}
          prop-on-off-market-list(
            :is-app="dispVar.isApp",
            :prop="prop",
            :price-hist="priceHist",
            :his.sync='addrHist',
            :searchById.sync='searchById', 
            :isVip='dispVar.isVipUser',
            :isLoggedIn='dispVar.isLoggedIn',
            showed-all-records=false,
            @show-listing-history="showListingHistory")
        div(v-if="prop.aSIDs && prop.aSIDs.length",style="padding: 10px 0 0 26px;")
          div.listing-performance(style="margin-bottom: 10px")
            span(style="font-size: 14px;color: #000;") {{_('Associate Listing')}}
          div.card-content(style="padding-left: 0;")
            div.prop-price-desc.flexAlignCenter.prop-promote.aSid(v-for="aProp in prop.aSIDs")
              span.link {{aProp.sid || aProp.id}}
              span.view.pull-right(v-if="dispVar.isLoggedIn",@click='searchById(aProp)') {{_('View')}}
              span.view.pull-right(v-else,@click='redirectLogin(true)') {{_('Login To View')}}            
        div(v-if="showListingInsight && (prop.marketRmProp? dispVar.isAssignAdmin:true)")
          div.listing-performance(v-show='showTopStats || showBoostBtn || prop.topup_pts')
            label {{_('Listing Performance','prop')}}
          div.card-content
            div.prop-promote(v-if="(isUserTopListing || dispVar.isAdmin || dispVar.isProdSales) && prop.topup_pts")
              b(v-if="!isTopExpired") {{_('Promotion Expires:')}}
              b(v-if="isTopExpired") {{_('Promotion Expired:')}}
              span(style="color:#E03131;margin-left:5px;") {{prop.topTs|dotdate}}
              a.pull-right(@click="boostListing()", v-show="computedShowRenew",data-sub="renew top listing")
                span {{_('Renew')}}
                span.icon.icon-right-nav(style="font-size:14px;color:#666;margin-left:10px;")
            div.prop-stats(v-if="showTopStats")
              div.prop-count
                div.cnt {{prop.vc||0}}
                div.unt {{_('Views')}}
                div.stats(v-if="dispVar.isAdmin") {{prop.vcapp||0}}APP | {{(prop.vcappr||0)+(prop.vcr||0)}}R | {{(prop.vcappc||0)+(prop.vcc||0)}}C
              div.prop-count
                div.cnt {{prop.shr || prop.shrp || 0}}
                div.unt {{_('Shares')}}
                div.stats(v-if="dispVar.isAdmin") {{(prop.shrc||0)+(prop.shrappc||0)}}C | {{(prop.shrr||0)+(prop.shrappr||0)}}R | {{prop.shrp||0}}P
              div.prop-count
                div.cnt {{prop.favcnt||0}}
                div.unt {{_('Favorites')}}
          div.card-content.top-desc(v-if="showBoostBtn")
            div.desc
              | {{_('Highlight your listing at the top of the search results to ensure maximum exposure.')}}
          div.links(v-show="showBoostBtn")
            //- v-if="showTopLinks()" a.top-listing(v-if="dispVar.isRealtor && !prop.topup_pts",@click="showPays()") {{_('TOP Listing')}}
            a.top-listing(@click="boostListing()",data-sub="top listing") {{_('Top This Listing Now','topListing')}}
      div.card.location-card#location
        div.card-header
          span {{_('Location')}}
          span.pull-right(@click="showMapSMB()",data-sub="operate map", data-query='place:2')
            span.icon-desc {{_('Map')}}
            span.icon.icon-right-nav
        div.card-content.content-padded
          .tabs
            a.tabs-container.location-container
              .tab.pagination.locationTags(v-for='(tag,idx) in locationTags',:data-sub="'tab '+tag.k", @click='selectTag(tag)', :key='idx', :class="locationTag == tag.k?'selected':''")
                div(:data-sub="'tab '+tag.k") {{_(tag.desc)}}
                .red-border(:class='calcDirection(tag.k,idx)',:data-sub="'tab '+tag.k")
          div(v-show="locationTag =='cmty' && ((prop.bndCmty && prop.bndCmty.nm)|| prop.origCmty || prop.cmty)")
            div.prop-cmty-wrapper(@click="openNearbyMap()", :id="prop._id",data-sub="open map", data-query='place:2')
              lazy-image.prop-cmty-image(v-if="prop.cmty && prop.cmty_image",
                :src="prop.cmty_image.fn",
                :imgstyle="'width:100%;border-radius:5px;vertical-align: middle;'",
                :emit="false",
                :error="cmtyImageError"
              )
              span.prop-pos-on-cmty(v-if="prop.cmty_image")
              span.open-full-screen-on-cmty(v-if='prop.cmty_image')
                span.fa.fa-full-screen
                span.txt {{_('View in full map')}}
            div.prop-cmty-crossroad.prop-cmty-header(style="padding-top:0px;")
              span {{_('Community')}}
              span.cmty-nm {{prop.bndCmty?prop.bndCmty.nm:(prop.origCmty || prop.cmty)}}
            div.prop-cmty-crossroad.prop-cmty-header(v-show="prop.crsst")
              span {{_('Crossroad', 'prop')}}
              span.cmty-nm {{prop.crsst}}
            div.watchArea
              div.watchBlock(v-if="prop && prop.hasCmtyId")
                span
                  span.txtTitle {{_('Community Watch')}}
                  span.txt {{`${_('Receive updates on new and sold homes in')} ${prop.bndCmty.nm}, ${prop.city}`}}
                span.watchBtn(@click="popupWatch('cmty')",data-sub="community watch", data-query='place:2')
                  span.fa(:class="prop.isCmtyFav?'fa-rmalert':'fa-rmalert-o'")
                  span.text {{_(getCmtyFavText())}}
              div.watchBlock(v-if="prop && prop.uaddr")
                span(v-if='prop.isBuilding')
                  span.txtTitle {{_('Building Watch')}}
                  span.txt {{_('Receive updates on new and sold homes in this building')}}
                span(v-else)
                  span.txtTitle {{_('Location Watch')}}
                  span.txt {{_('Receive updates (sold, off-market and price change) on this property or homes around this location')}}
                span.watchBtn(@click="popupWatch(prop.isBuilding?'building':'location')",:data-sub="prop.isBuilding?'building':'location'+' watch'", data-query='place:2')
                  span.fa(:class="prop.isWatching?'fa-rmalert':'fa-rmalert-o'")
                  span.text {{_(getWatchText())}}
          div(v-show="locationTag =='nearby'")
            div.links()
              a(href="javascript:;",data-sub="amenities",data-query='place:2'
                @click="showAmenity({prop,dispVar,title:_('Amenities')})")
                span.fa.ss.ss15.ss-shopping-cart
                | {{_('Amenities')}}
          div(v-show="locationTag =='commute'")
            div.links()
              div.locations
                div
                  span.addr(v-show='prop.daddr!="N"')
                    | {{prop.addr}}
                  span.desc
                    | {{prop.formattedCity}},&nbsp;{{prop.prov}}
              div.dottedLine
              div(v-for="(location,idx) in locList")
                commute-addr(:location='location',:idx='idx')
              div.desc {{_('You can save an address when using Commute feature. It is convenient if you often plan a commute to the address.')}}
          div(v-show="locationTag == 'demographics'")
            Demographics(:prop="prop" :censusId="getCensusId()" :censusYear="getCensusYear()" :loadData="locationTag == 'demographics'" :lang="dispVar.lang" :setPosOnMapImage="setPosOnMapImage")
        img(id="id_map_pic", style="height:150px; width: 100%; vertical-align: top;", v-show="(prop.lat !== undefined) && staticMaploaded", src="/img/noPic.png")
      div.card.card-segment
        div.card-header(style='padding-right: 10px;')
          span {{_('Details','prop')}}
        .tabs.segment
          a.tabs-container
            .tab.pagination(data-sub="tabsummary",@click="changeSegmentTag('itemSummary')",:class="{selected:activeSegment == 'itemSummary' }", href="#itemSummary")
              div {{_('Summary')}}
              .red-border(:class="calcTagDirection('itemSummary',0)")
            .tab.pagination(data-sub="tab detail",@click="changeSegmentTag('itemDetail')",:class="{selected:activeSegment == 'itemDetail' }", href="#itemDetail")
              div {{_('Detail')}}
              .red-border(:class="calcTagDirection('itemDetail',1)")
            .tab.pagination(data-sub="tab room",@click="changeSegmentTag('itemRoom')",:class="{selected:activeSegment == 'itemRoom' }", href="#itemRoom", v-show="prop.rm1_len || (prop.rms && prop.rms.length)")
              div {{_('Room')}}
              .red-border(:class="calcTagDirection('itemRoom',2)")
            .tab.pagination(data-sub="tab room",@click="changeSegmentTag('itemUnit')",:class="{selected:activeSegment == 'itemUnit' }", href="#itemUnit", v-show="prop.unitTypes")
              div {{_('Unit Type')}}
              .red-border(:class="calcTagDirection('itemUnit',3)")
            .tab.pagination(data-sub="tab insider",@click="showVIPSegment()",:class="{selected:activeSegment == 'itemVIP' }", href="#itemVIP",v-show="(isTrebProp && dispVar.isVipRealtor && dispVar.isTrebUser) || dispVar.isRealGroup || dispVar.isDevGroup")
              div {{_('Insider')}}
              .red-border(:class="calcTagDirection('itemVIP',4)")
            span.toggle.ftm.pull-right(v-show='activeSegment == "itemRoom" && (prop.rm1_len || prop.rms)',:class="{active:unit == 'Ft' }", @click="unit = unit == 'Ft' ? 'M' : 'Ft'",data-sub="switch area unit")
              span.toggle-handle
        div.card-content
          span#itemSummary.control-content(:class="{active:activeSegment == 'itemSummary' }")
            div.detailWrapper
              // 房源基本信息
              div
                label.col-5 {{_('ID','prop')}} :
                span(v-show="prop.id && prop.id.substr(0,2) == 'RM'")
                  | {{ prop.id }}
                  span(v-show="prop.ml_num || prop.sid")
                    | ({{prop.ml_num || prop.sid}})
                span(v-show="!isRMProp()")
                  span.treb-link(v-if="prop.src == 'TRB' && dispVar.isRealtor" @click="showInBrowser('http://www.torontomls.net/')",data-sub="open offical website")
                    | {{ prop.sid || prop._id }}
                  span(v-else)
                    | {{ prop.sid || prop._id }}
              div(v-show="prop.lstStr || prop.saleTpTag")
                label.col-5 {{_('Status','prop')}} :
                | {{prop.lstStr || prop.saleTpTag}}
              div(v-show="showDom")
                label.col-5 {{_('DOM','prop')}} :
                | {{prop.dom}} {{_('Days')}}
              div(v-if="prop.OwnershipType")
                label.col-5 {{prop.OwnershipType.n}} :
                | {{prop.OwnershipType.v}}
              div(v-if="prop.BusinessType")
                label.col-5 {{prop.BusinessType.n}} :
                | {{prop.BusinessType.v}}
                div(v-if="prop.BusinessSubType")
                  | {{prop.BusinessSubType.v}}
              div(v-show="prop.poss_date || prop.psn || prop.rmPsn_en")
                label.col-5 {{_('Possession')}} :
                span(v-show="prop.poss_date")
                  | {{prop.poss_date | dotdate}},
                span(v-show="prop.psn || prop.rmPsn_en")
                  | {{prop.rmPsn || prop.rmPsn_en}} ({{Array.isArray(prop.psn)?prop.psn.join(','):prop.psn}})
              div(v-show="prop.poss_type")
                label.col-5 {{_('Possession Type')}} :
                | {{prop.poss_type}}
              div(v-show="prop.rltr")
                label.col-5 {{_('Brokered By')}} :
                div.col-7 {{prop.rltr}}
              // 建筑信息
              div(v-show="prop.unt")
                label.col-5 {{_('Unit No.','propertyListing')}} :
                | {{prop.unt}}
              div
                label.col-5 {{_('Type','prop')}} :
                span.col-7 {{prop.ptype}} {{prop.ptype2?prop.ptype2.join(','):prop.pstyl}}
              div(v-show="prop.link_yn")
                label.col-5 {{_('Link','prop')}} :
                span {{_(prop.link_yn)}}
              div(v-show="prop.fce")
                label.col-5(@click="alertExplain('EXPOSURE')",data-sub="explain",data-query="type:exposure") {{_ab('fce','propertyListing')}} :&nbsp;
                  span.fa.fa-question-circle-o
                | {{prop.fce}}
              div(v-show="prop.bltYr || prop.age || prop.Age || prop.ConstructedDate || prop.rmBltYr || prop.bltYr1 || prop.bltYr2 || condoInfo.condoAge")
                label.col-5(v-if='prop.bltYr') {{_('Built Year')}} :
                label.col-5(v-else-if="prop.age||prop.Age") {{_('Age','prop')}} :
                label.col-5(v-else-if='prop.ConstructedDate') {{_('Constructed Date')}} :
                label.col-5(v-else) {{_('Built Year')}} :
                span(v-html='computedBltYr()')
              div(v-if="prop.tot_area")
                label.col-5 {{_('Total Area','prop')}}
                div.col-7 {{prop.tot_area}} {{prop.tot_areacd}}
              div(v-show="(prop.front_ft && prop.depth) || (prop.dim)")
                label.col-5 {{_('Lot Size')}} :
                div.sqft.col-7(v-if="prop.front_ft") {{prop.front_ft}} * {{prop.depth}} {{prop.lotsz_code}} {{prop.irreg}}
                div.sqft.col-7(v-else) {{prop.dim}} {{prop.lotsz_code}} {{prop.irreg}}
              div(v-if="prop.lotsz")
                label.col-5 {{_('Land Size')}}:
                div.col-7 {{prop.lotsz}} {{prop.lotszUt || ''}}
              div(v-show='showMeasure()')
                label.col-5 {{_('Above Ground Internal Size')}} ({{_('ft&sup2;','prop')}}):
                div.sqft(style="font-size:14px;",
                  v-show="prop.lat && prop.lng",data-sub="size measure"
                  @click="openMeasure()")
                  span.link(v-show="prop.sqft") {{parseSqft(prop.sqft)}}&nbsp;
                  span(v-show="prop.sqft && /\-/.test(prop.sqft) && (prop.sqft!=prop.rmSqft) && prop.rmSqft && dispVar.isLoggedIn")
                    span(v-show='prop.sqft') (
                    span(:class='{link:!prop.sqft}') {{parseSqft(prop.rmSqft)}}
                      b &nbsp;{{_('Estimated')}}
                    span(v-show='prop.sqft') )
                  span.link(v-show="prop.rmSqft && !dispVar.isLoggedIn",@click.stop='redirectPage()',data-sub="size measure")
                    span(v-if="prop.sqft") ({{_('Show Estimate')}})
                    span(v-else) {{_('Show Estimate')}}
                  span.link(v-show="!prop.sqft && !prop.rmSqft") {{_('Measure')}}
              div(v-if="prop.LivingAreaSF || prop.LivingAreaMetres")
                label.col-5 {{_('Living Area')}} ({{prop.LivingAreaSF?_('ft&sup2;','prop'):_('m&sup2;','prop')}}):
                div.col-7 {{prop.LivingAreaSF || prop.LivingAreaMetres}}
              div(v-if="prop.MainLevelFinishedArea || prop.MainLevelFinishedAreaMetres")
                label.col-5 {{_('Main Level Finished')}} ({{prop.MainLevelFinishedArea?_('ft&sup2;','prop'):_('m&sup2;','prop')}}):
                div.col-7 {{prop.MainLevelFinishedArea || prop.MainLevelFinishedAreaMetres}}
              div(v-if="prop.UpperLevelFinishedArea || prop.UpperLevelFinishedAreaMetres")
                label.col-5 {{_('Upper Level Finished')}} ({{prop.UpperLevelFinishedArea?_('ft&sup2;','prop'):_('m&sup2;','prop')}}):
                div.col-7 {{prop.UpperLevelFinishedArea || prop.UpperLevelFinishedAreaMetres}}
              div(v-if="prop.BelowGradeFinishedArea || prop.BelowGradeFinishedAreaMetres")
                label.col-5 {{_('Below Grade Finished')}} ({{prop.BelowGradeFinishedArea?_('ft&sup2;','prop'):_('m&sup2;','prop')}}):
                div.col-7 {{prop.BelowGradeFinishedArea || prop.BelowGradeFinishedAreaMetres}}
              div(v-if="prop.retail_a")
                label.col-5 {{_('Retail Area','prop')}}
                div.col-7 {{prop.retail_a}} {{prop.retail_ac}}
              // 户型信息
              div(v-show="prop.ptyep != 'Commercial'")
                label.col-5 {{_('Bed','room')}} :
                span {{computeBdrms(prop)}}
              div(v-show="prop.rmbthrm || prop.tbthrms || prop.bthrms")
                label.col-5 {{_('Bath','room')}} :
                span {{prop.rmbthrm || prop.tbthrms || prop.bthrms}}
              div(v-show="prop.kch")
                label.col-5 {{_('Kitchen')}} :
                span {{prop.kch}}
              div(v-show="prop.locker_num")
                label.col-5 {{_ab('locker_num','propertyListing')}} :
                | {{prop.locker_num}}
              div(v-show="prop.frnshd")
                label.col-5 {{_('Furnished')}} :
                | {{prop.frnshd}}
              div(v-show="prop.Inclusions")
                label.col-5 {{_('Inclusions')}} :
                div.col-7 {{prop.Inclusions}}
              div
                label.col-5(@click="alertExplain('GARAGE')",data-sub="explain",data-query="type:garage") {{_('Parking','prop')}} :
                  span.fa.fa-question-circle-o
                div.col-7
                  div(v-if="prop.gr || prop.gatp") {{prop.gr?prop.gr:''}} {{prop.gatp}}
                  div(v-if="prop.park_desig || prop.park_spc1 || prop.park_desig_2 || prop.park_spc2")
                    | {{prop.park_desig || prop.park_spc1 || prop.park_desig_2 || prop.park_spc2}} {{_ab('park_desig','propertyListing')}}
                  div(v-if="prop.park_spcs") {{prop.park_spcs}} {{_('Parking Drive','prop')}}
                  div(v-if="prop.tgr") {{prop.tgr}} {{_('Total Parking Spaces','prop')}}
              // 税费相关(10)
              div(v-if="prop.type_taxes")
                label.col-5 {{_('Type Taxes','prop')}}
                div.col-7 {{prop.type_taxes}}
              div(v-show="prop.tax")
                label.col-5 {{_('Property Tax')}} :
                div.col-7.link(@click="openTBrowser('/1.5/tools/currency?p=' + prop.tax + '&nn=1',{nojump:true})",data-sub="exchange", data-query='target:tax,place:2')
                  | {{prop.tax}}
                  span.frequency(v-show="prop.taxyr") &nbsp;/ {{prop.taxyr}}
              div(v-show="prop.AssociationFee")
                label.col-5 {{_('Association Fee','prop')}} :
                div.col-7.link(@click="openTBrowser('/1.5/tools/currency?p=' + prop.AssociationFee + '&nn=1',{nojump:true})",data-sub="exchange", data-query='target:AssociationFee,place:2')
                  | {{prop.AssociationFee}}
                  span.frequency(v-show="prop.AssociationFeeFrequency") &nbsp;/ {{prop.AssociationFeeFrequency}}
              div(v-show="prop.CondoFee")
                label.col-5 {{_('Condo Fee','prop')}} :
                div.col-7.link(@click="openTBrowser('/1.5/tools/currency?p=' + prop.CondoFee + '&nn=1',{nojump:true})",data-sub="exchange", data-query='target:CondoFee,place:2')
                  | {{prop.CondoFee}}
                  span.frequency(v-show="prop.CondoFeePaymentSched") &nbsp;/ {{prop.CondoFeePaymentSched}}
              div(v-show="prop.mfee && !prop.AssociationFee && !prop.CondoFee")
                label.col-5 {{_('Maint Fee','prop')}} :
                div.col-7.link(@click="openTBrowser('/1.5/tools/currency?p=' + prop.mfee + '&nn=1',{nojump:true})",data-sub="exchange", data-query='target:mfee,place:2')
                  | {{prop.mfee}}
                  span.frequency(v-if="prop.MaintenanceFeePaymentUnit") &nbsp;/ {{prop.MaintenanceFeePaymentUnit.v}}
                  span.frequency(v-else-if="prop.mfeeunt") &nbsp;/ {{prop.mfeeunt}}
              div(v-show="prop.condo_corp")
                label.col-5 {{_('Condo Corp')}} :
                | {{prop.condo_corp}}
                span(v-show="prop.corp_num") &nbsp;/ {{prop.corp_num}}
              div(v-if="prop.MaintenanceFeeType")
                label.col-5 {{_('Maint Fee Inclusions','prop')}} :
                div.col-7 {{Array.isArray(prop.MaintenanceFeeType.v)?prop.MaintenanceFeeType.v.join(','):prop.MaintenanceFeeType.v}}
              // 租赁相关
            div.content-padded.detailWrapper(v-show="prop.stp_en == 'Rent' || prop.stp_en == 'Lease'",
              style="border-top: 1px solid #f1f1f1; margin: 0;")
              div(style="margin-bottom:0; color:#989898; padding: 10px 10px 14px 10px;") {{_('Rental Features')}}
              div(v-if="prop.ptnLsOut")
                label.col-5 {{_('Property Lease Out')}} :
                div.col-7 {{prop.ptnLsOut.join()}}
              div(v-show="prop.rtp")
                label.col-5 {{_('Rent Type','prop')}} :
                | {{prop.rtp}}
              div(v-if="prop.ptnLsCmt")
                label.col-5 {{_('Lease Comments')}} :
                div.col-7 {{prop.ptnLsCmt}}
              div(v-show="prop.mintrm")
                label.col-5 {{_('Min Term','prop')}} :
                | {{prop.mintrm}}
                span(v-show="prop.mintrm") {{_('Month')}}
              div(v-show="prop.water_inc")
                label.col-5 {{_('Water Included','prop')}} :
                | {{prop.water_inc}}
              div(v-show="prop.hydro_inc")
                label.col-5 {{_('Hydro Included','prop')}} :
                | {{prop.hydro_inc}}
              div(v-show="prop.entr")
                label.col-5 {{_('Entrance','prop')}} :
                | {{prop.entr}}
              div(v-show="prop.kchntp")
                label.col-5 {{_('Kitchen','prop')}} :
                | {{prop.kchntp}}
              div(v-show="prop.wrmtp")
                label.col-5 {{_('Washroom','prop')}} :
                | {{prop.wrmtp}}
              div(v-show="prop.lndrytp")
                label.col-5 {{_('Laundry','prop')}} :
                | {{prop.lndrytp}}
              div(v-show="prop.pets")
                label.col-5 {{_('Pets','prop')}} :
                | {{prop.pets}}
              div(v-show="prop.smok")
                label.col-5 {{_('Smoking Allowed','prop')}} :
                | {{prop.smok}}
              div(v-show="prop.rgdr")
                label.col-5 {{_('Gender Req','propertyListing')}} :
                | {{prop.rgdr}}
            // 楼花转让添加显示字段
            div.content-padded.detailWrapper(v-show="prop.ltp == 'assignment'",
              style="border-top: 1px solid #f1f1f1; margin: 0;")
              div(style="margin-bottom:0; color:#989898; padding: 10px 10px 14px 10px;") {{_('Project Info')}}
              div
                label.col-5 {{_('Project Name','prop')}} :
                | {{prop.prj}}
              div
                label.col-5 {{_('Developer','prop')}} :
                | {{prop.dvlpr}}
              div
                label.col-5 {{_('Builder','prop')}} :
                | {{prop.bldr}}
              div
                label.col-5 {{_('Phase','prop')}} :
                | {{prop.fas}}
              div
                label.col-5 {{_('Model','prop')}} :
                | {{prop.mdl}}
          span#itemRoom.control-content(:class="{active:activeSegment == 'itemRoom' }")
            div.tableDiv
            div.tableR
              label.col-3 {{_('Room','propertyListing')}}
              label.col-2 {{_('Level','propertyListing')}}
              label.col-2 {{_('Length','propertyListing')}}
              label.col-2 {{_('Width','propertyListing')}}
              label.col-2 {{_('Size')}}
                span(v-show="unit == 'M'",style='font-size: 10px;') ({{'M&sup2;'}})
                span(v-show="unit == 'Ft'",style='font-size: 10px;') ({{'Ft&sup2;'}})
            div.tableR(v-for="i in 12", v-show="prop.rm1_out")
              div
                span.col-3 {{prop['rm'+i+'_out']}}
                span.col-2 {{prop['level'+i]}}
                span.col-2(v-show="unit == 'M'")
                  span(v-show="prop['rm'+i+'_len']") {{prop['rm'+i+'_len'] | number(2)}}
                span.col-2(v-show="unit == 'Ft'")
                  span(v-show="prop['rm'+i+'_len']") {{prop['rm'+i+'_len'] * 3.28084 | number(2)}}
                span.col-2(v-show="unit == 'M'")
                  span(v-show="prop['rm'+i+'_wth']") {{prop['rm'+i+'_wth'] | number(2)}}
                span.col-2(v-show="unit == 'Ft'")
                  span(v-show="prop['rm'+i+'_wth']") {{prop['rm'+i+'_wth'] * 3.28084 | number(2)}}
                span.col-2(v-show="unit == 'M'")
                  span(v-show="prop['rm'+i+'_len'] && prop['rm'+i+'_wth']") {{prop['rm'+i+'_len'] * prop['rm'+i+'_wth'] | number(2)}}
                span.col-2(v-show="unit == 'Ft'")
                  span(v-show="prop['rm'+i+'_len'] && prop['rm'+i+'_wth']") {{prop['rm'+i+'_len'] * prop['rm'+i+'_wth'] * 3.28084 | number(2)}}
              div.text-align-left
                span {{prop['rm'+i+'_dc1_out']}}
                span {{prop['rm'+i+'_dc2_out']}}
                span {{prop['rm'+i+'_dc3_out']}}
            div.tableR(v-for="rm in prop.rms", v-show="prop.rms")
              div
                span.col-3 {{rm.t}}
                span.col-2 {{rm.l}}
                span.col-2(v-show="unit == 'M'")
                  span(v-show="rm.h") {{rm.h}}
                span.col-2(v-show="unit == 'Ft'")
                  span(v-show="rm.h_ft") {{rm.h_ft}}
                span.col-2(v-show="unit == 'M'")
                  span(v-show="rm.w")  {{rm.w}}
                span.col-2(v-show="unit == 'Ft'")
                  span(v-show="rm.w_ft")  {{rm.w_ft}}
                span.col-2(v-show="unit == 'M'")
                  span(v-show="parseFloat(rm.h * rm.w)")  {{rm.h * rm.w  | number(2)}}
                span.col-2(v-show="unit == 'Ft'")
                  span(v-show="parseFloat(rm.h_ft * rm.w_ft)")  {{rm.h_ft * rm.w_ft  | number(2)}}
              div.text-align-left(v-show="rm.d")
                span {{rm.d}}
          span#itemDetail.control-content(:class="{active:activeSegment == 'itemDetail' }")
            div.tableDiv(v-if="prop.Building")
              div.tableHead {{prop.Building.n}}
              div.tableR2(v-for="v in prop.Building.v")
                div.col-6 {{v.t}}
                div.col-6(@click='toggleHeight($event)',:class="{limitSummaryHeight:isLimitHeight(v.m)}") {{v.m | arrayValue}}
                  span.fa.fa-caret-down(v-if="isLimitHeight(v.m)")

            div.tableDiv(v-if="prop.Basement")
              div.tableHead {{prop.Basement.n}}
              div.tableR2(v-for="v in prop.Basement.v")
                div.col-6 {{v.t}}
                div.col-6(@click='toggleHeight($event)',:class="{limitSummaryHeight:isLimitHeight(v.m)}") {{v.m | arrayValue}}
                  span.fa.fa-caret-down(v-if="isLimitHeight(v.m)")

            div.tableDiv(v-if="prop.Land")
              div.tableHead {{prop.Land.n}}
              div.tableR2(v-for="v in prop.Land.v")
                div.col-6 {{v.t}}
                div.col-6(@click='toggleHeight($event)',:class="{limitSummaryHeight:isLimitHeight(v.m)}") {{v.m | arrayValue}}
                  span.fa.fa-caret-down(v-if="isLimitHeight(v.m)")

            div.tableDiv(v-if="prop.Parking")
              div.tableHead {{prop.Parking.n}}
              div.tableR2(v-for="v in prop.Parking.v")
                div.col-6 {{v.t}}
                div.col-6(@click='toggleHeight($event)',:class="{limitSummaryHeight:isLimitHeight(v.m)}") {{v.m | arrayValue}}
                  span.fa.fa-caret-down(v-if="isLimitHeight(v.m)")

            div.tableDiv(v-if="prop.Utilities")
              div.tableHead {{prop.Utilities.n}}
              div.tableR2(v-for="v in prop.Utilities.v")
                div.col-6 {{v.t}}
                div.col-6(@click='toggleHeight($event)',:class="{limitSummaryHeight:isLimitHeight(v.m)}") {{v.m | arrayValue}}
                  span.fa.fa-caret-down(v-if="isLimitHeight(v.m)")

            div.tableDiv(v-if="prop.Surrounding")
              div.tableHead {{prop.Surrounding.n}}
              div.tableR2(v-for="v in prop.Surrounding.v")
                div.col-6 {{v.n}}
                div.col-6(@click='toggleHeight($event)',:class="{limitSummaryHeight:isLimitHeight(v.v)}") {{v.v | arrayValue}}
                  span.fa.fa-caret-down(v-if="isLimitHeight(v.v)")

            div.tableHead
              | {{_('Other')}}
            div.tableDiv(v-if='prop.Other')
              div.tableR2(v-for="v in prop.Other.v")
                div.col-6 {{v.n}}
                div.col-6(@click='toggleHeight($event)',:class="{limitSummaryHeight:isLimitHeight(v.v)}") {{v.v | arrayValue}}
                  span.fa.fa-caret-down(v-if="isLimitHeight(v.v)")

            div.tableR2(v-for="n in detailFields", :class="{hide:!getShowCond(n)}")
              div.col-6 {{_ab(n[0],'propertyListing')}}
              div.col-6
                span(v-for="i in n")
                  //- span(v-show="isDateFld(i)") {{prop[i]}}
                  span(v-show="isPropFld(i)",@click.stop.prevent='toggleHeight($event)',:class="{limitSummaryHeight: isLimitHeight(prop[i])}") {{prop[i] | arrayValue}}
                    span.fa.fa-caret-down(v-if="isLimitHeight(prop[i])")
                  span(v-show="isTextFld(i)") {{i}}

            div.tableDiv(v-if="prop.Inclusions")
              div.tableR2
                  div.col-6 {{_('Inclusions')}}:
                  div.col-6(@click='toggleHeight($event)',:class="{limitSummaryHeight:isLimitHeight(prop.Inclusions)}") {{prop.Inclusions}}
                    span.fa.fa-caret-down(v-if="isLimitHeight(prop.Inclusions)")

            div.tableDiv(v-if="prop.Exclusions")
              div.tableR2
                div.col-6 {{_('Exclusions')}}:
                div.col-6(@click='toggleHeight($event)',:class="{limitSummaryHeight:isLimitHeight(prop.Exclusions)}") {{prop.Exclusions}}
                  span.fa.fa-caret-down(v-if="isLimitHeight(prop.Exclusions)")

            div.tableDiv(v-if="prop.rental_items")
              div.tableR2
                div.col-6 {{_('Rental Items')}}:
                div.col-6(@click='toggleHeight($event)',:class="{limitSummaryHeight:isLimitHeight(prop.rental_items)}") {{prop.rental_items}}
                  span.fa.fa-caret-down(v-if="isLimitHeight(prop.rental_items)")

            div.tableDiv(v-if='prop.src')
              div.tableR2
                div.col-6 {{_('Source','prop')}}:
                div.col-6(@click='toggleHeight($event)',:class="{limitSummaryHeight:isLimitHeight(sourceFrom)}") {{sourceFrom}}
                  span.fa.fa-caret-down(v-if="isLimitHeight(sourceFrom)")

          span#itemUnit.control-content(:class="{active:activeSegment == 'itemUnit' }")
            div.tableDiv.unit-type-box(v-if="prop.unitTypes")
              div.tableR
                label.unit-type(v-for='k in prop.unitKeys',:style="{width:(100/prop.unitKeys.length)+'%'}") {{_(UNIT_TYPES_MAP[k])}}
              div.tableR(v-if="prop.unitTypes",v-for="unit in prop.unitTypes")
                span.unit-type(v-for='k in prop.unitKeys',:style="{width:(100/prop.unitKeys.length)+'%'}") {{unit[k] || '-'}}


          span#itemVIP.control-content(:class="{active:activeSegment == 'itemVIP' }")
            span(v-show='dispVar.isRealGroup || dispVar.isDevGroup')
              div.tableDiv(v-if="prop.possesion")
                div.tableR2
                  div.col-6 {{_('Possession')}}:
                  div.col-6 {{prop.possesion}}
              div.tableDiv(v-if="prop.isEstate")
                div.tableR2
                  div.col-6 {{_('Is Estate')}}:
                  div.col-6 {{prop.isEstate}}
              div.tableDiv(v-if="prop.isPOS")
                div.tableR2
                  div.col-6 {{_('Is POS')}}:
                  div.col-6 {{prop.isPOS}}
              div.tableDiv(v-if="prop.rmStoreys")
                div.tableR2
                  div.col-6 {{_('Storeys')}}:
                  div.col-6 {{prop.rmStoreys}}&nbsp;({{_('Estimated')}})
              div.tableDiv(v-if="prop.untStorey")
                div.tableR2
                  div.col-6 {{_('Unit Storey')}}:
                  div.col-6 {{prop.untStorey}}
              div.tableDiv(v-if="prop.missingLvls")
                div.tableR2
                  div.col-6 {{_('Missing levels')}}:
                  div.col-6 {{prop.missingLvls}}
              div.tableDiv(v-if="prop.lst")
                div.tableR2
                  div.col-6 {{_('Status')}}:
                  div.col-6 {{prop.lst}}
              div.tableDiv(v-if="prop.comm")
                div.tableR2
                  div.col-6 {{_('Comm')}}:
                  div.col-6 {{prop.comm}}
              div.tableDiv(v-if="prop.offerD")
                div.tableR2
                  div.col-6 {{_('Offer Date')}}:
                  div.col-6 {{prop.offerD | dotdate}}
              div.tableDiv(v-if='brkgs.length')
                div.tableR2
                  div {{_('Listing Agents')}}:
                    div.brkgs.link(v-for="(b,idx) in brkgs",@click='searchAgentInBrowser(idx)',data-sub="search agent", :data-query="'nm:'+b.anm||b.nm")
                      span(v-show="b.anm") {{b.anm}} {{b.ambl}}
                      span(v-show="!b.anm") {{b.nm}} {{b.tel}}
                      //- span.btn.btn-positive.pull-right(@click='searchAgentInBrowser(idx)') {{_("Search")}}
              div.tableDiv(v-if="prop.merged && dispVar.isDevGroup")
                div.tableR2
                  div.col-6 {{_('Merged ID')}}:
                  div.col-6 {{prop.merged}}
              div.tableDiv(v-if="dispVar.isDevGroup")
                div.tableR2
                  div.col-6 {{_('Src')}}:
                  div.col-6 {{prop.src}}
                div.tableR2
                  div.col-6 Uaddr:
                  div.col-6 {{prop.uaddr}}
              div.tableDiv
                div.tableR2
                  span.btn.btn-positive(@click="showPropAdminPage()",data-sub="edit management", data-query='place:2') {{_('Open Edit Prop Management')}}
                  //- 有管理费显示
              div.tableDiv(v-show='prop.mfee && dispVar.isRealGroup')
                div.tableR2
                  span.btn.btn-positive(@click="showGroupForum()",data-sub="forum in group") {{_('Forum in group')}}
            span(v-show='isTrebProp && dispVar.isVipRealtor && dispVar.isTrebUser')
              // div.tableDiv(v-if="prop.bm")
              //   div.tableR2
              //     div {{_('Broker Remarks')}}: {{prop.bm}} 改为Private Remarks，并放到Remark里面
              div.tableDiv(v-if="prop.onm && dispVar.isTrebUser2")
                div.tableR2
                  div {{_('Owner')}}: {{prop.onm}}
              div.tableDiv(v-if="prop.coopcpny && dispVar.isTrebUser2")
                div.tableR2
                  div {{_('Co-Op')}}: {{prop.coopcpny}} {{prop.coopagnt}}
      div.card.card-m
        div.card-header(style='padding-right:10px')
          span {{_('Remarks','props')}}
          //- a.fa.ss.ss15.ss-question-circle-lightgray(v-show="!inFrame", @click="showRemarksAbbr()")
            //-NOTE: if translate and show popup, unknown bug occur in native
          span.translate.pull-right(v-show="(showTranslate && dispVar.isLoggedIn && (dispVar.isVipRealtor || dispVar.isRealGroup)) || dispVar.isAdmin",@click="showAdminOptions()",style='padding:0 4px;margin-left:10px;',data-sub="map", data-query='place:2')
            span.fa.fa-gear(style="font-size:17px")
          span.toggle.trans.pull-right(v-show='showTranslate',:class="{active:mlang == 'Zh',zh:!isTranslated('TRANSLATE') }", @click="translate_m(prop)",data-sub="translate remarks")
            span.toggle-handle
        div.card-content.content-padded
          div(v-show="prop.m")
            span.pull-spinner.block(v-show="translating")
            div.prop_m(v-show="mlang !== 'Zh' && !translating", :class="{'prop_m_g': !showPropM}") {{prop.m}}
            div.prop_m(v-show="mlang !== 'En' && !translating", :class="{'prop_m_g': !showPropM}") {{prop.m_zh}}
          div(v-if="showPrivateRemarks")
            hr(style='margin:10px 0;border-top:0.5px solid #f0eeee;')
            div {{_('Private Remarks')}}: &nbsp;{{prop.bm}}
          //- div.showDetail(v-show="!showPropM")
          //-   a.btn.btn-outlined(href="javascript:;", @click="showPropM = true")
          //-     | {{_('View All')}}
          //-     span.fa.ss.ss9.ss-angle-down
          div.copy-right(v-show="prop.disclaimerTerms")
            //- div.desc {{prop.trademarkDesc}}
            div.desc
              div(v-for="term in prop.disclaimerTerms") {{ term }}
            //- div.cpimg-wrapper.rltr(v-if="prop.poweredBylogoPath")
            //-   img(@click="showInBrowser(prop.link)", :src="prop.poweredBylogoPath")
            div.cpimg-wrapper.rltr(v-if="prop.disclaimerIcons",v-for="icon in prop.disclaimerIcons")
              img(@click="showInBrowser(prop.link)", :src="icon.poweredBylogoPath",data-sub="disclaimer",:data-query="'path:'+poweredBylogoPath")

      div.card.invest-card(v-show="prop.ptype !== 'Commercial'")
        div.card-header
          span {{_('Investment Analysis')}}
          a.pull-right(v-if="dispVar.lang !== 'en'",
            @click="showStigma('/1.5/map/webMap?tab=stigma&lat=' + prop.lat + '&lng=' + prop.lng+'&from=propdetail')",
            style="color:#428BCA;",data-sub="stigmatized")
            span.icon-desc {{_('Creepy House','creepyHouse')}}
            span.icon.icon-right-nav()
        div.card-content
          div.city-or-cmty
            div(v-if="hasCmtyStat")
              label {{_('Community')}}:
              span {{prop.bndCmty?prop.bndCmty.nm:(prop.origCmty || prop.cmty)}} {{prop.cmtycd}}
            div(v-else)
              label {{_('City')}}:
              span {{prop.formattedCity}}
          div.numbers
            div.avgp
              div.tl {{_('Avg Price')}} / {{_('YoY')}}
              div.inline.val(v-if="prop.avgS || prop.avgSld") ${{(prop.avgS || prop.avgSld) | propPrice}}
              div.inline.val(v-else) N/A
              div.inline.index(:class="[prop.cmpSy > 0 ? 'inc':'dec']")
                span.fa(:class="{'fa-caret-up':prop.cmpSy > 0,'fa-caret-down':prop.cmpSy < 0}")
                span(v-if="prop.cmpSy") {{prop.cmpSy | percentage}}%
                span(v-else) N/A%
            div.avgrp
              div.tl {{_('Avg Rent')}} / {{_('YoY')}}
              div.inline.val(v-if="prop.avgR || prop.avgRtd") {{(prop.avgR || prop.avgRtd) | currency('$', 0)}}
              div.inline.val(v-else) N/A
              div.inline.index(:class="[prop.cmpRy > 0 ? 'inc':'dec']")
                span.fa(:class="{'fa-caret-up':prop.cmpRy > 0,'fa-caret-down':prop.cmpRy < 0}")
                span(v-if="prop.cmpRy") {{prop.cmpRy | percentage}}%
                span(v-else) N/A%
          div.line-chart(v-show="showLineChart")
            canvas(id="priceLineChart", height="200")
          div.links()
            a.tax(href="javascript:;",
              @click="goPropStat()",data-sub="trends")
              span.fa.ss.ss15.ss-bar-chart
              | {{_('Market Trends')}}
          div.close-cost(v-show="isSale")
            label.desc {{_('Estimated Closing Costs')}}
            div.val-wrapper
              span.val {{closeCostMin() | currency('$', 0)}}
              | &nbsp; - &nbsp;
              span.val {{closeCostMax() | currency('$', 0)}}
          div.links(v-show="isSale")
            a.tax(href="javascript:;",
              data-sub="transfer tax", data-query='place:2'
              v-show="prop.stp_en == 'Sale' || prop.stp_en == 'Lease'",
              @click="openTBrowser('/1.5/tools/transtax?p='+prop.lp+(prop.area_code == '01' ? '&trnt=1' : '&trnt=0') + '&nn=1', {nojump:true})")
              span.fa.ss.ss15.ss-calculator
              | {{_('Transfer Tax')}}
          div.mortgage(v-show="isSale")
            label.desc
              | {{_('Monthly Cash Flow')}}
          div(v-show="isSale", style="margin: 15px 10px 14px;")
            div.prop-cmty-header(style="margin-bottom: 15px;")
              div
                div.monthlyInfo(style="font-weight: bold;")
                  span(v-if="monthlyBalance")
                    span(v-if="monthlyBalance < 0") -&nbsp;
                    span {{ monthlyBalance | currency('$', 0) }}
                  span(v-else) N/A
                div.monthlyTitle {{_("Balance")}}
              div
                div.monthlyInfo
                  span(v-if="prop.estRent !== undefined") {{ prop.estRent | currency('$', 0) }}
                  span(v-else) N/A
                div.monthlyTitle
                  span.dot.bgColor1abc9c
                  span &nbsp;{{_("Money In")}}
              div
                div.monthlyInfo
                  | {{ principalMonthly | currency('$', 0) }}
                div.monthlyTitle
                  div.dot.bgColore74c3c
                  span &nbsp;{{_("Money Out")}}
            div.cashflow-progress
              div.bgColor1abc9c(:style="{width: monthlyInBar}")
              div.bgColore74c3c(:style="{width: monthlyOutBar}")
          div.bar-wrapper(v-show="isSale")
            div.cashFlowAny.prop-cmty-header
              span.prop-price-desc
                span {{_('Est. Rental Income')}}
                span.fa.fa-question-circle-o(style="margin-right:0",@click.stop="alertExplain('ESTIMATE')")
                span :
              span.val(v-if="prop.estRent !== undefined") {{ prop.estRent | currency('$', 0) }}
              span.val(v-else) N/A
            div.cashFlowAny.prop-cmty-header
              span {{_('Property Tax')}}:
              div.val(v-show="!(taxMonthly == 0 || taxMonthly == null)")
                span -&nbsp;{{taxMonthly | currency('$', 0)}}
              div.val(v-show="taxMonthly == 0 || taxMonthly == null") N/A
            div.cashFlowAny.prop-cmty-header(v-show="prop.mfee")
              span {{_('Maintenance Fee')}}:
              span.val -&nbsp;{{prop.mfee | currency('$', 0)}}
            div.cashFlowAny.prop-cmty-header(style="margin-bottom: 2px;",v-if="mortgageMonthly")
              span {{_('Mortgage')}}:
              span.val -&nbsp;{{mortgageMonthly | currency('$', 0)}}
            div.tip
              | {{downpayPercentage*100}}%
              | {{_('downpayment')}},
              | {{interestRate}}%
              | {{_('interest rate and')}}
              | {{amortization}}
              | {{_('years')}}
          div.links.prop-cmty-header(v-show="isSale")
            a.mortgage(style="width: 48%;padding:10px 0;",href="javascript:;",
              v-show="prop.stp_en == 'Sale' || prop.stp_en == 'Lease'",data-sub="mortgage", data-query='place:1',
              @click="openTBrowser(computedMortgageLink, {nojump:true})")
              | {{_('Customize Mortgage')}}
            a(style="width: 48%;padding:10px 0;",href="javascript:;",data-sub="rent or buy"
              v-show="prop.stp_en == 'Sale' || prop.stp_en == 'Lease'",
              @click="openTBrowser(url = '/1.5/tools/rentorbuy?inframe=1&p=' + prop.lp + (prop.tax ? '&pt=' + prop.tax : '') + (prop.avgR ?'&avgR=' + prop.avgR : '') + (prop.mfee ? '&mfee=' + prop.mfee : '') + (prop.area_code == '01' ? '&trnt=1' : '&trnt=0') + '&nn=1', {nojump:true})")
              | {{_('Rent Or Buy')}}
      div.card.school-card(v-show="schs.length && prop.ptype != 'Commercial' && prop.lat")
        div.card-header
          span {{_('School Info')}}
          span.pull-right(style="width: calc(100% - 107px); overflow: hidden; white-space: nowrap; text-align: right;")
            a(href="javascript:;", @click="redirect('/1.5/map/webMap?tab=private&lat=' + prop.lat + '&lng=' + prop.lng+'&from=propdetail')", v-show="!inFrame",data-sub="private school")
              span.icon-desc {{_('Private Schools')}}
              span.icon.icon-right-nav
        div.card-content
          school-list(:disp-var="dispVar", :schs="schsShort")
        div.showDetail(v-show="!showedAllSch")
          a.btn.btn-outlined(href="javascript:;", @click="showAllSchools()",data-sub="all school")
            | {{_('View All')}}
            span.fa.ss.ss9.ss-angle-down
      div.img-wrapper
        div(v-if="computedShowRelatedVideo",
          style="text-align: center; position:relative;",
          @click="showPropVideo()",data-sub="video", data-query='place:2')
          img(:src="computedShowRelatedVideo",
            class="can-scale-head cant-scale-head detail")
          span.fa.fa-rmplay#picArrayVideoImg

        lazy-image(
          :src="url",
          :imgclass="'can-scale-head cant-scale-head detail no-preview'",
          :load="$event => setUpPreview($event, false)",
          v-for="(url,$index) in picUrls",
          :key="$index",
          :alt="url",
          :dataindex="$index",
          data-sub="preview img", data-query='place:2'
        )
    div(v-if="wId")
      sign-up-form(:feedurl="feedurl", :dispVar="dispVar", :owner="owner", :user-form="userForm", :title="signupTitle")
      user-brief-info(:owner.sync="owner", :qrcd.sync="qrcd")
    div(v-if="dis")
      disclaimer(:no-bot-bar="true")
  prop-detail-admin-options(:prop="prop", :disp-var="dispVar",:mlang='mlang')
  prop-saved-logs(:log='prop.pnSrc')
  remind-upgrade.upgrade
  div#obj-id(style='display:none') {{prop._id}}
  div#obj(style='display:none') prop
  div#user-roles(style='display:none') {{dispVar.userRoles}}
  div(style="display:none")
    span(v-for="(v,k) in strings") {{_(v[0], v[1])}}
</template>
  
<script>
import WechatQrModal from './WechatQrModal.vue'
import Disclaimer from './Disclaimer.vue'
import SignUpForm from './SignUpForm.vue'
import UserBriefInfo from './UserBriefInfo.vue'
import SchoolList from './SchoolList.vue'
import PropOnOffMarketList from './PropOnOffMarketList.vue'
import watchingPopup from './watchingPopup.vue'
import MainInfo from './PropDetailMainInfo.vue'
import CommuteAddr from '../commute/CommuteAddr.vue'
import PropDetailAdminOptions from './PropDetailAdminOptions.vue'
import Demographics from '../census/Demographics.vue'
import EstimatedValue from './EstimatedValue.vue'


import LazyImage from './LazyImage.vue'

import filters from '../filters'

import pagedata_mixins from '../pagedata_mixins'
import prop_mixins from '../prop_mixins'
import rmsrv_mixins from '../rmsrv_mixins'
import mapSearch_mixins from '../mapSearch_mixins'
import contactRealtor_mixins from '../contactRealtor_mixins'
import propMap from '../mixin/propMap.js'
import propSavedLogs from '../prop/propSavedLogs.vue'
import remindUpgrade from '../showing/remindUpgrade'
// import html2canvas from 'html2canvas';
export default {
  filters:{
    time:filters.time,
    day:filters.day,
    dotdate:filters.dotdate,
    number:filters.number,
    propPrice:filters.propPrice,
    percentage:filters.percentage,
    currency:filters.currency,
    arrayValue:filters.arrayValue
  },
  mixins:[pagedata_mixins,rmsrv_mixins,mapSearch_mixins,prop_mixins,propMap,contactRealtor_mixins],
  props:{
    signupTitle:{
      type:String,
      default:null,
    },
    mode:{
      type:String,
      default:'map'
    },
    noShareImage:{
      type:Boolean,
      default:false,
    },
    showClose:{
      type:Boolean,
      default:false
    },
    loading:{
      type:Boolean,
      default:false
    },
    wId:{//show user qrmodal and feedback
      type:Boolean,
      default:false
    },
    owner:{
      type:Object
    },
    userForm:{
      type:Object
    },
    dis:{//disclaimer
      type:Boolean,
      default:true
    },
    btnClose:{
      type:Boolean,
      default:false,
    },
    fromMarket:{
      type:Boolean,
      default:false,
    },
    fromShare:{
      type:Boolean,
      default:false,
    },
    inFrame:{
      type:Boolean,
      default:false
    },
    noTitleBar:{
      type:Boolean,
      default:false
    },
    showShowingIcon:{
      type:Boolean,
      default:true
    }
  },
  data () {
    return {
      strings:{
        ERR_TRANS:['Error when getting translate'],
        DIS_COMMENT: ['Comment disabled','forum'],
        CONTACT_RLTR:['Please ask your agent to contact us for listing promotion service.'],
        TOP_FIRST:['You need to TOP this listing first, then you can edit open house time.'],
        VERIFY_FIRST:['Verification request first'],
        COPIED:['ID is copied.'],
        TRANSLATE:['Translate'],
        ALERT:['Alert'],
        calendarSaved:{key:"Calendar Saved", ctx:'showing'},
        saveFailed:{key:"Save Failed", ctx:'showing'},
        OHTIMEOUT:{key:"This open house itinerary has ended."},
        SELECTCLIENT:{key:"Please select client first."},
        NEEDSTAKEHOLDERS:{key:'Participant is required.'},
        NEEDSTAKEHOLDERSROLE:{key:'Participant role is required.'},
      },
      strings_translated:{
        EXPOSURE:["For apartments, this is the direction of the window opening. For houses, this means the location of the house relative to the street. Usually it's the direction the house's backyard windows point.",
          '對於公寓，這是窗戶開口的方嚮。對於房屋，這意味著房屋相對於街道的位置。通常是房屋後院窗戶指向的方向。',
          '对于公寓，这是窗户开口的方向。对于房屋，这意味着房屋相对于街道的位置。通常是房屋后院窗户指向的方向。'
        ],
        GARAGE:['"Garage" refers to built-in, attached, carport, or underground garage parkings and "parking space" refers to driveway, surface, or outside street parking spaces. The "total parking" is equal to the sum of garage and parking space. However, for some condos or apartments, garage and parking space may refer to the same spots and the total parking may equal either garage or parking spaces.',
          '"車庫"指的是內置、附屬、車庫或地下車庫停車位，而"停車位"指的是車道、表面或外部街道停車位。"總停車位"等於車庫和停車位的總和。然而，對於一些公寓或公寓，車庫和停車位可能指的是同一個位置，總停車位可能等於車庫或停車位。',
          '"车库"指的是内置、附属、车库或地下车库停车位，而"停车位"指的是车道、表面或外部街道停车位。"总停车位"等于车库和停车位的总和。然而，对于一些公寓或公寓，车库和停车位可能指的是同一个位置，总停车位可能等于车库或停车位。'
        ],
        SIZE:['The size in brackets is estimated based on our data.',
          '括号中的面積是基於我們數據的估值。',
          '括号中的面积是基于我们数据的估值。'
        ],
        ESTIMATE:["The RealMaster Estimate is a data-driven calculation of a home’s market value based on public records, historical sales, and proprietary algorithms. It is not a professional appraisal and does not replace the expertise of a licensed real estate agent or certified appraiser. Actual market value may differ based on property condition, use, and changing market conditions. RealMaster accepts no responsibility for decisions made based on this estimate.",
          'RealMaster 估值是基於公開市場數據、歷史交易記錄以及算法模型生成的房產估算值。它不是正式的評估報告，也不能替代持牌房地產經紀人或註冊評估師的意見。實際房產價值可能因具體物業狀況、市場變化、用途及其他因素有所差異。RealMaster 對因依賴估值資訊所作出的任何決定不承擔法律責任。',
          'RealMaster 估值是基于公开市场数据、历史交易记录以及算法模型生成的房产估算值。它不是正式的评估报告，也不能替代持牌房地产经纪人或注册评估师的意见。实际房产价值可能因具体物业状况、市场变化、用途及其他因素有所差异。RealMaster 对因依赖估值信息所作出的任何决定不承担法律责任。'
        ]
      },
      feedurl:'/1.5/form/forminput',
      schs:[],
      records:[],
      lastRecords:[],
      recordName:'2016',
      lastRecordName:'2015',
      myChart:null,
      labels:[],
      showTerm:false,
      showPropM:  true,
      // showTermWhenDDF:false,
      cmntNum:0,
      imgidx:0,
      dispVar:    {
        defaultEmail:'<EMAIL>',
        isCip:false,
        isRealtor:false,
        isApp:false,
        lang:'zh',
        isVipRealtor:false,
        isDevGroup:false,
        isAdmin: false,
        isPropAdmin: false,
        rltrTopAd:false,
        // isAllowedPredict:false,
        isPaytopAll:false,
        sessionUser:{},
        userFollowedRltr:{},
        languageAbbrObj:[],
        isEmailVerified:true,
        isNoteAdmin:false,
        isAssignAdmin: false,
        isRealGroup:false,
        userRoles:'guest',
        isEditorAdmin:false,
        brokerRmrkToVip:false,
        isVipPlus: false
      }, //shared between childs
      qrcd: false,
      oldVerBrowser: window.oldVerBrowser || false,
      unit:       'Ft',
      picUrls:    [],
      sizes:      [],
      mlang:       'En',
      showedAllSch: false,
      hasCmtyStat: false,
      gotsprice:  false,
      getpricing: false,
      translating: false,
      showPrincipalBase:false,
      staticMaploaded:false,
      showBackdrop:false,
      displayHoverBar: false,
      showPredictErrorInput:false,
      mortgageMonthly: 0,
      predError:'',
      taxList: [],
      addrHist: [],
      hasWechat:false,
      mapMode:this.$parent.mapMode || (vars && vars.mode) || 'map',
      activeSegment:'itemSummary',
      datas:[
        'defaultEmail',
        'isCip',
        'isRealtor',
        'isVipUser',
        'isVisitor',
        'isApp',
        'isLoggedIn',
        // 'isAllowedPredict',
        // 'gmapStatic',
        'exMapURL',
        'lang',
        'rltrTopAd',
        'coreVer',
        // 'allowedPromoteProp',
        'isVipRealtor',
        'isTrebUser',
        'isTrebUser2',
        'hasFollowedRealtor',
        'hasFollowedVipRealtor',
        'userFollowedRltr',
        'isDevGroup',
        'isProdSales',
        // 'canShowSoldPrice',
        // 'showSoldPriceBtn',
        'isPaytop',
        'isRealGroup',
        'isPaytopAll',
        // 'appVerNum',
        'isAdmin',
        'showedTerm',
        'isPropAdmin',
        'sessionUser',
        'languageAbbrObj',
        'isAgreementAdmin',
        // 'jsGmapUrl'
        'isGeoAdmin',
        'isEmailVerified',
        'isNoteAdmin',
        'isAssignAdmin',
        'userRoles',
        'isEditorAdmin',
        'brokerRmrkToVip',
        'isVipPlus'
      ],
      priceHist:[],
      ta05:[],// 有subcity 的ta1
      ta1:[],
      ta15:[],// 有subcity 的ta2
      ta2:[],
      agentAvts:[],
      computedAgentSrc:'/img/icon_nophoto.png',
      prop:       this.$parent.prop || {},
      // inFrame has no close btn and bottom nav
      schVueClick:this.$parent.schVueClick || false,
      exrates:    this.$parent.exrates || {},
      exnames:    this.$parent.exnames || {},
      detailFields:[
        ['lpunt'],
        ['bsmt'],['den'],
        ['blcny'],
        ['grdn'],
        ['gym'],['pool'],
        ['fpop'],['lkr'],
        ['fpl'],['htb'],
        ['ac'],['heat'],
        ['wifi', ''],
        ['tv', ''],
        ['hydro_inc', ''],
        ['water_inc', ''],
        ['cac_inc', ''],
        ['comel_inc', ''],
        ['heat_inc', ''],
        ['prkg_inc', ''],
        ['all_inc', ''],
        ['pvt_ent', ''],
        ['insur_bldg', ''],
        ['laundry_lev', ''],
        ['pets', ''],
        ['laundry', ''],
        ['wsr', ''],
        ['dyr', ''],
        ['frdg', ''],
        ['movn', ''],
        //['frnshd', ''],
        ['share_perc'],
        ['lvl'],
        ['constr1_out','constr2_out'],
        // ['condo_corp','corp_num'],
        ['prop_mgmt'],
        ['com_cn_fee'],
        // ['fin_stmnt'],
        ['franchise'],
        ['ChannelName'],
        ['DevelopmentChargesPaid'],
        ['DockingType'],
        ['FireplaceFeatures'],
        ['FireplacesTotal'],
        ['FoundationDetails'],
        // ['InteriorFeatures'],
        ['LeaseToOwnEquipment'],
        ['Topography'],
        ['WaterView'],
        ['WaterFront'],
        ['Roof']
        // ['rltr']
      ],
      histcnt:0,
      noeval: 0,
      showShareIcon: false,
      calculatedSpWidth:144,
      spWrapperWidth:'100%',
      validOh:[],
      schsShort:[],
      locale:'en',
      watchingType:'',
      watchEvent:null,
      agrmntImgSize:[],
      revokeReason:'',
      locationTag:'',
      lastLocationTag:'',
      defaultLocationTags:[{
        desc:'Community',
        k:'cmty'
      },{
        desc:'Nearby',
        k:'nearby'
      },{
        desc:'Commute',
        k:'commute'
      },{
        desc:'Demographics',
        k:'demographics'
      }],
      locationTags:[],
      locList:[],
      toAddress:null,
      segmentList:['itemSummary','itemDetail', 'itemRoom', 'itemUnit', 'itemVIP'],
      lastActiveSegment:'',
      showID:'',
      formPage:'',
      brkgs:[],
      mainInfoRows:[],
      cmtyImageLoaded:false,
      hasGoogleService:false,
      showCopyID:true,
      retset:false,
      accuracyInfo:[],
      accuracyField:[],
      amortization     : 30,
      interestRate     : 5.3,
      downpayPercentage: 0.35,
      hasSelfM: '-',
      totalCount: '-',
      addedShowingLogs:[],
      showLogs:false,
      noteId: '',
      swiper:null,
      showClaim:false,
      UNIT_TYPES_MAP:{
        m: 'Description',
        tp: 'Type',
        units: 'UnitsTotal',
        actualRent: 'ActualRent',
        totalRent: 'TotalRent',
        baths: 'BathsTotal',
        beds: 'BedsTotal',
      },
      condoInfo:{
        hasCondoInfo:false
      },
      estimateInfo: null,
      gettingEstimate: false,
      gotEstimate: false,
      getExchangeCalling: false,
      getExchangeCalled: false,
      allEstimateInfo:{},
      soldAccuracy:[],
      soldAccuracyField:[],
      showSoldPriceEst: false,
      containerWidth: 0
    };
  },
  computed: {
    preLoadPicUrls() {
      return this.picUrls.slice(0,3);
    },
    lazyLoadPicUrls() {
      return this.picUrls.slice(3);
    },
    computedRmType(){
      if (this.prop.ltp == 'exlisting'){
        return 'exclusive properties'
      }else if (this.prop.ltp == 'assignment'){
        return 'assignment'
      }else if (this.prop.ltp == 'rent'){
        return 'rental'
      }else{
        return ''
      }
    },
    sourceFrom(){
      var src = '';
      switch (this.prop.src) {
        case 'TRB':
          src = 'TRREB'
          break;
        case 'DDF':
          src = 'CREA'
          break;
        case 'BRE':
          src = 'Greater Vancouver'
          break;
        case 'RHB':
          src = 'Hamilton'
          break;
        case 'OTW':
          src = 'Ottawa'
          break;
        case 'CLG':
          src = 'Calgary'
          break;
        case 'EDM':
          src = 'Calgary'
          break;
        case 'CAR':
          src = 'Hamilton'
          break;
        case 'RM':
          src = 'RM'
          break;
        default:
          break;
      }
      return src;
    },
    computedMortgageLink(){
      let area = this.prop.area_code == '01' ? '&trnt=1' : '&trnt=0'
      let url = `/1.5/tools/mortgage?inframe=1&p=${this.prop.lp}${area}&irate=${this.interestRate}&ayear=${this.amortization}&dppercent=${this.downpayPercentage*100}&nn=1`
      return url
    },
    // [assignment/exlisting/rental(agent)] uses signup, exclude 房东直租
    showContactLandlord(){
      // agent posted rental
      if(this.hasWechat){
        return true
      }
      if(this.prop.ltp == 'rent' && this.prop.cmstn){
        return false
      }
      if(/assignment|exlisting/.test(this.prop.ltp)){
        return false
      }
      return true
    },
    formatedAddr(){
      return `${this.prop.addr}, ${this.prop.city_en||this.prop.city}`
    },
    showRefreshPicButton() {
      // dispVar.isRealtor && isTrebProp && !(prop.pho || prop.photonumbers
      if(this.prop.src!=='TRB' && this.prop.src!=='DDF') {
        return false;
      }
      if (this.dispVar.isPropAdmin || this.dispVar.isRealGroup) {
        return true;
      }
      if (this.dispVar.isRealtor  && !(this.prop.pho || this.prop.photonumbers) && this.prop.phodl){
        return true;
      }
      return false;
    },
    showRmpropTip(){
      // 独家房才有可能出现提示
      if(!this.isRMProp()){
        return false;
      }
      // 创建房源的人为非vip经济并且房源未验证，显示提示
      if(this.prop.creatorIsVipRealtor || this.prop.isV){
        return false;
      }
      return true;
    },
    computedShowOnOffMarket(){
      if (this.isTrebProp) {
        return true;
      } else {
        return this.dispVar.isLoggedIn; // NOTE: @fred 登录用户可以看到房源历史(售价)
      }
    },
    // computedBthrms(){
    //   var prop = this.prop;
    //   if (prop.rmbthrm) {
    //     return prop.rmbthrm;
    //   }
    //   return prop.bthrms;
    // },
    // computedGr(){
    //   var prop = this.prop;
    //   if (prop.rmgr) {
    //     return prop.rmgr;
    //   }
    //   return prop.tgr;
    // },
    // computedShowSqft(){
    //   console.log(!this.isDDFProp(this.prop));
    //   return !this.isDDFProp(this.prop);
    // },
    // computedLa2Agents(){
    //   if (this.prop && this.prop.la2 && this.prop.la2.agnt) {
    //     let str = '';
    //     for (let ag of this.prop.la2.agnt) {
    //       str += 'nm: '+ag.nm+', _id: '+ag._id+' \n';
    //     }
    //     return str;
    //   }
    //   return null;
    // },
    is3DVturl(){
      return /matterport\.com/.test(this.prop.vturl)
    },
    computedShowRelatedVideo(){
      let vid = this.prop.ytvid;
      if (this.dispVar.isCip) {
        vid = this.prop.vurlcn;//054101015D3D270E15673C8F9586346E
        if (vid) {
          return this.prop.vimgcn||'/img/noPic.png';
          // return `https://vthumb.ykimg.com/${vid}`;
        }
        return null;
      }
      if (vid) {
        return `https://img.youtube.com/vi/${vid}/0.jpg`
      }
      return null;
    },
    // computedHist() {
    //   if (this.addrHist.length > 2) {
    //     return this.addrHist.slice(0,2);
    //   } else {
    //     return this.addrHist;
    //   }
    // },
    showExrates(){
      var prop = this.prop;
      var result = (prop.lp || prop.lpr) && (prop.status_en !== 'U');
      if (!this.dispVar.isApp) {
        return result;
      }
      return false;
    },
    showLineChart(){
      for (var i of this.records) {
        if (i != null) {
          return true;
        }
      }
      for (var j of this.lastRecords) {
        if (j != null) {
          return true;
        }
      }
      return false;
    },
    showTermWhenDDF(){
      var self = this;
      return this.showTerm && this.isDDFProp(self.prop);
    },
    computedVideoWitdhAndHeight(){
      let w = window.innerWidth/4;
      let h = w/4*3;
      return [w,h];
    },
    computedWith(){
      let w = window.innerWidth*0.78-24;
      return w+'px';
    },
    computedImgHeight(){
      var height1 = (window.innerWidth || 375) / 1.50;
      var height2 = 320;
      return Math.min(height1,height2)
    },
    hasPriceInfo(){
      var prop = this.prop;
      if (prop.lp ===0 || prop.lpr === 0) {
        return true;
      }
      if (prop.showSoldPrice && prop.status_en == 'U') {
        return prop.sp;
      } else {
        return (prop.lp || prop.lpr);
      }
    },
    computedCmntNum() {
      return this.cmntNum > 99 ? '99+' :  this.cmntNum;
    },
    hasProvAndCity(){
      if (this.inFrame) {
        return false;
      }
      if (this.prop.city_en && this.prop.prov_en) {
        return true;
      }
      return false;
    },
    isSold:function () {
      if (!this.prop.lst) {
        return false;
      }
      if(this.prop.isSoldOrRented){
        return true
      }
      return /Sld|Lsd|Cld|Pnd/.test(this.prop.lst) //this.prop.lst === 'Sld' || this.prop.lst === 'Lsd';
    },
    isSale:function () {
      if (!this.prop.saletp_en) {
        return false;
      }
      if (Array.isArray(this.prop.saletp_en)) {
        return /Sale/.test(this.prop.saletp_en.join(','))
      }
      return /Sale/.test(this.prop.saletp_en.toString())
    },
    isLease:function () {
      if (!this.prop.saletp_en) {
        return false;
      }
      if (Array.isArray(this.prop.saletp_en)) {
        return /Lease|Rent/.test(this.prop.saletp_en.join(','))
      }
      return /Lease|Rent/.test(this.prop.saletp_en.toString())
    },
    computedMortgageMonthly:function(){
      if (this.mortgageMonthly < 2) {
        return 0;
      }
      return this.mortgageMonthly-1;
    },
    principalMonthly: function () {
      if (this.prop.mfee > 0 && parseInt(this.prop.mfee)) {
        return this.mortgageMonthly+this.taxMonthly+this.prop.mfee;
      }
      return this.mortgageMonthly+this.taxMonthly;
    },
    taxMonthly: function () {
      if (this.prop.tax) {
        return this.prop.tax/12;
      }
      return 0;
    },
    ownerFnm:{
      cache:false,
      get(){
        var owner = this.owner;
        if (!owner) {
          return;
        }
        if (owner.fnm) {
          return owner.fnm;
        }
        return (owner.fn || '')+' '+(owner.ln || '');
      }
    },
    isZh: function (){
      return this.dispVar.lang == 'zh' || 'zh-cn';
    },
    showDom: function () {
      var prop = this.prop;
      if (prop.id && prop.id.substr(0,2) == 'RM') {
        return false;
      }
      return prop.dom !== undefined;
    },
    shareImage: function () {
      return this.picUrls.length > 0 ? this.picUrls[0] : '/img/logo.png';
    },
    // canShowSoldPrice: function () {
    //   var prop = this.prop;
    //   if (!this.dispVar.isApp) {
    //     return false;
    //   }
    //   if (prop.id && prop.id.substr(0,2) == 'RM') {
    //     return false;
    //   }
    //   // prop.stp_en == 'Sale'
    //   return (prop.status !== 'A') && prop.pcls !== 'b';
    // },
    // canShowSoldPriceBtn:function () {
    //   if (/^DDF/.test(this.prop._id)) {
    //     return false;
    //   }
    //   if (this.prop.lst !== 'Sld') {
    //     return false;
    //   }
    //   // if (this.mode == 'map') {
    //   //   if (!this.dispVar.canShowSoldPrice) {
    //   //     return false;
    //   //   }
    //   // }
    //   return !(this.gotsprice || this.prop.sp || this.getpricing);
    // },
    isUserTopListing:function(){
      return this.prop.isUserTopListing;
      // if (this.prop && this.prop.topuids && this.prop.topuids.length > 0) {
      //   const length = this.prop.topuids.length;
      //   const lastIdx = length - 1;
      //   return (this.prop.topuids[lastIdx] == this.dispVar.sessionUser._id);
      // }
      // return false;
    },
    showListingInsight:function() {
      return true;
      if (!this.dispVar.isLoggedIn) {
        return false;
      }
      if (!(this.dispVar.sessionUser && this.dispVar.sessionUser._id) ) {
        return false;
      }
      if (this.isRMProp() && !this.dispVar.isPropAdmin) {
        return false;
      }
      if (!this.dispVar.isApp) {
        return false;
      }
      // if (this.dispVar.isAdmin) {
      //   return true;
      // }
      // NOTE: @Tao show to all user
      return true;
      // if (this.prop.topup_pts) {
      //   return this.isUserTopListing || this.dispVar.isVipRealtor;
      // } else {
      //   return this.dispVar.isRealtor || this.dispVar.isVipRealtor;
      // }
    },
    showTopStats:function() {
      // NOTE: @tao show too all
      return this.prop.vc > 10 || this.dispVar.isAdmin;
      return true//this.dispVar.isVipRealtor;
      // if (this.dispVar.isAdmin) {
      //   return true;
      // }
      // if (this.prop.topup_pts) {
      //   return this.isUserTopListing || this.dispVar.isVipRealtor;
      // } else {
      //   return this.dispVar.isVipRealtor;
      // }
    },
    // TODO: remove all predicts
    showPred:function() {
      return this.prop.dpred != 'N' && this.prop.pred_sp;
    },
    isTopExpired:function() {
      return new Date(this.prop.topTs) < new Date()
    },
    // @isOwner:  prop.uid == user.id; //RM-prop: user created this prop
    // @isPaytopAll: switch cfg for adminOnly or agents/all people
    // @isPaytop: user role, indicate this user is topup admin
    showBoostBtn:function(){
      //only mls prop and rm-agent can do toplisting 2022-02-20 dual_homepage
      var canSee = this.dispVar.isPaytop;//this.dispVar.isPaytopAll || (this.dispVar.isRealtor) || 
        // if owner not agent and not paytopall, he cant do toplisting
      var preCondition = (this.prop.status == 'A') && //active
        ((this.prop.topup_pts && this.isTopExpired) || !this.prop.topup_pts) &&
        this.dispVar.isApp && canSee;
      if (!this.isRMProp()) {
        // also admin cant do top
        return preCondition && (this.dispVar.isRealGroup || this.dispVar.isPaytop);
      }
      // RM-prop has to be owner or devGrp, also admin cant do top
      return preCondition && (this.dispVar.isPropAdmin || this.prop.isOwner);
    },
    computedShowRenew:function(){
      return this.dispVar.isApp && this.prop.status == 'A' && this.prop.topup_pts && (this.dispVar.isDevGroup || this.prop.isOwner || this.prop.canEditOhs)
    },
    computedShowID(){
      if(this.isRMProp()){
        return this.prop.id
      }else{
        return this.prop.sid || this.prop._id
      }
    },
    computedMergedID(){
      if(!this.dispVar.isPropAdmin){return null}
      if(!this.dispVar.isApp){return null}
      return this.prop.merged//&& this.isDevGroup
    },
    // TODO: simplify this logic
    showAdminOptionsIcon:function(){
      if(this.dispVar.isAdmin){
        return true;
      }
      if(this.dispVar.isProdSales && this.isTrebProp){
        return true;
      }
      if(this.showEditOpenHouse){
        return true;
      }
      if(this.showTranslate && this.dispVar.isLoggedIn && this.dispVar.isVipRealtor){
        return true;
      }
      if(this.dispVar.isRealtor){
        return true;
      }
      if((this.dispVar.isDevGroup || this.dispVar.isGeoAdmin) && !this.isRMProp()){
          return true;
      }
      if(this.dispVar.isDevGroup && this.prop.hasCmtyId){
        return true;
      }
      return false
    },
    showNotesIcon(){
      if(this.isRMProp() || !this.dispVar.isApp || !this.prop.uaddr){
        return false
      }
      this.getNotesCount();
      if(this.dispVar.isRealGroup || this.dispVar.isNoteAdmin){
        return 'inReal';
      } else {
        return 'personal';
      }
    },
    showLayoutIcon(){
      if((this.dispVar.isRealGroup || this.dispVar.isEditorAdmin) && this.dispVar.isApp && this.prop.uaddr && this.condoInfo.hasFloorPlan){
        return true;
      }
      return false;
    },
    showCondoInfo(){
      if(this.prop.uaddr && this.prop.isBuilding){
        this.getCondoBasicInfo();
        return true;
      }
      return false;
    },
    // 如果设置了brokerRmrkToVip，则所有vip用户可以查看broker remark，否则只有real group可以查看
    showPrivateRemarks(){
      if (!this.prop.bm) {
        return false;
      }
      if(this.dispVar.isAdmin){
        return true;
      }
      if ((this.dispVar.brokerRmrkToVip && this.dispVar.isVipPlus) || this.dispVar.isRealGroup){
        return true;
      }
      return false;
    },
    showAllEstimate(){
      if (!this.dispVar.isRealGroup && !this.dispVar.isPropAdmin){
        return false;
      }
      if(this.accuracyInfo.length){
        return true;
      }
      return false;
    },
    showSpEstimate(){
      if (!this.dispVar.isRealGroup && !this.dispVar.isPropAdmin){
        return false;
      }
      if(this.soldAccuracy.length){
        return true;
      }
      return false;
    },
    monthlyBalance() {
      if(this.prop.estRent == undefined){
        return null
      }
      return this.prop.estRent - this.principalMonthly;
    },
    monthlyInBar() {
      // 进度条绿色部分百分比
      let estRent = this.prop.estRent;
      if(estRent == undefined){
        return '0%';
      }
      let total = estRent + this.principalMonthly;
      if (total === 0) return '0%';
      return `${Math.round((estRent / total) * 100)}%`;
    },
    monthlyOutBar() {
      // 进度条红色部分百分比
      let estRent = this.prop.estRent;
      let total = (estRent || 0) + this.principalMonthly;
      if (total === 0) return '0%';
      return `${Math.round((this.principalMonthly / total) * 100)}%`;
    }
  },
  mounted () {
    var self = this, bus = window.bus;
    this.containerWidth = window.innerWidth;
    //after getPageData
    bus.$on('on-prop-detail-map-menu-click', function(tp) {
      if((tp == 'map' || tp == 'mapNative') && self.dispVar.isApp){
        return self.showNearby('sale');
      }
      if(tp == 'map') {
        self.showMapMixView({prop:self.prop,dispVar:self.dispVar});
      } else if (tp == 'mapNative'){
        self.showMapMixView({prop:self.prop,dispVar:self.dispVar,native:true});
      } else if (tp == 'streetview') {
        self.showMapMixView({prop:self.prop,dispVar:self.dispVar,type:'streetView'});
      } else if (tp == 'navigation') {
        self.exMap({prop:self.prop,dispVar:self.dispVar});
      } else if (tp == 'commute') {
        self.showCommute({prop:self.prop,dispVar:self.dispVar,title:self._('Commute')});
      } else if (tp == 'amenity') {
        self.showAmenity({prop:self.prop,dispVar:self.dispVar,title:self._('Amenity')});
      } else if (tp == 'community') {
        self.showCommunity({prop:self.prop,dispVar:self.dispVar,title:self._('Community')});
      }
    });
    // bus.$on('find-realtor', function (idx) {
    //   self.findARealtor()
    // });
    bus.$on('select-to-addr', function ({location,idx}) {
      self.toAddress = location.fullAddr;
      self.showCommute({prop:self.prop,dispVar:self.dispVar,title:self._('Commute')});
    });
    bus.$on('swipe-next-page', function (idx) {
      // console.log(idx);
      self.imgidx = idx;
    });
    bus.$on('lazy-image-onpress', function (e) {
      self.previewPic(e);
    });
    bus.$on('prop-changed', function (prop) {
      // console.log(self.prop._id,prop._id == self.prop._id);
      if (prop._id && prop._id == self.prop._id) {
        self.picUrls = self.setupThisPicUrls(prop)
        // self.picUrls = listingPicUrls(self.prop, self.dispVar.isCip);
        if(!prop.thumbUrl){
          self.prop.thumbUrl = self.picUrls[0] || (window.location.origin + "/img/noPic.png");
        }
        bus.$emit('prop-retrived', self.prop);
        return;
      }
      bus.$emit('clear-cache');
      self.getPropDetail(prop);
    });
    bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
      // isTrebProp
      if (d.showedTerm === true) {
        localStorage.showedTerm = true;
      }
      if (d.isApp && !localStorage.showedTerm) {
        self.showTerm = true;
        // if (self.prop.ptype && self.isDDFProp(self.prop)) {
        //   self.showTermWhenDDF = true;
        // }
      }
      if(d.jsWechat && d.jsWechat.length>0){
        self.showCopyID = false;
      }
    });
    bus.$on('show-topup', function (p) {
      self.showPays(p);
    });
    bus.$on('close-watch-popup', function () {
      self.watchingPopup = false;
      self.closeModals();
    });
    bus.$on('watch-prop', function ({params,watching}) {
      self.watchProp(params,watching);
    });
    bus.$on('prop-retrived', function (p) {
      setTimeout(function () {
        // lazy-image no need to replace src
        // if (window.replaceSrc) {
        //   window.replaceSrc()
        // }
        self.setupSwiper();
        self.getEstimatedValue();
        self.getAllEstimateInfo();
        window.bus.$emit('reset-signup',null);
      }, 1);
    });
    // if(window.localStorage.hasWechat != null){
    //   let hasWechat = window.localStorage.hasWechat == 'true'?true:false;
    //   self.hasWechat = hasWechat
    // }
    if (window.RMSrv && RMSrv.hasWechat) {
      RMSrv.hasWechat((ret)=>{
        self.hasWechat = ret;
      })
    }
    setTimeout(function () {
      self.loadJs('/js/lz-string.min.js','lzString')
      // NOTE: if not show no need to get
      self.getTopAgents();
    }, 500);
    // bus.$on('get-school-info', function(q){
    //   self.getSchoolsInfo(q.prop, q.cfg);
    // });
    self.getPageData(self.datas,{showedTerm:localStorage.showedTerm},true);
    // this.initProp();
    this.contentWrapper = document.getElementById('contentWrapper');
    this.contentWrapper.addEventListener("scroll", this.scrollListener, {passive: true});
    // this.contentWrapper.removeEventListener("scroll", this.scrollListener, {passive: true});
    if(vars.noeval) {
      this.noeval = vars.noeval;
    }
    this.showShareIcon = vars.showShareIcon?true:false;
    if(vars.prop) {
      this.processOpenHouse();
      this.toggleHist();
      this.taxList = this.calcTransferTax();
      this.initMainInfo();
      bus.$emit('prop-retrived', vars.prop);
    }
    this.selectLang = vars.lang || this.dispVar.lang;
    if(this.fromShare){
      this.setLangTemplate()
    }
    self.locale = vars.locale || vars.lang;
    self.setLocationTags();
    if (vars && vars.formPage && vars.formPage !== 'undefined') {
      self.formPage = vars.formPage;
    }
    bus.$on('prop-got-brkgs', function (brkgs) {
      self.brkgs = brkgs;
    });
    bus.$on('admin-fn', function ({fn,status}) {
      if(fn == 'openTBrowser'){
        return self.openTBrowser(status,{nojump:true})
      }
      if(fn == 'createLangTemplate'){
        self.setLangTemplate()
        return self.createLangTemplate(self.locale)
      }
      self[fn](status);
    });
    bus.$on('alert-info', function (k) {
      self.alertExplain(k);
    });
    bus.$on('calender-selected-time',function (time) {
      self.resetUpgrade()
      var d={
        id: self.prop._id,
        tp: 'listing',
        lang: self.dispVar.lang,
        wDl: 1,
        aid: self.dispVar.sessionUser._id,
        channel: 'open-house'
      }
      self.getPropShortUrl(d,(url)=>{
        self.addToCalendar({url,time})
      })
    });
    bus.$on('remind-reset',function () {
      self.resetUpgrade()
    });
    this.transToggleContent();
    if(window.RMSrv && RMSrv.hasGoogleService){
      RMSrv.hasGoogleService((ret)=>{
        self.hasGoogleService = ret
      })
    }
    window.addEventListener('click', checkAndSendLogger);
    bus.$on('condo-desc-zh', function (value) {
      self.condoInfo.condoDesc_zh = value;
    });
  },
  beforeMount (){
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
  },
  components: {
    remindUpgrade,
    PropOnOffMarketList,
    WechatQrModal,
    Disclaimer,
    SignUpForm,
    UserBriefInfo,
    SchoolList,
    LazyImage,
    watchingPopup,
    CommuteAddr,
    MainInfo,
    PropDetailAdminOptions,
    propSavedLogs,
    Demographics,
    EstimatedValue
  },
  methods: {
    checkAndAddFields(){
      if(!this.dispVar.isVipRealtor){
        return;
      }

      if(this.prop.ShowingAppointments){
        this.prop.ShowingAppointments = this._(this.prop.ShowingAppointments,'prop');
        this.detailFields.push(['ShowingRequirements']);
      }
      
      if(this.prop.ShowingRequirements){
        this.prop.ShowingRequirements = this._(this.prop.ShowingRequirements,'prop');
        this.detailFields.push(['ShowingAppointments']);
      }      
    },
    openClaim(){
      if(!this.prop.canClaim && !this.prop.claimed){
        return
      }
      var url = '/propClaim?id='+this.prop._id+'&popup=1',self = this;
      let cfg =  {hide:false, title: this._('Claim')};
      RMSrv.getPageContent(url,'#callBackString', cfg, function(val) {
        if (val == ':cancel') {
          return;
        }
        try {
          let ret = JSON.parse(val)
          if(ret.claimed){
            self.prop.claimed = true
          }
        } catch (e) {
          console.error(e)
        }
      });
    },
    toggleHeight(e){
      var e = e.target;
      if(e.classList.contains('fa')){
        e = e.parentNode
      }
      if(!e.querySelector('.fa')){
        return
      }
      e.classList.toggle('limitSummaryHeight');
      e.querySelector('.fa').classList.toggle('fa-caret-down');
      e.querySelector('.fa').classList.toggle('fa-caret-up');
    },
    byteCount(s) {
      return encodeURI(s).split(/%..|./).length - 1;
    },
    isLimitHeight(info){
      if (Array.isArray(info)) {
        info.join(' ');
      }
      if(this.byteCount(info) > 30){
        return true;
      }
      return false;
    },
    goMylisting(){
      if(this.redirectPage()){
        return ;
      }
      var url = '/1.5/promote/mylisting';
      if (RMSrv.closeAndRedirectRoot) {
        RMSrv.closeAndRedirectRoot(this.appendDomain(url));
        return;
      }
      window.location = url;
    },
    goSimilarProps(isBuilding){
      if(this.redirectPage()){
        return ;
      }
      var url = '/1.5/similar?',self=this,title='Similar Listings';
      if(isBuilding){
        url += `&building=true`;
        title = 'Same Building'
      }
      url += `&id=${this.prop._id}&type=${this.prop.ptype2_en.join(',')}`
      url = self.appendDomain(url);
      let cfg = {toolbar:false};
      RMSrv.getPageContent(url,'#callBackString', cfg, function(val) {
        try {
          if(self.dispVar.isDevGroup || self.dispVar.isRealGroup){
            self.getPropFavStatus();
          }
        } catch (e) {
          console.error(e);
        }
      });
    },
    isTranslated(word){
      var transWord = this._(this.strings[word][0]);
      return /[a-z]/.test(transWord)
    },
    setLangTemplate(){
      this.createLangTemplate = createLangTemplate;
      window.setLang = this.setLanguage;
    },
    transToggleContent(){
      var headDiv = document.querySelector('head');
      const cssRule = `
      .toggle.trans::before{
        content: "${this._(this.strings['TRANSLATE'][0])}";
      }
      `;
      const style = document.createElement('style');
      style.type = 'text/css';
      style.innerHTML = cssRule;
      headDiv.appendChild(style);
    },
    popupSavedLogs(){
      if(this.redirectPage()){
        return ;
      }
      this.showBackdrop = !this.showBackdrop;
      return toggleModal('savedLogs','open')
    },
    popupWatch(tp,event){
      if(this.redirectPage()){
        return ;
      }
      this.watchEvent = event?event:null;
      this.watchingType = tp;
      this.showBackdrop = !this.showBackdrop;
      this.watchingPopup = true;
      // window.bus.$emit('checkNotify',{});
      return toggleModal('watchingModal','open')
    },
    watchProp(params,watching){
      var self = this,isFav = true;
      if(!params.uaddr){
        return
      }
      getStandardFunctionCaller() ('watchLocation',params,(err,msg)=>{
        if (err) {
          bus.$emit('flash-message',err.toString());
          return;
        };
        if(params.unwatch){
          isFav = false
        }
        if(self.watchingType == 'cmty'){
          self.prop.isCmtyFav = isFav;
          if(params.unwatch){
            delete self.prop.cmtyWatch
          }else{
            self.prop.cmtyWatch = Object.assign({},watching);
          }
        }else if(self.watchingType == 'location' || self.watchingType == 'building'){
          self.prop.isWatching = isFav;
          if(params.unwatch){
            delete self.prop.watching
          }else{
            self.prop.watching = Object.assign({},watching);
          }
        }
        // self.closeModals()
        bus.$emit('flash-message',msg);
      })
    },
    getWatchText() {
      return this.prop.isWatching?'Watched':'Watch';
    },
    alertExplain(key){
      let index = 0; // 默认英语 index = 0
      if (this.dispVar.lang === 'zh') {
        index = 1; // 繁体中文 index = 1
      } else if (this.dispVar.lang === 'zh-cn') {
        index = 2; // 简体中文 index = 2
      }
      RMSrv.dialogAlert(this.strings_translated[key][index]), this._(this.strings.ALERT[0]);
    },
    changeSegmentTag(tag){
      this.lastActiveSegment = this.activeSegment;
      this.activeSegment = tag
      if(tag == 'itemDetail'){
        this.checkAndAddFields();
      }
    },
    getListAgentInfo(){
      bus.$emit('show-brkg', {title:'',prop:this.prop, picUrls:this.picUrls, ta1:this.ta1,ta2:this.ta2,isInsider:true});
    },
    searchAgentInBrowser(idx){
      var brkg = this.brkgs[idx];
      var info = '';
      if(brkg.anm){
        info = `${brkg.anm}  ${brkg.ambl}`;
      }else{
        info = `${brkg.nm}  ${brkg.tel}`;
      }
      RMSrv.showInBrowser('https://www.google.com/search?q='+encodeURIComponent(info));
    },
    locationClicked(elemId){
      var self = this;
      self.scrollLoadJs();
      document.querySelector(elemId).scrollIntoView(true);
      setTimeout(() => {        
        var scrollTop = self.contentWrapper.scrollTop;
        self.contentWrapper.scrollTop = scrollTop - 61;
      }, 10);
    },
    copyToClipboard(textToCopy){
      //  # navigator clipboard api needs a secure context (https)
      if(this.isNewerVer(this.dispVar.coreVer,'6.2.6') && RMSrv.copyToClipboard && !RMSrv.isIOS()){
        return RMSrv.copyToClipboard(textToCopy);
      } else if (navigator.clipboard){
        //  # navigator clipboard api method'
        navigator.clipboard.writeText(textToCopy)
      } else {
        //  # text area method
        var textArea = document.createElement('textarea');
        textArea.value = textToCopy;
        textArea.id = 'IDArea';
        //  # make the textarea out of viewport
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy', true);
      }
    },
    copyId(){
      var self = this;
      if(!self.showCopyID){
        return
      }
      this.copyToClipboard(this.computedShowID)
      bus.$emit('flash-message',self._(self.strings.COPIED[0]));
      if(document.getElementById('IDArea')){
        document.getElementById('IDArea').remove();
      }
    },
    redirectPage(){
      if (this.checkAndRedirectDownload()){
        return true
      }
      if (this.checkAndRedirectToLogin(!this.dispVar.isLoggedIn)) {
        return true
      }
      return false
    },
    getCmtyImage(prop){
      var self = this,post;
      if(prop.hasCmtyId){
        post = {id:prop.bndCmty._id}
      }else{
        post = {
          cnty:prop.cnty,
          prov:prop.prov_en,
          city:prop.city_en,
          cmty:prop.cmty,
        };
      }
      fetchData('/1.5/props/cmtyImage',{body:post},function(err,ret) {
        var errMsg = ret.e || err;
        if (errMsg) {
          bus.$emit('flash-message',errMsg.toString());
          return;
        }
        if (ret.cmty_image) {
          self.prop.cmty_image = ret.cmty_image;
          // NOTE： dev或者测试加上下面的代码
          // self.prop.cmty_image.fn = 'https://www.realmaster.com'+ret.cmty_image.fn;
          setTimeout(() => {
            self.cmtyImageLoaded = false;
            self.setupPropPosOnCmtyImage();
          }, 100);
        }
      })
    },
    calcTagDirection:function(k,idx){
      let curK = this.activeSegment;
      // this.propTags
      // idx > cur -> l2r
      // index < cur -> r2l
      let lastK = this.lastActiveSegment;
      let curIdx = this.segmentList.findIndex((e)=>{return e==curK})
      let lastIdx = this.segmentList.findIndex((e)=>{return e==lastK})
      if(idx==curIdx){
        return lastIdx>idx?'r2l':'l2r';//last value
      }
      return '';
    },
    calcDirection:function(k,idx){
      let curK = this.locationTag;
      // this.propTags
      // idx > cur -> l2r
      // index < cur -> r2l
      let lastK = this.lastLocationTag;
      let curIdx = this.locationTags.findIndex((e)=>{return e.k==curK})
      let lastIdx = this.locationTags.findIndex((e)=>{return e.k==lastK})
      if(idx==curIdx){
        return lastIdx>idx?'r2l':'l2r';//last value
      }
      if(idx==lastIdx){
        return 'out'
      }
      return '';
    },
    formateCommuteAddr(addr,type){
      // "0213 Mary St, Palmerston, ON N0G 2P0, Canada"
      var addrArr = addr.split(',');
      var addrObj = {type};
      if(addrArr.length > 1){
        addrObj.addr = addrArr[0];
        addrObj.city = addrArr[1];
        addrObj.prov = addrArr[2].replace(' ','').slice(0,2);
      }
      addrObj.fullAddr = addr;
      addrObj.selected = false;
      return addrObj
    },
    getCommuteLocation(){
      var self = this;
      self.locList = [self.formateCommuteAddr('blank','any')];
      if(window.localStorage.toAddress && window.localStorage.toAddress!=='undefined' ){
        self.toAddress = window.localStorage.toAddress;
        var recently = self.formateCommuteAddr(window.localStorage.toAddress,'recent')
        recently.selected = true;
        self.locList.unshift(recently)
      }else{
        self.locList[0].selected = true
      }
      self.$http.post('/1.5/direction/savedaddr',{}).then(
        function (ret) {
          if (ret.data.ok) {
            var savedAddr = ret.data.savedAddr;
            for (let saved of savedAddr) {
              if (saved.addr) {
                var save = self.formateCommuteAddr(saved.addr,'saved');
                self.locList.push(save);
              }
            }
          }
        },
        function (ret) {
          ajaxError(ret);
        });
    },
    selectTag(tag){
      if (tag.k !== 'cmty') {
        this.cmtyImageLoaded = false;
      }
      var self = this;
      setTimeout(() => {
        self.scrollBar()
      }, 20);
      this.lastLocationTag = this.locationTag;
      this.locationTag = tag.k;
      if(this.locationTag == 'commute' && this.locList.length<=1){
        this.getCommuteLocation();
      }
    },
    scrollBar(){
      var navLinksContainer = document.querySelector('.location-container');
      if (!navLinksContainer) {
        return;
      }
      var navLinkSelected = document.querySelector('.locationTags.selected');
      var boundData = navLinkSelected.getBoundingClientRect();
      navLinksContainer.scrollLeft = boundData.left + (boundData.width)/2 - window.innerWidth/2;
    },
    openMeasure(){
      this.openTBrowser('/1.5/tools/measure?inframe=true&loc=' + this.prop.lat + ',' + this.prop.lng, {nojump:true})
    },
    showMeasure(){
      if(this.prop.ptype2_en && this.prop.ptype2_en.indexOf('Condo') > -1){
        return false;
      }
      return true;
    },
    cmtyImageError(err){
      // console.error(err)
      this.prop.cmty_image = null;
      return;
    },
    getCmtyFavText() {
      return this.prop.isCmtyFav?'Watched':'Watch';
    },
    setPosOnMapImage({mapImage,bbox,posEl,pos}) {
      const {lat,lng} = pos;
      var {width} = mapImage.getBoundingClientRect();//ios has issue with height
      var height = (425/640)*width;
      var imageWidth = width;
      var imageHeight = height;
      var bboxWidth = bbox[2] - bbox[0];
      var bboxHeight = bbox[3] - bbox[1];
      var widthPct = ( lng - bbox[0] ) / bboxWidth;
      var heightPct = ( lat - bbox[1] ) / bboxHeight;
      var x = Math.floor( imageWidth * widthPct );
      var y = Math.floor( imageHeight * ( 1 - heightPct ) );
      posEl.style.top = `${y}px`;
      posEl.style.left = `${x}px`;
    },
    setupPropPosOnCmtyImage() {
      if (this.cmtyImageLoaded) return;
      if (this.prop.cmty_image) {
        // NOTE: 列表分享有好几个房源，未来可能好几个prop 同时展示
        var cmtyWrapper = document.getElementById(this.prop._id)
        var mapImage = cmtyWrapper.querySelector('.prop-cmty-image img');
        // 图片使用v-if ，加载有延迟
        if (!mapImage) {
          // console.error('map image not found');
          setTimeout(() => {
            this.setupPropPosOnCmtyImage();
          }, 100);
          return ;
        }
        this.cmtyImageLoaded = true;
        // console.log('map image found');
        const {bbox} = this.prop.cmty_image;
        var posEl = document.querySelector('.prop-pos-on-cmty');
        var pos = {lat:this.prop.lat,lng:this.prop.lng};
        this.setPosOnMapImage({mapImage,bbox,posEl,pos});
      }
    },
    newSwiper(){
      var self = this,opt = {slidesPerView:'auto'};
      if (self.picUrls.length <= 3) {
        opt.centeredSlides = true;
      }
      self.swiper = new Swiper('#prop-detail-photos',opt);
      self.swiper.on('transitionEnd', function() {
        self.imgidx = self.swiper.realIndex;
      });
    },
    setupSwiper() {
      var self = this;
      if(self.swiper){
        self.swiper.destroy();
        self.imgidx = 0
        return self.newSwiper()
      }
      self.loadJs('/js/swiper.min.js','swiperJs',function() {
        self.newSwiper();
      });
    },
    setLanguage(lang){
      var url = window.location.href;
      var url = url.replace(/=(zh-cn|en|zh|kr)/,'='+lang);
      window.location = url;
    },
    isActive (lang) {
      return this.locale === lang ? 'active' : '';
    },
    showCurrencyIcon() {
      if (this.prop.lp === 0 || this.prop.lpr === 0) {
        return false;
      }
      return this.prop.priceValStrRed;
    },
    openCurrencyTool(p) {
      var priceStr = ''
      if(p){
        priceStr = p
      }else if (!this.showCurrencyIcon()){
        return;
      }else{
        priceStr = this.prop.priceValStrRed
      }
      this.openTBrowser('/1.5/tools/currency?p=' + priceStr.replace(/[A-Z\$,]/g,'') + '&nn=1',{nojump:true});
    },
    onOpenHouseClicked(oh) {
      if (!oh.l) {
        return;
      }
      RMSrv.showInBrowser(oh.l);
    },
    checkAndRedirectDownload(){
      if (!this.dispVar.isApp) {
        window.location = '/adPage/needAPP';
        return true;
      }
      return false;
    },
    //userStatus !this.dispVar.isLoggedIn || self.dispVar.isVisitor
    checkAndRedirectToLogin(userStatus){
      if (userStatus) {
        var url = '/1.5/user/login#index'
        url = this.appendDomain(url);
        RMSrv.closeAndRedirectRoot(url)
        return true;
      }
      return false;
    },
    toggleShowing () {
      // use common module
      if(this.redirectPage()){
        return ;
      }
      trackEventOnGoogle('propDetail', 'addToShowing')
      window.bus.$emit('prop-showing-add', this.prop._id);
    },
    showAgentWesite(user){
      if (this.owner && this.owner._id) {
        var _id = this.owner._id;
      }else if(this.dispVar.isRealGroup){
        var _id = user._id;
      }else{
        return ;
      }
      var url = `/1.5/wesite/${_id}?inFrame=1`;
      RMSrv.openTBrowser(url);
    },
    getDay(oh) {
      let date = oh.f;
      var d = new Date(date.replace(/-/g, "/").split(' ')[0]);
      var weekday = [
        "Sunday","Monday","Tuesday", "Wednesday","Thursday","Friday","Saturday"
      ];
      return weekday[d.getDay()];
    },
    getMonth(oh) {
      // NOTE: safari new Date("2020/08/29 24:00") Invalid Date
      let date = oh.f;
      var monthArray = ['Jan','Feb','Mar','Apr','May','Jun','Jul','Aug','Sep','Oct','Nov','Dec'];
      let month =  monthArray[new Date(date.replace(/-/g, "/").split(' ')[0]).getMonth()];
      return month;
    },
    getAMOrPM(time) {
      if(time >=12) {
        return 'PM';
      } else {
        return 'AM';
      }
    },
    getOhTime(oh) {
      let from = oh.f.split(' ')[1];
      let to = oh.t.split(' ')[1];
      let ohzFrom = this.specialDealOhzTime(from);
      let ohzTo = this.specialDealOhzTime(to);
      return `${ohzFrom?ohzFrom:''} - ${ohzTo?ohzTo:''}`;
    },
    showPropVideo(){
      var url;
      if (this.dispVar.isCip) {
        url = this.prop.vurlcn;
        // url = 'https://v.youku.com/v_show/id_'+this.prop.ykvid
      } else {
        url = 'https://www.youtube.com/watch?v='+this.prop.ytvid
      }
      this.showInBrowser(url);
    },
    openHistPage() {
      var url = '/1.5/evaluation/histPage.html?uaddr=' + this.prop.uaddr+'&inframe=1&nobar=1&fromMls=1';
      url = this.appendDomain(url);
      RMSrv.openTBrowser(url, {nojump:true, title:this._('Evaluation History','evaluation')});
    },
    getEvaluateHist() {
      var self = this;
      let params ={};
      if (this.prop.uaddr) {
        params.uaddr = this.prop.uaddr;
      } else {
        params = {cmty:this.prop.cmty_en, city:this.prop.city_en, prov: this.prop.prov_en, cnty: this.prop.cnty_en};
        let fields = ['lng','lat','addr','st','unt'];
        for (let key of fields) {
          if (this.prop[key]) {
            params[key] = this.prop[key];
          }
        }
      }
      params.pid = self.prop._id
      this.$http.post('/1.5/evaluation/histcnt',params).then(
        function(ret) {
          ret = ret.data;
          if (ret.ok == 1) {
            self.histcnt = ret.histcnt;
          } else {
            console.log(ret.e)
            self.msg = "error";
          }
        },function(ret){
          ajaxError(ret);
        });
    },
    getPropType(){
      var containAny = function(arr, arrC) {
        var el = arr.find(function(e) {
          for (let str of arrC) {
            if (e.indexOf(str)>=0) {
              return true;
            }
          }
        });
        return el;
      }
      var type;
      if (this.prop.ptype2_en) {
        if (containAny(this.prop.ptype2_en, ['Semi-Detached','1/2 Duplex'])) {
          type = 'Semi-Detached';
        } else if (containAny(this.prop.ptype2_en, ['Townhouse','Stacked'])) {
          type = 'Townhouse';
        } else if (containAny(this.prop.ptype2_en, ['Apartment','Condo'])) {
          type = 'Apartment';
        } else {
          type='Detached';
        }
      }
      return type;
    },
    goEvaluation() {
      if(this.redirectPage()){
        return ;
      }
      let sqft = this.getPropSqft(this.prop);
      var type = this.getPropType();
      var arr = []
      var fields = ['lp','lng','lat','addr','st','bdrms','bthrms','tp','br_plus','gr','unt','sqft1','sqft2','depth','front_ft','lotsz_code','irreg'];
      for (let key of fields) {
        if (this.prop[key]) {
          arr.push(key+'='+this.prop[key]);
        }
      }
      arr.push('tax='+ parseInt(this.prop.tax));
      arr.push('cmty='+this.prop.cmty_en);
      arr.push('city='+this.prop.city_en);
      arr.push('prov='+this.prop.prov_en);
      arr.push('cnty='+this.prop.cnty_en);

      var url = '/1.5/evaluation/evaluatePage.html?nobar=1&inframe=1&fromMls=1&' + arr.join('&') +'&mlsid='+(this.prop._id||'')+'&sqft='+(sqft||'')+'&tp='+type+'&img='+(this.getImgEncodeUrl()||'');
      url = this.appendDomain(url);
      RMSrv.getPageContent(url,'#callBackString', {hide:false, title: this._('Evaluation Conditions','evaluation')}, function(val) {
        if (val == ':cancel') {
          return;
        }
      });
    },
    showVipGuidePage(idx) {
      if (idx == 2) {
        url = 'https://www.realmaster.ca/membership'
        RMSrv.showInBrowser(url);
      }
    },
    goPrediction(){
      const self = this;
      if(this.redirectPage()){
        return ;
      }
      if (self.prop.predOutrange) {
        const showPredict = self.prop.showPredict
        const fn = self._?self._:self.$parent._;
        const optTl = "";
        let later = fn('Okay');
        let seemore = ''
        let tip = fn('Daily limit reached!')+' '+fn('Welcome back tomorrow.');
        if (showPredict) {
          //非vip plus经纪，normal经纪
          later = fn('Later');
          seemore = fn('Upgrade Now');
          tip = fn('Daily limit reached!')+' '+fn('Upgrade to Premium VIP.')+' '+fn('Get more advanced features.');
        }
        RMSrv.dialogConfirm(tip, self.showVipGuidePage, optTl, [later, seemore]);
      } else {
        var url = '/1.5/prop/prediction?id=' + self.prop._id + '&lang=' + self.dispVar.lang;
        url = self.appendDomain(url);
        RMSrv.getPageContent(url,'#callBackString', {hide:false, title: self._('RealMaster AI-Estimate','prediction')}, function(val) {
          if (val == ':cancel') {
            return;
          }
        });
      }
    },
    boostListing(){
      if(/assignment|exlisting/.test(this.prop.ltp) && this.prop.isV != true){
        return window.bus.$emit('flash-message',this.$parent._(this.strings.VERIFY_FIRST[0]));
      }
      if (!this.dispVar.isLoggedIn) {
        var url = '/1.5/prop/topup/charge';
        if (RMSrv.closeAndRedirectRoot) {
          RMSrv.closeAndRedirectRoot(this.appendDomain(url));
          return;
        }
        window.location = url;
      }
      this.showPays();
    },
    showPropAdminPage(){
      if (!this.dispVar.isPropAdmin && !this.prop.topup_pts && !this.dispVar.isDevGroup && !this.dispVar.isRealGroup) {
        RMSrv.dialogAlert(this._(this.strings.TOP_FIRST[0]));
        return;
      }
      if (this.prop.canEditOhs || this.dispVar.isDevGroup || this.dispVar.isRealGroup) {
        var url = '/1.5/prop/manage?id='+this.prop._id+'&sid='+this.prop.sid;
        if (this.picUrls && this.picUrls[0]) {
          url+='&img='+encodeURIComponent(this.picUrls[0])
        }
        RMSrv.openTBrowser(url);
        return;
      }
      this.showPays();
    },
    showGroupForum(){
      var tl = (this.prop.addr || '')
      var url = '/1.5/forum/details?inFrame=1&id='+ this.prop.uaddr+'&src=building'
      if (this.dispVar.isRealGroup){
        url += '&gid='+this.dispVar.isRealGroup
      } else {
        return ;
      }
      if (tl)
        url += '&tl='+tl.replace(/#/g,'')
      if (this.prop.city_en)
        url += '&city='+this.prop.city_en
      if (this.prop.prov_en)
        url += '&prov='+this.prop.prov_en

      url+='&inframe=1&nobar=1';
      url = this.appendDomain(encodeURI(url));
      if (this.dispVar.isApp) {
        this.tbrowser(url,{title:this._('RealMaster')});
      } else {
        window.location.href = url;
      }
    },
    showStaging(){
      var url = this.dispVar.stagingHelpUrl;
      this.showInBrowser(url);
    },
    // parseSqft(sqft){
    //  see: prop_mixins
    // },
    isDDFProp(prop){
      // var prop = this.prop;
      if(/^RM/.test(prop.id)){
        return false
      }
      return prop.isDDFProp;
      // var tmp = prop.trbtp || [];
      // return tmp.length === 0;
    },
    showRemarksAbbr(){
      var url = '/event?tpl=translation&feature=abbr&u=/1.5/index&inframe=1&noShare=1';
      this.openTBrowser(url,{nojump:1});
    },
    // load js for content rendered after scroll
    scrollLoadJs(){
      var self = this;
      var chartjsArray = [
        {
          path:'/js/Chart-3.6.2.min.js',
          id:'chartJs'
        },
        {
          path:'/js/chartjs-plugin-datalabels.js',
          id:'datalabels'
        }
      ]
      self.loadJsSerial(chartjsArray,function(){
        console.log('Chart js loaded');
      });
    },
    scrollListener(){
      // console.log(this.contentWrapper.scrollTop,this.dispVar.isApp && this.dispVar.lang !== 'en' && this.prop.status=='A');
      var self = this;
      var value = this.contentWrapper.scrollTop,wait = 400;

      if (!self.waiting) {
        self.waiting = true;
        setTimeout(function () {
          self.waiting = false;
          self.checkScrollAndSendLogger(self.contentWrapper)
        }, wait);
      }
      // if (this.dispVar.lang !== 'en' && this.prop.status=='A') {
      if (value>90+this.computedImgHeight) {
        // if (!this.displayHoverBar) {
        this.displayHoverBar = true;
        if (!Object.keys(self.exrates).length) {
          self.getExchangeRates();
        }
        // }
      } else {
        // if (this.displayHoverBar) {
        this.displayHoverBar = false;
        // }
      }
      // }
      if (value > 500) {
        if (!window.Chart) {
          // RMSrv.dialogAlert('Chart not loaded yet')
          console.error('Chart not loaded yet');
          return self.scrollLoadJs();
        }
        this.initChartOptions();
        this.getHistoPrice();
        self.initSchoolInfoAndMortgage();
      }
      if (value > 100) {
        if (this.addrHist.length == 0) {
          this.getAddrHist({redirect:false});
        }
        self.scrollLoadJs()
      }
    },
    parseZeroToNull(ret){
      for (var tp of ['records','lastRecords']) {
        if (!ret[tp]) {
          continue;
        }
        for (var i = 0; i < ret[tp].length; i++) {
          if (ret[tp][i] === 0) {
            ret[tp][i] = null;
          }
        }
      }
    },
    getHistoPrice(){
      var prop = this.prop;
      var self = this;
      if (self.gettingHisto || self.gotHisto || self.records.length) {
        return;
      }
      self.gettingHisto = true;
      var d = {
        city:prop.city_en || prop.city,
        prov:prop.prov_en || prop.prov,
        cmty:prop.cmty_en || prop.cmty
      };
      // NOTE: no city and prov makes no sense, no data corresponsded
      if (!(d.city && d.prov)) {
        return;
      }
      self.$http.post('/1.5/prop/stats/priceLineChart', d).then(
        function (ret) {
          ret = ret.data;
          if (ret.ok) {
            self.parseZeroToNull(ret);
            for (var i of ['labels','records','recordName','lastRecords','lastRecordName']) {
              // console.log(i);
              self[i] = ret[i];
            }
            if (ret.cmty) {
              self.hasCmtyStat = true;
            }
            self.drawChart();
            self.gettingHisto = false;
            self.gotHisto = true;
          } else {
            self.gettingHisto = false;
            if (!ret.silent) {
              RMSrv.dialogAlert(ret.err);
            }
          }
        },
        function (ret) {
          ajaxError(ret);
        }
      );
    },
    drawChart(){
      var self = this;
      if (self.myChart) {
        self.myChart.data.datasets[0].label = self.recordName;
        self.myChart.data.datasets[0].data = self.records;
        self.myChart.data.datasets[1].label = self.lastRecordName;
        self.myChart.data.datasets[1].data = self.lastRecords;
        self.myChart.data.labels = self.labels;
        self.myChart.update();
      } else {
        self.ctx.width = window.innerWidth - 20; //parseInt(window.innerWidth*mul) || 640;
        self.ctx.height = 200;
        Chart.defaults.plugins.responsive = false;
        var datasets = [
            {
                label: self.recordName,
                yAxisID: 'Sale',
                fill: false,
                lineTension: 0.3,
                borderWidth:2,
                backgroundColor: "rgba(75,192,192,1)",
                borderColor: "rgba(75,192,192,1)",
                borderCapStyle: 'butt',
                borderDash: [],
                borderDashOffset: 0.0,
                borderJoinStyle: 'miter',
                pointBorderColor: "rgba(75,192,192,1)",
                pointBackgroundColor: "#fff",
                pointBorderWidth: 2,
                pointHoverRadius: 4,
                pointHoverBackgroundColor: "rgba(75,192,192,1)",
                pointHoverBorderColor: "rgba(220,220,220,1)",
                pointHoverBorderWidth: 2,
                pointRadius: 1,
                pointHitRadius: 10,
                data: self.records,
                spanGaps: false,
                thisYear:true,
                datalabels: {
                  align: 'end',
                  anchor: 'end'
                }
            },
            {
                label: self.lastRecordName,
                yAxisID: 'Rent',
                fill: false,
                lineTension: 0.3,
                borderWidth:2,
                backgroundColor: "#F0951C",
                borderColor: "#F0951C",
                borderCapStyle: 'butt',
                borderDash: [],
                borderDashOffset: 0.0,
                borderJoinStyle: 'miter',
                pointBorderColor: "#F0951C",
                pointBackgroundColor: "#ffdaa9",
                pointBorderWidth: 2,
                pointHoverRadius: 4,
                // pointHoverBackgroundColor: "rgba(75,192,192,1)",
                // pointHoverBorderColor: "rgba(220,220,220,1)",
                pointHoverBorderWidth: 2,
                pointRadius: 1,
                pointHitRadius: 10,
                data: self.lastRecords,
                spanGaps: false,
                thisYear:false,
                datalabels: {
                  align: 'end',
                  anchor: 'end'
                }
            }
        ];
        // console.log(self.ctx);
        // console.log(self.labels);
        // console.log(datasets);
        self.myChart = new Chart(self.ctx, {
          type: 'line',
          data: {
            labels: self.labels,
            datasets: datasets
          },
          plugins:[ChartDataLabels],
          options: {
            plugins: {
              datalabels: {
                color: function(context) {
                  return context.dataset.backgroundColor;
                },
                font: {
                  size: '10px'
                },
                formatter: Math.round
              }
            },
            scales: {
              Rent:{
                type: 'linear',
                position:'right',
                formatter: Math.round
              },
              Sale: {
                type:'linear',
                position:'left',
                formatter: Math.round
              }
            }
          }
        });
      }
    },
    initChartOptions(){
      var self = this;
      if (self.myChart) {
        return;
      }
      self.ctx = document.getElementById('priceLineChart');
      // Chart.defaults.global.tooltips.enabled = false;
      self.legendLabel = 'clear';
      Chart.defaults.plugins.legend.display = true;
      Chart.defaults.plugins.legend.position = 'bottom';
      Chart.defaults.plugins.legend.labels.boxWidth = 5;
      Chart.defaults.plugins.legend.labels.usePointStyle = true;
      Chart.defaults.plugins.legend.fontSize = 12;
      var oldOnClick = Chart.defaults.plugins.legend.onClick;
      Chart.defaults.plugins.legend.onClick = function () {
        // self.legendLabel = self.legendLabel == 'clear'?'set':'clear';
        return false;
      };
    },
    computedExpireDate(h){
      // h.mt || h.sldd || h.exp
      if (h.exp) {
        return h.exp;
      }
      if (h.sldd) {
        return h.sldd;
      }
      return h.mt;
    },
    showMapSMB() {
      window.bus.$emit('toggle-prop-detail-map-smb')
    },
    showAdminOptions() {
      this.showBackdrop = true;
      toggleModal('propDetailAdmin','open');
    },
    closeModals(){
      if(this.watchingPopup){
        return ;
      }
      this.showBackdrop = false;
      this.showClaim = false;
      toggleModal('oh-modal','close');
      toggleModal('listing-hist-modal','close');
      toggleModal('watchingModal','close');
      toggleModal('propDetailAdmin','close');
      toggleModal('savedLogs','close');
    },
    showListingHistory(){
      if (this.checkAndRedirectDownload()){
        return
      }
      if (!this.dispVar.isLoggedIn) {
        return this.redirect('/1.5/user/login');
      }
      if (!this.addrHist.length) {
        this.getAddrHist({rediret:true});
      }
      // this.toggleHist();
      this.showBackdrop = !this.showBackdrop;
      toggleModal('listing-hist-modal');
    },
    showOhHistory(){
      this.showBackdrop = !this.showBackdrop;
      toggleModal('oh-modal');
    },
    agreeAndRegister(){
      // this.showTermWhenDDF = false;
      this.showTerm = false;
      localStorage.showedTerm = true;
      // this.register();
    },
    getAddrHist(opt={}){
      var self = this;
      // show to all user, if click more, request login
      // if (!self.dispVar.isLoggedIn) {
      //   if (opt.rediret) {
      //     return self.redirect('/1.5/user/login');
      //   } else {
      //     return;
      //   }
      //   // return window.location = '/1.5/user/login';
      // }
      if (!this.dispVar.isApp) {
        return;
      }
      // NOTE: @fred 未登录也可以看到房源历史，不能看到价格
      // if (!this.dispVar.isVipRealtor && !this.isTrebProp) {
      //   return;
      // }
      if (!this.dispVar.isVipRealtor && this.prop.daddr == 'N') {
        return;
      }
      if (self.gettingAddrHist || self.gotAddrHist || self.addrHist.length) {
        return;
      }
      self.gettingAddrHist = true;
      var d = {
        addr:self.prop.addr,
        prov:self.prop.prov_en,
        city:self.prop.city_en,
        unt: self.prop.unt,//was overwritten by origUnt, so is cmty/city
        _id: self.prop._id,
        lat: self.prop.lat,
        lng: self.prop.lng,
        // faddr: self.prop.faddr,
        uaddr:  self.prop.uaddr,
        st_num: self.prop.st_num,
        zip: self.prop.zip,
        type:self.getPropType()
      };
      self.$http.post('/1.5/props/lstdhist', d).then(
        function (ret) {
          self.gettingAddrHist = false;
          self.gotAddrHist = true;
          ret = ret.data;
          if (ret.ok) {
            self.addrHist = ret.l;
          } else {
            console.error(ret.e);
          }
        },
        function (ret) {
          self.gettingAddrHist = false;
          self.gotAddrHist = true;
          ajaxError(ret);
        }
      );
    },
    searchById(prop){
      var url;
      // 未登录不能点击房源历史,查看房源详情
      if (!this.dispVar.isLoggedIn){
        return;
      }
      if (this.dispVar.isApp) {
        url = '/1.5/prop/detail/inapp?id=';
      } else {
        return; //
        // url = '/1.5/search/prop?d=/1.5/mapSearch&id=';
      }
      if (!prop) {
        return;
      }
      if (prop._id) {
        url += prop._id;
      } else if (prop.id) {
        url += prop.id;
      } else {
        url += prop.sid;
        url += '&src='+prop.src;
      }
      if (this.inFrame) {
        url += '&inframe=1';
      }
      url = this.appendDomain(url);
      // this.redirect(url);
      // window.location = url;
      let cfg = {hide:false,title:this._('RealMaster')};
      if (this.dispVar && this.dispVar.isApp) {
        RMSrv.getPageContent(url,'#callBackString', cfg, function(val){
          if (val == ':cancel') {
            return;
          }
        });
      } else {
        window.location.href = url;
      }
    },
    appendDomain(url){
      // var tmp    = document.createElement ('a');
      // tmp.href   = document.URL;
      // var domain = tmp.hostname;
      var location = window.location.href;
      var arr = location.split("/");
      var domain = arr[0] + "//" + arr[2];
      url = domain + url;
      return url;
    },
    showPays(prop){
      //If version < 5 alert,
      // if (this.dispVar.appVer[0] !== '5') {
      //   return;
      // }
      if (!this.dispVar.isLoggedIn) {
        this.redirectLogin()
        return;
      }
      //- if (!this.dispVar.isAdmin && !this.dispVar.isVipUser) {
      //-   this.confirmVip(this.dispVar.lang);
      //-   return;
      //- }
      var url = '/1.5/prop/topup/charge?id=';
      if (!prop) {
        prop = this.prop;
      }
      // if (this.isRMProp) {
      //   url = '/1.5/prop/rmtopup/charge?id='
      //   url += prop.id;
      // } else {
      url += prop._id;
      // }
      url += '&addr='+prop.addr;
      url += '&city='+prop.city_en;
      url += '&prov='+prop.prov_en;
      url += '&unt='+(prop.origUnt || prop.unt);
      url += '&cnty='+prop.cnty_en;
      if( prop.hideInfo ){
        url += '&hideInfo='+prop.hideInfo;
      }
      this.openTBrowser(this.appendDomain(url), {title:this._('TOP Listing'), nojump:true});
      // window.location = url;
    },
    openVirtualTour(){
      let prop = this.prop;
      if (!prop.vturl) {
        return;
      }
      // NOTE:  user didnt input protocol
      if (!/^http/.test(prop.vturl)) {
        prop.vturl = 'https://'+prop.vturl;
      }
      if (RMSrv.isIOS()) {
        this.openTBrowser(prop.vturl,{nojump:true})
      } else {
        RMSrv.showInBrowser(prop.vturl)
      }
    },
    setAgentSrc(){
      var self = this;
      var src = self.agentAvts[(self.adRealtorsIdx++)%self.adRealtorsCount]
      self.computedAgentSrc = src || '/img/icon_nophoto.png';
      // for (let elem of document.querySelectorAll('.agent-avt')) {
      //   elem.classList.add('fadeIn')
      // }
      // setTimeout(function () {
      //   for (let elem of document.querySelectorAll('.agent-avt')) {
      //     elem.classList.remove('fadeIn')
      //   }
      // }, 1000);
      // console.log(self.computedAgentSrc);
    },
    getTopAgents(){
      var self = this;
      var prop = self.prop;
      if (!(prop.city_en && prop.prov_en)) {
        return;// RMSrv.dialogAlert('No city');
      }
      // out side app wont show agents
      if (!self.dispVar.isApp) {
        return;
      }
      if (this.isRMProp() || this.inFrame) {
        return;
      }
      // dont show to realtor unless rltrTopAd
      if ((this.dispVar.isRealtor || this.fromMarket) && !this.dispVar.rltrTopAd) {
        return;
      }
      if (this.dispVar.hasFollowedVipRealtor) {
        return;
      }
      var d = {
        city:prop.city_en,
        prov:prop.prov_en,
      };
      if (prop.bndCity && prop.bndCity.nm){
        d.city = prop.bndCity.nm
      }
      if (prop.bndSubCity && prop.bndSubCity.nm){
        d.subCity = prop.bndSubCity.nm
      }
      self.$http.post('/1.5/topagent/topagents', d).then(
        function (ret) {
          ret = ret.body;
          self.loading = false;
          // console.log(ret);
          if (ret.ok) {
            // TODO: set avt roll
            self.adRealtorsCount = ret.ta05.length+ret.ta1.length+ret.ta15.length+ret.ta2.length;
            self.adRealtorsIdx = 0;
            for (let tp of ['ta05','ta1','ta15','ta2']) {
              self[tp] = ret[tp] || [];
              for (let rl of ret[tp]) {
                // 过滤重复的agent avt
                if(self.agentAvts.includes(rl.avt)){
                  self.adRealtorsCount--;
                  continue
                }
                self.agentAvts.push(rl.avt);
              }
            }
            self.setAgentSrc();
            setInterval(function () {
              self.setAgentSrc();
            }, 2000);
          } else {
            console.log(ret.err);
            RMSrv.dialogAlert(ret.err);
          }
        },
        function (ret) {
          ajaxError(ret);
        }
      );
    },
    getCmntNum:function() {
      var self = this;
      if (self.isRMProp()) {
        return;
      }
      self.$http.get('/1.5/forum/cmntnum/'+self.prop._id).then(
        function(ret) {
          self.cmntNum = ret.data.cmntnum;
        },
        function(err) {
          ajaxError(err);
        }
      );
    },
    // TODO: add admin func prop., either or
    refreshPic(){
      var self = this;
      // if (!self.prop.phodl) {
      //   return;
      // }
      self.$http.post('/1.5/prop/manage', {_id:this.prop._id, sid: this.prop.sid, src:this.prop.src, refreshPic:true}).then(
        function (ret) {
          ret = ret.data;
          if (ret.ok) {
            self.prop = Object.assign({},self.prop,{phodl:null});
            window.bus.$emit('flash-message', 'Request successful, please wait a while.');
            if (ret.msg) {
              RMSrv.dialogAlert(ret.msg);
            }
          } else {
            RMSrv.dialogAlert(ret.err);
          }
        },
        function (ret) {
          ajaxError(ret);
        }
      );
    },
    useTrebPhoto() {
      var self = this;
      self.refreshPic();
      var useTrebPhoto = self.prop.useTrebPhoto;
      if (typeof useTrebPhoto == 'undefined') {
        useTrebPhoto = false;
      }
      useTrebPhoto = !useTrebPhoto;
      self.$http.post('/1.5/prop/manage', {_id:this.prop._id, useTrebPhoto}).then(
        function (ret) {
          ret = ret.data;
          if (ret.ok) {
            self.prop = Object.assign({},self.prop,{useTrebPhoto});
            if (ret.msg) {
              RMSrv.dialogAlert(ret.msg);
            }
            window.location.reload();
          } else {
            RMSrv.dialogAlert(ret.err);
          }
        },
        function (ret) {
          ajaxError(ret);
        }
      );
    },
    showVIPSegment(){
      var self = this;
      self.changeSegmentTag('itemVIP');
      if(!self.brkgs.length){
        self.getListAgentInfo();
      }
      if (self.prop.bm || self.prop.onm || self.prop.coop) {
        return;
      }
      if(!(self.isTrebProp && self.dispVar.isVipRealtor && self.dispVar.isTrebUser)){
        return;
      }
      self.$http.post('/1.5/props/vipinfo', {id:this.prop._id}).then(
        function (ret) {
          ret = ret.data;
          if (ret.ok) {
            self.prop = Object.assign({},self.prop,ret);
          } else {
            RMSrv.dialogAlert(ret.err);
          }
        },
        function (ret) {
          ajaxError(ret);
        }
      );
    },
    goPropStat(){
      if (!this.prop || this.noeval) {
        return;
      }
      var url = '/1.5/prop/stats', prop=this.prop;
      url = url+'?city='+prop.city_en+'&prov='+prop.prov_en+'&cityName='+prop.city;
      if (prop.cmty_en) {
        url += '&cmty='+encodeURIComponent(prop.cmty_en)+'&cmtyName='+encodeURIComponent(prop.cmty);
      }
      // if (this.$parent.closeAndRedirect) {
      //   this.$parent.closeAndRedirect(url);
      //   return;
      // }
      if (this.inFrame && RMSrv.closeAndRedirectRoot) {
        // window.location = url;
        url+='&d=/1.5/index';
        url = this.appendDomain(url);
        RMSrv.closeAndRedirectRoot(url);
        return;
      }
      this.redirect(url);
    },
    calcTransferTax(){
      if (!this.prop.lp) {
        return console.error('No prop lp yet');
      }
      var isTorontoProperty = this.prop.city_en == 'Toronto'?true:false;
      var price = this.prop.lp;
      var tax;
      var tax1;
      var tax_t = false;
      var prov = this.prop.prov_abbr;
      var ret = [];
      if(prov == 'ON' && window.ontario_transfer_tax){
        tax = ontario_transfer_tax(price);
        tax1 = ontario_transfer_tax(price,true);
        if (isTorontoProperty){
          tax += toronto_transfer_tax(price);
          tax1 += toronto_transfer_tax(price,true);
        }
        ret = [tax, tax1];
      } else if (prov == 'BC') {
        for (let foreignBuyer of [false, true]) {
          tax = british_columbia_transfer_tax(price, false, foreignBuyer);
          tax1 = british_columbia_transfer_tax(price, true, foreignBuyer);
          ret.push(tax);
          ret.push(tax1);
        }
      } else if (prov == 'AB') {
        tax_t = alberta_title_fee(price);
        ret.push(tax_t);
      }
      return ret;
    },
    calcMortgageMonthly () {
      //mortgage
      var self = this;
      var amortization      = self.amortization || 30;
      var interestRate      = self.interestRate || 5.3;
      var downpayPercentage = self.downpayPercentage || 0.35;
      if (!self.prop.lp) {
        console.error('No prop lp yet');
        return 1;
      }
      if (!window.mortgage_payment_canada) {
        console.error('No Library Included for calc Mortgate!');
        return 1;
      }
      var downpayAmount = Math.round(this.prop.lp * downpayPercentage);
      var amount = this.prop.lp - downpayAmount;
      var monthlyPayment = mortgage_payment_canada(amount, interestRate, amortization);
      var interest1 = mortgage_interest(amount, interestRate);
      var principal1 = monthlyPayment - interest1;
      return monthlyPayment;
    },
    toggleHist(){
      // if (this.priceHist.length) {
      //   return this.priceHist = [];
      // }
      var ret = (this.prop.his || []).slice();
      // if (this.prop.sp && this.prop.slddt) {
      //   ret.push({s:'Sld',ts:this.prop.slddt,c:this.prop.sp});
      // }
      // if (!this.dispVar.isApp) {
      //   ret = ret.slice(0,1)
      // }
      ret = ret.reverse()
      var removeInvalid = (i) => {
        // (/Sld|Lsd|Sold|Leased|Sc/i.test(i.n)||
        if (/Chg/i.test(i.s) && i.n == 'Pc') {
          return false;
        }
        return true;
      }
      ret = ret.filter(removeInvalid);
      this.priceHist = ret;
    },
    resetSlider () {
      if (!this.contentWrapper) {
        return;
      }
      // this.showPropM = false;
      this.gotsprice = false;
      this.showPrincipalBase = false;
      this.mlang = 'En';
      this.activeSegment = 'itemSummary';
      this.contentWrapper.scrollTop = 0;
      this.priceHist = [];
      this.picUrls = [];//'/img/ajax-loader.gif'
      this.addrHist = [];
      this.getpricing = false;
      this.staticMaploaded = false;
      // RMSrv.getPageContent("", "html", {close:true}, function() {});
      window.bus.$emit('reset-preview-prop');
      window.bus.$emit('prop-detail-close');
    },
    isPropFld (i) {
      return /^[a-z]+/i.test(i);
    },
    isTextFld (i) {
      return /^[\-|\*]+/i.test(i);
    },
    setUpPreview (evt, isListingImg) {
      var h, w, self=this, el=evt.target, idx;
      if (el) {
        if (/^data:image/.test(el.src)) {
          return;
        }
        w = el.naturalWidth;
        h = el.naturalHeight;
        el.setAttribute('data-size', w + 'x' + h);
        idx = parseInt(el.getAttribute('dataindex'));
        if (!self.sizes[idx]) {
          self.sizes[idx]=(w + 'x' + h);
        }
        if(isListingImg){
          const containerRatio = this.containerWidth / this.computedImgHeight;
          const imageRatio = w / h;
          if (Math.abs(containerRatio - imageRatio) < 0.3) {
            if (imageRatio <= containerRatio){
              el.style.width = '100%';
              return
            } else {
              el.style.height = '100%';
              return
            }
          }
        }
        if(el.classList.contains('detail-photo')){
          if (h > w) {
            el.classList.add('vertical');
          } else if (idx > 3) {
            const parentDiv = el.closest('div');
            if (parentDiv) {
              parentDiv.style.height = 'auto';
            }
          }
        }
      }
    },
    toggleFav () {
      // use common module
      window.bus.$emit('prop-fav-add', this.prop);
    },
    showPromoteModal () {
      var d = {
        _id: this.prop._id,
        sid: this.prop.sid,
        src: this.prop.src
      }, bus = window.bus;
      bus.$emit('show-promote', d);
      bus.$emit('toggle-drop', 'show');
    },
    isSharedProp(){
      if(this.fromShare || vars.share) return true;
      return false;
    },
    getPropDetail (prop) {
      // alert(JSON.stringify(prop)); // TODO: remove
      var self = this;
      var ml_num = prop._id, d = {};
      var mongoRegex = /^[a-f\d]{24}$/i
      if (/^RM/.test(prop.id) || /^RM/.test(ml_num)) {
        d.id = prop.id || ml_num;
      } else if (/^(DDF|TRB|BRE|RHB|OTW|CLG|EDM|CAR)/.test(ml_num) || mongoRegex.test(ml_num)){
        d._id = ml_num;
      } else {
        d.sid = ml_num;
      }
      if (!ml_num) {
        d.src = prop.src;
        d.sid = prop.sid;
        d.board = prop.board;
      }
      if (!vars) {
        vars = {}
      }
      if (this.isSharedProp()) {
        d.share = 1;
      }
      var locale = self.locale;
      if (locale) {
        d.locale = locale;
      }
      if (self.mapMode == 'fav') {
        d.fav = 1;
      }
      // console.log(d);
      //post to backend, let backend handle RM and case
      self.$http.post('/1.5/props/detail', d).then(
        function (ret) {
          ret = ret.data;
          var bus = window.bus;
          if (!ret.e) {
            // self.prop = ret.prop || {};
            self.prop = ret.detail
            self.disclaimer = ret.dis
            // if (self.isDDFProp(self.prop) && self.showTerm) {
            //   self.showTermWhenDDF = true;
            // }
            bus.$emit('prop-retrived', ret.detail);
            checkAndSendLogger(null,{sub:'detail',id:self.prop._id,act:'load'});
            self.initProp();
          } else {
            // console.error(ret.e);
            // return;
            if (ret.ec == 2) {
              return window.location = ret.url;
            }
            if (ret.ec == 1) {
              bus.$emit('prop-retrived', ret);
            }
            // RMSrv.dialogAlert(ret.e);
          }
        },
        function (ret) {
          ajaxError(ret);
        }
      );
    },
    showSMB(){
      // TODO: put back
      // window.html2canvas = html2canvas;
      // var opt = {proxy: '/1.5/htmltoimg/imgflyer',useCORS: true};
      // https://github.com/niklasvh/html2canvas-proxy-nodejs
      // html2canvas(document.querySelector('.prop-heading-wrapper')).then(function(canvas) {
      //   // canvas.setAttribute("id", "uniqueIdentifier");
      //   document.querySelector('#contentWrapper').appendChild(canvas);
      //   document.querySelector('#share-image').textContent = canvas.toDataURL("image/png")//.replace("image/png", "image/octet-stream")
      //   RMSrv.share('wechat-friend');
      // });
      // return;
      RMSrv.share('show');
    },
    showRMBrkgInfo ({cardTitle,mailBody,mailSubject}){
      // if visitor show signupform
      // if (this.dispVar.isVisitor && this.prop.ltp !== 'rent') {
      //   this.prop.rmcontact = true;
      //   // TODO: should not be here, user should popup signup form
      //   this.findARealtor();
      //   return;
      // }
      bus.$emit('show-rmbrkg', {prop:this.prop, picUrls:this.picUrls,cardTitle,mailBody,mailSubject});
    },
    showBrkgInfo ({title}) {
      var bus = window.bus;
      if (this.dispVar.isVipRealtor || this.prop.canEditOhs) {
        bus.$emit('show-brkg', {title,prop:this.prop, picUrls:this.picUrls, ta1:this.ta1,ta2:this.ta2});
      } else {
        // window.bus.$emit('flash-message', this.$parent._('VIP Only'));
        this.confirmVip(this.dispVar.lang);
        // this.findARealtor({showla:true})
      }
    },
    reportError(){
      this.$parent.signupTitle = this._('Report Error');
      this.$parent.userForm.tp = 'feedback';
      this.$parent.userForm.page = '';
      this.$parent.userForm.m = '';
      this.$parent.userForm.title = 'Report a phishing listing';
      toggleModal('SignupModal','open');
    },
    redirectLogin(isRoot){
      var url = '/1.5/user/login?d=/1.5/index#index';
      url = this.appendDomain(url);
      if (isRoot) {
        return RMSrv.closeAndRedirectRoot(url)
      }
      RMSrv.closeAndRedirect(url)
    },
    getImgEncodeUrl(){
      if (!this.picUrls.length) {
        return null
      }
      if (this.picUrls[0]) {
        return encodeURIComponent(this.picUrls[0])
      }
      return null;
    },
    showStigma(url){
      if (!this.dispVar.isLoggedIn) {
        this.redirectLogin(true);
        return;
      }
      this.redirect(url)
    },
    // findARealtor (opt={}){
    //   // check login first
    //   if (!this.dispVar.isLoggedIn) {
    //     this.redirectLogin(true);
    //     return;
    //   }
    //   // new signup form logic will be handled in contact realtor logic
    //   // if (this.prop.rmcontact) {
    //   //   this.$parent.forceShowSignup = false;
    //   //   //- this.$parent.userForm.ueml = this.dispVar.defaultEmail;
    //   //   this.$parent.signupTitle = this._('Book a Tour');
    //   //   if(!this.$parent.userForm.url){
    //   //     this.$parent.userForm.url = this.$parent.fullUrl || document.URL;
    //   //   }
    //   //   toggleModal('SignupModal','open');
    //   //   return;
    //   // }
    //   if(this.prop.bndCity && (this.prop.bndCity.nm != this.prop.city_en)){
    //     var city = this.prop.bndCity.nm
    //   }else{
    //     var city = this.prop.city_en
    //     var cityName = this.prop.city
    //   }
    //   var url = "/1.5/user/follow?city=" + city + "&prov=" + this.prop.prov_en;
    //   if (cityName){
    //     url += "&cityName=" + cityName
    //   }
    //   if (opt.showla) {
    //     url+='&showla=1'
    //   }
    //   if (this.prop) {
    //     url+='&_id='+this.prop._id;
    //     if(this.prop.saletp_en) {
    //       url+='&saletp='+this.prop.saletp_en[0];
    //     }
    //     url+='&new=1&img='+this.getImgEncodeUrl()
    //   } // url+='&propid='+this.prop._id
    //   if (this.prop.bndSubCity && this.prop.bndSubCity.nm){
    //     url+= '&subCity='+this.prop.bndSubCity.nm
    //   }
    //   // if (this.$parent.closeAndRedirect) {
    //   //   this.$parent.closeAndRedirect(url);
    //   //   return;
    //   // }
    //   // return window.location = url;
    //   if (this.dispVar.isApp) {
    //     // url = this.appendDomain(url);
    //     url += '&inframe=1';
    //     RMSrv.openTBrowser(url,{title:this._('RealMaster')});
    //     return;
    //   }
    //   this.redirect(url);
    // },
    // ref: see propMap.js
    //- showCommunity(){
    //-   // openTBrowser('/1.5/census/commu?loc=' + prop.lat + ',' + prop.lng, {nojump:true})
    //-   var prop = this.prop;
    //-   var url = '/1.5/census/commu'
    //-   if(prop.commuId && prop.commuId.length) {
    //-     url = url + '?commuId=' + prop.commuId.join(',');
    //-   } else {
    //-     url = url + '?loc=' + prop.lat + ',' + prop.lng;
    //-   }
    //-   url = this.appendDomain(url);
    //-   if(this.dispVar.isApp) {
    //-     RMSrv.getPageContent(url,'#callBackString', {hide:false, title: this._('Map')}, function(val) {
    //-       if (val == ':cancel') {
    //-         return;
    //-       }
    //-     });
    //-   } else {
    //-     document.location.href='/adPage/needAPP';
    //-   }

    //- },
    showNearby(mapmode){
      var self = this;
      // if (self.$parent.moveToRecord) {
      //   // alert(lat+':'+lng)
      //   self.showItems = false;
      //   self.focused = false;
      //   self.viewMode = 'map';
      //   self.resetFilter({keepType:true});
      //   self.moveToRecord({lat,lng});
      // } else {
      var lat = this.prop.lat;
      var lng = this.prop.lng;
      var saletp = this.isSale?'sale':'lease';
      // NOTE: &zoom=13 deprecated, use delta=0.0025
      var url = `/1.5/mapSearch?loc=${lat},${lng}&hMarker=1&mode=map&saletp=${saletp}&propId=${this.prop._id}`;
      if (!this.dispVar.isApp) {
        url = '/adPage/needAPP';
        return this.redirect(url);
      }
      // alert(this.$parent.page)
      // NOTE: case1: from appindex.js; cas2: from mapNative; case3: from mapWeb
      var fromNativeMap = false
      // this.$parent.page == 'appPropDetailPage'
      if(vars.mode == 'map'){
        fromNativeMap = true;
      }
      var mode = 'sale',appmode = 'mls'
      if(mapmode == 'sale'){
        if(self.isRMProp()){
          mode = this.prop.ltp
          if(this.prop.marketRmProp){
            appmode = 'mls'
            mode = 'sale'
          } else {
            appmode = 'rm'
          }
        }
      } else { // mapmode == 'sold'
        mode = 'sold&dom=-90'
      }
      url += `&mapmode=${mode}&appmode=${appmode}&resetmap=1`
      // alert(fromNativeMap+JSON.stringify(vars))
      // RMSrv.getPageContent(url,'#callBackString', {hide:true}, function(val) {
      //   if (val == ':cancel') {
      //     return;
      //   }
      // });
      // TODO: change this if do not want to stack maps
      // return window.location = url;
      // 直接处理
      RMSrv.setItemObj({key:'map.feature.prop.current_saved_search',value:null,stringify:false,store:false})
      RMSrv.setItemObj({key:'map.feature.prop.nearby_prop',value:this.prop,stringify:false,store:false},function(){
        // console.log('+++after set',tmp.url)
        window.location = url;
      })
      if (self.isNewerVer(self.dispVar.coreVer,'6.2.7') || (!RMSrv.isIOS() && self.hasGoogleService)) {
        return;
      }
      if (fromNativeMap) {
        var val = url.split('?')[1];
        var callBackStr = ':ctx:'+val;
        // alert(callBackStr);
        window.rmCall(callBackStr);
      } else {
        this.redirect(url);
      }
    },
    redirect(url){
      if (this.$parent.closeAndRedirect) {
        url = this.appendDomain(url);
        this.$parent.closeAndRedirect(url);
        return;
      }
      return window.location = url;
    },
    processOpenHouse() {
      function compare(a,b) {
        if (a.f < b.f)
          return -1;
        if (a.f > b.f)
          return 1;
        return 0;
      }
      if (this.prop.ohz && this.prop.ohz.length) {
        // TODO: ohvs and display and add calendar
        var ohList = [];
        this.prop.ohz.sort(compare);
        this.validOh = [];
        // https://stackoverflow.com/questions/4310953/invalid-date-in-safari
        // > new Date('2020-03-17 14:00')get Invalid Date in safari.

        for (let oh of this.prop.ohz) {
          let newoh = {}
          newoh.month = this.getMonth(oh);
          newoh.day = this.getDay(oh);
          newoh.time = this.getOhTime(oh);
          newoh.date = oh.f.split(' ')[0].split('-')[2];
          newoh.year = oh.f.split(' ')[0].split('-')[0];
          newoh.value = oh;
          newoh.l = oh.l;
          newoh.tp = oh.tp;
          if (!this.isPassed(oh.t)){
            this.validOh.push(newoh);
          }
        }
        this.calculatedSpWidth = parseInt((window.innerWidth-30)/1.6);
        this.spWrapperWidth = this.calculatedSpWidth * (this.validOh.length+0.2) +'px';
      }
    },
    initProp () {
      var ml_num  = vars.ml_num || this.prop.sid || this.prop._id;
      if (!ml_num) {
        return;
      }
      var self = this;
      self.picUrls = this.setupThisPicUrls(this.prop);
      if(this.isRMProp() & !self.$parent.userForm.uid) {
        self.$parent.userForm.uid = self.prop.uid
      }
      // this.loadMap();
      if (!this.prop.user) {
        this.prop.user = {};
      }
      this.hasCmtyStat = this.prop.hasCmtyStat;
      this.processOpenHouse();
      this.toggleHist();
      self.getCmntNum();
      self.getEvaluateHist();
      setTimeout(function () {
        if (typeof RMSrv !== "undefined" && RMSrv !== null ? RMSrv.wxOnShare : void 0) {
          RMSrv.wxOnShare();
        }
        // self.getSchoolsInfo(self.prop);
      }, 800);
      self.$parent.userForm.addr = (self.prop.unt?self.prop.unt+' ':'')+self.prop.addr;
      self.$parent.userForm.prov = self.prop.prov_en;
      self.$parent.userForm.city = self.prop.city_en;
      if(self.$parent.userForm.lp){
        self.$delete(self.$parent.userForm, 'lp');
      }
      if(self.$parent.userForm.lpr){
        self.$delete(self.$parent.userForm, 'lpr');
      }
      if(self.prop.lp) {
        self.$parent.userForm.lp = self.prop.lp;
      } else if (self.prop.lpr) {
        self.$parent.userForm.lpr = self.prop.lpr;
      }
      self.$parent.userForm.saletp = self.prop.saletp_en
      self.$parent.userForm.ptype2 = self.prop.ptype2_en
      if(!self.dispVar.isApp) {
        self.$parent.userForm.url = document.URL;
      }
      self.setLocationTags();
      self.taxList = self.calcTransferTax();
      self.initMainInfo();
      // TODO: delayed loading? +3 db read
      if(self.dispVar.isLoggedIn){
        self.getPropSavedLog()
        if(self.dispVar.isRealGroup || self.dispVar.isNoteAdmin){
          self.getPropShowingLog()
        }
      }
      for(let k in this.prop.dis){
        if(this.prop.dis[k]){
          this.prop.disclaimerTerms = this.disclaimer[k].terms
          this.prop.disclaimerIcons = this.disclaimer[k].icons
        }
      }
    },
    getNotesCount(){
      if(this.isRMProp()){
        return ;
      }
      var self = this;
      if(!this.prop.uaddr){
        return;
      }
      if(!this.dispVar.isLoggedIn){
        return;
      }
      getStandardFunctionCaller()('notesCount',{uaddr: this.prop.uaddr,unt: this.prop.unt},(err,ret)=>{
        if (err) {
          bus.$emit('flash-message',err.toString());
          return;
        }
        if(ret && ret.totalCount >= 0){
          self.$set(self,'hasSelfM',ret.hasSelfM);
          self.$set(self,'totalCount',ret.totalCount);
          self.$set(self,'noteId',ret.noteId);
        }
      })
    },
    addAccuracyFields(title,tp,params = 'accuracyField',number = 10){
      for(let i=1;i<=number;i++){
        this[params].push({
          title: title,
          tp: `${tp}${i}`
        })
      }
    },
    processInto2Columns(params){
      let rows = [];
      let row = [];
      for(let i=0;i<this[params].length;i++){
        if(this.allEstimateInfo[this[params][i].tp]){
          row.push(this[params][i]);
        }
        if(row.length == 2){
          rows.push(row);
          row = [];
        }
      }
      if(row.length > 0){
        rows.push(row);
      }
      return rows;
    },
    initAccuracyInfo(){
      let self = this;
      if(this.accuracyInfo.length){
        return;
      }
      let fields = [
        {
          title: 'Sold Price N Result',
          tp: 'sp_mn_result'
        },{
          title: 'Sold Price N Min',
          tp: 'sp_mn_min'
        },{
          title: 'Sold Price N Max',
          tp: 'sp_mn_max'
        },{
          title: 'Sold Price D Result',
          tp: 'sp_md_result'
        },{
          title: 'Sold Price D Min',
          tp: 'sp_md_min'
        },{
          title: 'Sold Price D Max',
          tp: 'sp_md_max'
        },{
          title: 'Dom Days',
          tp: 'dom_m1_days'
        },{
          title: 'Update',
          tp: 'ml_mt'
        }
      ]
      this.addAccuracyFields('Rent', 'rent_mn');
      this.addAccuracyFields('Value', 'value_mn');
      this.addAccuracyFields('Built Year', 'bltYr_mn');
      this.addAccuracyFields('Size', 'sqft_mn');
      fields.forEach(function(item) {
        self.accuracyField.push(item);
      });
      this.addAccuracyFields('Sold Price', 'sp_mn', 'soldAccuracyField', 30);
      this.addAccuracyFields('Sold Price', 'sp_md','soldAccuracyField', 30);
      for(let i=1;i<=10;i++){
        this.dealbltYrAcuValue(`bltYr1_mn${i}`,`bltYr2_mn${i}`,`bltYr1_mn${i}_acu`,`bltYr2_mn${i}_acu`,`bltYr_mn${i}`,`bltYr_mn${i}_acu`);
      }
      if(Object.keys(this.allEstimateInfo).length){
        this.accuracyInfo = this.processInto2Columns('accuracyField');
        this.soldAccuracy = this.processInto2Columns('soldAccuracyField');
      }
    },
    initMainInfo(){
      var left=[],right=[],rows=[];
      if(this.computeBdrms(this.prop) != '' ||
        this.computeBthrms(this.prop) != null ||
        this.computeGr(this.prop) != null){
        left.push({tp:'badroom'})
      }
      if(this.prop.sqft || this.prop.rmSqft || this.prop.front_ft){
        left.push({tp:'size'})
      }
      if(this.prop.fce){
        left.push({tp:'fce'})
      }
      if((this.prop.rmStoreys || this.prop.condoStories)  && this.prop.propType.indexOf('Townhouse') < 0){
        left.push({tp:'storeys'})
      }
      if(this.prop.bltYr || this.prop.age|| this.prop.Age|| this.prop.ConstructedDate || this.prop.rmBltYr|| this.prop.bltYr1|| this.prop.bltYr2 || this.prop.condoAge){
        left.push({tp:'bltYr'})
      }
      if(this.prop.condoUnits){
        left.push({tp:'condoUnits'})
      }
      if(this.prop.propType){
        right.push({tp:'propType'})
      }
      if(this.prop.pricePerSqft || this.prop.ListPriceSquareFoot){
        right.push({tp:'pricePerSqft'})
      }
      if(this.prop.mfee){
        right.push({tp:'mfee'})
      }
      if(this.prop.tax){
        right.push({tp:'tax'})
      }
      if(this.prop.lp && this.taxList.length>0){
        right.push({tp:'transferTax'})
      }
      if(this.prop.poss_date || this.prop.psn){
        right.push({tp:'poss'})
      }
      if(left.length == 0 && right.length>0){
        // 如果左侧数据均为空，右侧有数据，则将右侧数据放在左侧
        for(let i in [0,1,2,3,4]){
          rows.push([right[i],left[i]])
        }
      }else{
        for(let i in [0,1,2,3,4,5]){
          rows.push([left[i] || {},right[i]])
        }
      }
      this.mainInfoRows = rows;
    },
    getCensusId() {
      var commuId = null;
      if (this.prop.cs21 && this.prop.cs21.length) {
        commuId = [this.prop.cs21];
      } else if (this.prop.commu2016Id && this.prop.commu2016Id.length) {
        commuId = this.prop.commu2016Id;
      } else if (this.prop.cs16 && this.prop.cs16.length) {
        commuId = [this.prop.cs16];
      }
      return commuId;
    },
    getCensusYear() {
      if (this.prop.cs21 && this.prop.cs21.length) {
        return '2021';
      }
      if (this.prop.commu2016Id && this.prop.commu2016Id.length) {
        return '2016';
      }
      if (this.prop.cs16 && this.prop.cs16.length) {
        return '2016';
      }
      return null;
    },
    // TODO: change to onscroll
    setLocationTags(){
      this.locationTags = [].concat(this.defaultLocationTags)
      if(!(this.prop.cmty || this.prop.origCmty)){
        this.locationTags.shift()
      }
      //check prop has censusId or loc, has loc can query loc for census data
      // if both none hide demographics
      if (!(this.getCensusId() || this.prop.loc)) {
        this.locationTags.pop();
      }
      this.locationTag = this.locationTags[0].k;
      if((this.prop.hasCmtyId) || this.prop.cmty){
        this.getCmtyImage(this.prop);
      }
    },
    initSchoolInfoAndMortgage(){
      var self = this;
      if(self.initedSchool){
        return;
      }
      self.getSchoolsInfo(self.prop);
      self.amortization      = 30;
      self.interestRate      = 5.3;
      self.downpayPercentage = 0.35;
      self.mortgageMonthly = self.calcMortgageMonthly();
      // if (self.mortgageMonthly < 2) {
      //   self.mortgageMonthly = 1;
      // }
      self.initedSchool = true;
    },
    getShowCond (n) {
      var k = n[1];
      if (k && (k == '')) {
        return this.prop.stp_en == 'Rent' || this.prop.stp_en == 'Lease';
      } else {
        return this.prop[n[0]];
      }
    },
    findSch () {
      if (this.schVueClick) {
        //TODO: emit find event
      } else {
        window.location = "/1.5/mapSearch?loc="+this.prop.lat+','+this.prop.lng+"&zoom15=1";
      }
    },
    removePicSize(l=[]){
      let ret = [];
      l.forEach((i) => {
        ret.push(i.replace(/(\/(414|768|828)\.)/,'/'))
      });
      return ret;
    },
    getShortUrlId(url){
      // 参数校验
      if (!url || typeof url !== 'string') {
        return false;
      }
      try {
        // 使用正则表达式匹配格式
        const match = url.match(/^\/s\/([^\/\s]+)$/);
        // 如果匹配成功，返回捕获组（ID部分）
        return match ? match[1] : false;
      } catch (error) {
        return false;
      }
    },
    hasShortUrl(){
      if(vars.wDl === '1' || this.isSharedProp()){
        let currentPath = window.location.pathname;
        let shortUrlId = this.getShortUrlId(currentPath);
        if(shortUrlId){
          return encodeURIComponent(shortUrlId);
        }
        return false
      }
      return false
    },
    previewPic (evt,isFloorPlan) {
      var compressed, index, text, url, self=this;
      // if (self.tbrowser) {
      //   return;
      // }
      if(isFloorPlan){
        index = 0;
        // self.addClassNameInOnimgWrapperLeft('picUrlShow')
      }else{
        let el=evt.target||evt.srcElement;
        index = parseInt(el.getAttribute('dataindex'));
      }
      if (isNaN(index)) {
        index = Math.abs(window.slideNumber) || this.imgidx ||0;
      }
      if (!LZString) {
        return console.error('LZString is required!');
      }
      let propId = this.prop._id || this.prop.id;
      url = '/1.5/SV/photoScrollSwipe?picIndex=' + index + `&id=${propId}&isBuilding=${this.prop.isBuilding}`;
      if (!window.RMSrv && !this.inFrame) {
        return;
      }
      if(this.prop.sid){
        url += `&sid=${this.prop.sid}`
      }
      if(this.prop.addr){
        url += `&addr=${encodeURIComponent(this.prop.addr)}`;
      }
      if(this.prop.origUnt || this.prop.unt){
        let unt = this.prop.origUnt || this.prop.unt;
        url += `&unt=${encodeURIComponent(unt)}`;
      }
      // if(this.prop.cmty){
      //   url += `&cmty=${this.prop.origCmty || this.prop.cmty}`
      // }
      if(this.condoInfo.condoNm){
        url += `&condoNm=${encodeURIComponent(this.condoInfo.condoNm)}`
      }
      // if(this.prop.city){
      //   url += `&city=${this.prop.city}`
      // }
      if(this.prop.uaddr){
        url += `&uaddr=${encodeURIComponent(this.prop.uaddr)}`
      }
      let bdrm  = this.computeBdrms(this.prop);
      if(bdrm != ''){
        url += `&bdrm=${encodeURIComponent(bdrm)}`
      }
      let bthrm = this.computeBthrms(this.prop);
      if(bthrm != null){
        url += `&bthrm=${encodeURIComponent(bthrm)}`
      }
      if(this.prop.fce){
        url += `&fce=${this.prop.fce}`
      }
      if(this.prop.sqft || this.prop.rmSqft){
        url += `&sqft=${this.prop.sqft || this.prop.rmSqft}`
      }
      if(isFloorPlan){
        url += '&isFloorPlan=true';
      }
      let shortUrlId = this.hasShortUrl();
      if(shortUrlId){
        url += `&shortUrlId=${shortUrlId}`;
      }
      self.openTBrowser(url,{nojump:true,title:self._('RealMaster')});
    },
    previewAgrmntImg (evt) {
      var compressed, index, text, url, h, w, self=this, el=evt.target||evt.srcElement;

        w = el.naturalWidth;
        h = el.naturalHeight;
        el.setAttribute('data-size', w + 'x' + h);
        self.agrmntImgSize.push((w + 'x' + h));
        // if(el.classList.contains('detail-photo') && h > w){
        //   el.classList.add('vertical')
        // }
      text = JSON.stringify({
        urls: self.removePicSize([self.prop.agrmntImg]),
        index: 1,
        sizes: self.agrmntImgSize
      });
      if (!LZString) {
        return console.error('LZString is required!');
      }
      compressed = LZString.compressToEncodedURIComponent(text);
      url = '/1.5/SV/photoSwipe?data=' + compressed;
      if (!window.RMSrv && !this.inFrame) {
        return;
      }
      self.openTBrowser(url,{nojump:true,title:self._('RealMaster')});
    },
    doEditPropM(prop){
      var url = '/1.5/prop/editremarks?id='+prop._id;
      RMSrv.openTBrowser(url,{});
    },
    editPropLoc(){
      var url = '/1.5/prop/editloc?id='+this.prop._id;
      RMSrv.openTBrowser(url,{});
    },
    editPropCmtyMap() {
      var url = '/1.5/prop/cmtyStaticMap?id='+this.prop.bndCmty._id;;
      RMSrv.openTBrowser(url,{});
    },
    deleteProp(){
      var self = this;
      var _doDelete = function(r) {
        var follow;
        if (r === 2) {
          var data = {id:self.prop.id};
          if(self.prop.cmstn || self.prop.market){
            data._id = self.prop._id;
          }
          self.$http.post('/1.5/props/delete', data).then(
            function (ret) {
              ret = ret.data;
              if (ret.ok) {
                RMSrv.dialogAlert(ret.msg)
              } else {
                RMSrv.dialogAlert(ret.err);
              }
            },
            function (ret) {
              ajaxError(ret);
            }
          );
        } else {
          console.log('canceld delete');
        }
      };
      RMSrv.dialogConfirm('Are you sure',
        _doDelete,
        "Message",
        ['Yes', 'Cancel']
      );
    },
    editPropM(){
      if (!this.prop.m_zh) {
        return;
        // return this.translate_m(prop,{edit:1})
      }
      this.doEditPropM(this.prop);
    },
    translate_m (prop,cfg={}) {
      var self = this;
      var setLang = function(fld,m) {
        self.mlang = self.mlang === 'En' ? 'Zh' : 'En';
        self.translating = false;
        var tmp = {};
        tmp[fld] = m;
        self.prop = Object.assign({}, prop, tmp);
      };
      if (prop.m_zh && self.mlang == 'En') {
        return self.mlang = 'Zh';
        // return setLang('m',prop.m_zh);
      }
      if (prop.m_zh && self.mlang == 'Zh') {
        return self.mlang = 'En';
        // return setLang('m',prop.m_zh);
      }
      async function _doTranslate_m(idx) {
        if (idx+'' != '2') {
          return;
        }
        self.translating = true;
        if (prop.m_zh) {
          return setLang('m_zh',prop.m_zh);
        } else {
          try {
            const ret = await self.$http.post('/1.5/prop/pretranslate', { id: prop._id, m: prop.m }).then(response => response.data);
            if (ret.ok && ret.m_zh) {
              return setLang('m_zh', ret.m_zh);
            } else if (ret.m) {
              const gret = await RMSrv.getTranslate(ret.m);
              if (gret && gret !== '') {
                await self.$http.post('/1.5/prop/update', { id: prop._id, m_zh: gret.m, src: gret.src });
                setLang('m_zh', gret.m);
                if (cfg.edit) {
                  self.doEditPropM(prop)
                }
                return;
              }
              RMSrv.dialogAlert(self._('Error when getting translate'));
            } else {
              // self.translating = false;
              console.log(ret.err);
            }
          } catch (error) {
            ajaxError(error);
          } finally {
            self.translating = false;
          }
        }
      }
      var optTip = '"Remarks" is automatically translated by Google Translate. Sellers,Listing agents, RealMaster, Canadian Real Estate Association and relevant Real Estate Boards do not provide any translation version and cannot guarantee the accuracy of the translation. In case of a discrepancy, the English original will prevail.';
      var fn = this._?this._:this.$parent._;
      var tip = fn(optTip);
      var later = fn('Cancel');
      var seemore = fn('Translate');
      var optTl = "";
      // _doTranslate_m(2)
      return RMSrv.dialogConfirm(tip, _doTranslate_m, optTl, [later, seemore]);
    },
    showInMap(){
      var prop = this.prop;
      this.openTBrowser('/1.5/SV/googlemap?lat=' + prop.lat + '&lng='+ prop.lng + '&addr=' + (prop.addr ? (prop.addr +  ',' + prop.city_en + ',' + prop.prov_en) : ''), {nojump:true} )
    },
    loadMap() {
      var imgSrc, mapWidth, self = this, prop = this.prop;
      if (!(prop && prop.lat && prop.lng)) {
        return;
      }
      if (self.dispVar.isCip) {
        return self.showInMap();
      }
      if (self.dispVar.isApp && self.isNewerVer(self.dispVar.coreVer,'5.2.0') && RMSrv.showInMap) {
        RMSrv.showInMap({lat:prop.lat, lng:prop.lng, title:self._('Location')});
        return;
      }
      mapWidth = parseInt((window.innerWidth || 400) * 1.5);
      var firstChar = this.dispVar.gmapStatic.indexOf('?')>0?'&':'?';
      imgSrc = this.dispVar.gmapStatic + firstChar+"sensor=false&center=" + prop.lat + "," + prop.lng + "&zoom=14&size=" + mapWidth + "x" + 225 + "&maptype=roadmap&markers=color:red%7Clabel:J%7C" + prop.lat + "," + prop.lng;
      document.getElementById('id_map_pic').src = imgSrc;
      self.staticMaploaded = true;
    },
    limitObjKeys(n,obj={}){
      var keys = Object.keys(obj);
      var ret = {};
      for (var i = 0; i < n; i++) {
        var key = keys[i];
        ret[key] = obj[key];
      }
      return ret;
    },
    showAllSchools(){
      this.showedAllSch = true;
      this.schsShort = this.schs;
    },
    getExchangeRates () {
      var self = this;
      // 1. 增加调用状态锁
      if (self.getExchangeCalling) {
        return;
      }
      // 2. 增加数据缓存判断
      if (Object.keys(self.exrates).length !== 0 && Object.keys(self.exnames).length !== 0) {
        return;
      }
      // 3. 增加错误重试次数限制
      if (self.getExchangeCalled) {
        if(!self.getExchangeError || self.getExchangeError >1){
          return;
        }
      }
      self.getExchangeCalling = true;
      self.$http.post('/1.5/calc/exchange.json', {cmd: 'pmt-p-exrt',data: {}}).then(
        function(data) {
          self.getExchangeCalling = false;
          self.getExchangeCalled = true;
          data = data.data;
          var currency, exDirection, exIndex, exIndexNames, k, ref, v;
            if (data.ok === 0) {
              return;
            }
            if (data != null) {
              delete data.exnames.EUR;
              delete data.exnames.JPY;
              delete data.exnames.KRW;
              delete data.exnames.MYR;
            }
            self.exrates = data.exrates;
            self.exnames = data.exnames;
            exIndex = 0;
            exIndexNames = [];
            exDirection = 1;
            ref = self.exnames;
            for (k in ref) {
              v = ref[k];
              exIndexNames.push(k);
            }
            self.curName = exIndexNames[0];
            currency = setInterval((function() {
              var getIndex;
              getIndex = function() {
                if (exIndex + exDirection >= exIndexNames.length) {
                  exDirection = -1;
                }
                if (exIndex + exDirection < 0) {
                  exDirection = 1;
                }
                return (exIndex + exDirection) % (exIndexNames.length || 1);
              };
              exIndex = getIndex();
              var elems =  document.querySelectorAll(".exrates-wrapper");
              for (var i = 0; i < elems.length; i++) {
                elems[i].style.webkitTransform = 'translate3d(0,' + (exIndex * -20) + 'px,0)';
              }
            }), 2000);
        }, function (ret) {
            self.getExchangeCalling = false;
            self.getExchangeError = (self.getExchangeError || 0) + 1;
            ajaxError(ret);
        }
      );
    },
    // formatPropAddr(prop){
    //   return prop.addr+', '+prop.city+', '+prop.prov;
    // },
    openTBrowser (url, cfg={}) {
      if (!cfg.title) {
        cfg.title = this._('RealMaster');
      }
      if (/transtax|mortgage|direction|rentorbuy/.test(url)) {
        url += '&lang='+this.dispVar.lang;
        url = this.appendDomain(url);
      }
      if (cfg.nojump && this.dispVar.isApp) {
        RMSrv.getPageContent(url,'#callBackString', cfg, function(val) {
          if (val == ':cancel') {
            return;
          }
        });
        return;
        // return RMSrv.openTBrowser(url, cfg);
      }
      if (this.$parent.openTBrowser) {
        return this.$parent.openTBrowser(url, cfg);
      }
      return window.location = url;
    },
    showInBrowser (url) {
      if (!url) {
        return;
      }
      RMSrv.showInBrowser(url);
    },
    toggleModal (a, b) {
      toggleModal(a, b);
    },
    appendDomain(url){
      var location = window.location.href;
      var arr = location.split("/");
      var domain = arr[0] + "//" + arr[2];
      url = domain + url;
      return url;
    },
    propertyComments() {
      if (this.cmntNum < 0 )
        return window.bus.$emit('flash-message',this.$parent._(this.strings.DIS_COMMENT[0],this.strings.DIS_COMMENT[1]));
      this.getCmntNum();
      var img = this.picUrls[0];
      var tl = (this.prop.addr || '') + ' ' + this.prop.city + ' $' + (this.prop.lp || this.prop.lpr)

      var url = '/1.5/forum/details?inFrame=1&id='+ this.prop._id+ '&sid='+this.prop.sid+'&src=property'
      if (tl)
        url += '&tl='+tl.replace(/#/g,'')
      if (img)
        url += '&img='+img
      if (this.prop.city_en)
        url += '&city='+this.prop.city_en
      if (this.prop.prov_en)
        url += '&prov='+this.prop.prov_en

      url+='&inframe=1&nobar=1';
      url = this.appendDomain(encodeURI(url));
      // console.log(url);
      if (this.dispVar.isApp) {
        this.tbrowser(url,{title:this._('RealMaster')});
      } else {
        window.location.href = url;
      }
    },
    toggleVerifyAgreement(status){
      var self = this;
      var body = {id:self.prop.id,verify:status};
      fetchData('/1.5/prop/agreementVerify',{body},function(err,ret) {
        var errMsg = ret.e || err;
        if (errMsg) {
          bus.$emit('flash-message',errMsg.toString());
          return;
        }
        if (ret.ok) {
          self.prop.isV = status
          bus.$emit('flash-message',ret.msg);
        } 
      })
    },
    revokeProp(){
      var self = this;
      if(!this.revokeReason){
        return bus.$emit('flash-message','Please enter a reason');
      }
      var body = {id:self.prop.id,reason:this.revokeReason};
      fetchData('/1.5/prop/adminRevoke',{body},function(err,ret) {
        var errMsg = ret.e || err;
        if (errMsg) {
          bus.$emit('flash-message',errMsg.toString());
          return;
        }
        bus.$emit('flash-message',ret.msg);
      })
    },
    getPropSavedLog(){
      var self = this;
      var body = {id:self.prop._id};
      fetchData('/1.5/prop/getUserSavedLog',{body},function(err,ret) {
        var errMsg = ret.e || err;
        if (errMsg) {
          bus.$emit('flash-message',errMsg.toString());
          return;
        }
        if (ret.ok) {
          self.prop.pnSrc = ret.logs;
        } 
      })
    },
    openNearbyMap(){
      var tp = this.hasGoogleService?'mapNative':'map'
      window.bus.$emit('on-prop-detail-map-menu-click',tp)
    },
    gotoNoteBrowse(){
      var self = this;
      if(!this.prop.uaddr){
        return bus.$emit('flash-message',self._(NOTE_TIPS.NOUADDR));
      }
      if(!this.prop._id){
        return bus.$emit('flash-message',self._(NOTE_TIPS.NOPROPID));
      }
      let url = `/1.5/notes?propId=${this.prop._id}&uaddr=${encodeURIComponent(this.prop.uaddr)}&addr=${encodeURIComponent(this.prop.addr)}&lat=${this.prop.lat}&lng=${this.prop.lng}&isBuilding=${this.prop.isBuilding}`;
      let cfg = {toolbar:false};
      if (this.dispVar && this.dispVar.isApp) {
        RMSrv.getPageContent(url,'#callBackString', cfg, function(val){
          if (val == ':cancel') {
            return;
          }
          try {
            let note = JSON.parse(val);
            if(note.noteDetail && (self.hasSelfM == 0 || self.hasSelfM == '-')){ // 新添加笔记
              self.$set(self,'hasSelfM',1);
              self.$set(self,'totalCount',note.totalCount+1); // totalCount+1：totalCount不包含当前uid
            } else if(!note.noteDetail && self.hasSelfM != 0){ // 删除笔记
              self.$set(self,'hasSelfM',0);
              if (note.totalCount >= 0){
                self.$set(self,'totalCount',note.totalCount);
              }
            }
          } catch (e) {
            console.error(e);
          }
        });
      } else {
        window.location.href = url;
      }
    },
    getPropShowingLog(){
      var self = this;
      var body = {id:self.prop._id,uaddr:self.prop.uaddr,unit:self.prop.unt};
      if(!self.dispVar.isRealGroup){
        return
      }
      getStandardFunctionCaller()('getGroupAddShowingLog',body,(err,ret)=>{
        if (err) {
          bus.$emit('flash-message',err.toString());
          return;
        }
        if(ret.length){
          self.addedShowingLogs = ret;
        }
      })
    },
    hideAssignListing(status){
      var self = this;
      var body = {id:self.prop.id,hide:status,marketRmProp:self.prop.marketRmProp};
      fetchData('/1.5/prop/hideListing',{body},function(err,ret) {
        var errMsg = ret.e || err;
        if (errMsg) {
          bus.$emit('flash-message',errMsg.toString());
          return;
        }
        if (ret.ok) {
          self.$set(self.prop, 'private', status)
          bus.$emit('flash-message',ret.msg);
        } 
      })
    },
    getPropFavStatus(){
      let self = this;
      let d = {
        id:self.prop._id
      }
      self.$http.post('/1.5/propFav/isFavedProp', d).then(
        function (ret) {
          let data = ret.data;
          if (!data.e && data.ok) {
            self.$set(self.prop, 'fav', data.fav);
            self.$set(self.prop, 'favGrp', data.favGrp);
          }
        },
        function (ret) {
          ajaxError(ret);
        }
      );
    },
    resetUpgrade(){
      var appBar = document.querySelector('#getAppBar');
      if(appBar){
        appBar.style.display = 'block';
      }
      document.querySelector('.upgrade').style.display = 'none';
    },
    // 在remindUpgrade提示选择时间
    toCalendarSelectTime(info){
      if (this.checkAndRedirectDownload()){
        return
      }
      this.openHouseInfo = info;
      if(new Date()> this.setTime(info.value.t)){
        return window.bus.$emit('flash-message', this._(this.strings.OHTIMEOUT.key));
      }
      var appBar = document.querySelector('#getAppBar');
      if(appBar){
        appBar.style.display = 'none';
      }
      document.querySelector('.upgrade').style.display = 'block';
      window.bus.$emit('prompt-calendar-select-time')
    },
    setTime(dateTime){
      let dateArr = dateTime.split(' ');
      let date = dateArr[0],time = dateArr[1];
      let year = Number(date.slice(0,4))
      let month = Number(date.slice(5,7))
      let day = Number(date.slice(8,10))
      var hour = Number(time.slice(0,2)),min = Number(time.slice(3,5))
      let newDate = new Date()
      newDate.setFullYear(year)
      newDate.setMonth(month-1)
      newDate.setDate(day)
      newDate.setHours(hour,min)
      return newDate;
    },
    getPropShortUrl(d,cb){
      var self = this;
      self.$http.post('/1.5/api/rm/shareInfo', d).then(
        function (ret) {
          let data = ret.data;
          if (!data.e && data.ok) {
            cb(data.url);
          }
        },
        function (ret) {
          ajaxError(ret);
        }
      );
    },
    addToCalendar(data) {
      var self = this,startDate,endDate,time = data.time;
      if(!self.isNewerVer(self.dispVar.coreVer,'6.0.1')){
        return self.confirmUpgrade(self.dispVar.lang);
      }
      if(self.openHouseInfo.value.f){
        startDate = self.setTime(self.openHouseInfo.value.f);
      }
      if (self.openHouseInfo.value.t) {
        endDate = self.setTime(self.openHouseInfo.value.t);
      }
      var obj={
        title:'Open House',
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString()
      };
      obj.description = obj.notes = `${data.url}\nID:${self.prop.sid||self.prop.id||self.prop._id}\nAddress:${self.prop.addr},${self.prop.city},${self.prop.prov}`
      if (time) {
        if (RMSrv.isIOS()) {
          let alertDate = new Date(startDate.getTime()-time*60*1000)
          time = alertDate.toISOString()
        }
        obj.alarms = [{
          date: time
        }]
      }
      obj.location = self.prop.addr+','+self.prop.city+','+self.prop.prov

      let addToCalendarSuccess = function(ret) {
        trackEventOnGoogle('propdetail','propOpenHouseAddToCalendar')
        return window.bus.$emit('flash-message', self._(self.strings.calendarSaved.key,self.strings.calendarSaved.ctx));
      };
      // calendar id from android is nubmer, has to check first to avoid crash
      RMSrv.calendar('saveEvent',obj,function(ret){
          // alert('ret:'+ret)
        if(!/error/i.test(ret)){
          addToCalendarSuccess(ret)
        } else {
          return window.bus.$emit('flash-message', self._(self.strings.saveFailed.key,self.strings.saveFailed.ctx));
        }
      })
    },
    gotoNoteEdit(){
      var self = this;
      if(!this.prop.uaddr){
        return bus.$emit('flash-message',self._(NOTE_TIPS.NOUADDR));
      }
      if(!this.prop._id){
        return bus.$emit('flash-message',self._(NOTE_TIPS.NOPROPID));
      }
      let url = `/1.5/notes/editNotes?propId=${this.prop._id}&uaddr=${encodeURIComponent(this.prop.uaddr)}`;
      if(this.prop.unt){
        url = url + `&unt=${encodeURIComponent(this.prop.unt)}`
      }
      if(this.noteId.length){
        url = url + `&id=${this.noteId}`
      }
      let cfg = {toolbar:false};
      if (this.dispVar && this.dispVar.isApp) {
        RMSrv.getPageContent(url,'#callBackString', cfg, function(val){
          if (val == ':cancel') {
            return;
          }
          try {
            let info = JSON.parse(val);
            if(info.type == 'delete'){
              self.$set(self,'hasSelfM',0);
              self.$set(self,'noteId','');
              return;
            }
            let note = info.note;
            if(note._id){
              self.$set(self,'hasSelfM',1);
              self.$set(self,'noteId',note._id);
            }
          } catch (e) {
            console.error(e);
          }
        });
      } else {
        window.location.href = url;
      }
    },
    dealbltYrAcuValue(bltYr1M,bltYr2M,bltYr1MAcu,bltYr2MAcu,title,titleAcu){
      if(!this.allEstimateInfo[bltYr1M] && !this.allEstimateInfo[bltYr2M]) return;
      let bltYr1 = this.allEstimateInfo[bltYr1M]? parseInt(this.allEstimateInfo[bltYr1M]): undefined; // 见到过 2011.534069400631 数据
      let bltYr2 = this.allEstimateInfo[bltYr2M]? parseInt(this.allEstimateInfo[bltYr2M]) : undefined;
      let bltYr1Acu = this.allEstimateInfo[bltYr1MAcu];
      let bltYr2Acu = this.allEstimateInfo[bltYr2MAcu];
      let bltYr1AcuPer = bltYr1Acu? (bltYr1Acu/100) + '%' : '';
      let bltYr2AcuPer = bltYr2Acu? (bltYr2Acu/100) + '%' : '';
      if(bltYr1 && !bltYr2){
        this.$set(this.allEstimateInfo,title,bltYr1);
        this.$set(this.allEstimateInfo,titleAcu,bltYr1AcuPer);
      }else if(bltYr2 && !bltYr1){
        this.$set(this.allEstimateInfo,title,bltYr2);
        this.$set(this.allEstimateInfo,titleAcu,bltYr2AcuPer);
      }else if(bltYr1 == bltYr2){
        this.$set(this.allEstimateInfo,title,bltYr1);
        this.determineBltYrAcuValue(bltYr1AcuPer,bltYr2AcuPer,bltYr1Acu,bltYr2Acu,titleAcu);
      }else if((bltYr1 != bltYr2) && (Number(bltYr1) > Number(bltYr2))){
        this.$set(this.allEstimateInfo,title,`${bltYr2}-${bltYr1}`);
        this.determineBltYrAcuValue(bltYr1AcuPer,bltYr2AcuPer,bltYr1Acu,bltYr2Acu,titleAcu);
      }else if((bltYr1 != bltYr2) && (Number(bltYr1) <= Number(bltYr2))){
        this.$set(this.allEstimateInfo,title,`${bltYr1}-${bltYr2}`);
        this.determineBltYrAcuValue(bltYr1AcuPer,bltYr2AcuPer,bltYr1Acu,bltYr2Acu,titleAcu);
      }
    },
    determineBltYrAcuValue(bltYr1AcuPer,bltYr2AcuPer,bltYr1Acu,bltYr2Acu,titleAcu){
      if(bltYr1Acu <= bltYr2Acu){
        this.$set(this.allEstimateInfo,titleAcu,bltYr1AcuPer);
      } else {
        this.$set(this.allEstimateInfo,titleAcu,bltYr2AcuPer);
      }
    },
    getCondoBasicInfo(){
      let self = this;
      if(!this.prop.uaddr || !this.prop.isBuilding || Object.keys(this.condoInfo).length > 1) return;
      let d = {uaddr: this.prop.uaddr}
      self.$http.post('/floorPlan/condoBasicInfo', d).then(
        function (ret) {
          ret = ret.data;
          if (!ret.e) {
            self.condoInfo = Object.assign(self.condoInfo, ret.result);
            const condoProps = ['condoStories', 'condoAge', 'condoUnits'];
            // 将condoInfo中的condoStories, condoAge, condoUnits赋值给prop, 优先显示mls房源的信息，如果没有则显示building中的信息
            condoProps.forEach(item => {
              if (self.condoInfo[item] !== undefined && self.condoInfo[item] !== null) {
                self.$set(self.prop, item, self.condoInfo[item]);
              }
            });
            self.initMainInfo();
          }
        },
        function (ret) {
          ajaxError(ret);
        }
      );
    },
    openCondoDes(){
      if(this.showCondoInfo && this.condoInfo.condoNm){
        let info = 'No description yet';
        window.bus.$emit('show-condo-desc',{
          condoNm:this.condoInfo.condoNm,
          condoDesc:this.condoInfo.condoDesc || info,
          uaddr:this.prop.uaddr,
          condoDesc_zh: this.condoInfo.condoDesc_zh || null,
          descText: this.condoInfo.condoDesc || info,
          lang: this.dispVar.lang
        });
      }
    },
    openFloorPlan(){
      if(!this.condoInfo.canAccess){
        bus.$emit('flash-message','Resource request limit exceeded!');
        return;
      }
      this.previewPic(null,true);
    },
    getEstimatedValue() {
      const self = this;
      // 如果已经获取过或正在获取，则不重复请求
      if (this.isRMProp() || this.gettingEstimate || this.gotEstimate || this.estimateInfo) {
        return
      }
      // 如果prop.hideEstVal为true，则非房大师自己的经纪以及admin不能看
      // 如果状态不是A，并且用户不是noteAdmin或inRealGroup，则不调用接口
      if ((this.prop.status !== 'A' || this.prop.hideEstVal) && !this.dispVar.isRealGroup && !this.dispVar.isNoteAdmin && !this.dispVar.isDevGroup) {
        return
      }
      this.gettingEstimate = true
      const data = {
        propId: this.prop._id,
        propStatus: this.prop.status,
        saleType: this.prop.saleTpTag_en,
        soldPrice: this.prop.sp,
        pointHalfWidth: 11,
        screenWidth: window.innerWidth - 20, // 页面左右有10px padding
        prov: this.prop.prov_abbr,
        ptype: this.prop.ptype_en,
        ptype2: this.prop.ptype2_en,
        onD: this.prop.onD,
        merged: this.prop.merged,
        saletp: this.prop.saletp_en || this.prop.saletp || false
      }
      let shortUrlId = this.hasShortUrl();
      if(shortUrlId){
        data.shortUrlId = shortUrlId;
      }
      this.$http.post('/estimated/getEstimate', data).then(
        function(response) {
          const ret = response.data
          self.gettingEstimate = false
          self.gotEstimate = true
          if (ret.ok && ret.result) {
            self.estimateInfo = ret.result
          } else {
            // 处理没有数据的情况
            self.estimateInfo = null
          }
        },
        function(error) {
          self.gettingEstimate = false
          ajaxError(error)
        }
      )
    },
    getAllEstimateInfo() {
      let self = this;
      if (!this.prop || !this.prop._id || this.isRMProp()) {
        return
      }
      // 只有对特定的用户权限组可见
      if (!(this.dispVar.isRealGroup || this.dispVar.isPropAdmin)) {
        return
      }
      self.$http.post('/estimated/getAllEstimate', {propId: this.prop._id}).then(
        function(ret){
          if (ret.data && ret.data.ok && ret.data.result) {
            self.allEstimateInfo = ret.data.result;
            self.initAccuracyInfo();
          }
        },
        function (ret) {
          ajaxError(ret);
        });
    },
    toggleSoldPrice() {
      this.showSoldPriceEst = !this.showSoldPriceEst;
    }
  }
}
</script>


<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
@import '../../../style/sass/_base.scss';
@import "../../../style/sass/web/components/_swiper.scss";
@import "../../../style/sass/apps/components/watching.scss";
@import '../../../style/sass/apps/components/toggleTitle.scss';
@import '../../../style/sass/apps/components/commuteAddr.scss';
@import '../../../style/sass/apps/components/home/<USER>';
.limitSummaryHeight{
  max-height: 22px;
  overflow: hidden;
  display: inline-block;
}
.upgrade{
  display:none;
  position: fixed;
  height: 100%;
  width: 100%;
  top: 0;
  z-index: 11;
}
.my-listing{
  padding: 15px 0;
  background: #fff;
  border-top: .5px solid #ddd;
  font-size: 14px;
}
.sold{
  font-size: 15px;
  background: #e03131;
  padding: 0 3px;
  color: #fff;
  border-radius: 2px;
  margin-right: 3px;
}
.showingLog{
  border: 1px solid #428bca;
  border-radius: 14px;
  padding: 2px 5px 2px;
  display: inline-block;
  margin-right: 7px;
  flex-shrink: 0;
  color: #428bca;
  img{
    height: 20px;
    width: 20px;
    border-radius: 50%;
    margin-right:3px;
    vertical-align: bottom;
  }
}
.similar{
  padding: 19px 0px;
  background-color: #fff;
  border-top: 0.5px solid #ddd;
  .similar-desc{
    font-size: 15px;
    color: #333;
    font-weight: bold;
    padding-top: 18px;
  }
  .btn-line{
    display: flex;
    justify-content: space-between;
    span{
      border: 1px solid #1ea61b;
      min-width: 88px;
      padding: 14px 0px;
      border-radius: 3px;
      color: #1ea61b;
      font-size: 15px;
      text-align: center;
      width: 48%;
      font-weight: bold;
    }
  }
}
.price-caret{
  margin-right: 5px;
}
.match{
  margin: 0;
  padding: 10px 10px;
}
.prop-price-change,.numbers .index, .prop-tag{
  .fa-caret-up{
    color: #41B433;
  }
  .fa-caret-down{
    color: #e03131;
  }
}
.idbtn{
  color: #777;
  font-size: 13px;
  .copy{
    padding: 4px 6px 4px 6px;
    vertical-align: baseline;
  }
}
.toggle{
  width: 54px;
  height: 24px;
  background: #ddd;
  &:before {
    position: absolute;
    top: -1px;
    right: 6px;
    font-size: 11px;
    color: #fff;
    text-transform: capitalize;
  }
  &.active {
    background-color: #40BC93;
    border: 2px solid #40BC93;
    &:before {
      right: auto;
      left: 7px;
      color: #fff;
    }
  }
  &.ftm {
    width: 46px;
    height: 24px;
    margin-top: 5px;
    margin-right: 10px;
    &:before {
      content: "M";
      width: 10px;
      text-align: center;
    }
    &.active{
      .toggle-handle{
        transform: translate3d(24px, 0, 0);
      }
      &:before {
        content: "Ft";
        left: 8px;
      }
    }
  }
  &.trans {
    width: 80px;
    height: 24px;
    &.active{
      .toggle-handle{
        transform: translate3d(58px, 0, 0);
      }
      &:before {
        left: 6px;
      }
    }
  }
  .toggle-handle{
    top: 0px;
    width: 20px;
    height: 20px;
  }
  &.zh.trans {
    width: 58px;
    &.active{
      .toggle-handle{
        transform: translate3d(36px, 0, 0);
      }
    }
  }
}
.segment{
  .tabs-container{
    padding:0;
    .tab:first-child{
      padding-left: 10px;
    }
  }
}
.pull-right *{
  vertical-align: middle;
}
.tabs-container{
  padding-bottom: 13px;
  .tab:first-child{
    padding-left: 0;
  }
}
.location-card{
  .watchArea{
    margin: 0;
    padding: 0 10px 10px;
  }
  .dottedLine{
    height: 25px;
    margin-left: 5px;
    &:before{
      content: '';
      float: left;
      display: inline-block;
      padding-left: 8px;
      height: 25px;
      padding-top: 0px;
      border-left: 2px dotted #999;
    }
  }
  .circle{
    border: 0.5px solid #40BC93;
    display: inline-block;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin: 2px;
  }
  .fa-check-circle{
    color: #40BC93;
    font-size: 24px;
  }
  .addr{
    font-size: 15px;
    color: #333;
  }
  .desc{
    font-size: 12px;
    color: #a0a0a0;
    display: block;
    line-height: 14px;
  }
  .locations{
    @include flexbox();
    justify-content: space-between;
    align-items: center;
    padding: 5px 0;
  }
  .marginBottomp10{
    margin-bottom: 10px;
  }
  .links{
    a:not(:first-child){
      margin-top: 10px;
    }
    &>.desc{
      padding: 5px 0 10px;
    }
  }
  .cmty-nm{
    color: #333;
    font-weight: 500;
    margin-left: 10px;
  }
}
.err-report {
  @include flexbox();
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  margin: 5px 0;
  border-radius: 5px;
  background-color: #f2f5fa;
  .report-btn {
    border: 0.5px solid #40BC93;
    color: #40BC93;
    min-width: 70px;
    padding: 5px 0;
    border-radius: 3px;
    font-size: 13px;
    font-weight: bold;
    text-align: center;
    display: inline-block;
  }
}
.detail-photo.vertical {
  height: 100%;
  width: auto;
}
.swiper-container{
  background-color: #f1f1f1;
  height: 100%;
}
.swiper-slide img.vertical{
  min-width: 160px;
}
.assignment-tip{
  background: #FBEDEB;
  color: #333;
  font-size: 12px;
  padding: 10px;
  line-height: 14px;
  display: flex;
  align-items: center;
  .sprite16-24{
    margin-right: 5px;
  }
}
@media screen and (min-width: 800px) {
  .prop-main-body {
    padding-left: 25%;
    padding-right: 25%;
  }
}
.exchange {
  height: 15px;
  width: 15px;
  margin: 0 5px;
  vertical-align: text-bottom;
}
.cert {
  margin-left: 5px;
  background-color: #efc439;
  border-radius: 2px;
  color: #fff;
  display: inline-block;
  font-size: 11px;
  padding: 1px 3px 1px 3px;
  line-height: 14px;
  vertical-align: top;
  margin-top: 2px;
  background-color: #1EB1ED;
}
.one-line {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.split-thin {
  border-bottom: 1px solid #f1f1f1;
  margin: 0px 10px;
}
.showDetail a {
  color: #a0a0a0;
  font-size: 13px;
  font-weight: normal;
  padding: 5px 8px 5px;
}
.showDetail a .fa {
  margin-left: 3px;
  vertical-align: middle
}
#listing-hist-modal .on-off-market {
  padding-top:10px!important;
}
#listing-hist-modal .on-off-market .current, #listing-hist-modal .on-off-market .all-records {
  padding-left:10px!important;
}
#listing-hist-modal .on-off-market .split{
  margin-left: 10px!important;
}
.on-off-market label, .listing-performance label, .oh label{
  padding: 10px 5px 5px 10px;
  color: #000;
  display: block;
  font-size: 15px !important;
}
// .oh-msg {
//   margin: 5px 10px 15px;
//   background-color: #ffffdd;
//   padding: 10px 15px;
//   display: flex;
//   align-items: center;
// }
// .oh-msg .msg {
//   margin-left:15px;
//   max-width: inherit;
//   font-size: 14px;
//   color: #666;
// }
.sp-holder {
  width: 100%;
  overflow-x: scroll;
  min-width: 205px;
}
.sp-wrapper {
  padding: 0px 10px 18px 10px;
  display: -webkit-flex;
  display: flex;
  min-width: 205px;
}
.sp-wrapper .sp {
  width: 100%;
  display: inline-block;
  border: 1px solid #f1f1f1;
  vertical-align: top;
  overflow: hidden;
  align-items: stretch;
  position: relative;
  padding: 8px;
  margin-right: 10px;
  height: 90px;
  box-shadow: 1px 1px 8px 1px #f1f1f1;
  position: relative;
  background: rgba(255,255,255,0.7);
  min-width: 210px;
}
.sp-wrapper .sp .top {
  display: flex;
  align-items: center;
  justify-content: center;
}
.black {
  font-weight: 700;
}
.grey {
  color: #777;
  font-size: 12px;
  font-weight: normal;
}
.sp-wrapper .sp .top .left {
  font-size: 24px;
  border: 1px solid #a0a0a0;
  background-color: #f1f1f1;
  color: #000;
  border-radius: 5px;
  width: 40px;
  text-align: center;
  font-weight: bold;
  padding: 7px 1px 7px 0px;
  margin-top: -31px;
}
.sp-wrapper .sp .top .right {
  padding-left: 10px;
  width: calc(100% - 35px);
}
.sp-wrapper .sp .bottom {
  color: #777;
  /* text-align: center */
}
.sp-wrapper .hour {
  font-size: 12px;
  width:100%;
}
.inline {
  display: inline-block;
  vertical-align: middle;
}
.row-online {
  width: 100%;
  padding-top: 10px;
}
.oh .add-calendar {
  float: right;
  padding-right: 10px;
  font-size: 12px;
  display: flex;
  align-items: center;
  color: #777;

}
.oh .add-calendar span {
  color: #777;
  padding-left:5px;
}
/* .manvsai {
  margin-top:15px;
}
.manvsai-link {
  font-size: 13px;
  color:#428bca;
}
.manvsai-link .icon {
  font-size: 14px;
}
.manvsai-container {
  font-size: 8px;
  color:#fff;
  border:1px solid #000;
  display: inline-block;
}
.manvsai .triangles {
  border: 11px solid;
  border-color: #000 #F0951C #F0951C #000;
}
.manvsai .ai {
  background-color: #000;
  padding-right:2px;
}
.manvsai .logo_ai {
  background-color:#000;
  border:1px solid #fff;
  padding:1px 2px;
  margin-right:3px;
}
.manvsai .man {
  background-color: #F0951C;
  padding: 0 5px;
  text-transform:capitalize;
} */
.vs-pred-sp {
  color: #F0951C;
}
.vs {
  background-color: orange;
  color: #fff;
  font-size: 8px;
  padding: 0 2px;
  border-radius: 4px;
  margin: 0 3px;
  line-height: 16px;
}
.quick-links > div{
  display: inline-block;
  width: 20%;
  vertical-align: top;
  text-align: center;
  position: relative;
}
.quick-links > div .desc{
  line-height: 15px;
}
.quick-links{
  font-size: 12px;
  color: #888;
  text-align: left;
  background: white;
  // margin-bottom: 10px;
  padding: 12px 10px;
  // border-bottom: 4px solid #e6e6e6;
  overflow: auto;
  white-space: nowrap;
}
.quick-links span{
  width: 25px;
  height: 25px;
}
.quick-links span.disable, .right img.disable {
  opacity: 0.3;
}
.quick-links div.disable, .right div.disable {
  color: #d3d3d3;
}
#hist{
  background:#e5e5e5;
  height: 70px;
}
#hist div {
  min-height: 55px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 0 15px;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background: white;
  justify-content: space-between;
  border-bottom: 1px solid #c5c5c5;
  border-top: 1px solid #c5c5c5;
}
#hist .icon {
  color: #c7c7cc;
  margin-left: auto;
}
@font-face {
  font-family: 'Timeburner';
  src: url('../../../webroot/public/fonts/timeburnernormal.ttf?v=4.4.0') format('truetype');
  font-weight: normal;
  font-style: normal;
}
#listing-hist-modal,
#oh-modal{
  z-index: 20;
}
#listing-hist-modal .btn,
#oh-modal .btn{
  border: none;
  font-size: 15px;
}
#oh-modal .row > div,
.listing-hist-modal .row > div{
  display: inline-block;
  width: 65%;
}
#oh-modal .row .pricetp,
#oh-modal .row .lp,
.listing-hist-modal .row .pricetp,
.listing-hist-modal .row .lp{
  width: 34%;
  text-align: right;
}
#listing-hist-modal .row .lp.sp{
  color:#e03131;
}
#listing-hist-modal .red{
  color: #e03131;
}
.link{
  color: #428bca;
}
.listing-hist-modal .split{
  padding-top: 5px;
  margin-bottom: 5px;
  border-bottom: 1px solid #f1f1f1;
}
.listing-hist-modal .pricehist,
.listing-hist-modal .addrHist{
  padding: 10px;
  font-size: 14px;
}
.listing-hist-modal .addrHist{
  color: #777;
}
.my-swipe img{
  width: 100%;
  height: 100%;
}
.footer-tab{
  padding-left: 0;
}
.bar-footer{
  padding-right: 5px;
}
.bar-footer span.pull-left{
  height: 100%;
}
.bar-footer span.pull-left .btn-segment{
  top:0;
  /* padding-top: 15px; */
  padding: 0 0 0 0;
  min-width:74px;
}
.bar-footer .fav{
  margin-left:0;
}
.bar-footer .fa-rmshare{
  font-size: 19px;
  padding-top: 1px;
}
.prop_m_g{
  max-height: 60px;
  overflow: hidden;
  background: linear-gradient(to bottom, rgb(0, 0, 0), rgb(210, 210, 210));
  /*background: -webkit-linear-gradient(to bottom, rgb(0, 0, 0), rgb(210, 210, 210));*/
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.hover-bar .icon-desc,
.card .icon-desc{
  font-size: 13px;
  font-weight: normal;
  vertical-align: middle;
  padding-right: 8px;
}
.hover-bar .right {
  position: relative;
}
.hover-bar .right .badge, .quick-links .badge{
  position: absolute;
  top: 0px;
  background: #f65f5f;
  font-size: 7px;
  padding: 0 3px;
  color: white;
  border-radius: 8px;
  min-width: 12px;
  line-height: 12px;
}
.hover-bar .right .badge.ai, .quick-links .badge.ai {
  background-color: #f53f2f !important;
  padding: 3px;
}
#translate_btn{
  position: relative;
  right: 0;
  background: #428BCA;
  color: white;
  padding: 3px;
  font-size: 12px;
  width: 21px;
  height: 21px;
  text-align: center;
}
label, .card{
  font-size: 14px;
}
.sqft{
  vertical-align: top;
  display:inline-block;
  max-width: calc(100% - 35%);
}
.segmented-control.scrollable .control-item{
  padding: 18px 0px 17px 0px;
}
.segmented-control.scrollable .control-item.active{
  border-bottom: 2px solid #e03131;
}
.content-list{
  background-color: rgb(230, 230, 230);
  padding: 10px 0px 5px 0px;
}
.card{
  margin: 0px;
  border-radius: 0px;
  position: relative;
}
.card .card-header {
  border: none;
  padding: 18px 6px 8px 9px;
  font-size: 17px;
}
.card .card-header .translate{
  font-size: 13px;
  padding-left: 8px;
  vertical-align: top;
  margin-top: 0px;
  display: inline-block;
}
.card .card-header.segmented-control{
  padding: 0 10px;
  border-radius: 0;
}
.card .card-content.content-padded{
  padding: 10px;
  margin: 0;
}
.prop_m{
  white-space: pre-line; // or pre
}
.card .card-header > span:first-child {
  font-weight: bold;
}
.card:not(:first-child) {
  margin-top: 10px;
}
.prop-heading-wrapper .brief-info {
  background-color: #fff;
  padding: 12px 10px 0px 10px;
  position: relative;
}
.prop-heading-wrapper .open-house{
  background: white;
  border-top: 1px solid #f1f1f1;
  padding: 10px 6px 10px 10px;
  font-size: 15px;
}
.prop-heading-wrapper .open-house .pull-right{
  font-size: 13px;
  color: #428bca;
  /* padding-right: 8px; */
}
.admin-card .card-header .pull-right .icon,
.hover-bar .pull-right .icon,
.card-m .card-header .pull-right .icon,
.school-card .card-header .pull-right .icon,
.location-card .card-header .pull-right .icon,
.invest-card .card-header .pull-right .icon,
.predict-card .card-header .pull-right .icon,
.prop-heading-wrapper .open-house .pull-right .icon,
.icon-right-nav {
  font-size: 14px;
  color: #666;
  padding-left: 0px;
  vertical-align: middle;
  // padding-top: 3px;
}
.card.predict-card .card-content{
  padding: 10px;
}
.card.predict-card .card-content .pred_his span{
  padding: 0 5px;
}
.card.predict-card .card-content .error .ok{
  color:#5cb85c;
}
.card.predict-card .card-content .error .err{
  color: #e03131;
}
.card.predict-card .card-content .error{
  padding-top: 10px;
}
.prop-heading-wrapper .open-house .pull-right .ts{
  padding-right: 8px;
}
#propDetailModal .exrates{
  /* display: inline-block; */
  height: 20px;
  font-size: 14px;
  overflow: hidden;
  font-weight: normal;
  color: #428bca;
  margin: 8px 0 18px;
  /* background: #f5f5f5; */
  /* border-radius: 3px; */
  /* padding-right: 10px; */
}

.exrates-wrapper{
  transition: 0.25s;
  text-align: left;
}
.exrates-wrapper div{
  white-space: nowrap;
  overflow: hidden;
}
.prop-heading-wrapper .brief-info .price{
  color: #E03131;
  display: inline;
}
.prop-heading-wrapper .brief-info .price .saletp.padding{
  padding-left: 8px;
}
.prop-heading-wrapper .brief-info .addr,
.prop-heading-wrapper .brief-info .views,
.prop-heading-wrapper .brief-info .map-btn{
  display: inline-block;
  width: 50%;
  overflow: hidden;
  vertical-align: top;
}
.prop-heading-wrapper .brief-info .addr{
  /*border-bottom: 1px solid #ddd;*/
  padding-bottom: 3px;
  padding-top: 2px;
  font-size: 14px;
  width: 100%;
}
.prop-heading-wrapper .brief-info .addr.crsst{
  width: 100%;
  padding-top: 10px;
}
.prop-heading-wrapper .brief-info .map-btn{
  width: 30%;
}
.prop-heading-wrapper .brief-info .views{
  width: 40%;
  margin-top: 10px;
}
.prop-tags {
  padding: 15px 0 0;
}
.prop-tag {
  font-size: 10px;
  color: #666;
  display: inline-block;
  margin-right: 5px;
  line-height: 12px;
  background: #f1f1f1;
  border-radius: 4px;
  padding: 4px ;
}
.prop-price-desc {
  display: flex;
  align-items: center;
}
.prop-price-change{
  font-size: 14px;
  color: #999;
  padding:3px 0 5px;
  font-weight: bold
}
.prop-price-per-ft {
  font-size: 14px;
  margin-left: 5px;
}
.prop-desc {
  font-weight: bold;
  font-size: 14px;
  span {
    margin-right: 4px;
  }
}
.prop-slddt {
  font-weight: normal;
}
.prop-updated-mt {
  font-size: 12px;
  color: #777;
  font-weight: normal;
}
.prop-heading-wrapper .brief-info .price-wrapper{
  margin: 18px 0 0;
  display: flex;
  justify-content: space-between;
}
.prop-heading-wrapper .brief-info .map-btn img{
  width: 50px;
  height: 50px;
  border-radius: 50%;
}
.prop-heading-wrapper .brief-info .map-btn .img-wrapper{
  text-align: center;
  width: 50px;
  display: inline-block;
}
.prop-heading-wrapper .brief-info .map-btn .text{
  position: relative;
  top: -29px;
  color: white;
  font-size: 12px;
  padding-top: 4px;
}
.prop-heading-wrapper .brief-info .price-wrapper .dom{
  color: white;
  background: #e03131;
  border-radius: 3px;
  padding: 3px 8px 4px;
  margin-left: 10px;
  font-size: 11px;
  vertical-align: top;
}
.hover-bar{
  z-index: 11;
  padding: 0px 0px 0px 10px;
  vertical-align: top;
  border-bottom: none;
  background: rgb(241, 241, 241);
  height: 61px;
}
.hover-bar .left,
.hover-bar .right,
.hover-bar .right > div{
  display: inline-block;
  vertical-align: top;
}
.hover-bar .right{
  text-align:right;
}
.hover-bar .right .desc{
  height: 24px;
  padding-top: 7px;
  /* display: flex;
  justify-content: center;
  flex-direction: column; */
}
.hover-bar .left,
.hover-bar .right{
  width: 50%;
}
.hover-bar .left{
  /* width: 40%; */
  padding-top: 10px;
}
.hover-bar .right{
  /* width: 60%; */
  padding-top: 8px;
}
.hover-bar .right .badge{
  top:4px;

}
.quick-links .badge{
  top:-4px!important;
}
.hover-bar .right > div{
  width: 33.3%;
  font-size: 11px;
  text-align: center;
  line-height: 12px;
}
.hover-bar .right .fa{
  font-size: 22px;
}
.hover-bar .right img{
  width: 22px;
  height: 22px;
}
.hover-bar .exrates{
  max-width: 123px;
  overflow: hidden;
  vertical-align: top;
  /* display: inline-block; */
  padding: 0;
  margin-top: 1px;
}
.hover-bar .price{
  color: #e03131;
  /* font-weight: bold; */
  /* display: inline-block; */
  max-width: 130px;
  overflow: hidden;
  white-space: nowrap;
}
.hover-bar .price .val{
  /* font-family: 'Timeburner'; */
  font-size: 14px;
}
.hover-bar .pull-right{
  color: rgb(66, 139, 202);
  padding-top: 1px;
}
.prop-heading-wrapper .brief-info .price-wrapper .val{
  // font-family: 'Timeburner';
  font-size: 20px;
  color: #e03131;
}
.prop-heading-wrapper .brief-info .price-wrapper .val .desc{
  font-size:12px;
  padding-left:5px;
  font-family: "Helvetica Neue",Helvetica,sans-serif;
}
.prop-heading-wrapper .brief-info .exrates-wrapper{
  text-align: left !important;
}
/* .prop-heading-wrapper .brief-info .hist{
  font-size: 14px;
  color: #666;
}
.prop-heading-wrapper .brief-info .hist .fa{
  padding-left: 5px;
} */
.histDetail .wrapper{
  padding: 5px 0 5px 0;
  /*border-top: 1px solid #f1f1f1;*/
  line-height: 14px;
}
.histDetail .header{
  color: #666;
  border-bottom: 1px solid #f1f1f1;
}
.histDetail .wrapper > div{
  display: inline-block;
  width: 33.3%;
  overflow: hidden;
  color: #777;
  font-size: 12px;
  white-space: nowrap;
}
/*.addrHist .wrapper > div{
  width: 25%;
}*/
.addrHist .wrapper .id{
  color: #007aff;
}
.prop-heading-wrapper .brief-info .map-btn{
  text-align: right;
  height: 50px;
}
.prop-heading-wrapper .brief-info .vc-wrapper .text{
  padding-left: 3px;
}
.prop-heading-wrapper .brief-info .vc-wrapper{
  visibility: hidden;
  background: #f1f1f1;
  color: #777;
  border-radius: 4px;
  font-size: 13px;
  padding: 2px 6px 3px 6px;
  /* margin-left: 12px; */
  display: inline-block;
  vertical-align: text-bottom;
}
.vc-wrapper.visible{
  visibility: visible!important;
}
.prop-heading-wrapper .brief-info .views .vc{
  /* font-family: 'Timeburner'; */
  /* font-size:14px; */
  padding-right: 5px;
}
.prop-heading-wrapper .brief-info .addr .line2{
  font-size: 12px;
  color: #666;
  line-height: 14px;
}
.prop-heading-wrapper .brief-info .line3{
  font-size: 14px;
  display: block;
}
.prop-heading-wrapper .brief-info .views .text.vip{
  margin-left: 10px;
  color: white;
  background: #e03131;
  padding: 2px 5px;
  border-radius: 3px;
}
.prop-heading-wrapper .brief-info .price{
  height: auto;
}
.prop-heading-wrapper .brief-info .price, .prop-heading-wrapper .brief-info .addr .line1{
  font-weight: bold;
}
.brief-info .addr .line1{
  font-size: 14px;
}
.prop-heading-wrapper .brief-info .askprice{
  font-size: 14px;
  color: #666;
}
.prop-heading-wrapper .brief-info .line{
  text-decoration: line-through;
}
.prop-heading-wrapper .brief-info .row{
  padding-top: 0px;
  line-height: 0.9em;
}
.prop-heading-wrapper .brief-info .row > div.tax {
  font-size: 13px;
  color: #666;
}
.prop-heading-wrapper .brief-info .row > div.tax div:first-child{
  font-size: 17px;
  padding-bottom: 10px;
  color: black;
  font-weight: bold;
}
.prop-heading-wrapper .brief-info .row > div {
  padding: 5px 5px 5px 5px;
  display: inline-block;
  width: 33%;
  text-align: center;
}
.prop-heading-wrapper .brief-info .row > div.split{
  height: 40px;
  margin-bottom: 10px;
  width: 0;
  border-left: 1px solid #f1f1f1;
}
.prop-heading-wrapper .brief-info .row > div a{
  display: block;
  font-size: 13px;
}
.prop-heading-wrapper .brief-info .row > div img{
  width: 40px;
  height: 40px;
}
.card.location-card .card-content{
  /* border-bottom: 1px solid #f1f1f1; */
  border-bottom: none;
  color: #666;
  padding-top: 5px;
}
.card .card-footer.row{
  color: #666;
  /*font-weight: bold;*/
  display: flex;
  display: -webkit-flex;
  text-align: center;
}
.card .card-footer.row a{
  /*color: #666;*/
  font-size: 14px;
}
.card .card-footer.row a i.fa{
  font-size: 21px;
}
.card .card-footer.row > div {
  padding-top: 10px;
  padding-bottom: 10px;
  display: inline-block;
  width: 33.3%;
  border-right: 1px solid #f1f1f1;
}
.card .card-footer.row > div:last-child{
  border-right: none;
}
.control-content {
  padding: 110px 0px 15px 0px;
  margin-top: -100px;
}
.detailWrapper > div{
  padding: 0 10px;
}
.admin-card .showDetail {
  margin-bottom: 10px;
  margin-top: 15px;
}
.school-card .showDetail {
  margin-bottom: 15px;
}
.showDetail{
  margin-top: 15px;
  height: 25px;
  margin-bottom: 5px;
  text-align: center;
}
#item1detail{
  padding-left: 0;
  padding-right: 0;
}
#item1detail .content-padded{
  margin: 0;
  padding: 10px;
}
.unit-conv{
  text-decoration: none;
  position: relative;
  margin-top: -30px;
  margin-right: 9px;
  padding: 0px 5px;
  color: white;
  background-color: #007aff;
  font-size: 11px;
}
/* .slider-icon{

} */
.slider-icon.style{
  top: 9px;
}
.videoThumb-wrapper {
  position:relative;
}
#videoThumb{
  width: 92px;
  height: 67px;
  display: block;
  border: 2px solid white;
  border-radius: 3px;
  box-shadow: 2px 2px 2px #888888;
}
.ss-youtube-play {
  position:absolute;
  top: 50%;
  transform: translateY(-50%);
  left:0;
  right:0;
  margin:auto;
  color: #e03131;
  font-size: 18px;
  display: block;
  text-align: center;
}
.onimg-wrapper{
  -webkit-transform: translate3d(0,0,0);
  display:block;
  width:auto;
  line-height: 14px;
  /* background-color:white; */
  position: absolute;
  z-index: 12;
  // top: 168px;
  bottom: 10px;
  color: white;
  font-size: 13px;
}
.onimg-wrapper.left{
  left: 10px;
}
.onimg-wrapper.right{
  right: 10px;
}
.onimg-wrapper.top{
  top:10px;
  bottom: initial;

}
.onimg-wrapper > *{
  border-radius: 3px;
  background-color: rgba(0,0,0,0.7);
}
.onimg-wrapper .imgidx{
  padding: 0px 8px 0px 8px;
}
.onimg-wrapper .imgidx .red{
  color: #e03131;
  vertical-align: top;
}
.tour-url {
  text-decoration: none;
  color: white;
  padding: 0px 8px 0px 8px;
  /* position: absolute; */
  /* left: 50%; */
  /* margin-left: -20px; */
  /* width: 40px; */
  /* height: 40px; */
  /* background-color: white; */
  /* border-radius: 50%; */
  /* padding: 7px 0px 0px 16px; */
  /* box-shadow: 2px 2px 2px #888888; */
}
.tour-url .fa{
  font-size: 15px;
  vertical-align: top;
  margin-right: 7px;
  margin-top: 2px;
  display: inline-block;
}
.slider-container {
  position:relative;
}
.slider {
  margin-bottom: 0;
}
.slider img {
  display: block;
  padding: 0;
  color:#ffffff;
  width: 100%;
  height: 100%;
}
img.can-scale-head {
  width: 100%;
  max-height: 207px;
}
img.wxImg.can-scale-head.cant-scale-head{
  width:auto;
  height: auto;
  margin-left: auto;
  margin-right: auto;
  display: block;
  max-width: 100%;
}
img.can-scale-head.detail.cant-scale-head{
  width:100%;
  height: auto;
  margin-left: auto;
  margin-right: auto;
  display: block;
  max-width: 100%;
  max-height: none;
  margin-bottom: 5px;
}
.slide-text{
  position: absolute;
  top: 45%;
  left: 0;
  width: 100%;
  font-size: 24px;
  color: #fff;
  text-align: center;
  text-shadow: 0 0 10px rgba(0,0,0,.5);
}
.segmented-control.scrollable {
  /*border: 1px none;*/
  display: block;
  text-align: left;
}
.segmented-control.scrollable .control-item {
  display: inline-table;
  width: auto;
  font-weight: bold;
  border: 1px none;
  font-size: 17px;
}
.segmented-control.scrollable .control-item:not(:first-child){
  margin-left: 15px;
}
.segmented-control.scrollable.rmdetail .control-item:first-child{
  margin-left: 10px;
}
.segmented-control.scrollable.rmdetail{
  display: flex;
  display: -webkit-flex;
  justify-content: space-between;
}
.segmented-control.scrollable .control-item.active {
  color: #e03131;
  background-color: transparent;
}
.col-8{
  vertical-align: top;
  max-width: 67%;
  min-width: 60%;
  display: inline-block;
}
.admin-card .verified .col-7{
  text-align: right;
  color: #333;
  font-weight: 500;
}
.admin-card .verified .col-5{
  font-size: 14px;
  color: #666;
  font-weight: normal;
}
.col-7{
  width: 58%;
  display: inline-block;
  vertical-align: top;
}
.col-5 {
  width: 42%;
  display: inline-block;
}
.col-6 {
  width: 50%;
  display: inline-block;
}
.col-4 {
  min-width: 33%;
}
.col-3 {
  min-width: 25%;
  display: inline-block;
}
.col-2 {
    width: 18.75%;
}
.unit-type-box {
  white-space: nowrap;
  overflow: auto;
}
.unit-type {
  min-width: 85px;
  width: 14.5%;
  // max-width: 100px;
  display: inline-block;
}
label {
  display: inline-block;
  margin-bottom: 5px;
  font-weight: 700;
}
.detail-ul label{
  font-weight: inherit;
}
label::after {
  margin: 0 5px 0 0;
}
.tableR2 > div{
  vertical-align: top;
  position: relative;
}
.tableR2 > div span.fa{
  position: absolute;
  right: 5px;
  top: 3px;
  color: #999;
}
.tableR:not(.ng-hide):nth-child(odd) {
  background-color: #eee;
}
.unit-type-box .tableR {
    background: #fff !important;
}
.unit-type-box .tableR:nth-child(even) {
    background-color: #eee !important;
}
.tableR, .tableR2{
  padding-left: 10px;
  padding-top: 3px;
}
.tableR.hide, .tableR2.hide{
  display: none;
}
.tableHead{
  background: #f1f1f1;
  font-size: 17px;
  font-weight: bold;
  padding: 10px;
}
.text-align-left {
  text-align: left;
  font-size: 0.8em;
  opacity: 0.6;
}
.text-align-left span {
  margin-right: 5px;
}
.bar-footer.footer-tab a {
  text-align: center;
  vertical-align: middle;
  height: 50px;
  cursor: pointer;
}
@media only screen and (max-width: 413px) {
  .bar-footer.footer-tab a.pull-right{
    overflow: hidden;
    width: 12.5%;
    padding: 4px 0;
  }
}
.bar-footer.footer-tab a span.sprite16-21 {
  color: #e03131;
  top: 3px;
  width: 21px;
  height: 21px;
  padding-top: 0;
  padding-bottom: 0;
  display: block;
  margin: auto;
  font-size: 21px;
}
.bar-footer.footer-tab a.top span.fa{
  color: #428bca;
}
.bar-footer.footer-tab a span.tab-label {
  display: block;
  font-size: 10px;
  color: #666;
}
#itemDetail{
  margin-top: -110px;
}

.openhouse-card .day{
  height: auto;
  padding: 10px 0 8px 10px;
}
.openhouse-card .day.passed{
  color: #9c9c9c;
}
.openhouse-card .day:not(:first-child){
  border-top: 1px solid #f1f1f1;
}
.openhouse-card .day > div{
  display: inline-block;
  height: 100%;
  vertical-align: top;
}
.openhouse-card .day .left{
  width: 50px;
  font-size: 30px;
  padding-top: 7px;
}
.openhouse-card .day .right{
  width: calc(100% - 50px);
}
.pull-spinner{
  display: inline-block;
}
.pull-spinner.block{
  display: block;
}
.treb-link{
  color: #428bca;
}
.city-or-cmty{
  display: flex;
  justify-content: space-between;
  /* font-size: 12px; */
  color: #666;
  padding:10px 10px 10px 10px;
}
.city-or-cmty span{
  padding-left: 7px;
}
.hidden{
  visibility: hidden;
}
.schs-showmore{
  text-align: center;
  font-size: 17px;
  padding: 11px 10px;
  color: #428bca;
}
.card.invest-card .line-chart{
  padding: 10px;
}
.card.invest-card .numbers{
  /* display: flex; */
  /* border-bottom: 1px solid #f1f1f1; */
}
.card.invest-card .numbers > div,
.card.predict-card .numbers > div{
  display: inline-block;
  width: 50%;
  padding: 10px 0 10px 10px;
  position: relative;
  text-align: center;
}
.card.invest-card .numbers > div:not(:first-child):before,
.card.predict-card .numbers > div:not(:first-child):before{
  content: '';
  height: 50%;
  position: absolute;
  left: 0;
  top: 16px;
  border-left: 1px solid #f1f1f1;
}
.card.invest-card .numbers .inline{
  display: inline-block;
}
.card.invest-card .numbers .tl{
  font-size: 10px;
  color: #777;
}
.card.invest-card .numbers .index{
  padding-left: 7px;
  font-size: 12px;
}
.card.invest-card .numbers .val,
.card.predict-card .numbers .val{
  font-weight: bold;
  font-size: 20px;
}
.card.invest-card .val,
.card.predict-card .val{
  font-family: 'Timeburner';
}
.card.admin-card .desc{
  /* padding: 0px 11px 0 11px; */
  color: #666;
}
.links.no-padding {
  padding:0 !important;
}
.card.invest-card .links{
  border-bottom: 1px solid #f1f1f1;
  padding: 20px 10px;
}
.card.admin-card .links > a,
.card.location-card .links > a,
.card.invest-card .links > a{
  padding: 10px;
  color: black;
  font-weight: bold;
  font-size: 15px;
  display: inline-block;
  /*width: calc(10% - 5px);*/
  width: 100%;
  text-align: center;
  background: #f1f1f1;
}
.card.invest-card .close-cost, .card.invest-card .mortgage{
  text-align: center;
}
.card.invest-card .close-cost .desc,
.card.invest-card .mortgage .desc{
  text-align: left;
  /* font-size: 12px; */
  color: #666;
  display: block;
  padding: 10px 0 10px 10px;
}
.card.invest-card .val-wrapper{
  padding:15px 0 5px 0;
}
.card.invest-card .val-wrapper .val{
  font-size: 20px;
  font-weight: bold;
}
.card.invest-card .bar-wrapper {
  padding: 0px 10px;
}
.card.invest-card .bar-wrapper .tip{
  color: #777;
  font-size: 12px;
  margin-bottom: 5px;
}
.card.invest-card .bar-wrapper .ss-question-circle-lightgray{
  color: #bdbdbd;
  margin: 0px 0 0 10px;
}
.cashflow-progress{
  width: 100%;
  height: 15px;
  background: #eee;
  border-radius: 3px;
  overflow: hidden;
}
.cashflow-progress > div{
  height: 100%;
  display: inline-block;
}
.card.invest-card .bar-wrapper .tax > div,
.card.invest-card .bar-wrapper .mfee > div{
  display: inline-block;
}
.bgColore74c3c{
  background: #e74c3c;
}
.bgColor1abc9c{
  background: #1abc9c;
}
.card.invest-card .monthlyTitle .dot{
  border-radius: 50%;
  width: 6px;
  height: 6px;
}
.card.invest-card .bar-wrapper .desc{
  width: calc(100% - 65px);
  color: #777;
  font-size: 13px;
  margin-left: 8px;
}
.card.invest-card .bar-wrapper .val{
  text-align: right;
  font-family: "Helvetica Neue", Helvetica, sans-serif;
  font-weight: normal;
}
.card.invest-card .bar-wrapper .val.red{
  color: #e03131;
}
.getMapBtn > .btn .fa.fa-group{
  font-size: 13px;
}
.getMapBtn > .btn .fa,
.card.location-card .links > a .fa,
.card.invest-card .links > a .fa{
  color: #777;
  margin-right: 10px;
}
.card.location-card .links > a .fa,
.card.invest-card .links > a .fa{
  vertical-align: text-bottom;
}
.getMapBtn {
  color: #428bca;
  text-align: center;
  padding: 10px;
  color: #428bca;
  text-align: center;
  padding: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.getMapBtn .btn {
  background: #f1f1f1;
  border: none;
  font-weight: 700;
  font-size: 15px;
  border-radius: 0;
  width: 90%;
  padding: 10px 0;
}
// .getMapBtn .btn.half {
//   width: 48%;
// }

// .getMapBtn .left {
//   float: left;
//   margin-right: 10px;
// }
.manage-card .row > label{
  display: inline-block;
  width: 40%
}
.manage-card .row > select{
  display: inline-block;
  width: 60%
}
.manage-card .btn-wrapper{
  padding: 10px;
}
.copy-right{
  padding-top: 30px;
  font-size: 12px;
  color: #a0a0a0;
  /* text-align: center; */
}
.copy-right div.desc{
  text-align: left;
  padding-top: 10px;
}
.copy-right div.cpimg-wrapper{
  /* margin-left: -6px; */
  padding-top: 0;
  margin-top: 1px;
  display: inline-block;
  vertical-align:top;
}
.copy-right div.cpimg-wrapper.rltr{
  padding-right: 14px;
}
.copy-right div.cpimg-wrapper.mls{
  padding-top: 2px;
  vertical-align: bottom;
}
.copy-right img{
  height: 50px;
  vertical-align: top;
  display: inline-block;
  padding-top: 4px;
  /* margin-left: 6px; */
}
/* .copy-right img.realtor{
  width: 17px;
} */
.cmnt-icon {
  padding: 3px 7px;
  color: #e03131;
  font-size: 14px;
  padding:3px
}
.comment-number {
  font-size: 9px;
  position: absolute;
  border: none;
  padding: 1px 1px;
  width: 15px;
  margin-left: 5px;
  border-radius: 5px;
  color: white !important;
  background: #e03131;
}
.verified{
  padding: 12px 10px 10px 10px;
  /*height: 41px;*/
  font-size: 14px;
  background: white;
  margin-top: 10px;
}
.verified .block{
  display: block;
  padding: 5px 0;
}
.verified .fa-check-circle{
  color:rgba(30,166,27,0.9);
}
.verified .fa-exclamation-circle{
  color:#e03131;
}
.verified-status span{
  vertical-align: middle !important;
  margin: 0 !important;
}
.verified-status input{
  font-size: 14px;
  width: calc(100% - 80px);
  margin: 0 5px 5px 0;
}
.verified .block img{
  width: 30px;
  height: 40px;
  vertical-align: middle;
}
.verified .btn{
  vertical-align: middle;
  background: #e03131;
  color: #fff;
  font-weight: bold;
  border-color: #e03131;
  border-radius: 0;
  min-width: 75px;
  margin-left: 10px;
}
.verified .time .fa,
.verified .cmstn .fa,
.verified .adok .fa{
  margin-right: 7px;
  font-size: 16px;
  margin-left: 1px;
  display: inline-block;
  vertical-align: top;
  margin-top: 1px;
  width: 21px;
}
.verified .adok .fa{
  /*font-size: 15px;*/
}
// .verified .time,
// .verified .cmstn,
// .verified .adok{
//   color: #989898;
// }
.verified .word{
  padding-left: 5px;
  color: #e03131;
  margin-top: 0;
  display: inline-block;
  /* position: absolute; */
  padding-top: 1px;
  vertical-align: top;
}
.verified .ss-verify{
  color: #e03131;
  font-size: 16px;
  display: inline-block;
  margin-top: 2px;
  vertical-align: top;
}
.verified .ver{
  margin-top: 5px;
}
.hover-bar .price .padding{
  font-weight: normal;
  font-size: 15px;
}
.price .padding{
  padding-left: 6px;
}
.bar.bar-standard.bar-footer .btn.btn-segment.brkg,
.bar.bar-standard.bar-footer .btn.btn-segment.top{
  color: white;
}
.bar.bar-standard.bar-footer .btn.btn-segment.brkg .brkg-wrapper {
  position: absolute;
  top:50%;
  transform:translateY(-50%);
  margin:auto;
  left:0;
  right:0;
}
#propDetailModal .bar-footer span.pull-left .btn-segment.brkg img{
  height: 32px;
  width: 32px;
  border-radius: 50%;
  border: 1px solid #fff;
  /* margin: 8px 3px 0; */
  vertical-align: middle
}
#propDetailModal .bar-footer span.pull-left .btn-segment.brkg span{
  color: white;
  vertical-align: top;
  width: calc(100% - 55px);
  display: inline-block;
  vertical-align: middle;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: 17px;
  line-height: 20px;
}
.staging{
  margin: 8px 0 4px;
}
.staging img{
  width: 100%;
  height: auto;
  padding: 10px;
  background: white;
}
.frame{
  position: absolute;
  z-index: 17;
  width: 78%;
  min-height: 305px;
  background: white;
  top: 50%;
  transform: translateY(-50%);
  margin: 0 11%;
  padding: 10px 12px 10px 12px;
}
.iframe-wrapper{
  height: 240px;
  width: 270px;
  -webkit-overflow-scrolling: touch;
  overflow-y: scroll;
}
.frame iframe{
  width: 100%;
  height: 100%;
  /* overflow: scroll; */
}
.frame .btn-wrapper{
  margin-top: 11px;
}
.fadeIn{
  animation: fadein 2s;
}
@keyframes fadein {
  from { opacity: 0; }
  to   { opacity: 1; }
}
.fadeInout {
  opacity: 1;
  animation: fade 2s linear;
}
@keyframes fade {
  0%,100% { opacity: 0 }
  50% { opacity: 1 }
}
[v-cloak] { display:none !important; }
.invest-card .realprediction_title {
  margin: 10px 0 0 10px;
}
.realprediction_title {
}
.real {
  font-weight: 700;
  display: inline;
  vertical-align: middle;
}
.logo_ai {
  background: #f53f2f;
  color: #fff;
  font-size: 9px;
  padding: 2px 3px;
  margin-left: 3px;
  border-radius: 2px;
  display: inline;
  vertical-align: middle;
}
.realprediction_color {
  color:#f53f2f;
}
.admin-card {
  padding-bottom: 10px;
}
.admin-card .card-content {
  padding: 0 10px;
  margin-bottom:10px;
}
.admin-card .card-content.top-desc {
  margin-bottom:0;
}
.prop-promote {
  padding-bottom: 10px;
}
.prop-promote button {
  background-color:#fff;
  color: #5cb85c;
}
.prop-promote.aSid:last-child {
  padding-bottom: 0px;
}
.prop-stats {
  display: flex;
  padding: 0px;
}
.prop-count {
  width: 33%;
  text-align: center;
}
.prop-count .cnt {
  font-size: 17px;
  color:#000;
  line-height: 24px;
  font-weight: bold;
}
.prop-count .unt {
  font-size: 12px;
  color: #6f6f6f;
}
.prop-count .stats {
  font-size: 12px;
}
.card.admin-card .links {
  padding-top:0;
  padding: 0px 10px 5px 10px;
}
.card.admin-card .links > a {
  margin-top: 15px;
}
.links .top-listing {
  color: #5cb85c !important;
  border: 1px solid #5cb85c;
  background-color: #fff !important;
}
#picArrayVideoImg{
  color: white;
  background: rgba(0,0,0,0.6);
  font-size: 62px;
  width: 61px;
  display: inline-block;
  position: absolute;
  top: 50%;
  margin-left: -31px;
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  height: 61px;
  padding-top: 0px;
  border-radius: 50%;
  padding-left: 0;
  /*box-shadow: 2px 2px 2px #2f2f2f */
}
.showing img{
  padding-top: 5px;
  width: 21px;
  margin: 0 auto;
  display: block;
}
.prop-cmty-wrapper {
  position: relative;
  /*padding: 0 10px;*/
}
.prop-cmty-image {
  vertical-align: top;
  margin-bottom: 15px;
  /*padding-top: 10px;*/
}
.prop-pos-on-cmty {
  position: absolute;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background-color: $main_red;
  border: 2px solid #fff;
  box-shadow: 2px 2px 2px #b7b7b7;
  margin-left: -7px;
  margin-top: -7px;
}
.open-full-screen-on-cmty{
  position: absolute;
  bottom: 0;
  right: 0;
  font-size: 20px;
  padding: 3px;
  background: rgba(255,255,255,.7);
  color: #1ea61b;
  // border-radius: 3px;
  span{
    vertical-align: middle;
  }
  .txt{
    font-size: 12px;
    line-height: 20px;
    padding: 0 5px;
    font-weight: bold;
  }
}
.prop-cmty-btns {
  bottom: 10px;
  left: 0;
  right: 0;
  margin-top: 20px;
  &.hasCmtyImage {
    position: absolute;
    margin-top: 0;
  }
}
.prop-cmty-header {
  display: flex;
  justify-content: space-between;
}
.prop-cmty-fav-btn {
  padding: 5px;
  border: 1px solid #f1f1f1;
  .fa {
    margin-left: 10px;
  }
  .fa-heart {
    color: #e03131;
  }
}
.prop-cmty-title {
  color: #000;
  font-size: 15px;
  font-weight: bold;
  margin-bottom: 5px;
}
.prop-cmty-crossroad{
  color: #666;
  font-size: 14px;
  padding-top: 5px;
}
.prop-cmty-sub {
  font-size: 10px;
  margin-bottom: 20px;
}
.changeLang a{
  border-radius: 4px;
  height: auto;
  padding: 3px !important;
  border: 1px solid white;
  font-size: 15px;
  margin-top: 11px;
  margin-right: 10px;
}
@media (min-width: 768px) {
  .picUrlsLength {
    display:none;
  }
}
.brkgs{
  min-height: 32px;
}
.brkgs .btn-positive{
  margin-right: 10px;
}
.fa-question-circle-o{
  margin: 0px 5px 0px 5px;
  vertical-align: text-bottom;
  font-size: 14px;
  color: #777;
}
.price-drop{
  font-size: 15px;
  margin: 0;
  font-weight: bold;
  .fa{
    font-size: 18px;
    margin-right: 3px;
    vertical-align: text-top;
  }
  >div{
    padding: 15px 0;
    border-top: 0.5px solid #ddd;
  }
}
.cutoff{
  border-top: 0.5px solid #ddd;
}
.accuracy{
  padding: 0 10px 10px 10px;
}
.notesBtnPot{
  position: fixed;
  z-index: 10;
  left: 80%;
  bottom: 15%;
  border-radius: 50px;
  box-shadow: 0px 0px 5px 4px rgba(0, 0, 0, .12);
  background: #fff;
}
.notesBtn{
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 50px;
  min-height: 50px;
  background: transparent;
  border: 0;
  font-size:12px
}
.notesBtn .fa{
  color: #999;
}
.notesBtn .fa.active{
  color: #e03131;
}
.claim{
  border-bottom: 1px solid #ccc;
}
.noteIcon{
  margin-top: 4px;
  font-size: 24px;
}
.padding44 {
  padding-top: 44px;
}
.Assign{
  padding: 0 10px 10px;
  .AssignBtn{
    border: 1px solid #5cb85c;
    padding: 14px 6px;
    border-radius: 3px;
    color: #ffffff;
    font-size: 17px;
    text-align: center;
    margin-top: 18px;
    background:#5cb85c;
    width: 100%;
  }
}
.partLine{
  border-top: 0.5px solid #ddd;
  width: 120%;
  height: 1px;
}
#condoNm{
  font-weight: bold;
  font-size: 15px;
  margin-right: 7px;
  vertical-align: middle;

}
#condoDvlpr{
  font-size: 13px;
  color: #bdbdbd;
  font-weight: normal;
  margin-left: 19px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
#ellipsisTitle{
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.fpPadding{
  padding: 5px 0 7px 0;
}
.intraInfo{
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  border-radius: 3px;
  height: 28px;
}
.onimg-wrapper .intraActive{
  border-radius: 2px;
  background-color: #fff;
  color: #000;
  padding: 2px 6px 1px;
  line-height: 21px;
}
.iconDescMargin{
  margin-top: 7px;
}
.soldPriceHeard{
  display: flex;
  justify-content: space-between;
  padding-top: 18px;
  align-items: center;
}
.basic-info{
  font-weight: bold;
  font-size: 14px;
  .fa{
    margin: 0px 5px;
  }
}
.basic-info .ss14:first-of-type {
  margin-left: 0;
}
.squareDiv {
  width: 25px;
  height: 25px;
  background-color: #ddd;
  color: #000;
  text-align: center;
  border-radius: 2px;
}
.displayColCenter {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.flexAlignCenter {
  align-items: center;
}
.padding15-0{
  padding: 15px 0;
}
.paddingBottom7{
  padding: 10px 0 7px;
}
.paddingBottom0{
  padding: 10px 0 0;
}
.v-lazy-item img{
  max-width: 100% !important;
  max-height: 100% !important;
}
.propTypeDiv{
  font-weight: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 110px;
  margin-left: 4px;
}
.paddingBottom15{
  padding-bottom: 15px;
}
.borderTop{
  border-top:0.5px solid #ddd;
}
.statusTag {
  font-size: 12px;
  padding: 0px 2px;
  border-radius: 2px;
  color: #999;
  background-color: #f1f1f1;
}
.red-bg {
  background-color: #e03131;
  color: #fff;
}
.green-bg {
  background-color:rgba(30, 166, 27, 0.9);
  color: #fff;
}
.cashFlowAny{
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 5px;
}
.monthlyInfo{
  font-size: 17px;
  font-weight: normal;
  color: #000;
}
.monthlyTitle{
  font-size: 12px;
  font-weight: normal;
  color: #777;
  line-height: 12px;
  padding: 3px 0;
  display: flex;
  align-items: center;
}
.frequency{
  color: #000;
}
</style>
  