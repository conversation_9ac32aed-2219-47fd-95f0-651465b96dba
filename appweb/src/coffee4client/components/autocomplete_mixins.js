import mapGeocode from './mixin/mapGeocode.js'
var mixins = {
  mixins: [mapGeocode],
  data () {
    return {
      isSearchAutocomplete:false,
      searchGoogle:false,
    };
  },
  mounted :  function () {
    let self = this;
    this.autoPreviousRequestTs = null;
    this.googlePreviousRequestTs = null;
    window.bus.$on('close-google-autocomplete', function() {
      self.searchGoogle = false;
      self.gautocompletes = [];
    });
    window.bus.$on('open-google-autocomplete',function(){
      self.searchGoogle = true;
      self.searchGoogleAutoComplete();
    });
    window.bus.$emit('onoff-google-autocomplete',this.getSeachGoogleCache());
  },
  computed:{
    showAutocompletes:function(){
      return this.searchStr.length >=4;
    },
  },
  watch:{
    searchStr:function(val, oldVal){
      var self = this;
      if (val.length >= 4) {
        clearTimeout(self.debounceTimeout);
        self.debounceTimeout = setTimeout(function () {
          self.debounceTimeout = null;
          self.getAutoCompletes();
          if(self.searchGoogle) {
            self.searchGoogleAutoComplete();
          }
        }, 500);
      }
    }
  },
  data () {
    return {
      debounceTimeout:null,
      searchStr: window.vars?(vars.id||''):'',
      autocompletes:[],
      gautocompletes:[],
      propItems:[],
      hist:[],
      showItems:false,
      cnt:0,
      errorCount:0,
    }
  },
  methods : {
    getSeachGoogleCache () {
      let googleAutoComplete=localStorage.googleAutoComplete
      if (googleAutoComplete &&  /^\d+$/.test(googleAutoComplete)) {
        return (Date.now() < googleAutoComplete);
      }
      return false;
    },
    searchGoogleAutoComplete() {
      // let self = this;
      // if (!/^[a-zA-Z]\d+/.test(self.searchStr) && !self.dispVar.isCip && (self.errorCount < 3) && self.isNewerVer(self.dispVar.coreVer, '5.4.0')) {
      //   self.getGoogleAutoCompletes();
      // }
    },
    removeHist (key) {
      var self = this;
      indexHistCtrl.removeHist(key);
      indexHistCtrl.setHist();
      self.hist = indexHistCtrl.getHist();
      self.jumping = true;
      setTimeout(function () {
        self.jumping = false;
      }, 10);
    },
    getAutoCompletes(){
      // TODO: !deprecate, moved to native
      // if from share with ec
      if (window.vars && vars.ec) {
        return;
      }
      if(!this.isSearchAutocomplete){
        return;
      }
      // console.log('called getAutoCompletes')
      var self = this;
      var data = {s:self.searchStr};
      self.autoPreviousRequestTs = Date.now();
      data.ts = self.autoPreviousRequestTs;
      self.acloading = true;
      self.$http.post('/1.5/props/autocompleteGetNext', data, {
        // use before callback
        before(request) {
          // abort previous request, if exists
          if (this.previousRequest) {
            this.previousRequest.abort();
          }
          // set previous request on Vue instance
          this.previousRequest = request;
        },
        _timeout:8000
      }).then(
        function (ret) {
          ret = ret.data;
          if (ret.ts !== self.autoPreviousRequestTs) {
             // console.log(self.previousRequestTs);
             self.acloading = false;
             console.log('dump request');
             return;
          }
          setTimeout(function () {
            self.acloading = false;
          }, 200);
          if (ret.err) {
            RMSrv.dialogAlert(ret.err)
          } else {
            self.cnt = ret.cnt != null ? ret.cnt : self.propItems.length;
            self.autocompletes = ret.l;
            self.initPropListImg({target:'autocompletes'});
            // alert(self.isNewerVer(self.dispVar.coreVer, '5.4.0'));
            // alert(self.dispVar.coreVer);
          }
        },
        function (ret) {
          console.log( "server-error" );
        }
      );
    },
    formatSecibdaryText(s='') {
      return s.replace(/\s/g, '').split(',');
    },
    goBack(){
      if (!vars.focus && (this.showItems || this.focused)) {
        this.showItems = false;
        this.focused = false;
        return;
      }
      if (vars.src == 'nativeMap' || vars.src == 'nativeAutocomplete') {
        // RMSrv.dialogAlert('here')
        return window.rmCall(':ctx::cancel')
        // return;
      }
      this.focused = false;
      // this.clearCache();
      var url = '/1.5/index';
      if (vars.d) {
        url = vars.d;
      }
      // use back may cause crash on iphone6
      window.location = url;
      // return window.location = this.navBackLink;
    },
    // invoke search list api and dispList
    // TODO: show list in index or map mode
    searchAddrAndShowList(k,opt){
      // alert(k);
      this.searchStr = k;
      this.showItems = true;
      // this.viewMode = 'list';
      this.showMoreProps(opt);
    },
    showMoreProps(opt={}) {
      var self = this,
          data = {cache:true, id:this.searchStr, p:this.pgNum, fullAddr:opt.fullAddr};
      // soldOnly:true
      self.loading = true;
      self.$http.post('/1.5/search/prop/list', data).then(
        function(ret) {
          ret = ret.data;
          self.loading = false;
          if (ret.redirect) {
            return window.location = ret.redirect;
          }
          if (ret.e) {
            console.error(ret.e);
            self.err = ret.e;
            return;
          }
          // self.err = '';
          // self.searchAddrG();
          if (ret.resultList) {
            self.propItems = self.propItems.concat(ret.resultList);
          }
          // console.log(self.items);
          self.initPropListImg({target:'propItems'});
          self.offSet = ret.offSet;

          // self.cnt = ret.cnt != null ? ret.cnt : self.propItems.length;
          self.hasMorePropInList =  ret.cnt > ret.resultList.length;
          self.pSize = ret.pSize || 20;
          // if (self.resultList.length == 1) {
          //   self.viewListing(self.resultList[0]);
          // }
          // if (ret.barTitle) {
          //   self.barTitle = ret.barTitle;
          //   document.querySelector("#wx-title").innerHTML = ret.barTitle;
          // }
          // if (ret.desc) {
          //   document.querySelector("#wx-desc").innerHTML = ret.desc;
          // }
          // setTimeout(function () {
          //   if (window.RMSrv && RMSrv.wxOnShare) {
          //     RMSrv.wxOnShare();
          //   }
          // }, 0);
        }, function() {
          RMSrv.dialogAlert('Cannot connect to Database');
          console.error('Server Error');
        });
      self.pgNum += 1;
    },

    getLatLngByPlaceId(r){
      if (!r.description && r.v) {
        r.description = r.v + ',' + r.city + ',' + r.prov + ',' + r.cnty;
      }
      if (!r.description) {
        console.log('Error: need place description')
        return;
      }
      var self = this;
      self.loading = true;
      let geocoder = this.get_map_geo_fn();
      return geocoder.geocodePosition({address:r.description, vueSelf:self}, function(addrObj) {
        let lat = addrObj.lat();
        let lng = addrObj.lng();
        self.loading = false;
        if (!addrObj) {
          return;
        }
        if (vars.src == 'nativeMap') {//&& vars.focus
          return window.rmCall(':ctx::loc='+lng+','+lat)
        }
        if (self.moveToRecord) {
          self.showItems = false;
          self.focused = false;
          self.viewMode = 'map';
          if (!window.gmapInited) {
            window.gmapInited = true;
            window.gMapsCallback();
          }
          setTimeout(function() {
            self.resetFilter({keepType:true});
            self.moveToRecord({lat:lat, lng:lng});
          },200);
        } else {
          var url = '/1.5/mapSearch?loc='+lat+','+lng+'&zoom=15&cMarker=1';
          window.location = url;
        }
      });
    },
    searchAutocomplete(r={}){
      // console.log(r);
      var self = this;
      self.focused = false;
      self.pgNum = 0;
      self.propItems = [];
      var opt = {};
      if (r.tp == 'gps') {
        trackEventOnGoogle('autocomplete','gps')
        // alert(vars.src)
        if (vars.src == 'nativeMap' ) {//&& vars.focus
          return window.rmCall(':ctx::gps')
        }
        if (self.moveToRecord) {
          self.showItems = false;
          self.focused = false;
          // self.viewMode = 'map';
          self.resetFilter({keepType:true,keepPtype:true});
          if (!window.gmapInited) {
            window.gmapInited = true;
            window.gMapsCallback();
          }
          setTimeout(function () {
            self.showMapView({gps:1});
            self.locateMe();
          }, 500);
        } else {
          setTimeout(function () {
            let url = '/1.5/mapSearch?gps=1'
            if (r.saletp) {
              url+='&saletp='+r.saletp;
            }
            if(r.ptype == 'Commercial'){
              url+='&mapmode=commercial'
            }
            window.location = url;
          }, 100);
        }
        return;
      }
      if ('string' == typeof(r)) {
        r = {k:r, v:r, tp:'hist', all:true}
        // trackEventOnGoogle('autocomplete','hist');
      }
      if (r.tp == 'prop') {
        opt.fullAddr = true;
        // trackEventOnGoogle('autocomplete','prop');
      }
      if (!r.k) {
        if (!self.searchStr) {
          return;
        }
        r = {k:self.searchStr, v:self.searchStr, tp:'hist',all:r.all};
        // r.k = self.searchStr;
        // trackEventOnGoogle('autocomplete','hist');
      }
      if(r.all) {
        trackEventOnGoogle('autocomplete','searchBtn')
      }
      if (r.tp == 'prop' || r.tp == 'hist' || r.tp == 'google') {
        indexHistCtrl.pushHist(r);
        indexHistCtrl.setHist();
        self.hist = indexHistCtrl.getHist();
        trackEventOnGoogle('autocomplete',r.tp);
      }
      // console.log(r);
      // alert(JSON.stringify(self.dispVar));
      // if in mapMode
      if (self.moveToRecord || !self.dispVar.isCip || self.goBackIndex) {
        if (r.tp == 'google') {
          self.getLatLngByPlaceId(r);
          // trackEventOnGoogle('autocomplete','google')
        } else {
          if (r.all || (r.tp === 'hist' && r.k)) {
            self.searchAddrAndShowList(r.k,opt);
          } else {
            return window.bus.$emit('prop-changed',r);
          }
        }
      } else if (self.dispVar.isCip) {
        self.directSearch(r.id || r._id);
      }
    },
    parseGoogleResult(gret){
      var self = this;
      try {
        gret = JSON.parse(gret);
        if (gret.status !== 'OK') {
          if (/ZERO|TIMEOUT|INVALID/.test(gret.status)) {
            console.log('No match addr')
            self.errorCount += 1;
            return [];
          } else if (/QUERY|LIMIT|OVER/.test(gret.status)){
            self.errorCount += 10;
            return [];
          }
          RMSrv.dialogAlert(gret.status+' '+gret.error_message)
          return [];
        }
        self.errorCount = 0;
        var predicts = gret.predictions || [];
        var results = [];
        for (let p of predicts) {
          // alert(JSON.stringify(p))
          let tmp = {
            place_id: p.place_id,
            tp: 'google',
            k: p.structured_formatting.main_text,
            v: p.structured_formatting.main_text,
            description: p.description
          };
          var ref = self.formatSecibdaryText(p.structured_formatting.secondary_text);
          tmp.city = ref[0], tmp.prov = ref[1], tmp.cnty = ref[2];
          results.push(tmp);
        }
        return results;
      } catch (err) {
        RMSrv.dialogAlert(err.toString())
        return [];
      }
    },
    getGoogleAutoCompletes(){
      return;
      var self = this;
      self.googlePreviousRequestTs = Date.now();
      var url = self.dispVar.autocomplete;
      url += '&input='+self.searchStr;
      // data.ts = self.googlePreviousRequestTs;
      // Invoke native call
      if (!self.dispVar.autocomplete) {
        return console.error('no google autocomplete url!');
      }
      RMSrv.fetch(url, {}, function(ret){
        if (/^Error:/.test(ret)) {
          return RMSrv.dialogAlert(ret);
        } else {
          // alert(ret);
          var list = self.parseGoogleResult(ret);
          self.gautocompletes = list;
          // alert(JSON.stringify(list));
        }
      })
      // self.$http.get(url, {
      //   // headers: {'Access-Control-Allow-Origin': '*'},
      //   before(request) {
      //     if (this.previousRequest) {
      //       this.previousRequest.abort();
      //     }
      //     this.previousRequest = request;
      //   },
      //   _timeout:8000
      // }).then(
      //   function (ret) {
      //     ret = ret.data;
      //     if (ret.ts !== self.googlePreviousRequestTs) {
      //        console.log('dump google auto request');
      //        return;
      //     }
      //     if (ret.err) {
      //       RMSrv.dialogAlert(ret.err)
      //     } else {
      //       console.log(l);
      //       // self.autocompletes = ret.l;
      //     }
      //   },
      //   function (ret) {
      //     console.log( "server-error google autocomplete" );
      //   }
      // );
    }
  }
}
export default mixins;
