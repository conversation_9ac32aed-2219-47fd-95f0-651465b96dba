RMSrv =
  getCookie: (cname)->
    name = cname + "=";
    ca = document.cookie.split(';')
    for c in ca
      while (c.charAt(0) is ' ')
        c = c.substring(1)
      if c.indexOf(name) is 0
        return c.substring(name.length,c.length)
    ""
  init: ->
    @geolocation = false;
    @ver = @getCookie 'apsv'
    if @ver < '3.2'
      if navigator.geolocation
        @geolocation = navigator.geolocation
    getAndroidVersion = (ua) ->
      ua = (ua or navigator.userAgent).toLowerCase()
      match = ua.match(/android\s([0-9\.]*)/)
      if match then match[1] else false
    if getAndroidVersion() and parseFloat(getAndroidVersion()) < 4.4
      window.oldVerBrowser = true
    @bindEvents()
  getGeoPosition:(cb)->
    locationService = null
    if window.LocationServices
      locationService = window.LocationServices;
    else if RMSrv?.geolocation
      locationService = RMSrv.geolocation
    else
      locationService = navigator.geolocation
    # success,error,opt={}
    success = (pos)=>
      cb(pos) if cb
    error = (err)=>
      cb({err:err}) if cb
      console.log err.toString()
    # {enableHighAccuracy: true, timeout: 20000, maximumAge: 1000}
    return locationService.getCurrentPosition(success, error)
  bindEvents: ->
    document.addEventListener 'deviceready', @onDeviceReady, false
  onDeviceReady: ->
    # get location
    if (manufacturer = device.manufacturer?.toLowerCase()) and (['xiaomi','huawei'].indexOf(manufacturer) >= 0)
      delete window.LocationServices
    # get push notification id
    # setupMenuBtn
    RMSrv.setupMenuBtn()
    # stop back key
    RMSrv.setupBackBtn()
    # get pn
    RMSrv.regDevice()
    # facebook init for browser only
    # facebookConnectPlugin.browserInit('357776481094717')
    # setup file choosers for android 4.4.2
    RMSrv._setFileChooser()
    RMSrv._getKeyboard()
    RMSrv.ready = true
    # TODO: listen all error events
  enableMenuButton: (enable = true)->
    RMSrv._disableMenuKey = not enabled
  setupMenuBtn: ->
    #if (this.deviceType == 'Android'){
    #document.addEventListener("backbutton", function(){self.onBackKeyDown();}, false);
    document.addEventListener "menubutton", (->
      return if RMSrv._disableMenuKey
      window.location = "/1.5/settings"
      ),false
  enableBackButton: (enabled = true)->
    RMSrv._disableBackKey = not enabled
  setupBackBtn: ->
    document.addEventListener "backbutton",((e)->
      return if RMSrv._disableBackKey
      if document.getElementById('top-page')
        e.preventDefault()
        navigator.notification.confirm "Quit?",((btn)->
          if btn is 1
            navigator.app.exitApp()
          )
      else if document.getElementById('news-page')
        e.preventDefault()
        window.location = '/1.5/index'
      else if document.getElementById('wecard-list-page')
        e.preventDefault()
        window.location = '/1.5/settings'
      else if document.getElementById('dl-share-content')
        window.location.href = document.referrer
      else if document.getElementById('srvEle')
        e.preventDefault()
        toggleModal('propDetailModal', 'close')
      else
        navigator.app.backHistory()
    ), false
  scanQR: (returnUrl)->
    cordova.plugins.barcodeScanner.scan ((r)->
      RMSrv.QRsuccess(r,returnUrl)),RMSrv.QRfailed
    # false
  QRsuccess: (result,returnUrl)->
    #StatusBar.hide()
    return null if result.cancelled
    setTimeout ( ->
      if result.format != 'QR_CODE'
        return RMSrv.dialogAlert "Scan Error. Please try again."
      if domain = result.text.match(/(https|http):\/\/([a-z0-9\.\-\_]+)/i)
        #RMSrv.showInBrowser result.text
        if typeof returnUrl is 'function'
          return returnUrl(result.text)
        else
          window.location = (returnUrl or "/1.5/iframe?u=") + encodeURIComponent(result.text)
      else
        RMSrv.dialogAlert "Unknown Code : " + result.text
    ), 10
  QRfailed: (err)->
    #StatusBar.hide()
    RMSrv.dialogAlert "Scan Failed: " + err
  isIOS: -> /iPhone|iPad|iPod/i.test(navigator.userAgent)
  isAndroid: -> /Android/i.test(navigator.userAgent)
  isWeChat: -> /MicroMessenger/i.test(navigator.userAgent)
  isBlackBerry: -> /BlackBerry/i.test(navigator.userAgent)
  appendDomain:(url)->
    location = window.location.href
    arr = location.split("/")
    domain = arr[0] + "//" + arr[2]
    domain + url
  showInBrowser: (url)->
    unless /^(http|https)/.test url
      url = this.appendDomain(url)
    if RMSrv.ready # only jump link when browser is ready
      window.open url, '_system'
    #if RMSrv.isIOS()
    #  navigator.startApp.start url
    #else if RMSrv.isAndroid()
    #  window.open url, '_system'
  openTBrowser: (url, cfg)->
    unless /^(http|https)/.test url
      url = this.appendDomain(url)
    if cordova.ThemeableBrowser
      return cordova.ThemeableBrowser.open(url, '_blank', cfg)
    else
      return window.open(url, '_blank')
  openInAppBrowser: (url,options='location=false')-> #',toolbar=false')->
    ref = window.open(encodeURI(url), '_blank', options)
    closeWin = ->
      ref.removeEventListner 'loadstop'
      ref.removeEventListner 'loadstart'
      if timer
        clearInterval timer
        timer = null
      ref.close()
    checkClose = (event,inject)->
      if event.url.match("in_app_close")
        closeWin()
      if inject
        ref.executeSript({code: "function(){var _ref;(_ref = document.getElementById('goback-link')) != null ? _ref.href = '/in_app_close' : void 0;return true;}.();"});
    ref.addEventListener 'loadstop', (e)-> checkClose e,true
    ref.addEventListener 'loadstart', (e)-> checkClose e,false
    checkClose2 = ->
      ref.executeScript {code: "window.location"},(data)->
        if data.toString().match '/tools'
          closeWin()
    timer = setInterval checkClose2, 2000
  dialogAlert: (msg)->
    navigator.notification.alert (if 'string' is typeof msg then msg else msg.message or msg.toString())
  # cb(ret), ret = which key user clicked, start from 1
  dialogConfirm: (msg,cb,title,btns)->
    if not btns
      if title and Array.isArray title
        btns = title
        title = 'Confirm'
    navigator.notification.confirm msg.toString(),cb,title,btns
  isDBG: ->  /i\.realmaster/i.test window.location.hostname or /app\.test/i.test window.location.hostname
  fDoc: ->
    doc = null
    if iframe = document.getElementById 'iframe'
      try
        doc = iframe.contentDocument
        doc ?= document
        doc = doc.document if doc.document
      catch e
        console.log e
    #console.log doc
    doc
  getMeta: (doc)->
    meta = doc.querySelectorAll 'meta'
    ret = title:doc.title
    for m in meta
      ret[m.getAttribute('name')] = m.getAttribute('content')
    ret
  getShareImage: (doc)->
    if div = (doc.getElementById('content_div') or doc.body)
      for img in div.getElementsByTagName 'img'
        if img?.src
          return img.src
    'https://realmaster.com/img/logo.png'
  logoImg: "data:image/png;base64,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"
  resizeImage: (doc,url,max,cb)->
    done = (d)->
      done = null
      cb d or RMSrv.logoImg
    canvas = doc.createElement 'canvas'
    img = doc.createElement 'img'
    img.onload = ->
      try
        wc = w = this.width
        hc = h = this.height
        if (w > h)
          if (w > max)
            wc = max
            hc = Math.round(h / w * max)
        else
          if (h > max)
            hc = max
            wc = Math.round(w / h * max)
        canvas.width = wc
        canvas.height = hc
        ctx = canvas.getContext('2d')
        ctx.drawImage(this, 0, 0, wc, hc)
        data = canvas.toDataURL("image/png")
        done data if done
      catch e
        if RMSrv.isDBG()
          RMSrv.dialogAlert "Error:" + e
        done() if done
    img.onerror = (e)->
      if RMSrv.isDBG()
        alert "resizeImage Error:" + img.src
        alert JSON.stringify e
      done() if done
      #RMSrv.dialogAlert "Load Error:" + e
    img.setAttribute('crossOrigin', 'anonymous')
    img.crossOrigin = '' # "Anonymous"
    if m = /^(.*\?v=)\d+$/g.exec url
      img.src = m[1] + Date.now()
    else if (url[url.length - 1] is '#')
      #with '#' not work in android
      url = url.substring(0, url.length-1)
      img.src = url + '?v=' + Date.now()
    else if (/f\.i\.realmaster.*\.(jpg|png)$/.test url) or (/f\.realmaster.*\.(jpg|png)$/.test url)
      img.src = url + '?v=' + Date.now()
    else
      img.src = url
    # if img.complete or img.complete is undefined
    #   img.src = "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///ywAAAAAAQABAAACAUwAOw==";
    #   img.src = src

    null
  hasWechat: (cb)->
    RMSrv.onReady ->
      if Wechat?.isInstalled
        Wechat.isInstalled ((has)-> cb has),(-> cb(false))
      else
        cb false
  wechatAuth: ->
    RMSrv.onReady ->
      Wechat?.auth "snsapi_userinfo",((resp)->
          #alert JSON.stringify resp
          # got code here, will call /schema?code=xxx automatically
          # android don't go to /schema automatically, need jump
          if RMSrv.isAndroid() and resp?.code?.length > 10
            window.location.href = "/scheme?code=" + resp.code
        ),(reason)->
          location.reload() # when bind/or auth
          console?.log reason
  wechatShareError: (err)->
    switch err?.toString()
      when 'ERR_USER_CANCEL'
        return
      when 'ERR_WECHAT_NOT_INSTALLED'
        RMSrv.dialogAlert('WeChat Not Installed')
      when 'ERR_UNKNOWN' # user can't do anything with unknown error. Don't show it.
        window.onerror "WeChat Sharing ERR_UNKNOWN",window.location.href,""
        return
      else
        RMSrv.dialogAlert(err.toString())
  wechatShare: (doc,share,tp)->
    if (RMSrv.ver >= '3.1.0') and Wechat?
      opts =
        message:
          title: share.title or "RealMaster Sharing"
          description: share.description or "RealMaster App Sharing"
          thumb: share.image
          media:
            type: Wechat.Type.WEBPAGE
            webpageUrl: share.url.replace('realmaster.com','realmaster.cn')
        scene: tp
      RMSrv.shared share, true
      Wechat.share opts, (-> RMSrv.shared share),(reason)->
        if reason is '发送请求失败'
          RMSrv.resizeImage doc,share.image,100,(imgData)->
            opts.message.thumb = imgData
            Wechat.share opts, (-> RMSrv.shared share),RMSrv.wechatShareError
        else
          RMSrv.wechatShareError(reason)
      return
    # get image thumbnail
    RMSrv.resizeImage doc,share.image,100,(imgData)->
      opts =
        title:  share.title or "RealMaster Sharing"
        description: share.description or "RealMaster App Sharing"
        thumbData: imgData.split(',')[1],
        url: share.url.replace('realmaster.com','realmaster.cn')
      #alert("WeChat URL:" + opts.url)
      WeChat.share opts, tp, (-> RMSrv.shared share),RMSrv.wechatShareError
  facebookLogin: (cb)->
    facebookConnectPlugin.login ["public_profile"],cb,(err)->
      RMSrv.dialogAlert "Login Error:" + JSON.stringify err
  facebookLoginNShare: (doc,share,tp)->
    RMSrv.facebookLogin -> # (userData)->
      RMSrv.facebookShare doc,share,tp
      #RMSrv.dialogAlert "UserInfo: " + JSON.stringify(userData)
      #facebookConnectPlugin.getLoginStatus (status)->
        #RMSrv.dialogAlert  "current status: " + JSON.stringify(status)
  _fbErrorCounter: 0
  facebookShare: (doc,share,tp)->
    opts =
      method: tp
      picture: share.image
      link: share.url
      caption: share.title or "RealMaster Sharing"
      description: share.description or "RealMaster App Sharing"

    facebookConnectPlugin.showDialog opts,(-> RMSrv.shared share),(err)->
      if RMSrv._fbErrorCounter++ > 1
        RMSrv.dialogAlert "Dialog Error:" + JSON.stringify err
        RMSrv._fbErrorCounter = 0
        return
      if err.toString() is 'No active session'
        RMSrv.facebookLoginNShare doc,share,tp

  qrcodeShare: (cmd,url,id)->
    id ?= 'id_share_qrcode'
    if cmd is 'show'
      if dialog = document.getElementById(id)
        dialog.style.display = 'block'
        genQrCode = ->
          # generate qrcode
          holder = document.getElementById id+'_holder'
          holder.innerHTML = ''
          new QRCode(holder, url)
        if QRCode?
          genQrCode()
        else
          # load js if not yet
          po = document.createElement 'script'
          po.type = 'text/javascript'
          po.src = '/js/qrcode/qrcode.min.js'
          document.getElementsByTagName('head')[0].appendChild po
          po.onload = genQrCode
    else
      if dialog = document.getElementById(id)
        dialog.style.display = 'none'
  showSMB: (cmd,prefix='share-')-> # backdrop must be after the main detail content div, with class 'backdrop', style is 'display:none'
    if not RMSrv._shareMask
      RMSrv._shareMask = document.getElementById("backdrop")
      RMSrv._shareMask?.addEventListener 'click', -> RMSrv.showSMB 'hide'
    if cmd is 'show'
      RMSrv._sharePrefix = prefix
      if newParent = document.getElementById prefix + 'placeholder'
        newParent.appendChild document.getElementById 'shareDialog'
      document.body.classList.add 'smb-open'
      RMSrv._shareMask?.style.display = 'block'
      RMSrv.shareLang()
    else if cmd is 'hide'
      document.body.classList.remove 'smb-open'
      RMSrv._shareMask?.style.display = 'none'
  _shareMap: {title:'title',desc:'description',url:'url',image:'image', data:'data',dnurl:'dnurl'} # set default. Some case has no language selection
  _shareLang: null
  shareLang: (lang,doc = window.document)-> # en or not
    lang_en = document.getElementById('id_share_lang_en')
    lang_zh = document.getElementById('id_share_lang_zh')
    lang_kr = document.getElementById('id_share_lang_kr')
    lang_cur = document.getElementById('id_share_lang_cur')

    lang_en?.classList.remove('active')
    lang_cur?.classList.remove('active')
    lang_zh?.classList.remove('active')
    lang_kr?.classList.remove('active')

    langCur = lang_cur?.dataset?.lang
    if (not lang) or (lang is 'cur')
      lang = langCur
      lang_cur?.classList.add('active')
    else if (lang is 'en')
      lang_en?.classList.add('active')
    else if lang in ['zh-cn','zh']
      lang_zh?.classList.add('active')
    else if lang is 'kr'
      lang_kr?.classList.add('active')

    if (lang is 'en')
      RMSrv._shareMap = {"title-en":'title',"desc-en":'description',url:'url',image:'image', data:'data',dnurl:'dnurl'}
    else
      RMSrv._shareMap = {title:'title',desc:'description',url:'url',image:'image', data:'data',dnurl:'dnurl'}

    _shareInfo = RMSrv._getShareInfo doc,RMSrv.getMeta(doc)
    document.getElementById('id_share_title')?.value = _shareInfo.title
    document.getElementById('id_share_desc')?.value = _shareInfo.description
    if lang? and lang isnt 'cur' # only need to specify
      RMSrv._shareLang = lang
    else
      RMSrv._shareLang = null # TODO
  _getShareInfo: (doc,share)->
    _getInfo = (key,name)->
      try
        if tmp = (doc.getElementById(RMSrv._sharePrefix + key) or doc.getElementById('share-' + key) or doc.getElementById('alt-' + key))
          share[name or key] = tmp.value or tmp.textContent
      catch e
        console?.log e
    for k,n of RMSrv._shareMap # {title:'title',desc:'description',url:'url',image:'image'}
      _getInfo k,n
    if not share.image? then share.image = RMSrv.getShareImage doc
    if not share.url? then share.url = doc.URL or window.location.href
    share
  share: (type,doc = window.document)->
    _getShareInfo = (data, cb)->
      done = (d)->
        done = null
        cb d
      _shareInfo = RMSrv._getShareInfo doc,RMSrv.getMeta(doc)
      if title = doc.getElementById('id_share_title')?.value
        _shareInfo.title = title
      if desc = doc.getElementById('id_share_desc')?.value
        _shareInfo.description = desc
      # update url for language
      if RMSrv._shareLang
        if m = /\?.*(lang\=[a-zA-Z\-]+)/.exec _shareInfo.url
          _shareInfo.url = _shareInfo.url.replace m[0],(m[0].replace m[1],"lang=#{RMSrv._shareLang}")
        else if /\?[a-z0-9]+\=/i.test _shareInfo.url
          _shareInfo.url += '&lang=' + RMSrv._shareLang
        else
          _shareInfo.url += '?lang=' + RMSrv._shareLang
        if _shareInfo.data and m = /.*(lang\=[a-zA-Z\-]+)/.exec _shareInfo.data
          _shareInfo.data = _shareInfo.data.replace m[0],(m[0].replace m[1],"lang=#{RMSrv._shareLang}")
        # _shareInfo.url
      #POST to mp get share info
      try
        if not _shareInfo.data
          #RMSrv.dialogAlert "Cannot Share! No Data"
          unless _shareInfo.data = document.querySelector('#share-data')?.innerHTML
            return done _shareInfo
        if typeof(data) is 'string'
          _shareInfo.data += "&channel=#{data}"
        xmlhttp = new XMLHttpRequest()
        xmlhttp.open "POST", '/1.5/api/rm/shareInfo',true #RMSrv.origin() +
        xmlhttp.setRequestHeader "Content-type","application/x-www-form-urlencoded"
        xmlhttp.timeout = 8000
        xmlhttp.ontimeout = ()->
          RMSrv.dialogAlert 'Timeout! Try again Later'
        xmlhttp.onreadystatechange = ()->
          if xmlhttp.readyState is 4
            if xmlhttp.status is 200
              ret = JSON.parse(xmlhttp.responseText)
              console?.log ret.url
              #open link in browser
              unless ret.ok
                if ret.err
                  return RMSrv.dialogAlert ret.err
              else
                _shareInfo.url2 = _shareInfo.url
                _shareInfo.url = ret.url
            return done _shareInfo if done and (data isnt 'share')
        # alert _shareInfo.data
        xmlhttp.send _shareInfo.data
        return
      catch e
        console?.log e
        done _shareInfo if done
        return
    # get meta; get title/desc/url/image
    switch type
      when 'show','hide'
        RMSrv.showSMB type
      when 'lang-en'
        RMSrv.shareLang 'en',doc
      when 'lang-cur'
        RMSrv.shareLang 'cur',doc
      when 'lang-zh-cn'
        RMSrv.shareLang 'zh-cn',doc
      when 'lang-kr'
        RMSrv.shareLang 'kr',doc
      when 'qr-code'
        RMSrv.showSMB 'hide'
        _getShareInfo type, (sInfo)->
          RMSrv.qrcodeShare 'show',sInfo.url
      when 'qr-code-close'
        RMSrv.qrcodeShare 'hide'
      when 'wechat-friend'
        RMSrv.showSMB 'hide'
        _getShareInfo type, (sInfo)->
          #alert sInfo.image if RMSrv.isDBG()
          # alert JSON.stringify sInfo ＃ (Wechat?.Scene.SESSION or WeChat.Scene.session or 0)
          RMSrv.wechatShare doc,sInfo,(if RMSrv.ver >= '3.1.0' then 0 else WeChat.Scene.session or 0)
      when 'wechat-moment'
        RMSrv.showSMB 'hide'
        _getShareInfo type, (sInfo)->
          RMSrv.wechatShare doc,sInfo,(if RMSrv.ver >= '3.1.0' then Wechat?.Scene.TIMELINE else WeChat.Scene.timeline)
      when 'facebook-feed'
        RMSrv.showSMB 'hide'
        _getShareInfo type, (sInfo)->
          RMSrv.facebookShare doc,sInfo,'feed'
      else
        RMSrv.showSMB 'hide'
        _getShareInfo type, (sInfo)->
          window.plugins.socialsharing.share (sInfo.title or sInfo.description or 'Shared with RealMaster App'),(sInfo.title or sInfo.description or 'RealMaster App Sharing'),sInfo.image,sInfo.url
  origin: ->
    window.location.origin or (window.location.protocol + "//" + window.location.hostname + if window.location.port then ':' + window.location.port else '')
  shared: (share, preShare)->
    getECodeFromUrl = (url)->
      if /propDetailPage/.test url
        return url.split('ec=')[1].split('&')[0]
      else
        return url.split('?')[0].split('/').pop()
    url = RMSrv.origin() + '/1.5/user/update?act=share'
    if share.dnurl?
      url = share.dnurl
      if /^\//.test share.dnurl
        url = RMSrv.origin() + share.dnurl
    if share.url
      sharedEData = getECodeFromUrl(share.url)
      split = if url.indexOf('?') > 0 then '&' else '?'
      url += split+'data='+sharedEData
      url += '&pre=1' if preShare
    xmlhttp = new XMLHttpRequest()
    xmlhttp.onreadystatechange = ->
      if (xmlhttp.readyState is 4) and (xmlhttp.status is 200)
        try
          if (j = JSON.parse(xmlhttp.responseText))?.j
            window.location.href = j.j
        catch e
    xmlhttp.open "POST",url,true
    #xmlhttp.setRequestHeader "Content-type","application/x-www-form-urlencoded"
    xmlhttp.send()
  clearCache: ->
    window.cache?.clear (->),(e)-> console.log e
  setFileChooser: (id,chooser)->
    RMSrv._fileChooser ?= {}
    RMSrv._fileChooser[id] = chooser
    RMSrv._setFileChooser()
  _setFileChooser: ->
    if (device?.platform?.toLowerCase() is 'android') and (device?.version?.indexOf( '4.4.2' ) is 0)
      window.requestFileSystem LocalFileSystem.PERSISTENT, 0, ((fs)->
        RMSrv.fileSystem = fs
        ), (e)-> RMSrv.dialogAlert e
      for id,chooser of RMSrv._fileChooser
        if ele = document.getElementById(id)
          ele.addEventListener 'click', (->
            filechooser.open {},((f)->
              RMSrv.fileSystem.root.getFile f.filepath, null, ( (fileEntry)->
                fileEntry.file chooser, (e)-> RMSrv.dialogAlert e
                ),( (e)-> RMSrv.dialogAlert e)
              )
            ),(err)-> RMSrv.dialogAlert err
      delete RMSrv._fileChooser
  getKeyboard: (cb)->
    if cordova?.plugins?.Keyboard
      return cb cordova.plugins.Keyboard
    RMSrv._cbKeyBoard ?= []
    RMSrv._cbKeyBoard.push cb
  _getKeyboard: ->
    if cordova?.plugins?.Keyboard && RMSrv._cbKeyBoard
      for cb in RMSrv._cbKeyBoard
        cb cordova.plugins.Keyboard
      delete RMSrv._cbKeyBoard
  onReady: (cb)->
    if @_readyWaitingList
      @_readyWaitingList.push cb
      return true
    else
      @_readyWaitingList = []
      @_readyWaitingList.push cb
    @getKeyboard =>
      #if before and 'function' is typeof angular?.resumeBootstrap
      #  angular.resumeBootstrap()
      #  window.name = oldName
      for c in @_readyWaitingList
        c() if 'function' is typeof c
      delete @_readyWaitingList
    true
  sendToken: (tp,token)->
    pn = tp + ':' + token
    if (not @_fnWhenReg) and pn is localStorage.pn
      return @_notifyReg pn
    localStorage.pn = pn
    # send to server
    xmlhttp = new XMLHttpRequest()
    xmlhttp.onreadystatechange = ->
      if xmlhttp.readyState is 4 and xmlhttp.status is 200 then console?.log xmlhttp.responseText
    xmlhttp.open "POST", '/1.5/user/updPn'
    xmlhttp.setRequestHeader "Content-type","application/x-www-form-urlencoded"
    xmlhttp.send "pn="+pn
    @_notifyReg pn
  regDevice: ->
    return null if @wakenByMsg # don't register again, if waken by message
    try
      pushNotification = window.plugins.pushNotification
      if (device.platform == 'android' || device.platform == 'Android' || device.platform == 'amazon-fireos' )
        pushNotification.register (->),((e)-> RMSrv.dialogAlert(e)), {"senderID":"339283724232","ecb":"onNotification"}
      else
        pushNotification.register ((token)->
          RMSrv.sendToken 'ios',token
        ), ((e)-> RMSrv.dialogAlert(e)), {"badge":"true","sound":"true","alert":"true","ecb":"onNotificationAPN"}
    catch err
      RMSrv.dialogAlert "Error: " + err.message
  _notifyReg: (pn)->
    @_RegFinished = true
    if fns = @_fnWhenReg
      for fn in fns
        try
          fn pn
        catch e
  whenReg: (cb)->
    return cb() if @_RegFinished
    @_fnWhenReg ?= []
    @_fnWhenReg.push cb
  getTranslate: (m, cb) ->
    # setTimeout ->
    #   return cb 'xxxxx' + m
    # , 3000
    url = "https://translate.google.com/#auto/zh-CN/" + encodeURIComponent(m)
    selector = ".text-wrap .translation"
    RMSrv.getPageContent url, selector, {wait:12000}, (ret)->
      return cb ret
  getPageContent: (url, selector, opt, cb) ->
    if not cb?
      cb = opt
      opt = null
    RMSrv.getKeyboard ->
      if opt.close and ref and 'function' is typeof ref.close
        removeListenerAndClose()
        return
      hiddenBrowser = cordova.ThemeableBrowser
      btnLeft = {
        image: 'left'
        imagePressed: 'left'
        align: 'left'
        event: 'custClosePressed'
      }
      btnB = {
        image: 'back'
        imagePressed: 'back'
        align: 'left'
        event: 'backPressed'
      }
      if window.isIOS
        customButtons = [ btnLeft, btnB ]
      else
        customButtons = [ btnB, btnLeft ]
      customButtons.push {
        image: 'check'
        imagePressed: 'check'
        align: 'right'
        event: 'pagesPressed'
      }
      cfg =
        hidden: true
        toolbar:
          height: 44
          color: '#E03131'
        customButtons: customButtons
        fullscreen: false
        # backButtonCanClose: true
      if opt.hide is false
        cfg.hidden = false
      returned = false
      errorBody = null
      ref = hiddenBrowser.open(url, '_blank', cfg)
      removeListenerAndClose = ()->
        ref.removeEventListener 'custClosePressed', cancelImport
        ref.removeEventListener 'backPressed', backPressed
        ref.removeEventListener 'loaderror', whenError
        ref.removeEventListener 'pagesPressed', whenLoadStop
        ref.removeEventListener cordova.ThemeableBrowser.EVT_ERR, whenError
        ref.close()
      done = (body) ->
        return if returned
        returned = true
        removeListenerAndClose()
        cb body
      if opt.nostop isnt true
        setTimeout (-> whenLoadStop()), opt?.wait or 6000
      whenLoadStop =  ->
        #document.getElementById('result_box') works for desktop
        code = "(el = document.querySelector('#{selector}'))?el.textContent:''"
        code = 'document.documentElement.innerHTML' if selector in ['html','html:wechat']
        setTimeout ()->
          ref.executeScript {code: code}, (ret) ->
            ret = ret[0] if Array.isArray ret
            return done ret #if b2.length > 100
        , 1000
      whenError = (e) ->
        errorBody = "Error: #{e.code + ' : ' + e.message}"
      cancelImport = -> # conflict name event will not trigger
        done 'Cancelled', ->
          window.location = '/1.5/wecard'
      backPressed = ->
        script = "window.history.back(-1)"
        ref.executeScript {code: script}
      if (cfg.hidden is true) or (opt.isWechat)
        ref.addEventListener 'loadstop', whenLoadStop
      ref.addEventListener 'custClosePressed', cancelImport
      ref.addEventListener 'backPressed', backPressed
      ref.addEventListener 'pagesPressed', whenLoadStop #pagesPressed
      ref.addEventListener 'loaderror', whenError
      ref.addEventListener cordova.ThemeableBrowser.EVT_ERR, whenError

jumpURL = (url)->
  window.location = RMSrv.origin() + "/scheme/jump?u=" + encodeURIComponent url
confirmJump = (msg,url)->
  fnCfmJump = (idx)->
    if idx is 2
      jumpURL url
  RMSrv.dialogConfirm msg,fnCfmJump,"Message",['Cancel','Open']

onNotification = (e)->
  switch e.event
    when 'registered'
      if RMSrv.wakenByMsg then return # could be called when cold start
      if e.regid.length > 0
        # Your GCM push server needs to know the regID before it can push to this device
        # here is where you might want to send it the regID for later use.
        RMSrv.sendToken 'android',e.regid
    when 'message'
      RMSrv.wakenByMsg = true;
      # if this flag is set, this notification happened while we were in the foreground.
      # you might want to play a sound to get the user's attention, throw up a dialog, etc.
      navigator.vibrate 1000
      if e.foreground
        #on Android soundname is outside the payload.
        # On Amazon FireOS all custom attributes are contained within payload
        soundfile = e.soundname or e.payload.sound
        # if the notification contains a soundname, play it.
        # playing a sound also requires the org.apache.cordova.media plugin
        (new Media("/android_asset/www/"+ soundfile)).play()
        if e.payload?.url and e.payload?.message
          return confirmJump e.payload.message,e.payload.url
      else
        # otherwise we were launched because the user touched a notification in the notification tray.
        if e.coldstart
          if e.payload?.url
            return jumpURL e.payload.url
          #app.setupScreen("#notification?st=coldstart");
        else # background notification
          #app.setupScreen("#notification?st=background");
          if e.payload?.url and e.payload?.message
            return confirmJump e.payload.message,e.payload.url
      if e.payload?.message
        dialogAlert e.payload.message
      #android only
      # console.log(e.payload.msgcnt);
      updateNotification(e.payload.msgcnt) if updateNotification?
      #amazon-fireos only
      # console.log(e.payload.timeStamp);
    when 'error'
      RMSrv.dialogAlert e.msg
    else
      RMSrv.dialogAlert "Unknown Event"

onNotificationAPN = (event)->
  if navigator?.vibrate
    navigator.vibrate 1000
  if event.sound
    (new Media(event.sound)).play()
  if event.badge
    if pushNotification = window.plugins.pushNotification
      pushNotification.setApplicationIconBadgeNumber (->), RMSrv.dialogAlert, event.badge
  if event.url and event.alert # when has url, jump to it
    if event.foreground
      return confirmJump event.alert, event.url
    else
      return jumpURL event.url
  if event.alert
    RMSrv.dialogAlert event.alert

handleOpenURL = (url)->
  jump = ->
    if (p = url.indexOf('?')) >= 0
      params = url.substr(p)
    else
      params = ''
      return # it may be a return from WeChat or other app; this case we don't change url
    window.location = RMSrv.origin() + "/scheme" + params
  setTimeout jump,10

window.onerror = (msg, url, l)->
  m = msg + "\n" + url + "\n" + l
  if /i\.realmaster/.test window.location.href
    alert m
  try
    xmlhttp = new XMLHttpRequest()
    xmlhttp.onreadystatechange = ->
      if xmlhttp.readyState is 4 and xmlhttp.status is 200 then console?.log xmlhttp.responseText
    xmlhttp.open "POST", '/cError'
    xmlhttp.setRequestHeader "Content-type","application/x-www-form-urlencoded"
    xmlhttp.send "m="+encodeURIComponent(m)
  catch e
    console?.log e

RMSrv.init()
