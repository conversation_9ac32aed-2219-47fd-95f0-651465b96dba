$ ->
  controller =
    id: "housecard-page-show-body"
    init: ->
      @_doms = {}
      @_datas = {}
      @initDoms()
      @getData()
      @bindEvent()

    initDoms: ->
      $('#wechatQrModal').hide()
      page = @$ = $("#" + @id)
      $.extend @_doms,

        # page : page,
        ctctAvt: page.find("[data-role=ctct-avt]")
        ctctNm: page.find("[data-role=ctct-nm]")
        ctctSgn: page.find("[data-role=ctct-sng]")
        ctctMbl: page.find("[data-role=ctct-mbl]")
        ctctItr: page.find("[data-role=ctct-itr]")
        ctctMblCall: page.find("[data-role=ctct-mbl-call]")
        ctctEml: page.find("[data-role=ctct-eml]")
        ctctWeb: page.find("[data-role=ctct-web]")
        ctctWx: page.find("[data-role=ctct-wx]")
        ctctWxgrp: page.find("[data-role=ctct-wxgrp]")
        ctctCpny: page.find("[data-role=ctct-cpny]")
        ctctCpnypstn: page.find("[data-role=ctct-cpny_pstn]")
        ctctWurl: page.find("[data-role=ctct-wurl]")
        ctctFburl: page.find("[data-role=ctct-fburl]")
        ctctTwturl: page.find("[data-role=ctct-twturl]")
        ctctVip: page.find("[data-role=ctct-vip]")
        ctctRealtor: page.find("[data-role=ctct-realtor]")


    writeMeta: (meta) ->
      $("#share-title").html meta.title
      $("#share-desc").html meta.desc
      if meta.img
        $("#share-image").html meta.img
      if not meta.music
        $('#mp3-btn').remove()

    bindEvent: ->
      that = this
      #imgSrcList = []
      # $.each $('img'), (i, img) ->
      #   if img.src and (img.clientWidth > 280 and img.clientHeight > 200)
      #     imgSrcList.push img.src
      #     $(img).click (e) ->
      #       wx?.previewImage
      #         current: @src,
      #         urls: imgSrcList
      #       return
      #   return
      # console.log($('#items-pages').html());

      # origScrollTop = 0
      # isAndroid = navigator.userAgent.match(/Android/i)
      # $("#signUpForm .wxid, #signUpForm .m").focus ()->
      #   if isAndroid
      #     $('#signUpForm').css('margin-bottom', '200px')
      #     origScrollTop = $('#housecard-page-contents').scrollTop()
      #     # $('#housecard-page-contents').animate({ scrollTop: parseInt(origScrollTop+200) + 'px' })
      #     $('#housecard-page-contents').scrollTop(parseInt(origScrollTop+200))
      #
      # $("#signUpForm .wxid, #signUpForm .m").focusout ()->
      #   if isAndroid
      #     $('#signUpForm').css('margin-bottom', '0px')
      #     $('#housecard-page-contents').scrollTop origScrollTop

      $("#signUpForm .btn").bind "click", ->
        meta = that._datas.pageData.card.meta
        error = (nmE, emlE, mblE)->
          $("#signUpForm .nm").addClass('error') if nmE
          $("#signUpForm .eml").addClass('error') if emlE
          $("#signUpForm .mbl").addClass('error') if mblE
          return nmE or emlE or mblE
        nm   = $("#signUpForm .nm").val()
        eml  = $("#signUpForm .eml").val()
        wxid = $("#signUpForm .wxid").val()
        mbl  = $("#signUpForm .mbl").val()
        m    = $("#signUpForm .m").val()
        $("#signUpForm .nm").removeClass('error')
        $("#signUpForm .eml").removeClass('error')
        $("#signUpForm .mbl").removeClass('error')
        return if error (not nm),(not eml),(meta.mblreq and not mbl)
        unless isValidEmail(eml)
          return error(0,1)
        # url = '/' + document.URL.split('/').splice(3).join('/')
        # wpid = document.URL.split('?')[0].split('/').splice(-1).pop()
        url = document.URL
        wpid = amGloble.ml_num
        data = {formid:'system', tp:'wecard',id:wpid, nm:nm, eml:eml, wxid:wxid, mbl:mbl, m:m, img:meta.img, url:url, wpid:wpid}
        $.post("/1.5/form/forminput", data)
        .done((ret)->
          if ret.ok
            flashMessage 'sendSuccess'
            $("#signUpForm").hide()
            $("#signUpSuccess").show()
          else
            flashMessage "server-error"
            console.log ret.err
        )
        false

      $("#mp3-btn").bind "click", ->
        has = $(this).hasClass("anm-rotate")
        myAuto = document.getElementById("musicPlayer")
        if has
          $(this).removeClass "anm-rotate"
          myAuto.pause()
        else
          $(this).addClass "anm-rotate"
          myAuto.play()

      $(".qrcd-show").on "click", ->
        $("#wechatQrModal").show()
        false

      $(".qrcd-hide").on "click", ->
        $("#wechatQrModal").hide()
        false

      $("#content-cover").bind "click", ->
        $(this).hide()
        false

      $(".fa-qrcode.blink_text").bind "click", ->
        $("#qrcode-cover").show()
        $("<div class='backdrop'></div>").appendTo('body')
        false

      $(".icon-footer-left1").bind "click", ->
        $("#content-cover").toggle()

      # $("#quarstr-btn").bind "click", ->
      #   url = window.location.href
      #   that.writeQrCode url
      #   $("#qrcode-cover").toggle()
      #   false

      $("#qrcode-cover").on "click", ".btn-close", ->
        $("#qrcode-cover").hide()
        $(".backdrop").remove()
        false


    initData: ->

      # nothing to do
      @writeHtml ->
        # alert('write done');
        a = new pageSwitch("items-pages",
          duration: 600
          start: 0
          direction: 1
          loop: false
          ease: "linear"
          transition: "slideY"
          mousewheel: true
          arrowkey: true
        )
        a.on "after", (n) ->
          item = $("#items-pages").children("li").length - 1
          (if item is n then ($(".down-btns").hide()
          $("#getAppBar").show()
          ) else ($(".up-btns").show()
          $(".down-btns").show()
          ))
          (if n is 0 then $(".up-btns").hide() else undefined)

    isFlyer: (type, shSty) ->
      if type in ['xmas1', 'xmas2', 'spring_fest', 'flyer']
        return true
      if (shSty is 'vt') or (amGloble.shSty is 'vt')
        return true
      false

    getContentFromCt: (ct)->
      # console.log ct
      unless ct.m or ct.m is ""
        return "Error: no text"
      if ct._for is 'user'
        return ct.m
      ret = '
      <div style="" class="editPrev">
         <div style="position: absolute;  z-index: 10;width: 100%;height: 100%;  min-height: 360px; background-size: 100% auto;
         background-position: center ' + (ct.bgPos or 'center') + ';
         background-repeat: no-repeat;
         background-image: url(\'' + (ct.bg or '') + '\');
         background-color: black;
         ">
            <div class="animated long ' + (ct.ani or 'fadeInUp') + ' " style="' + (ct.tbg or '')  + 'width: 100%;  color: white; padding: 10px; box-sizing: border-box;
              position: absolute; ' + (ct.pos or 'top:10%;') + '">
              ' + ct.m + '
              </div>
         </div>
      </div>
      '
      return ret

    getData: ->
      that = this
      #getPropCardInfo
      $.ajax(
        url:amGloble.getPropCardInfo
        data: {}
        type:'post' #default: 'GET' # dataType: 'json' #default: Intelligent Guess
      ).done((ret)->
        if ret.success
          data = that._datas.pageData = ret
          # console.log data
          that.initData()
        else
          $("#items-pages").html ""
          alert ret.err
        return
      ).fail(->
        flashMessage "server-error"
        return
      )

    writeHtml: (cb) ->
      data = @_datas.pageData
      if data and not data.e
        #console.log "write HTML"

        # alert('assignment write!');
        card = data.card
        user = data.user
        # console.log(user)
        rcmds = data.rcmds
        meta = card.meta

        li = ""
        arrHtml = [ "<ul class=\"items-ul\">" ]
        htmls = []
        @_datas.card = card
        @writeMeta meta  if meta

        #seq: is array of contents
        unless card.seq
          seq = []
          alert "empty content!"
        else
          seq = card.seq
          @_datas.seq = seq

        # alert('music');
        $("#musicPlayer").attr "src", card.music.url  if card.music
        $("a.adLinkHref").attr "href", $("a.adLinkHref").attr("href") + "?uid=" + data.id  if data.id and amGloble.withSign

        if user.fnm
          userName = if user.fnm then user.fnm else ((user.fn or "") + " " + (user.ln or ""))
        else
          userName = user.nm or ""

        if amGloble.withSign
          document.title = userName + ' ' + document.title
        else if meta.title
          document.title = meta.title

        if meta.shSty is "blog"
          if not meta.fb
            $("#signUpForm").hide()
          else if meta.fb and (amGloble.withSign or user.evtAdmin)
            $("#signUpForm").show()
            $("#signUpForm .tl").text(meta.fbtl) if meta.fbtl
            #set mblreq
            $("#signUpForm .mblTitle .ast").remove() unless meta.mblreq
            if !amGloble.withSign and navigator.userAgent.match(/Android/i) and navigator.userAgent.match(/MicroMessenger/i)
              $("#androidWechatPadding").show()

          headerStr = "<div style=\"margin-bottom: 24px;margin-top: 20px;line-height: 1.4;font-weight: 400;\">"
          headerStr += "<div style='font-size: 22px;'>" + meta.title + "</div>"
          time = (if meta.ts then meta.ts.split("T")[0] else "")
          headerStr += "<br>" + "<span style=\"margin-right:10px;color: #8c8c8c;\">" + time + "</span>"
          wecardURL = amGloble.host + "/1.5/wesite/" + user._id
          if document.URL.indexOf('wDl=1') > -1
            wecardURL = wecardURL + "?share=1"
          if amGloble.withSign
            headerStr += "<span style=\"margin-right:10px;\"><a href=\"" + wecardURL + "\">" + meta.editor + "</a></span>"

          viewCountStr = if amGloble.viewCountStr then ( amGloble.viewCountStr + ': ' ) else 'Views: '
          openMapStr   = if amGloble.openMapStr   then ( amGloble.openMapStr + ': ' )   else 'Open Map: '
          followMeStr  = if amGloble.followMeStr then amGloble.followMeStr else 'Follow Me'

          # headerStr += "<a href='#' class='qrcd-show blink_text'><span style='color:red; padding-right:10px'>" + followMeStr + "</span></a>" if user.qrcd and amGloble.withSign
          if parseInt(meta.custvc) != -1
            headerStr += viewCountStr + "<span style=\"margin-right:10px; color:#607fa6\">" + parseInt((parseInt(meta.custvc or 0) or 0) + (meta.vc or 0)) + "</span>"
          url = amGloble.emurl + encodeURIComponent(meta.addr)
          headerStr += openMapStr + "<span style=\"color: #007aff;\" class=\"fa fa-location-arrow\" onclick=\"RMSrv.showInBrowser('" + url + "')\" > " + "</span>"  if meta.addr
          headerStr += "</div>"
          $headerObj = $(headerStr)
          htmls.push $headerObj

          # console.log (this._doms);
          @_doms.ctctAvt.attr "src", user.avt  if user.avt
          @_doms.ctctNm.html userName
          @_doms.ctctSgn.html user.sgn or ""
          @_doms.ctctItr.html user.itr or ""
          @_doms.ctctMbl.html user.mbl or ""
          @_doms.ctctMblCall.attr "href", "tel:" + user.mbl
          @_doms.ctctEml.html user.eml or ""
          @_doms.ctctEml.attr "href", "mailto:" + user.eml
          userWeb = if user.web then (if user.web.indexOf('http') > -1 then user.web else ('http://' + user.web)) else ''
          @_doms.ctctWeb.html userWeb
          @_doms.ctctWeb.attr 'href', userWeb
          @_doms.ctctWx.html user.wx or ""
          @_doms.ctctWxgrp.html user.wxgrp or ""
          unless user.wxgrp
            $('.ctct-wxgrp').hide()
          @_doms.ctctCpny.html user.cpny or ""
          @_doms.ctctCpnypstn.html user.cpny_pstn or ""

          if user.qrcd
            $("#qr_code_img img").attr('src', user.qrcd)
          else
            $("#qr_code_img").hide()
          if user.grpqrcd
            $("#grp_qr_code_img img").attr('src', user.grpqrcd)
          else
            $("#grp_qr_code_img").hide()

          if user.wurl
            userWurl = if user.wurl then (if user.wurl.indexOf('http') > -1 then user.wurl else ('http://' + user.wurl)) else ''
            @_doms.ctctWurl.attr "href", userWurl
          else
            @_doms.ctctWurl.css "display", 'none'

          if user.fburl
            userFburl = if user.fburl then (if user.fburl.indexOf('http') > -1 then user.fburl else ('http://' + user.fburl)) else ''
            @_doms.ctctFburl.attr "href", userFburl
          else
            @_doms.ctctFburl.css "display", 'none'

          if user.twturl
            userTwturl = if user.twturl then (if user.twturl.indexOf('http') > -1 then user.twturl else ('http://' + user.twturl)) else ''
            @_doms.ctctTwturl.attr "href", userTwturl
          else
            @_doms.ctctTwturl.css "display", 'none'

          if user.vip
            $('#userBriefInfo').addClass('vip')
            unless $('#withDl')[0]
              $('#getAppBar').remove()
          else
            @_doms.ctctVip.css "display", 'none'
            $('#getAppBar').display = 'block';
          @_doms.ctctRealtor.css "display", 'none'  unless user.realtor
          unless user.wurl or user.twturl or user.fburl
            $(".other-info").hide()

          $("#userDetail").show() if amGloble.withSign
          $("#getAppBar").hide()
          # $('#housecard-page-contents').height() <
          offset = 270
          checkScoll = (set)->
            $("#getAppBar").hide()
            scrollHeight = $('#housecard-page-contents').height() + $('#housecard-page-contents').scrollTop()
            pageHeight = $('#items-pages').height() + $('#userDetail').height()
            # console.log scrollHeight + ':' + pageHeight + ':' + offset + ':' + set
            if (scrollHeight >= pageHeight - 270 ) and  set is 1
              offset = 0
            if scrollHeight >= pageHeight - offset
              $("#getAppBar").show()

            return
          $('#housecard-page-contents').on('scroll',  checkScoll)
          setTimeout( ()->
            checkScoll(1)
          ,500)

        else
          $("#getAppBar").hide()
          $("#signUpForm").hide()
        i = 0
        len = seq.length

        while i < len
          ct = seq[i]
          # get imgs from ct and add watermark
          if meta.wmk
            try
              # imgs = $("img", ct.m)
              $cl = $(ct.m)
              $cl.find("img").each (f)->
                img = $(this)
                data_id = '' + i + f
                nimg = new Image
                src = img.attr('src')
                nimg.crossOrigin = "Anonymous"
                img.attr('data-id', data_id)
                nimg.onload = ()->
                  imgWidth = nimg.width
                  imgHeight = nimg.height
                  cvs = $("<canvas>")
                  ctx = cvs[0].getContext('2d')
                  cvs[0].width = imgWidth or $(window).width()
                  cvs[0].height = imgHeight or 100
                  ctx.font = "15px Arial"
                  ctx.textAlign = "right"
                  ctx.fillStyle = "white"
                  ctx.drawImage nimg, 0, 0
                  if meta.editor
                    ctx.fillText "@" + meta.editor, cvs[0].width-20, cvs[0].height-20
                  dataURL = cvs[0].toDataURL()
                  img = $("[data-id='" + data_id + "']")
                  img.attr 'src', dataURL
                  return
                nimg.src = src
                if img.complete or img.complete == undefined
                  nimg.src = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///ywAAAAAAQABAAACAUwAOw=='
                  nimg.src = src
                # img.replaceWith("<h1>test</h1>")
                # img.replaceWith(cvs)
              ct.m = $cl[0].outerHTML
            catch error
              console.log 'Err:' + error.toString()
          if @isFlyer(card.tp, card.meta?.shSty) and ct._for isnt 'user'
            console.log 'FLyer'
            ct.m = @getContentFromCt(ct)
            $htmlObj = $('<li>').append( $('<div class="full-screen">').append($.trim(ct.m)) )
          else
            $htmlObj = $('<li>').append( $('<div>').append($.trim(ct.m)) )

          # add ctrl buttons ;popEditContents(event);
          # ct.m = $html;
          has = $htmlObj.find("div.dis").length != 0

          # console.log($htmlObj[0].outerHTML);
          htmls.push $htmlObj[0].outerHTML  unless has

          # console.log(ct.m);
          ct.m = $htmlObj[0].outerHTML
          i++

        # this._doms.mapUl.remove();
        # write recommend
        if meta.shSty is "blog" and rcmds?.l?.length > 0
          rcmdList = "<div class='rcmd-tip'>" + rcmds.t + "</div>"
          rcmdList += "<div id='recommendList'>"
          dlLink = ""
          sgnLink = ""
          if document.URL.indexOf('wDl=1') > -1
            dlLink = "&wDl=1"
          if document.URL.indexOf('sgn=1') > -1
            sgnLink = "&sgn=1"
          for i in rcmds.l
            if i._id is card._id
              continue
            genHref = (card)->
              if card.meta.prop
                #http://www.realmaster.com/1.5/rmprop/detail/RM1-00160?lang=zh-cn&wDl=1&aid=5554e29d0db70c0b0ce67ffe
                return "/1.5/prop/detail?id=#{card.meta.id}#{dlLink}#{sgnLink}&lang=zh-cn&aid=#{user._id}&share=1"
              else
                return '/1.5/wecard/prop/'+user.id+'/'+i._id+'?utm_res=1'+dlLink+sgnLink
            rcmdList += "<a class='rcmd' href='" + genHref(i) + "'><div class='image'><img style='width:90px; \
            height:70px;' src='" + (i.meta.img or '/img/logo.png') + "'></div><div class='rcmd-title'><span>" + i.meta.title +
            "</span><br><span>" + i.meta.desc + "</span>" +
            "</div></a>"
          rcmdList += '</div>'
          htmls.push rcmdList
        # console.log(htmls);
        $("#items-pages").html htmls
        $(".blink_text.qrcd-show").on "click", (e) ->
          $("#wechatQrModal").show()
          false
        # console.log(card.meta.shSty);
        if card.meta.shSty
          if card.meta.shSty is "vt"
            $("#housecard-page-contents").removeClass "blog-style"
            $("#housecard-page-contents").addClass "vt-style"
            cb()
          else
            $("#drag-btn-up").css "display", "none"
            $("#drag-btn-down").css "display", "none"
            $("#housecard-page-contents").removeClass "vt-style"
            $("#housecard-page-contents").addClass "blog-style"
            $("#housecard-page-show-body").addClass "blog-style"
        $("#items-pages").css "display", "block"

        # $('#realtorInfoFrame .wrapper .info').css('display', 'none')
        pinfo = document.querySelectorAll('#realtorInfoFrame .wrapper .info')[0]
        qrcd = document.querySelectorAll('#realtorInfoFrame .wrapper .qrcd')[0]
        base = 0
        if pinfo
          pinfoS = new Hammer(pinfo)
          pinfoS.get('swipe').set({ direction: Hammer.DIRECTION_HORIZONTAL });
          pinfoS.on 'swipe', (ev) ->
            if ev.direction is Hammer.DIRECTION_LEFT
              qrcdGoLeft()
            else
              qrcdGoRight()
            return
        if qrcd
          qrcdS = new Hammer(qrcd)
          qrcdS.get('swipe').set({ direction: Hammer.DIRECTION_HORIZONTAL })
          qrcdS.get('pan').set({   direction: Hammer.DIRECTION_HORIZONTAL })
          qrcdS.on 'swipe', ->
            # if ev.direction is Hammer.DIRECTION_RIGHT
            #   qrcdGoRight()
            # else
            #   qrcdGoLeft()
            return

          qrcdS.on 'panstart', ->
            # set base
            translate3d = qrcd.style.webkitTransform.match(/translate3d\(([^,]*)/)
            # console.log parseInt( Math.max(window.outerWidth,window.innerWidth) - 10)
            ret = if translate3d then translate3d[1] else parseInt( Math.max(window.outerWidth,window.innerWidth)/2 + 100 - 10)
            base = parseInt ret, 10
            if base > 10
              base = base * -1
            # console.log "!!!!:" + base

          qrcdS.on 'panmove', (ev) ->
            if ev.direction is Hammer.DIRECTION_RIGHT
              # console.log ev
              moveQrcd(ev)
            else
              # console.log ev
              moveQrcd(ev)
            return
        # qrcd.addEventListener('click',)
        moveQrcd = (ev)->
          qrcd.style.webkitTransition = "none"
          #base = parseInt( Math.max(window.outerWidth,window.innerWidth) - 10)
          move = (base + ev.deltaX)
          # console.log move
          style = "translate3d(" + move + "px,0,0)"
          qrcd.style.webkitTransform = style
          # console.log move
          if move > -160
            #show pinfo -30 is 1
            move = if move > -30 then -30 else move
            opacity = 1 - Math.abs(-30 - move)/130
            opacity = Math.round(opacity * 10) / 10
            # console.log move + ":" + opacity
            # console.log opacity
            pinfo.style.opacity = opacity

        qrcdGoLeft = ()->
          # qrcd.style.transform = "translateX(-100px)"
          style = "translate3d(-" + parseInt( Math.max(window.outerWidth,window.innerWidth)/2 + 100 - 10) + "px,0,0)"
          qrcd.style.webkitTransition = "all 0.3s"
          qrcd.style.webkitTransform = style
          # qrcd.style.transform = style
          pinfo.style.opacity = 0
          # console.log 'left'

        qrcdGoRight = ()->
          qrcd.style.webkitTransition = "all 0.3s"
          qrcd.style.webkitTransform = "translate3d(-" + 0 + "px,0,0)"
          # qrcd.style.transform = "translate3d(-" + 0 + "px, 0, 0)"
          pinfo.style.opacity = 1
          # console.log 'right'

    #转码
    utf16to8: (str) ->
      out = undefined
      i = undefined
      len = undefined
      c = undefined
      out = ""
      len = str.length
      i = 0
      while i < len
        c = str.charCodeAt(i)
        if (c >= 0x0001) and (c <= 0x007F)
          out += str.charAt(i)
        else if c > 0x07FF
          out += String.fromCharCode(0xE0 | ((c >> 12) & 0x0F))
          out += String.fromCharCode(0x80 | ((c >> 6) & 0x3F))
          out += String.fromCharCode(0x80 | ((c >> 0) & 0x3F))
        else
          out += String.fromCharCode(0xC0 | ((c >> 6) & 0x1F))
          out += String.fromCharCode(0x80 | ((c >> 0) & 0x3F))
        i++
      out

    writeQrCode: (str) ->
      codeStr = @utf16to8(str)
      $("#qr_code_img").html ""
      $("#qr_code_img").qrcode
        text: codeStr
        width: 200
        height: 200


  controller.init()

$(document).ready ->
  delete window.document.referrer
  # console.log($('#items-pages').html());
  # if /iPhone|iPad|iPod/i.test(navigator.userAgent.toLowerCase())
  #   unless navigator.userAgent.indexOf("Safari") is -1
  #     window.isIOS = true
      # alert(window.innerHeight);
      # $("#housecard-page-contents").css "height", window.innerHeight + "px"

  imgs = document.getElementsByTagName('img')
  videos = document.getElementsByClassName('video_iframe')

  changeData = (data, tag) ->
    i = 0
    while i < data.length
      if !data[i].hasAttribute('data-src') and data[i].hasAttribute('src') and data[i].getAttribute('src').lastIndexOf('http://', 0) == 0 and data[i].getAttribute('src').indexOf('chuansong.me/') == -1
        data[i].setAttribute 'data-src', data[i].getAttribute('src')
      if data[i].hasAttribute('data-src')
        datasrc = data[i].getAttribute('data-src')
        datasrc = datasrc.replace('https://v.qq.com/', 'http://v.qq.com/')
        data[i].setAttribute 'src', if tag == 'image' and datasrc.indexOf('http://read.html5.qq.com/image') == -1 then 'http://read.html5.qq.com/image?src=forum&q=5&r=0&imgflag=7&imageUrl=' + datasrc else datasrc
        data[i].removeAttribute 'data-src'
      i++
    return

  changeData imgs, 'image'
  changeData videos, 'video'
  # console.log 'here'


# $('body.housecard-page-show-body .housecard-page.vt-style .items-ul li > div:first-child').css('-webkit-transform','translateY(-60%);');
