regression = require 'regression'
helpers = require('../lib/helpers')
geolib = require 'geolib'
debugHelper = require '../lib/debug'
debug = debugHelper.getDebugger()

addToSet = (ary,id)->
  if ary.indexOf(id) < 0
    ary.push id
  ary

addToSetArray = (ary,toAdd)->
  for a in toAdd
    ary = addToSet ary,a
  ary

MAP_PROP_FIELDS =
  lp:1
  lpr:1
  status:1
  daddr:1
  prov:1
  city:1
  unt:1
  st:1
  cmty_en:1
  addr:1
  bdrms:1
  tbdrms:1
  bthrms:1
  topup_pts:1
  topTs:1
  gr:1
  tgr:1
  sid:1
  ptype:1
  ptype2:1
  lat:1
  lng:1
  tax:1
  taxyr:1
  ddfID:1
  phosrc:1
  picUrl:1
  phoUrls:1
  pho:1
  pclass:1
  lst:1
  saletp:1
  ts:1
  mt:1
  trbtp:1
  zip:1
  sqft:1
  sqft1:1
  sqft2:1
  sqftEstm:1
  depth:1
  front_ft:1
  lotsz_code:1
  sp:1
  sldt:1
  br_plus:1
  loc:1
  thumbUrl:1
  tnLH:1
  phoP:1
  src:1

MAX_RANGE =
  'Apartment':
    distance:400
    month:8
  'Townhouse':
    distance:800
    month:8
  'Semi-Detached' :
    distance:2000
    month:8
  'Detached':
    distance:3000
    month:8
  'Bungalow':
    distance:3000
    month:8

NUM_PROPS_TO_COMPARE = 500

# WEIGHT_AVG_LEN = 3 # 计算平均差异值，用于过滤筛选
# TOTAL_AVG_LEN = 50 # 计算过滤后的房源的平均差异值

# weight for 1 room, ^2 diff
roomWeight = (weight, fld)->
  (prop, input)->
    fldnm = 'w'+ helpers.strCapitalize(fld)
    if (not prop[fld]) and (not input[fld])
      prop['weight'][fldnm] = 0
      return prop['weight'][fldnm]

    diff = Math.abs((prop[fld] or 0) - (input[fld] or 0))
    diff *= diff

    prop['weight'][fldnm] = +((weight * diff).toFixed(2))
    prop['weight'][fldnm]

# 根据选择的月份按分档计算时间权重基数
calcTSWeightNum = (weight,month,type)->
  if (not month) or (MAX_RANGE[type].month is month)
    return weight
  calcWeigh = weight * (MAX_RANGE[type].month / month)
  return calcWeigh

# weight for 6 month, liner
tsWeight = (weight)->
  (prop,p) ->
    diff = ((new Date()).getTime() - (new Date(prop.mt)).getTime()) / (24* 3600000 * 30 * 6)
    calcWeigh = calcTSWeightNum(weight,p.month,p.type)
    prop.weight.wTs = +((diff * calcWeigh).toFixed(2))
    return prop.weight.wTs

# weight for true/false
sldWeight = (weight)->
  (prop) ->
    if prop.sp
      prop.weight.wSld =  0
    else
      prop.weight.wSld = weight
    prop.weight.wSld

# weight for max range
rangeWeight = (weight, type)->
  (prop, input) ->
    rangePercent = prop.dist/MAX_RANGE[input.type].distance
    prop.weight.wRange =  +((rangePercent * weight).toFixed(2))
    prop.weight.wRange

# weight for diff percentage of input
sqftWeight = (weight)->
  (prop, input) ->
    if not input.sqft
      prop.weight.wSqft = 0
      return prop.weight.wSqft
    propSqft = getPropSqft prop
    unless propSqft
      prop.weight.wSqft = weight
      return weight
    diff = Math.abs(propSqft - input.sqft)
    prop.weight.wSqft = +((weight * diff/input.sqft).toFixed(2))
    prop.weight.wSqft

getSize = (prop) ->
  return 0 unless prop.front_ft and prop.depth and (prop.lotsz_code is 'Metres' or prop.lotsz_code is 'Feet')
  prop.sqsize = prop.front_ft * prop.front_ft
  prop.sqsize = prop.sqsize * 10.76391 if prop.lotsz_code is 'Metres'
  prop.sqsize

# weight diff percentage of input
sizeWeight = (weight)->
  (prop, input) ->
    unless input.sqsize
      prop.weight.wSize = 0
      return prop.weight.wSize
    input.sqsize = getSize input
    prop.sqsize = getSize prop
    unless input.sqsize and prop.sqsize
      prop.weight.wSize = weight
      return weight
    diff = Math.abs(prop.sqsize - input.sqsize)
    prop.weight.wSize = +((weight * diff/input.sqsize).toFixed(2))
    prop.weight.wSize

# weight when 20 percentage of input differencen
priceWeight = (weight)->
  (prop, input) ->
    if (not input.lp) or (input.rental)
      prop.weight.wPrice = 0
      return prop.weight.wPrice
    unless prop.lp
      prop.weight.wPrice = weight
      return prop.weight.wPrice
    diff = Math.abs(prop.lp - input.lp)
    diff20p = diff * 5 / input.lp
    prop.weight.wPrice =  +((diff20p * diff20p * weight).toFixed(2))
    prop.weight.wPrice

# 将ptype2含有Link作为权重计算
ptype2LinkWeight = (weight)->
  (prop, input)->
    if input.isLink
      if prop.ptype2.includes 'Link'
        prop.weight.wLink = 0
      else
        prop.weight.wLink = weight
    prop.weight.wLink or 0


CMD_WEIGHT =
  Detached:
    gr: roomWeight 30, 'gr'
    sld: sldWeight 20
    bdrms: roomWeight 20, 'bdrms'
    ts: tsWeight 17
    range: rangeWeight 15
    sqft: sqftWeight 15
    size: sizeWeight 15
    bthrms: roomWeight 10, 'bthrms'
    price: priceWeight 15
    link: ptype2LinkWeight 10
  'Semi-Detached':
    gr: roomWeight 30, 'gr'
    sld: sldWeight 20
    bdrms: roomWeight 20, 'bdrms'
    ts: tsWeight 17
    range: rangeWeight 15
    sqft: sqftWeight 15
    size: sizeWeight 10
    bthrms: roomWeight 10, 'bthrms'
    price: priceWeight 20
    link: ptype2LinkWeight 10
  Townhouse:
    gr: roomWeight 30, 'gr'
    bdrms: roomWeight 23, 'bdrms'
    sld: sldWeight 20
    sqft: sqftWeight 20
    bthrms: roomWeight 15, 'bthrms'
    ts: tsWeight 15
    range: rangeWeight 15
    size: sizeWeight 10
    price: priceWeight 40
    link: ptype2LinkWeight 10
  Apartment:
    range: rangeWeight 40
    bdrms: roomWeight 30, 'bdrms'
    br_plus: roomWeight 30, 'br_plus'
    bthrms: roomWeight 25, 'bthrms'
    sqft: sqftWeight 25
    sld: sldWeight 20
    ts: tsWeight 15
    gr: roomWeight 15, 'gr'
    price: priceWeight 40
    link: ptype2LinkWeight 10
  Bungalow:
    gr: roomWeight 30, 'gr'
    sld: sldWeight 20
    bdrms: roomWeight 20, 'bdrms'
    ts: tsWeight 17
    range: rangeWeight 15
    sqft: sqftWeight 15
    size: sizeWeight 15
    bthrms: roomWeight 10, 'bthrms'
    price: priceWeight 15
    link: ptype2LinkWeight 10

getMaxWeight = (type) ->
  maxWeight = 0
  prop =
    weight : {}
    bthrms : 3
    gr: 3
    bdrms : 3
    mt: new Date().getTime() - 3600 * 1000 * 24 * 30 * 12
    sld: 'Sale'
    br_plus: 3
    dist: MAX_RANGE[type].distance
    lp:80

  p =
    bthrms : 5
    gr: 5
    bdrms : 5
    br_plus: 5
    lp:100
    type: type

  for k, fn of CMD_WEIGHT[type]
    maxWeight += fn prop, p
  maxWeight

getTotalDiff = (prop, p) ->
  prop.weight = {}
  prop.weight.wTotal = 0
  for k, fn of CMD_WEIGHT[p.type]
    prop.weight.wTotal += fn prop, p
  prop.weight.wTotal = +(prop.weight.wTotal.toFixed(2))

getPropSqft = (l) ->
  propSqft = 0
  if l.sqft and helpers.isNumber l.sqft
    propSqft = l.sqft
  else if l.rmSqft and (not isNaN(l.rmSqft))
    propSqft = parseInt(l.rmSqft)
  else if l.sqftEstm and helpers.isNumber l.sqftEstm
    propSqft = l.sqftEstm
  else if l.sqft1 and l.sqft2
    propSqft = Math.round((l.sqft1+l.sqft2)/2)
  else
    propSqft = l.sqft1 or l.sqft2 or 0
  return propSqft

backMonth = (month)->
  now = new Date()
  now.setMonth(now.getMonth() - month)
  now

getDays = (d)->
  oneDay = 24*3600*1000
  startDateTime = new Date()
  startDateTime = startDateTime.setFullYear(startDateTime.getFullYear() - 30)
  Math.round(Math.abs((d.getTime() - startDateTime)/oneDay))

summarizeStage = (result)->
  ret = {}
  return {ret} unless result
  # debug.debug 1,results
  used = []
  maxWeight = 0
  usedProps = []
  maxWeight = result.maxWeight
  used = addToSetArray used,result.used if Array.isArray result.used
  usedProps = addToSetArray usedProps,result.usedProps if Array.isArray result.usedProps
  ret.used = used.slice(0,10)
  ret.usedProps = usedProps.slice(result.offset,result.offset+result.limit)
  ret.last = result.last
  ret.range = result.range
  ret.maxWeight = maxWeight
  return {ret}

# 随着选择的月份增大，线性降低查找距离
calcDistanceByMonth = (month,type) ->
  if MAX_RANGE[type].month is month
    return MAX_RANGE[type].distance
  calcDist = MAX_RANGE[type].distance / (Math.sqrt(month / MAX_RANGE[type].month))
  return calcDist

buildSimilarQuery = ({params, rental, isSold})->
  query = {}
  esParams = {}
  options = {projection:MAP_PROP_FIELDS,sort:{mt:-1}, limit:NUM_PROPS_TO_COMPARE}
  month = params.month or MAX_RANGE[params.type].month # 默认是对应类型写的月份，如果前端传入了月份就按前端给值
  range = MAX_RANGE[params.type].distance
  if params.mlsid
    query._id= {$ne:params.mlsid}
    esParams.notPropId = params.mlsid
  if isSold
    # query.lst = if rental then 'Lsd' else 'Sld' #成交/租出
    if rental
      query.lst = 'Lsd'
    else
      query.lst = {$in:['Sld','Pnd','Cld']}
    query.status = 'U'
    esParams.soldOnly = true
    esParams.saleDesc = if rental then 'Leased' else 'Sold'
  else
    query.status = 'A' #出售/租赁
  query.saletp = if rental then 'Lease' else 'Sale'
  query.ptype = 'r'
  query.ptype2 = params.type

  esParams = Object.assign esParams,query
  delete esParams._id

  query.geoq = $gte:100
  query.ts = $gt: backMonth month
  query.loc =
    $near:
      $geometry:
        type: 'Point'
        coordinates:[params.lng,params.lat]
      $maxDistance:range

  esParams.ptype2 = [params.type]
  esParams.geoq = 100
  esParams.ts = backMonth month
  esParams.centerLat = params.lat
  esParams.centerLng = params.lng
  esParams.dist = range
  
  return {query,options,range,month,esParams}

###
@param {object} params - the conditions of selecting similar properties
@param {number} range - the max distance
@param {number} month - the earliest months
@param {array.object} props - array of eligible properties
###
addWeightAndFilter = ({params, range, month, props})->
  sqft = params.sqft
  # params.rental = rental
  maxWeight = getMaxWeight(params.type)
  used = []

  notFinalRound = (range < MAX_RANGE[params.type].distance)

  for prop in props
    if params.lat and params.lng and prop.lat and prop.lng
      prop.dist = geolib.getDistance(
        {latitude: params.lat, longitude: params.lng},
        {latitude: prop.lat, longitude: prop.lng}
      )
    getTotalDiff prop, params

  props.sort (a,b)-> a.weight.wTotal - b.weight.wTotal

  # 20240428_fix_similarWTs.md 新需求不判断是否相似，因此将以下代码先注释
  # props.length = TOTAL_AVG_LEN if props.length > TOTAL_AVG_LEN

  # len = if props.length > WEIGHT_AVG_LEN then WEIGHT_AVG_LEN else props.length
  # totalWeight = 0
  # for i in [0..len-1]
  #   totalWeight += +(props[i].weight.wTotal)
  # averageWeight = totalWeight/len

  # threshold = averageWeight*2
  # props = props.filter (a)-> a.weight.wTotal < threshold

  usedProps = props.slice(0)

  weightSum = 0
  for prop in props
    used.push prop._id
    # weightSum += +(prop.weight.wTotal)
  # weightAvg = weightSum / props.length

  # if (weightAvg * 3 > maxWeight) and notFinalRound
  #   return null

  result =
    used:used
    usedProps:usedProps
    last:month
    range:range
    limit: params.limit or 50
    offset: params.offset or params.page*params.limit or 0
  # return {result:{usedProps:usedProps, last:month, range :range}}
  return summarizeStage result

getMaxDistRange = () ->
  return Object.assign {}, MAX_RANGE

module.exports.distRange = getMaxDistRange()

module.exports.buildSimilarQuery = buildSimilarQuery
module.exports.addWeightAndFilter = addWeightAndFilter
module.exports.getPropSqft = getPropSqft