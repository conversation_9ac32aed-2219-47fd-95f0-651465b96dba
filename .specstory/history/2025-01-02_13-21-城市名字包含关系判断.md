<!-- Generated by SpecStory -->

# 城市名字包含关系判断 (2025-01-02 21:21:27)

_**User**_

使用 isSubstring 判断城市名字是否为包含关系

---

_**Assistant**_

您可以使用 `isSubstring` 函数来判断城市名字是否存在包含关系。以下是您需要的代码更新：

```coffee:appweb/src/libapp/propertiesTranslate.coffee
appweb/src/libapp/propertiesTranslate.coffee
# ... existing code ...
        # 使用 isSubstring 判断城市名字是否为包含关系
        if isSubstring(cityLower, otherCityLower)
          delete cityMap[cityKey]
# ... existing code ...
        delete cityMap[cityKey]
        console.log("🚀 ~ cityKey:", cityKey)
# ... existing code ...
  sortedCities = Array.from(uniqueCities).sort((a, b) -> a.length - b.length) # 按长度降序排序
```

### 更新说明：
1. 在删除 `cityMap[cityKey]` 之前，添加了使用 `isSubstring` 判断城市名字是否为包含关系的逻辑。
2. 其余代码保持不变。

---



---

