<!-- Generated by SpecStory -->

# Grade ID Validation and Replacement (2025-04-26 21:53:31)

_**User**_

判断传入id与school的年级是否相同，不相同替换id中的[ELEMENTARY|SECONDARY]为正确的[ELEMENTARY|SECONDARY]

---

_**Assistant**_

我来帮你修改代码，添加年级判断和ID替换的逻辑。首先我们需要分析学校类型，然后根据类型修改ID。

我将修改 `addFraserData` 函数，添加年级判断和ID替换的逻辑：


---



---

让我重新尝试修改文件：


---

```diff
    return unless id
    
+   # 判断学校类型
+   isElementary = (school.ele is 1) or (school.mid is 1)
+   isSecondary = school.hgh is 1
+   
+   # 检查ID中的学校类型标记
+   idType = if id.includes('ELEMENTARY') then 'ELEMENTARY' else if id.includes('SECONDARY') then 'SECONDARY' else null
+   
+   # 如果ID类型与学校实际类型不匹配，则替换ID中的类型标记
+   if idType and ((isElementary and idType isnt 'ELEMENTARY') or (isSecondary and idType isnt 'SECONDARY'))
+     newType = if isElementary then 'ELEMENTARY' else 'SECONDARY'
+     id = id.replace(/(ELEMENTARY|SECONDARY)/, newType)
+   
    if fraserSchool?
      fraser = fraserSchool.fraser?[0]
```

---

我已经成功修改了 `addFraserData` 函数，添加了以下功能：

1. 判断学校类型：
   - 通过检查 `school.ele` 和 `school.mid` 判断是否为小学
   - 通过检查 `school.hgh` 判断是否为中学

2. 检查ID中的学校类型标记：
   - 检查ID中是否包含 'ELEMENTARY' 或 'SECONDARY' 标记
   - 如果找到标记，记录对应的类型

3. 类型匹配和替换：
   - 如果ID中的类型标记与学校实际类型不匹配
   - 根据学校实际类型生成新的类型标记
   - 使用正则表达式替换ID中的类型标记

这样修改后，当传入的Fraser ID与学校实际年级类型不匹配时，会自动将ID中的类型标记替换为正确的类型。例如：
- 如果学校是小学（`ele=1`或`mid=1`），但ID中包含'SECONDARY'，会将其替换为'ELEMENTARY'
- 如果学校是中学（`hgh=1`），但ID中包含'ELEMENTARY'，会将其替换为'SECONDARY'

这个修改可以确保Fraser ID与学校实际年级类型保持一致，提高数据匹配的准确性。

---

_**User**_

增加年级匹配，如果信息相同但年级不一致，删除与外部数据年级不一致的Id，保留数据

---

_**Assistant**_

我明白您的需求，我们需要在 `addFraserData` 函数中增加年级匹配的逻辑，如果学校信息相同但年级不一致，则删除不匹配的ID但保留其他数据。我来修改代码：



---



---



---



---

