<!-- Generated by SpecStory -->

# 年级匹配逻辑修改 (2025-04-26 22:39:58)

_**User**_

增加年级匹配，如果信息相同但年级不一致，删除与外部数据年级不一致的Id，保留数据

---

_**Assistant**_

我来帮你修改代码，增加年级匹配的逻辑。我们需要在比较学校信息时，同时检查年级信息是否匹配。

首先，我会修改 `handleDuplicateSchools` 函数，添加年级匹配的逻辑：


---

```diff
        score = 0
        if school[type+'Data']?
+         # 检查基本信息匹配
          for key in ['nm','addr','city','prov']
            if school[type+'Data'][key]?.toLowerCase().replace(/\s+/g, '') is school[key]?.toLowerCase().replace(/\s+/g, '')
              score += 1
+         
+         # 检查年级匹配
+         isSchoolElementary = (school.ele is 1) or (school.mid is 1)
+         isSchoolSecondary = school.hgh is 1
+         
+         isDataElementary = (school[type+'Data'].ele is 1) or (school[type+'Data'].mid is 1)
+         isDataSecondary = school[type+'Data'].hgh is 1
+         
+         # 如果年级类型匹配，加分
+         if (isSchoolElementary and isDataElementary) or (isSchoolSecondary and isDataSecondary)
+           score += 2
+         # 如果年级类型不匹配，减分
+         else if (isSchoolElementary and isDataSecondary) or (isSchoolSecondary and isDataElementary)
+           score -= 2
        
        # 有bns数据加分
```

---

我已经修改了代码，主要做了以下改动：

1. 在比较学校与外部数据时，增加了年级匹配的逻辑：
   - 判断学校是否为小学（`ele` 或 `mid` 为 1）
   - 判断学校是否为高中（`hgh` 为 1）
   - 判断外部数据是否为小学或高中

2. 根据年级匹配情况调整分数：
   - 如果年级类型匹配（都是小学或都是高中），加 2 分
   - 如果年级类型不匹配（一个是小学一个是高中），减 2 分

这样修改后，系统会：
1. 优先保留年级匹配的记录
2. 如果年级不匹配，会降低该记录的分数，使其不太可能被选为保留记录
3. 仍然保留原有的其他评分逻辑（如基本信息匹配、bns数据等）

这个改动确保了：
- 当学校信息相同但年级不一致时，会优先选择与外部数据年级匹配的记录
- 通过分数机制，让年级匹配成为重要的判断因素
- 保留了原有的其他匹配逻辑，确保整体匹配的准确性

需要注意的是，这个改动可能会导致一些原来被保留的记录因为年级不匹配而被排除，但这样可以确保数据的准确性和一致性。

---

_**User**_

在这部分增加年级判断

---

_**Assistant**_

我明白了，您想要在基础信息匹配的部分增加年级判断。我来修改代码：



---

```diff
            matchCount++
      
+     # 检查年级信息是否匹配
+     isFirstElementary = (firstSchool.ele is 1) or (firstSchool.mid is 1)
+     isFirstSecondary = firstSchool.hgh is 1
+     isSchoolElementary = (school.ele is 1) or (school.mid is 1)
+     isSchoolSecondary = school.hgh is 1
+     
+     # 如果年级类型不匹配，则认为不是同一所学校
+     if (isFirstElementary and isSchoolSecondary) or (isFirstSecondary and isSchoolElementary)
+       continue
+     
      # 如果有三个或以上字段匹配，则认为基础信息相同
      if matchCount >= 3 and matchCount/totalFields >= 0.6
```

---

我已经修改了代码，主要做了以下改动：

1. 在基础信息匹配部分增加了年级判断逻辑：
   - 判断第一个学校是否为小学（`ele` 或 `mid` 为 1）
   - 判断第一个学校是否为高中（`hgh` 为 1）
   - 判断当前比较的学校是否为小学或高中

2. 增加了年级匹配的判断条件：
   - 如果一个是小学，另一个是高中，则认为不是同一所学校，直接跳过当前比较
   - 只有当年级类型匹配（都是小学或都是高中）时，才继续比较其他信息

这样修改后：
1. 系统会先判断年级类型是否匹配
2. 只有年级类型匹配的学校才会被继续比较其他信息
3. 保持了原有的基础信息匹配逻辑（3个以上字段匹配且匹配率超过60%）

这个改动确保了：
- 不同年级类型的学校（小学和高中）不会被判定为同一所学校
- 只有相同年级类型的学校才会被合并或删除
- 保留了原有的其他匹配逻辑，确保整体匹配的准确性

这样的修改更符合实际需求，因为不同年级的学校即使地址和名称相同，也应该被视为不同的学校。

---



---

