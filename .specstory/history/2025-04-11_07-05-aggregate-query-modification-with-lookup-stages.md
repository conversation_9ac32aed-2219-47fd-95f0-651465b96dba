<!-- Generated by SpecStory -->

# Aggregate Query Modification with Lookup Stages (2025-04-11 15:05:48)

_**User**_

修改成使用aggregate查询，pipeline中添加buildRankLookupStages内容获取数据

---

_**Assistant**_

我来帮你修改代码，将查询改为使用aggregate pipeline的方式，并添加buildRankLookupStages的内容。

首先让我查看一下buildRankLookupStages的具体实现：


---

<details>
            <summary>Searched codebase "function buildRankLookupStages" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `appweb/src/model/school.coffee` | L718-815 |
| `appweb/src/model/school.coffee` | L815-897 |
| `appweb/src/model/school.coffee` | L897-960 |
| `appweb/src/model/school.coffee` | L960-1022 |
| `appweb/src/model/school.coffee` | L1270-1320 |
| `appweb/src/model/school.coffee` | L657-718 |
| `appweb/src/model/school.coffee` | L2042-2085 |
| `appweb/src/model/school.coffee` | L1415-1457 |
| `appweb/src/batch/export/schoolEqaoSortAndVcStats_2024.coffee` | L213-276 |
| `.specstory/history/2025-04-08_06-24-完善sch-rm-merged生成流程文档.md` | L378-456 |
| `appweb/src/batch/school_rank/school_rank_from_xlsx.coffee` | L125-163 |
| `.specstory/history/2025-03-19_03-32-handling-single-quotes-in-string-concatenation.md` | L85-158 |
| `appweb/src/libapp/cmtyTagHelper.coffee` | L179-257 |
| `.specstory/history/2025-03-19_03-32-handling-single-quotes-in-string-concatenation.md` | L242-313 |
| `appweb/src/batch/export/schoolEqaoSortAndVcStats.coffee` | L42-117 |
| `appweb/src/batch/school_rank/update_top100_school_rank.coffee` | L210-266 |
| `appweb/src/mlsImport/trrebReso/trebResoDownload.coffee` | L950-1037 |
| `appweb/src/libapp/evaluationEngine.coffee` | L453-513 |
| `appweb/src/batch/school_rank/private_school_ivy_rank.coffee` | L159-218 |
| `appweb/src/batch/school_rank/update_adj_factors.coffee` | L329-357 |
| `appweb/extra/schoolImport.coffee` | L1-71 |
| `appweb/src/model/school.coffee` | L2284-2314 |
| `appweb/src/libapp/schoolHelper.coffee` | L689-752 |
| `appweb/src/batch/school_rank/update_adj_factors.coffee` | L263-329 |
| `appweb/src/libapp/evaluationEngine.coffee` | L513-554 |

</details>

---

根据搜索结果，我看到了buildRankLookupStages的实现。现在我来修改代码，将查询改为使用aggregate pipeline的方式：



---

```diff
      unless id
        throw new Error('id Required')
+     
+     # 构建基础pipeline
+     pipeline = [
+       # 匹配条件
+       { 
+         $match: {
+           _id: id,
+           IsActive: 1
+         }
+       }
+     ]
+ 
+     # 添加projection
      projection = helpers.deepCopyObject PRIVATE_SCHOOL_FIELDS_EXTEND
      if isAdmin
        projection.vc = 1
        projection.vcd = 1
+     pipeline.push({ $project: projection })
  
-       # 关联 sch_rm_rank 表获取排名数据
+     # 关联 sch_rm_rank 表获取排名数据
      pipeline = pipeline.concat(buildRankLookupStages())
  
-       # 执行聚合查询
-       list = await PrivateSchools.aggregate(pipeline)
-     sch = await PrivateSchools.aggregate {_id:id,IsActive:1},{projection}
+     # 执行聚合查询
+     schools = await PrivateSchools.aggregate(pipeline)
+     sch = schools[0]
      return null unless sch
+ 
      sch.private = 1
      translateSch sch,locale
```

---

我已经修改了代码，主要做了以下改动：

1. 构建了一个完整的aggregate pipeline，包含以下阶段：
   - `$match` 阶段：匹配指定的学校ID和活动状态
   - `$project` 阶段：根据PRIVATE_SCHOOL_FIELDS_EXTEND和isAdmin条件设置返回字段
   - 通过buildRankLookupStages()添加关联sch_rm_rank表的查询阶段

2. 使用单个aggregate查询替代了原来的两个查询：
   - 移除了单独的`PrivateSchools.aggregate {_id:id,IsActive:1},{projection}`查询
   - 直接从pipeline查询结果中获取第一个文档作为学校数据

3. 保持了原有的数据处理逻辑：
   - 设置private标志
   - 翻译学校信息
   - 添加标签
   - 格式化排名数据

这样修改后的代码更加高效，只需要一次数据库查询就能获取所有需要的数据。同时保持了原有的功能完整性。

---



---

