<!-- Generated by SpecStory -->

# 优先保留source=findschool的数据 (2025-04-26 00:51:15)

_**User**_

需要优先保留source=findschool的数据

---

_**Assistant**_

我明白您的需求。您希望在处理重复学校数据时，优先保留 `source=findschool` 的记录。我将修改 `handleDuplicateSchools` 函数来实现这个逻辑。

让我先解释一下修改思路：
1. 在基础信息相同的情况下，优先保留 `source=findschool` 的记录
2. 如果有多条 `source=findschool` 的记录，则保留第一条
3. 如果没有 `source=findschool` 的记录，则保持原有逻辑

我将修改代码：


---

```diff
    # 2. 如果基础信息相同，删除所有重复记录
    if isBasicInfoSame
-     # 保留第一条记录
-     keepSchools = [firstSchool]
-     # 其他记录都需要删除，但排除已处理的学校
-     deleteSchools = schools[1..].filter((school) -> not handledSchools.includes(school._id))
+     # 优先保留source=findschool的记录
+     findSchoolRecords = schools.filter((school) -> school.source is 'findschool')
+     if findSchoolRecords.length > 0
+       # 保留第一条findschool记录
+       keepSchools = [findSchoolRecords[0]]
+       # 其他记录都需要删除，但排除已处理的学校
+       deleteSchools = schools.filter((school) -> 
+         school._id isnt findSchoolRecords[0]._id and not handledSchools.includes(school._id)
+       )
+     else
+       # 如果没有findschool记录，保持原有逻辑
+       keepSchools = [firstSchool]
+       deleteSchools = schools[1..].filter((school) -> not handledSchools.includes(school._id))
+     
      pushSchoolId()
      return {keepSchools, updateSchools: [], deleteSchools}
```

---

我已经修改了代码，主要变更如下：

1. 在基础信息相同的情况下，首先筛选出所有 `source=findschool` 的记录
2. 如果存在 `findschool` 记录：
   - 保留第一条 `findschool` 记录
   - 删除其他所有记录（除了已处理的记录）
3. 如果没有 `findschool` 记录：
   - 保持原有逻辑，保留第一条记录
   - 删除其他记录（除了已处理的记录）

这样修改后，在处理重复学校数据时，系统会优先保留 `source=findschool` 的记录，确保这些数据不会被错误删除。同时，如果有多条 `findschool` 记录，会保留第一条，其他记录会被删除。

这个修改不会影响其他部分的逻辑，只是在处理重复数据时增加了优先级判断。您觉得这样的修改是否符合您的需求？

---

_**User**_

优先保留source=findschool的记录

---

_**Assistant**_

我注意到您再次强调了需要优先保留 `source=findschool` 的记录。我理解您可能希望这个优先级不仅仅应用在基础信息相同的情况下，而是应该在整个处理过程中都优先考虑 `findschool` 的记录。

让我修改代码，将 `findschool` 的优先级应用到整个处理流程中：



---

```diff
      .map (school) ->
        score = 0
+       # 优先考虑findschool记录
+       if school.source is 'findschool'
+         score += 10  # 给findschool记录一个较高的基础分
+       
        if school[type+'Data']?
          for key in ['nm','addr','city','prov']
    schoolsWithScore.sort((a, b) -> b.score - a.score)
    
-   # 如果最高分相同，则保留第一条记录
+   # 如果最高分相同，优先保留findschool记录
    if schoolsWithScore?.length > 1 and schoolsWithScore[0].score is schoolsWithScore[1].score
+     findSchoolRecord = schoolsWithScore.find((item) -> item.school.source is 'findschool')
+     if findSchoolRecord?
+       keepSchools = [findSchoolRecord.school]
+       updateSchools = schoolsWithScore.filter((item) -> item.school._id isnt findSchoolRecord.school._id).map((item) -> item.school)
+     else
        keepSchools = [firstSchool]
-     if schoolsWithScore[1..]?.length > 0
        updateSchools = schoolsWithScore[1..].map((item) -> item.school)
      pushSchoolId()
```

---

我已经对代码进行了更全面的修改，主要变更如下：

1. 在评分系统中增加了对 `findschool` 记录的优先考虑：
   - 给 `source=findschool` 的记录额外增加10分的基础分
   - 这样即使其他条件相同，`findschool` 记录也会因为更高的基础分而优先被保留

2. 在分数相同的情况下，增加了额外的判断逻辑：
   - 首先查找是否存在 `findschool` 记录
   - 如果存在，则优先保留 `findschool` 记录
   - 如果不存在，则保持原有逻辑，保留第一条记录

这样修改后，`findschool` 记录会在整个处理流程中都得到优先考虑：
1. 在基础信息相同的情况下（之前的修改）
2. 在评分系统中（新增的修改）
3. 在分数相同的情况下（新增的修改）

这样可以确保 `source=findschool` 的记录在整个处理过程中都得到优先保留。您觉得这样的修改是否更符合您的需求？

---



---

