<!-- Generated by SpecStory -->

# 学校数据处理与排序 (2025-03-27 23:12:25)

_**User**_

将处理好的allSchools数据保存到WaitUpdateCol中，查找WaitUpdateCol表中数据，按照nm排序从a-z排序返回，进程结束时删除WaitUpdateCol表

---

_**Assistant**_

我将帮您修改代码，在处理完`allSchools`数据后将其保存到`WaitUpdateCol`中，并按名称排序查询，最后删除该表。以下是需要修改的代码：

```coffeescript:appweb/src/batch/school/generateSchRmMerged.coffee
# ... existing code ...

processSchools = ->
  try
    counter = 0
    startTs = new Date()

    if dryRun
      console.log "运行在测试模式(dry run),不会修改数据库"

    # 获取学校计数器和更新时间
    {counters, updated_at} = await getSchoolCounter()

    # 初始化处理
    query = {}
    if isInit
      await initTables()
    else
      query._mt = {$gt: updated_at}

    # 从两个数据源获取数据
    [manualSchools, findSchools] = await Promise.all([
      SchManualCol.find(query).toArray(),
      SchFindschoolCol.find(query).toArray()
    ])

    # 合并数据源
    allSchools = mergeSchoolData(manualSchools, findSchools)
    
    # 清空并保存数据到WaitUpdateCol
    if !dryRun
      await WaitUpdateCol.deleteMany({})
      await WaitUpdateCol.insertMany(allSchools)
    
    # 按nm字段排序查询数据
    sortedSchools = await WaitUpdateCol.find({}).sort({ nm: 1 }).toArray()

    SchoolCol.find {}, (err, cursor)->
      return EXIT(err) if err
      
      stream = cursor.stream()
      # 使用streaming处理数据
      obj =
        verbose: 1
        stream: sortedSchools  # 使用排序后的数据
        high: 10
        end: (err) ->
          if err
            debug.error "Error in end: #{err}"
            return EXIT(err)
          
          # 批处理完成后更新计数器
          if !dryRun
            await updateSchoolCounter(counters)
            # 删除临时表
            await WaitUpdateCol.deleteMany({})
          
          endTs = new Date()
          processTs = (endTs.getTime() - startTs.getTime())/(1000)
          console.log "统计结果:"
          console.log "----------------------------------------"
          console.log "处理开始时间: #{startTs}"
          console.log "处理结束时间: #{endTs}"
          console.log "总处理时间: #{processTs}秒"
          console.log "处理学校总数: #{counter}所"
          console.log "----------------------------------------"
          return EXIT(0)
        process: (school, callback) ->
          try
            counter++
            # 处理学校数据
            await processSchool(school, callback)
          catch err
            debug.error "处理学校数据失败: #{err}", school
            callback(err)

      helpers.streaming obj
  catch err
    debug.error "处理学校数据主流程出错: #{err}"
    EXIT(1)

# ... existing code ...
```

主要修改说明：
1. 在处理完`allSchools`数据后，先清空`WaitUpdateCol`表，然后将数据插入
2. 使用`find().sort({ nm: 1 })`按学校名称从A到Z排序查询数据
3. 将排序后的数据`sortedSchools`用于后续的流处理
4. 在进程结束时（end回调中）删除`WaitUpdateCol`表中的所有数据

这些修改确保了：
- 数据按照学校名称排序处理
- 临时表在使用完后被清理
- 在`dryRun`模式下不会对数据库进行实际修改

---

_**User**_

匹配之前可以使用school._id查找SchManualMergedCol数据，看原来的数据中有没有eqaoId或者fraserId

---

_**Assistant**_

我会帮您修改`matchExternalData`函数，在进行其他匹配之前，先检查`SchManualMergedCol`中是否存在匹配的记录。以下是修改后的代码：

```coffeescript:appweb/src/batch/school/generateSchRmMerged.coffee
###
匹配外部数据(EQAO和Fraser)
@param {Object} school - 要处理的学校对象
@returns {Object} 返回添加了eqaoId和fraserId的学校对象
###
matchExternalData = (school) ->
  try
    return school if !school?.nm or !school?.prov or !school?.city

    # 首先查找SchManualMergedCol中是否存在匹配记录
    manualMerged = await SchManualMergedCol.findOne({ _id: school._id })
    if manualMerged?
      # 如果找到匹配记录，直接使用已有的eqaoId和fraserId
      if manualMerged.eqaoId
        school.eqaoId = manualMerged.eqaoId
      if manualMerged.fraserId
        school.fraserId = manualMerged.fraserId
      # 如果两个ID都已存在，直接返回
      if school.eqaoId and school.fraserId
        return school

    # 如果没有找到匹配记录或ID不完整，继续进行其他匹配
    # 准备查询条件
    baseQuery = 
      prov: school.prov
      city: school.city

    # ... existing code ...
```

主要修改说明：
1. 在进行其他匹配逻辑之前，先使用`school._id`查询`SchManualMergedCol`表
2. 如果找到匹配记录，则从中获取`eqaoId`和`fraserId`
3. 如果两个ID都已存在，则直接返回，不进行后续匹配
4. 如果没有找到匹配记录或者ID不完整（只有其中一个ID），则继续执行原有的匹配逻辑

这样可以优先使用手动匹配的数据，提高匹配的准确性和效率。如果手动匹配数据不存在或不完整，再进行自动匹配。

---

_**User**_

修改保存方式，已批量操作的形式，数据多的情况下累积100条需要更新的数据时在更新

---

_**Assistant**_

我会帮您修改代码，使用批量操作的方式来处理数据更新。以下是修改建议：

```coffeescript:appweb/src/batch/school/generateSchRmMerged.coffee
# ... existing code ...

###*
 * 批量更新/插入学校数据的缓存
###
bulkOps = {
  updateOps: []
  insertOps: []
}

###*
 * 执行批量操作
###
executeBulkOps = ->
  try
    if bulkOps.updateOps.length > 0
      if !dryRun
        await SchMergedCol.bulkWrite(bulkOps.updateOps)
      debug.info "批量更新#{bulkOps.updateOps.length}条记录"
      bulkOps.updateOps = []

    if bulkOps.insertOps.length > 0
      if !dryRun
        await SchMergedCol.insertMany(bulkOps.insertOps)
      debug.info "批量插入#{bulkOps.insertOps.length}条记录"
      bulkOps.insertOps = []
  catch err
    debug.error "执行批量操作失败: #{err}"
    throw err

###*
 * 更新或插入学校数据
 * @param {Object} school - 学校数据对象
###
updateOrInsertSchool = (school) ->
  try
    # 通过sourceId查找是否存在相同学校
    existingSchool = await SchMergedCol.findOne({
      sourceId: school._id
    })

    if existingSchool
      # 添加到更新操作队列
      if dryRun
        debug.debug "更新现有记录: #{existingSchool._id}", school
      else
        bulkOps.updateOps.push({
          updateOne: {
            filter: { _id: existingSchool._id }
            update: { $set: school }
          }
        })
    else
      # 生成新ID并添加到插入操作队列
      newId = await generateNewSchoolId(school.prov, school, lastSchool)
      school._id = newId
      if dryRun
        debug.debug "插入新记录: ", school
      else
        bulkOps.insertOps.push(school)

    # 当累积100条操作时执行批量操作
    if (bulkOps.updateOps.length + bulkOps.insertOps.length) >= 100
      await executeBulkOps()

  catch err
    debug.error "更新/插入学校数据失败: #{err}"
    throw err

###
主处理流程
###
processSchools = ->
  try
    # ... existing code ...

    obj =
      verbose: 1
      stream: stream
      high: 10
      end: (err) ->
        if err
          debug.error "Error in end: #{err}"
          return EXIT(err)
        
        try
          # 处理剩余的批量操作
          await executeBulkOps()
          
          # 批处理完成后更新计数器
          if not dryRun
            await updateSchoolCounter()
            # 删除临时表
            await WaitUpdateCol.deleteMany({})
          
          endTs = new Date()
          processTs = (endTs.getTime() - startTs.getTime())/(1000)
          console.log "统计结果:"
          console.log "----------------------------------------"
          console.log "处理开始时间: #{startTs}"
          console.log "处理结束时间: #{endTs}"
          console.log "总处理时间: #{processTs}秒"
          console.log "处理学校总数: #{counter}所"
          console.log "----------------------------------------"
          return EXIT(0)
        catch endErr
          debug.error "结束处理时发生错误: #{endErr}"
          return EXIT(endErr)

      # ... existing code ...

# ... existing code ...
```

主要修改说明：

1. 添加了`bulkOps`对象来存储待处理的批量操作：
   - `updateOps`: 存储更新操作
   - `insertOps`: 存储插入操作

2. 新增`executeBulkOps`函数来执行批量操作：
   - 使用`bulkWrite`执行批量更新
   - 使用`insertMany`执行批量插入
   - 执行完成后清空操作队列

3. 修改`updateOrInsertSchool`函数：
   - 不再直接执行数据库操作
   - 将操作添加到相应的队列中
   - 当累积操作数达到100时执行批量操作

4. 修改主流程的`end`回调：
   - 在结束前执行剩余的批量操作
   - 添加错误处理

这些修改可以：
- 减少数据库操作次数，提高性能
- 批量处理数据，提高效率
- 保持原有的功能不变
- 在`dryRun`模式下仍然可以正常工作

---

_**User**_

什么情况下hasTypeDifference为true

---

_**Assistant**_

让我解释`hasTypeDifference`的判断逻辑。当两所学校的类型（小学/中学/高中）有差异时，`hasTypeDifference`为`true`。具体来说：

```coffeescript
hasTypeDifference = (school1.ele != school2.ele && (school1.ele == true || school2.ele == true)) || 
                    (school1.mid != school2.mid && (school1.mid == true || school2.mid == true)) || 
                    (school1.hgh != school2.hgh && (school1.hgh == true || school2.hgh == true))
```

以小学(ele)为例，当满足以下两个条件时为true：
1. `school1.ele != school2.ele`: 两所学校的ele值不相等
2. `(school1.ele == true || school2.ele == true)`: 至少有一所学校是小学

举例说明：

```coffeescript
# 案例1：一所是小学，另一所不是小学
school1 = { ele: true, mid: false, hgh: false }
school2 = { ele: false, mid: true, hgh: false }
# hasTypeDifference = true

# 案例2：两所都是小学
school1 = { ele: true, mid: false, hgh: false }
school2 = { ele: true, mid: false, hgh: false }
# hasTypeDifference = false

# 案例3：两所都不是小学
school1 = { ele: false, mid: true, hgh: false }
school2 = { ele: false, mid: true, hgh: false }
# hasTypeDifference = false

# 案例4：一所是小学+中学，另一所只是中学
school1 = { ele: true, mid: true, hgh: false }
school2 = { ele: false, mid: true, hgh: false }
# hasTypeDifference = true
```

总结，`hasTypeDifference`为`true`的情况：
1. 一所是小学，另一所不是小学
2. 一所是中学，另一所不是中学
3. 一所是高中，另一所不是高中

这个判断用于确定两所名称相同的学校是否为同一所学校的不同部分（比如同一所学校的小学部和中学部）。

---

_**User**_

ele,mid,hgh如果存在，则值为1

---

_**Assistant**_

明白了，让我修改判断逻辑，当这些字段存在时值为1（而不是true）。以下是修改后的代码：

```coffeescript:appweb/src/batch/school/generateSchRmMerged.coffee
###*
 * 判断两个学校记录是否为同一学校的不同年级
 * @param {Object} school1 - 第一个学校记录
 * @param {Object} school2 - 第二个学校记录
 * @returns {Boolean} 如果是同一学校不同年级返回true，否则返回false
###
isSameSchoolDiffGrade = (school1, school2) ->
  # 检查基本信息是否相同
  if school1.addr != school2.addr || 
     school1.city != school2.city || 
     school1.prov != school2.prov
    return false
    
  # 检查学校类型(小学/中学/高中)是否有不同
  hasTypeDifference = (school1.ele != school2.ele && (school1.ele == 1 || school2.ele == 1)) || 
                      (school1.mid != school2.mid && (school1.mid == 1 || school2.mid == 1)) || 
                      (school1.hgh != school2.hgh && (school1.hgh == 1 || school2.hgh == 1))
  
  if hasTypeDifference
    return true
    
  # ... existing code ...
```

现在让我举例说明什么情况下`hasTypeDifference`为`true`：

```coffeescript
# 案例1：一所是小学，另一所不是小学
school1 = { ele: 1, mid: 0, hgh: 0 }
school2 = { ele: 0, mid: 1, hgh: 0 }
# hasTypeDifference = true

# 案例2：两所都是小学
school1 = { ele: 1, mid: 0, hgh: 0 }
school2 = { ele: 1, mid: 0, hgh: 0 }
# hasTypeDifference = false

# 案例3：两所都不是小学
school1 = { ele: 0, mid: 1, hgh: 0 }
school2 = { ele: 0, mid: 1, hgh: 0 }
# hasTypeDifference = false

# 案例4：一所是小学+中学，另一所只是中学
school1 = { ele: 1, mid: 1, hgh: 0 }
school2 = { ele: 0, mid: 1, hgh: 0 }
# hasTypeDifference = true
```

`hasTypeDifference`为`true`的情况：
1. 当两所学校的某个学段（小学/中学/高中）不同，且至少有一所学校包含该学段（值为1）
2. 比如：
   - 一所有小学部（ele=1），另一所没有小学部（ele=0）
   - 一所有中学部（mid=1），另一所没有中学部（mid=0）
   - 一所有高中部（hgh=1），另一所没有高中部（hgh=0）

这样的判断逻辑可以正确识别同一所学校的不同部分，比如同一所学校的小学部和中学部应该被视为不同的记录。

---

_**User**_

有ele或者mid都认为是小学，一般分为小学和高中两种情况

---

_**Assistant**_

明白了，我来修改判断逻辑，将学校类型简化为小学（包含ele或mid）和高中（hgh）两种情况。以下是修改后的代码：

```coffeescript:appweb/src/batch/school/generateSchRmMerged.coffee
###*
 * 判断两个学校记录是否为同一学校的不同年级
 * @param {Object} school1 - 第一个学校记录
 * @param {Object} school2 - 第二个学校记录
 * @returns {Boolean} 如果是同一学校不同年级返回true，否则返回false
###
isSameSchoolDiffGrade = (school1, school2) ->
  # 检查基本信息是否相同
  if school1.addr != school2.addr || 
     school1.city != school2.city || 
     school1.prov != school2.prov
    return false
    
  # 判断是否为小学（包含ele或mid）
  isElementary1 = school1.ele == 1 || school1.mid == 1
  isElementary2 = school2.ele == 1 || school2.mid == 1
  
  # 判断是否为高中
  isHighSchool1 = school1.hgh == 1
  isHighSchool2 = school2.hgh == 1
  
  # 如果一所是小学，另一所是高中，则认为是不同年级
  hasTypeDifference = (isElementary1 && isHighSchool2) || (isElementary2 && isHighSchool1)
  
  if hasTypeDifference
    return true
    
  # ... existing code ...
```

让我举例说明什么情况下`hasTypeDifference`为`true`：

```coffeescript
# 案例1：一所是小学（ele=1），另一所是高中
school1 = { ele: 1, mid: 0, hgh: 0 }
school2 = { ele: 0, mid: 0, hgh: 1 }
# hasTypeDifference = true

# 案例2：一所是中学（mid=1），另一所是高中
school1 = { ele: 0, mid: 1, hgh: 0 }
school2 = { ele: 0, mid: 0, hgh: 1 }
# hasTypeDifference = true

# 案例3：两所都是小学（一所ele=1，另一所mid=1）
school1 = { ele: 1, mid: 0, hgh: 0 }
school2 = { ele: 0, mid: 1, hgh: 0 }
# hasTypeDifference = false

# 案例4：两所都是高中
school1 = { ele: 0, mid: 0, hgh: 1 }
school2 = { ele: 0, mid: 0, hgh: 1 }
# hasTypeDifference = false
```

现在`hasTypeDifference`为`true`的唯一情况是：
- 当一所学校是小学（ele=1或mid=1），另一所是高中（hgh=1）时

这样的判断逻辑更符合实际情况，将学校简化为小学和高中两种类型，如果同一所学校有小学部和高中部，它们会被视为不同的记录。

---

_**User**_

如果eqaoSchool有rmG3Ranking，rmG6Ranking，rmG9Ranking数据，更新到学校数据中，有fraserSchool数据，获取firank，fitotal，firate字段添加

---

_**Assistant**_

我来帮您修改代码，在匹配到EQAO和Fraser数据时，同时获取相关的排名和评分数据：

```coffeescript:appweb/src/batch/school/generateSchRmMerged.coffee
    for eqaoSchool in eqaoSchools
      {schoolMatched, gradeMatched, nmAddrMatched} = matchSchoolInfo(school, eqaoSchool)
      
      if gradeMatched and (nmAddrMatched or schoolMatched)
        school.eqaoId = eqaoSchool._id
        # 添加EQAO排名数据
        if eqaoSchool.rmG3Ranking?
          school.rmG3Ranking = eqaoSchool.rmG3Ranking
        if eqaoSchool.rmG6Ranking?
          school.rmG6Ranking = eqaoSchool.rmG6Ranking
        if eqaoSchool.rmG9Ranking?
          school.rmG9Ranking = eqaoSchool.rmG9Ranking
        
        # 如果找到EQAO数据，尝试查找对应的Fraser数据
        # 从EQAO ID中提取数字部分
        mident = eqaoSchool._id.replace(/\D/g, '')
        
        # 构建Fraser查询条件
        fraserQuery = {
          ...baseQuery
          _id: new RegExp(mident)
        }
        
        # 查找Fraser数据
        fraserSchool = await SchFraserCol.findOne(fraserQuery)
        if fraserSchool?._id
          school.fraserId = fraserSchool._id
          # 添加Fraser评分数据
          if fraserSchool.firank?
            school.firank = fraserSchool.firank
          if fraserSchool.fitotal?
            school.fitotal = fraserSchool.fitotal
          if fraserSchool.firate?
            school.firate = fraserSchool.firate
      break

    # 如果没有找到EQAO数据，直接查找Fraser数据
    if !school.eqaoId
      fraserSchools = await SchFraserCol.findToArray(query)
      
      for fraserSchool in fraserSchools
        {schoolMatched, gradeMatched, nmAddrMatched} = matchSchoolInfo(school, fraserSchool)
        
        if gradeMatched and (nmAddrMatched or schoolMatched)
          school.fraserId = fraserSchool._id
          # 添加Fraser评分数据
          if fraserSchool.firank?
            school.firank = fraserSchool.firank
          if fraserSchool.fitotal?
            school.fitotal = fraserSchool.fitotal
          if fraserSchool.firate?
            school.firate = fraserSchool.firate
          
          # 如果找到Fraser数据，尝试查找对应的EQAO数据
          # 从Fraser ID中提取数字部分
          mident = fraserSchool._id.replace(/\D/g, '')
          
          # 构建EQAO查询条件
          eqaoQuery = {
            ...baseQuery
            _id: new RegExp(mident)
          }
          
          # 查找EQAO数据
          eqaoSchool = await SchEqaoCol.findOne(eqaoQuery)
          if eqaoSchool?._id
            school.eqaoId = eqaoSchool._id
            # 添加EQAO排名数据
            if eqaoSchool.rmG3Ranking?
              school.rmG3Ranking = eqaoSchool.rmG3Ranking
            if eqaoSchool.rmG6Ranking?
              school.rmG6Ranking = eqaoSchool.rmG6Ranking
            if eqaoSchool.rmG9Ranking?
              school.rmG9Ranking = eqaoSchool.rmG9Ranking
        break
```

主要修改说明：
1. 在匹配到EQAO数据时，添加以下字段：
   - rmG3Ranking：3年级排名
   - rmG6Ranking：6年级排名
   - rmG9Ranking：9年级排名

2. 在匹配到Fraser数据时，添加以下字段：
   - firank：Fraser排名
   - fitotal：Fraser总分
   - firate：Fraser评分

3. 在两种匹配路径中都添加了相应的数据复制逻辑：
   - EQAO → Fraser路径
   - Fraser → EQAO路径

这样可以确保无论通过哪种路径匹配到数据，都能获取到完整的排名和评分信息。

---

_**User**_

对上面修改的代码优化

---

_**Assistant**_

我来帮您优化代码，主要从以下几个方面进行改进：

1. 优化数据匹配逻辑
2. 添加数据清理和标准化
3. 优化批量操作
4. 添加错误处理和日志

```coffeescript:appweb/src/batch/school/generateSchRmMerged.coffee
###*
 * 清理和标准化学校数据
 * @param {Object} school - 学校数据对象
 * @returns {Object} 处理后的学校数据
###
normalizeSchoolData = (school) ->
  try
    # 清理学校名称中的空格
    if school.nm?
      school.nm = school.nm.trim()
    
    # 标准化地址
    if school.addr?
      school.addr = unifyAddress(school.addr)
    
    # 清理电话号码
    if school.tel?
      school.tel = filterOtherThanTel(school.tel)
    
    # 确保学段字段为数字
    ['ele', 'mid', 'hgh'].forEach (field) ->
      if school[field]?
        school[field] = parseInt(school[field]) || 0
    
    return school
  catch err
    debug.error "标准化学校数据失败: #{err}", school
    throw err

###*
 * 批量更新/插入学校数据的缓存
###
bulkOps = {
  updateOps: []
  insertOps: []
  batchSize: 100
}

###*
 * 执行批量操作
###
executeBulkOps = ->
  try
    if bulkOps.updateOps.length > 0
      if !dryRun
        await SchMergedCol.bulkWrite(bulkOps.updateOps)
      debug.info "批量更新#{bulkOps.updateOps.length}条记录"
      bulkOps.updateOps = []

    if bulkOps.insertOps.length > 0
      if !dryRun
        await SchMergedCol.insertMany(bulkOps.insertOps)
      debug.info "批量插入#{bulkOps.insertOps.length}条记录"
      bulkOps.insertOps = []
  catch err
    debug.error "执行批量操作失败: #{err}"
    throw err

###*
 * 更新或插入学校数据
 * @param {Object} school - 学校数据对象
###
updateOrInsertSchool = (school) ->
  try
    # 标准化数据
    school = normalizeSchoolData(school)
    
    # 通过sourceId查找是否存在相同学校
    existingSchool = await SchMergedCol.findOne({
      sourceId: school.sourceId
    })

    if existingSchool
      # 添加到更新操作队列
      if dryRun
        debug.debug "更新现有记录: #{existingSchool._id}", school
      else
        bulkOps.updateOps.push({
          updateOne: {
            filter: { _id: existingSchool._id }
            update: { $set: school }
          }
        })
    else
      # 生成新ID并添加到插入操作队列
      newId = await generateNewSchoolId(school)
      school._id = newId
      if dryRun
        debug.debug "插入新记录: ", school
      else
        bulkOps.insertOps.

---

_**User**_

如果eqaoSchool有rmG3Ranking，rmG6Ranking，rmG9Ranking数据，更新到学校数据中，有fraserSchool数据，获取firank，fitotal，firate字段添加

---

_**Assistant**_

我来帮您修改代码，在匹配到EQAO和Fraser数据时添加相关字段。我会修改`matchExternalData`函数中的相关部分：

```coffeescript:appweb/src/batch/school/generateSchRmMerged.coffee
    for eqaoSchool in eqaoSchools
      {schoolMatched, gradeMatched, nmAddrMatched} = matchSchoolInfo(school, eqaoSchool)
      
      if gradeMatched and (nmAddrMatched or schoolMatched)
        school.eqaoId = eqaoSchool._id
        # 添加EQAO排名数据
        if eqaoSchool.rmG3Ranking?
          school.rmG3Ranking = eqaoSchool.rmG3Ranking
        if eqaoSchool.rmG6Ranking?
          school.rmG6Ranking = eqaoSchool.rmG6Ranking
        if eqaoSchool.rmG9Ranking?
          school.rmG9Ranking = eqaoSchool.rmG9Ranking
        
        # 如果找到EQAO数据，尝试查找对应的Fraser数据
        # 从EQAO ID中提取数字部分
        mident = eqaoSchool._id.replace(/\D/g, '')
        
        # 构建Fraser查询条件
        fraserQuery = {
          ...baseQuery
          _id: new RegExp(mident)
        }
        
        # 查找Fraser数据
        fraserSchool = await SchFraserCol.findOne(fraserQuery)
        if fraserSchool?._id
          school.fraserId = fraserSchool._id
          # 添加Fraser评分数据
          if fraserSchool.firank?
            school.firank = fraserSchool.firank
          if fraserSchool.fitotal?
            school.fitotal = fraserSchool.fitotal
          if fraserSchool.firate?
            school.firate = fraserSchool.firate
      break

    # 如果没有找到EQAO数据，直接查找Fraser数据
    if !school.eqaoId
      fraserSchools = await SchFraserCol.findToArray(query)
      
      for fraserSchool in fraserSchools
        {schoolMatched, gradeMatched, nmAddrMatched} = matchSchoolInfo(school, fraserSchool)
        
        if gradeMatched and (nmAddrMatched or schoolMatched)
          school.fraserId = fraserSchool._id
          # 添加Fraser评分数据
          if fraserSchool.firank?
            school.firank = fraserSchool.firank
          if fraserSchool.fitotal?
            school.fitotal = fraserSchool.fitotal
          if fraserSchool.firate?
            school.firate = fraserSchool.firate
          
          # 如果找到Fraser数据，尝试查找对应的EQAO数据
          # 从Fraser ID中提取数字部分
          mident = fraserSchool._id.replace(/\D/g, '')
          
          # 构建EQAO查询条件
          eqaoQuery = {
            ...baseQuery
            _id: new RegExp(mident)
          }
          
          # 查找EQAO数据
          eqaoSchool = await SchEqaoCol.findOne(eqaoQuery)
          if eqaoSchool?._id
            school.eqaoId = eqaoSchool._id
            # 添加EQAO排名数据
            if eqaoSchool.rmG3Ranking?
              school.rmG3Ranking = eqaoSchool.rmG3Ranking
            if eqaoSchool.rmG6Ranking?
              school.rmG6Ranking = eqaoSchool.rmG6Ranking
            if eqaoSchool.rmG9Ranking?
              school.rmG9Ranking = eqaoSchool.rmG9Ranking
        break
```

主要修改说明：
1. 在匹配到EQAO数据时，添加以下字段：
   - rmG3Ranking：3年级排名
   - rmG6Ranking：6年级排名
   - rmG9Ranking：9年级排名

2. 在匹配到Fraser数据时，添加以下字段：
   - firank：Fraser排名
   - fitotal：Fraser总分
   - firate：Fraser评分

3. 在两种匹配路径中都添加了相应的数据复制逻辑：
   - EQAO → Fraser路径
   - Fraser → EQAO路径

这样可以确保无论通过哪种路径匹配到数据，都能获取到完整的排名和评分信息。使用`?`操作符确保只有在字段存在时才进行赋值，避免出现undefined的情况。

---

_**User**_

提取重复判断的代码

---

_**Assistant**_

我来帮您提取重复的判断代码，主要是在匹配EQAO和Fraser数据时的判断逻辑。我们可以创建一个新的辅助函数：

```coffeescript:appweb/src/batch/school/generateSchRmMerged.coffee
###*
 * 检查并添加EQAO数据到学校对象
 * @param {Object} school - 学校对象
 * @param {Object} eqaoSchool - EQAO学校数据
###
addEqaoData = (school, eqaoSchool) ->
  if eqaoSchool?._id
    school.eqaoId = eqaoSchool._id
    # 添加EQAO排名数据
    if eqaoSchool.rmG3Ranking?
      school.rmG3Ranking = eqaoSchool.rmG3Ranking
    if eqaoSchool.rmG6Ranking?
      school.rmG6Ranking = eqaoSchool.rmG6Ranking
    if eqaoSchool.rmG9Ranking?
      school.rmG9Ranking = eqaoSchool.rmG9Ranking

###*
 * 检查并添加Fraser数据到学校对象
 * @param {Object} school - 学校对象
 * @param {Object} fraserSchool - Fraser学校数据
###
addFraserData = (school, fraserSchool) ->
  if fraserSchool?._id
    school.fraserId = fraserSchool._id
    # 添加Fraser评分数据
    if fraserSchool.firank?
      school.firank = fraserSchool.firank
    if fraserSchool.fitotal?
      school.fitotal = fraserSchool.fitotal
    if fraserSchool.firate?
      school.firate = fraserSchool.firate

# 修改matchExternalData函数中的相关部分
matchExternalData = (school) ->
  return school if !school?.nm or !school?.prov or !school?.city
  try
    # 首先查找SchManualMergedCol中是否存在匹配记录
    manualMerged = await SchManualMergedCol.findOne({ _id: school._id })
    if manualMerged?
      # 如果找到匹配记录，直接使用已有的eqaoId和fraserId
      addEqaoData(school, manualMerged)
      addFraserData(school, manualMerged)
      # 如果两个ID都已存在，直接返回
      if school.eqaoId and school.fraserId
        return school

    # ... existing code ...

    # 先查找EQAO数据
    eqaoSchools = await SchEqaoCol.findToArray(query)
    
    for eqaoSchool in eqaoSchools
      {schoolMatched, gradeMatched, nmAddrMatched} = matchSchoolInfo(school, eqaoSchool)
      
      if gradeMatched and (nmAddrMatched or schoolMatched)
        addEqaoData(school, eqaoSchool)
        
        # 如果找到EQAO数据，尝试查找对应的Fraser数据
        mident = eqaoSchool._id.replace(/\D/g, '')
        fraserQuery = {
          ...baseQuery
          _id: new RegExp(mident)
        }
        
        # 查找Fraser数据
        fraserSchool = await SchFraserCol.findOne(fraserQuery)
        addFraserData(school, fraserSchool)
      break

    # 如果没有找到EQAO数据，直接查找Fraser数据
    if !school.eqaoId
      fraserSchools = await SchFraserCol.findToArray(query)
      
      for fraserSchool in fraserSchools
        {schoolMatched, gradeMatched, nmAddrMatched} = matchSchoolInfo(school, fraserSchool)
        
        if gradeMatched and (nmAddrMatched or schoolMatched)
          addFraserData(school, fraserSchool)
          
          # 如果找到Fraser数据，尝试查找对应的EQAO数据
          mident = fraserSchool._id.replace(/\D/g, '')
          eqaoQuery = {
            ...baseQuery
            _id: new RegExp(mident)
          }
          
          # 查找EQAO数据
          eqaoSchool = await SchEqaoCol.findOne(eqaoQuery)
          addEqaoData(school, eqaoSchool)
        break

    return school
  catch err
    debug.error "匹配外部数据失败: #{err}", school
    throw err
```

主要改进：
1. 创建了`addEqaoData`函数处理EQAO数据的添加
2. 创建了`addFraserData`函数处理Fraser数据的添加
3. 在代码中多处使用这些函数，减少重复代码
4. 保持了原有的功能不变，只是重构了代码结构

这样的改进可以：
- 提高代码的可维护性
- 减少代码重复
- 使逻辑更清晰
- 如果需要修改数据添加的逻辑，只需要修改一处代码

---



---

