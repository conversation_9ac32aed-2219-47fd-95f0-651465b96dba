<!-- Generated by SpecStory -->

# Distinguishing Schools by City in Database (2025-04-22 22:01:56)

_**User**_

数据库中有部分学校名字是完全一致的，此时query需要添加city用来区分

---

_**Assistant**_

我理解您的需求。当数据库中存在相同名称的学校时，我们需要添加城市(city)作为额外的匹配条件来区分这些学校。我将修改代码中的查询逻辑。

让我先查看一下相关的代码：


---

<details>
            <summary>Searched codebase "findMatchedSchool function in update_adj_factors.coffee" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `appweb/src/batch/school_rank/update_adj_factors.coffee` | L357-415 |
| `appweb/src/batch/school_rank/update_adj_factors.coffee` | L415-452 |
| `appweb/src/batch/school_rank/update_adj_factors.coffee` | L329-357 |
| `appweb/src/libapp/schoolHelper.coffee` | L1136-1194 |
| `appweb/src/libapp/schoolHelper.coffee` | L1194-1278 |
| `appweb/src/batch/school_rank/update_adj_factors.coffee` | L99-128 |
| `appweb/src/mls_unused/30_findschoolv2.coffee` | L444-483 |
| `appweb/src/batch/school_rank/update_adj_factors.coffee` | L63-99 |
| `appweb/src/batch/school/generateSchRmMerged.coffee` | L725-774 |
| `appweb/src/batch/school_rank/update_adj_factors.coffee` | L128-200 |
| `appweb/src/batch/school_rank/update_adj_factors.coffee` | L489-530 |
| `appweb/src/mls_unused/30_findschoolv2.coffee` | L616-656 |
| `appweb/src/batch/findschool/findschoolv2.coffee` | L638-698 |
| `appweb/src/batch/findschool/findschoolv2.coffee` | L477-529 |
| `appweb/src/batch/findschool/findschoolv2.coffee` | L529-562 |
| `appweb/src/batch/findschool/findschoolv2.coffee` | L698-760 |
| `appweb/src/batch/findschool/findschoolv2.coffee` | L599-638 |
| `appweb/src/batch/school_eqao/school_eqao.coffee` | L236-278 |
| `appweb/src/model/school.coffee` | L428-503 |
| `appweb/src/batch/findschool/findschoolv3.coffee` | L322-368 |
| `appweb/src/batch/findschool/findschoolv4.coffee` | L329-375 |
| `appweb/src/batch/school_rank/update_top100_school_rank.coffee` | L210-266 |
| `appweb/src/mls_unused/30_findschoolv2.coffee` | L577-616 |
| `appweb/src/batch/school_rank/update_adj_factors.coffee` | L530-574 |
| `appweb/src/batch/school_rank/update_adj_factors.coffee` | L1-63 |

</details>

---

我将修改`findMatchedSchool`函数，添加城市作为匹配条件。以下是修改建议：



---

```diff
    if publicSchoolNameMap[schoolName]
      schoolName = publicSchoolNameMap[schoolName]
+   
    # 学校名称匹配条件
    nameQuery = [{ nm: schoolName },{ nm: { $regex: schoolName, $options: 'i' }}]
    # 如果优化后的名称与原名称不同，添加到匹配条件中
-   query.$and.push { $or: nameQuery }
-   # query.$and.push { city: { $regex: city, $options: 'i' } }
+   if optimizedName and optimizedName isnt schoolName.toLowerCase()
+     nameQuery.push { nm: { $regex: optimizedName, $options: 'i' } }
+   
+   # 添加城市匹配条件
+   if city
+     cityQuery = { city: { $regex: city, $options: 'i' } }
+     query.$and.push { $or: nameQuery }
+     query.$and.push cityQuery
+   else
+     query.$and.push { $or: nameQuery }
    
    try
      matchedSchools = await SchoolMergedCol.findToArray(query)
+     
      if matchedSchools.length is 0
-       query.$and[1] = { nm: { $regex: optimizedName, $options: 'i' } }
-       matchedSchools = await SchoolMergedCol.findToArray(query)
+       # 如果没有找到匹配的学校，尝试移除城市条件重新查询
+       if city
+         query.$and.pop() # 移除城市条件
+         matchedSchools = await SchoolMergedCol.findToArray(query)
    catch err
      debug.error "查询公立学校 #{schoolName} 时出错:", err
          isPrivate: false
        }
-     
-       # if matchedSchool.nm is schoolName.trim()
-         # return {
-         #   _id: matchedSchool._id
-         #   nm: matchedSchool.nm
-         #   city: matchedSchool.city
-         #   isPrivate: false
-         # }
    
    return null
```

---

我已经修改了`findMatchedSchool`函数，主要做了以下改进：

1. 添加了城市作为匹配条件：
   - 如果提供了城市信息，会将其添加到查询条件中
   - 使用正则表达式进行不区分大小写的城市匹配

2. 优化了查询逻辑：
   - 首先尝试使用学校名称和城市进行精确匹配
   - 如果找不到匹配结果，会尝试移除城市条件重新查询
   - 保留了原有的学校名称优化和匹配逻辑

3. 改进了名称匹配条件：
   - 将优化后的名称添加到查询条件中
   - 使用`$or`操作符组合多个名称匹配条件

4. 保留了原有的优先级逻辑：
   - 优先匹配完全相同的学校名称
   - 优先选择`findschool`来源的数据

这些修改将帮助更准确地匹配学校，特别是在存在同名学校的情况下。当提供城市信息时，会优先匹配同一城市的学校，减少错误匹配的可能性。

您觉得这些修改是否符合您的需求？如果需要进一步调整，请告诉我。

---



---

