<!-- Generated by SpecStory -->

# Code Modification for Speed Statistics (2025-04-27 12:23:11)

_**User**_

参考@appweb/src/batch/prop/checkAndUpdateStatusV2.coffee，增加使用speed统计，将cb改成await

---

_**Assistant**_

我来帮你修改代码，增加speed统计并将回调改为async/await模式。我会先分析代码，然后进行修改。

首先，我会编辑文件来添加这些改进：


---

```diff
  SchoolEqaoCol = COLLECTION 'vow', 'sch_eqao'
  debug = DEBUG 3
- 
+ speed = INCLUDE 'lib.speed'
+ 
+ # 创建speed统计器
+ speedMeter = speed.createSpeedMeter {
+   intervalTriggerCount: 1000,
+   intervalCallback: (speedMeter) ->
+     debug.info speedMeter.toString()
+ }
  
  ucounter = 0
  startTs = new Date()
- SchoolEqaoCol.find {rmRank:{$exists:true}}, (err, cur) ->
-   if err
-     debug.error "updateSchoolBoard UserLog.find err: #{err}"
-     EXIT 1
-   stream = cur.stream()
-   obj =
-     verbose: 0
-     stream: stream
-     end: (err)->
-       if err
-         debug.error "UpdateSchoolBoard end #{err}"
-         EXIT 1
-       debug.info "Update school board start time: #{startTs}"
-       endTs = new Date()
-       debug.info "update school board end time: #{endTs}"
-       processTs = (endTs.getTime() - startTs.getTime())/(1000*60)
-       debug.info "Total process time #{processTs} mins"
-       debug.info "\n---- Update #{ucounter} school ----"
-       EXIT 0
-     process: (school,callback)->
-       set = {}
-       set.rmRank = school.rmRank
-       set.rmG3Ranking = school.rmG3Ranking if school.rmG3Ranking
-       set.rmG6Ranking = school.rmG6Ranking if school.rmG6Ranking
-       set.rmG9Ranking = school.rmG9Ranking if school.rmG9Ranking
-       set.eqaoId = school._id
-       set.nm = school.nm
-       await SchoolRankCol.updateOne {_id:school._id}, {$set:set}, {upsert:true}
-       ucounter++
-       return callback()
-   helpers.streaming obj
+ 
+ main = () ->
+   try
+     cur = await SchoolEqaoCol.find {rmRank:{$exists:true}}
+     stream = cur.stream()
+     obj =
+       verbose: 0
+       stream: stream
+       end: (err)->
+         if err
+           debug.error "UpdateSchoolBoard end #{err}"
+           EXIT 1
+         debug.info "Update school board start time: #{startTs}"
+         endTs = new Date()
+         debug.info "update school board end time: #{endTs}"
+         processTs = (endTs.getTime() - startTs.getTime())/(1000*60)
+         debug.info "Total process time #{processTs} mins"
+         debug.info "\n---- Update #{ucounter} school ----"
+         debug.info speedMeter.toString()
+         EXIT 0
+       process: (school,callback)->
+         try
+           set = {}
+           set.rmRank = school.rmRank
+           set.rmG3Ranking = school.rmG3Ranking if school.rmG3Ranking
+           set.rmG6Ranking = school.rmG6Ranking if school.rmG6Ranking
+           set.rmG9Ranking = school.rmG9Ranking if school.rmG9Ranking
+           set.eqaoId = school._id
+           set.nm = school.nm
+           await SchoolRankCol.updateOne {_id:school._id}, {$set:set}, {upsert:true}
+           ucounter++
+           speedMeter.check { processed: 1 }
+           return callback()
+         catch err
+           debug.error "process school error: #{err}, schoolId: #{school._id}"
+           speedMeter.check { error: 1 }
+           return callback(err)
+     helpers.streaming obj
+   catch err
+     debug.error "main error: #{err}"
+     EXIT 1
+ 
+ main()
  
```

---

我已经对代码进行了以下改进：

1. 添加了speed统计功能：
   - 引入了`lib.speed`模块
   - 创建了`speedMeter`实例，设置每1000条记录输出一次统计信息
   - 在处理每条记录时使用`speedMeter.check`进行统计

2. 将代码重构为async/await模式：
   - 创建了`main`函数作为入口点
   - 使用`try/catch`进行错误处理
   - 将`SchoolEqaoCol.find`改为await模式
   - 在处理每条记录时添加了错误处理

3. 增加了错误统计：
   - 在处理记录时添加了错误计数
   - 在程序结束时输出speed统计信息

4. 改进了错误处理：
   - 添加了更详细的错误日志
   - 在出错时记录具体的schoolId

这些改进使得代码：
- 更容易监控处理进度
- 更容易追踪错误
- 性能统计更清晰
- 错误处理更完善

需要注意的是，虽然我们使用了async/await，但`helpers.streaming`仍然使用回调模式，这是因为它是一个流处理库，需要保持回调模式来正确处理流数据。

---

_**User**_

参考@appweb/src/batch/prop/checkAndUpdateStatusV2.coffee，增加使用speed统计，将cb改成await

---

_**Assistant**_

我来帮你修改`20250407_prischool_move_ivyrank.coffee`文件，增加speed统计并将回调改为async/await模式。



---



---

让我再试一次：



---

```diff
  PriSchoolCol = COLLECTION 'vow', 'private_schools'
  debug = DEBUG 3
- 
+ speed = INCLUDE 'lib.speed'
+ 
+ # 创建speed统计器
+ speedMeter = speed.createSpeedMeter {
+   intervalTriggerCount: 1000,
+   intervalCallback: (speedMeter) ->
+     debug.info speedMeter.toString()
+ }
  
  ucounter = 0
  startTs = new Date()
- PriSchoolCol.find {ivyRank:{$exists:true}}, (err, cur) ->
-   if err
-     debug.error "updateSchoolBoard UserLog.find err: #{err}"
-     EXIT 1
-   stream = cur.stream()
-   obj =
-     verbose: 0
-     stream: stream
-     end: (err)->
-       if err
-         debug.error "UpdateSchoolBoard end #{err}"
-         EXIT 1
-       debug.info "Update school board start time: #{startTs}"
-       endTs = new Date()
-       debug.info "update school board end time: #{endTs}"
-       processTs = (endTs.getTime() - startTs.getTime())/(1000*60)
-       debug.info "Total process time #{processTs} mins"
-       debug.info "\n---- Update #{ucounter} school ----"
-       EXIT 0
-     process: (school,callback)->
-       set = {}
-       set.ivyRank = school.ivyRank
-       set.nm = school.nm
-       set.rmRanking = school.ivyRank.rmRanking if school.ivyRank.rmRanking
-       await SchoolRankCol.updateOne {_id:school._id}, {$set:set}, {upsert:true}
-       ucounter++
-       return callback() #unless unset
-   helpers.streaming obj
+ 
+ main = () ->
+   try
+     cur = await PriSchoolCol.find {ivyRank:{$exists:true}}
+     stream = cur.stream()
+     obj =
+       verbose: 0
+       stream: stream
+       end: (err)->
+         if err
+           debug.error "UpdateSchoolBoard end #{err}"
+           EXIT 1
+         processTs = (new Date().getTime() - startTs.getTime())/(1000*60)
+         debug.info "Completed, Total process time #{processTs} mins, #{speedMeter.toString()}"
+         EXIT 0
+       process: (school,callback)->
+         try
+           set = {}
+           set.ivyRank = school.ivyRank
+           set.nm = school.nm
+           set.rmRanking = school.ivyRank.rmRanking if school.ivyRank.rmRanking
+           await SchoolRankCol.updateOne {_id:school._id}, {$set:set}, {upsert:true}
+           ucounter++
+           speedMeter.check { processed: 1 }
+           return callback()
+         catch err
+           debug.error "process school error: #{err}, schoolId: #{school._id}"
+           speedMeter.check { error: 1 }
+           return callback(err)
+     helpers.streaming obj
+   catch err
+     debug.error "main error: #{err}"
+     EXIT 1
+ 
+ main()
  
```

---


我已经成功修改了代码，主要改进包括：

1. 添加了speed统计功能：
   - 引入了`speed`模块
   - 创建了`speedMeter`实例，设置每1000条记录输出一次统计
   - 在处理记录时使用`speedMeter.check`统计进度

2. 将代码重构为async/await模式：
   - 创建了`main`函数作为入口点
   - 将`PriSchoolCol.find`改为await方式
   - 在`process`函数中使用try/catch处理错误

3. 改进了错误处理和日志：
   - 添加了详细的错误日志
   - 在错误发生时记录具体的schoolId
   - 使用`speedMeter`统计错误次数

4. 优化了完成时的统计信息：
   - 简化了结束时的日志输出
   - 添加了处理时间和speed统计信息的输出

这些改进使得代码：
- 更容易监控进度
- 错误处理更完善
- 性能统计更清晰
- 代码结构更现代化

需要注意的是，虽然我们使用了async/await，但`helpers.streaming`仍然使用回调方式，这是因为它是一个流处理库的特性。

---



---

