<!-- Generated by SpecStory -->

# Function Error: filterOtherThanTel Implementation (2025-03-25 23:45:14)

_**User**_

 filterOtherThanTel is not a function

---

_**Assistant**_

根据错误信息，看起来 `filterOtherThanTel` 函数未定义。这个函数应该是用来过滤和格式化电话号码的。让我为您提供一个实现建议。

我们可以在代码中添加这个函数的实现：

```coffee:appweb/src/batch/school/generateSchMerged.coffee
# ... existing code ...

###
过滤电话号码，只保留数字和必要的符号
@param {String} tel - 输入的电话号码
@returns {String} 格式化后的电话号码
###
filterOtherThanTel = (tel) ->
  return '' unless tel
  
  # 将电话号码转换为字符串
  telStr = String(tel)
  
  # 只保留数字、加号(+)、连字符(-)和括号
  filtered = telStr.replace(/[^0-9+\-()]/g, '')
  
  # 移除多余的空格
  filtered = filtered.trim()
  
  return filtered

# ... existing code ...
```

这个实现：
1. 检查输入是否存在，如果不存在返回空字符串
2. 将输入转换为字符串
3. 使用正则表达式只保留数字、加号、连字符和括号
4. 移除首尾空格
5. 返回格式化后的电话号码

您需要将这个函数添加到代码中，建议放在其他辅助函数的附近。这样在 `processSchool` 函数中调用 `filterOtherThanTel` 时就不会报错了。

如果您的代码库中已经有类似的电话号码处理函数，您也可以考虑使用现有的函数。如果需要查找现有的实现，您可以使用 agent 模式来搜索代码库。

---

_**User**_

{unifyAddress,filterOtherThanTel} = INCLUDE 'lib.helpers_string'

---

_**Assistant**_

明白了，看起来 `filterOtherThanTel` 函数实际上是在 `lib.helpers_string` 模块中定义的。让我帮您修改引入语句。

```coffee:appweb/src/batch/school/generateSchMerged.coffee
# ... existing code ...

# 引入辅助函数
{filterMergeFields, separateUnitFromAddr} = INCLUDE 'libapp.schoolHelper'
{matchSchoolInfo} = INCLUDE 'libapp.schoolHelper'
{judgeIsSameSchool, judgeIsSameStreet, generateSchoolName} = INCLUDE 'libapp.schoolHelper'
{unifyAddress, filterOtherThanTel} = INCLUDE 'lib.helpers_string'

# ... existing code ...
```

这个修改：
1. 从原来的 `libapp.schoolHelper` 中移除了 `unifyAddress` 的引入
2. 添加了从 `lib.helpers_string` 模块引入 `unifyAddress` 和 `filterOtherThanTel` 函数

这样应该就能正确引入 `filterOtherThanTel` 函数了。确保 `lib.helpers_string` 模块中确实包含了这两个函数的实现。

---

_**User**_

{separateUnitFromAddr,parseSfxAndDir} = INCLUDE 'lib.propAddress'

---

_**Assistant**_

好的，我来帮您更新引入语句。我们需要从 `lib.propAddress` 模块引入 `separateUnitFromAddr` 函数，而不是从 `libapp.schoolHelper` 引入。

```coffee:appweb/src/batch/school/generateSchMerged.coffee
# ... existing code ...

# 引入辅助函数
{matchSchoolInfo} = INCLUDE 'libapp.schoolHelper'
{judgeIsSameSchool, filterMergeFields, judgeIsSameStreet, generateSchoolName} = INCLUDE 'libapp.schoolHelper'
{unifyAddress, filterOtherThanTel} = INCLUDE 'lib.helpers_string'
{separateUnitFromAddr, parseSfxAndDir} = INCLUDE 'lib.propAddress'

# ... existing code ...
```

这个修改：
1. 从 `libapp.schoolHelper` 中移除了 `separateUnitFromAddr` 的引入
2. 添加了从 `lib.propAddress` 模块引入 `separateUnitFromAddr` 和 `parseSfxAndDir` 函数

这样应该就能正确引入所需的地址处理函数了。确保 `lib.propAddress` 模块中包含了这些函数的实现。

---

_**User**_

修改逻辑，先处理数据保存到merged表之后再根据city，prov，nm，等信息匹配eqao和fraser数据，findschool数据里面可能有fraserId，过滤出数字可以匹配eqao表数据

---

_**Assistant**_

好的，我来帮您修改处理逻辑。主要需要调整 `processSchools` 函数的处理流程，并添加新的匹配逻辑。

```coffee:appweb/src/batch/school/generateSchMerged.coffee
###
匹配外部数据
@param {Object} school - 学校数据对象
@returns {Promise<Object>} 匹配结果
###
matchExternalData = (school)->
  try
    # 1. 如果原始数据中有fraserId，先尝试通过它匹配
    if school.fraserId
      # 提取fraserId中的数字
      fraserId = String(school.fraserId).replace(/\D/g, '')
      if fraserId
        # 通过fraserId查找fraser数据
        fraserMatch = await SchFraserCol.findOne({_id: fraserId})
        if fraserMatch
          school.fraserId = fraserMatch._id
          # 通过fraserId查找eqao数据
          eqaoMatch = await SchEqaoCol.findOne({fraserId: fraserMatch._id})
          if eqaoMatch
            school.eqaoId = eqaoMatch._id
            return school

    # 2. 通过学校信息匹配
    let query = {
      prov: school.prov,
      city: school.city
    }
    
    # 如果有邮编则加入匹配条件
    if school.zip
      query.zip = school.zip
    
    # 尝试匹配fraser数据
    fraserMatch = await SchFraserCol.findOne(query)
    if fraserMatch
      matchResult = matchSchoolInfo(school, fraserMatch)
      if matchResult.schoolMatched
        school.fraserId = fraserMatch._id
        # 通过fraserId查找eqao数据
        eqaoMatch = await SchEqaoCol.findOne({fraserId: fraserMatch._id})
        if eqaoMatch
          school.eqaoId = eqaoMatch._id
    
    # 尝试匹配eqao数据
    if not school.eqaoId
      eqaoMatch = await SchEqaoCol.findOne(query)
      if eqaoMatch
        matchResult = matchSchoolInfo(school, eqaoMatch)
        if matchResult.schoolMatched
          school.eqaoId = eqaoMatch._id
          # 通过eqaoId查找fraser数据
          if not school.fraserId
            fraserMatch = await SchFraserCol.findOne({eqaoId: eqaoMatch._id})
            if fraserMatch
              school.fraserId = fraserMatch._id
    
    return school
  catch err
    debug.error "匹配外部数据失败: #{err}"
    throw err

###
主处理函数
###
processSchools = ->
  try
    counter = 0
    mergedCounter = 0
    matchedCounter = 0
    startTs = new Date()
    
    if dryRun
      console.log "运行在测试模式(dry run),不会修改数据库"
    
    # 1. 从sch_findschool表获取数据并处理
    SchFindschoolCol.find {}, (err, cursor)->
      return EXIT(err) if err
      
      stream = cursor.stream()
      obj =
        verbose: 1
        stream: stream
        high: 1
        end: (err)->
          if err
            debug.error "Error in end: #{err}"
            return EXIT(err)
          
          endTs = new Date()
          processTs = (endTs.getTime() - startTs.getTime())/(1000)
          console.log "统计结果:"
          console.log "----------------------------------------"
          console.log "处理开始时间: #{startTs}"
          console.log "处理结束时间: #{endTs}"
          console.log "总处理时间: #{processTs}秒"
          console.log "处理学校总数: #{counter}所"
          console.log "成功合并学校数: #{mergedCounter}所"
          console.log "成功匹配外部数据数: #{matchedCounter}所"
          console.log "----------------------------------------"
          return EXIT(0)
        
        process: (school, callback)->
          counter++
          
          try
            # 1. 处理学校基础数据
            processedSchool = processSchool(school)
            
            # 2. 检查是否已存在
            existingSchool = await SchMergedCol.findOne({
              nm: processedSchool.nm,
              prov: processedSchool.prov
            })
            
            # 3. 生成或使用现有ID
            if existingSchool
              processedSchool._id = existingSchool._id
            else
              processedSchool._id = await generateSchoolId(processedSchool)
            
            # 4. 保存基础数据
            if not dryRun
              await SchMergedCol.updateOne(
                {_id: processedSchool._id},
                {$set: processedSchool},
                {upsert: true}
              )
            mergedCounter++
            
            # 5. 匹配外部数据
            processedSchool = await matchExternalData(processedSchool)
            
            # 6. 如果匹配到外部数据，更新学校记录
            if processedSchool.eqaoId or processedSchool.fraserId
              if not dryRun
                await SchMergedCol.updateOne(
                  {_id: processedSchool._id},
                  {
                    $set: {
                      eqaoId: processedSchool.eqaoId,
                      fraserId: processedSchool.fraserId
                    }
                  }
                )
              matchedCounter++
            
            callback()
          catch err
            debug.error "处理学校 #{school._id} 失败: #{err}"
            callback(err)
      
      helpers.streaming obj

# ... existing code ...
```

主要修改内容：
1. 调整了处理流程：
   - 先处理和保存基础数据
   - 再进行外部数据匹配
   - 如果匹配到外部数据，再次更新记录

2. 增强了 `matchExternalData` 函数：
   - 首先尝试通过原始数据中的 fraserId 进行匹配
   - 提取 fraserId 中的数字
   - 然后通过城市、省份等信息进行匹配
   - 如果有邮编信息，加入匹配条件提高准确性

3. 添加了新的统计：
   - 增加了 `matchedCounter` 来统计成功匹配外部数据的数量
   - 在统计结果中显示匹配成功数

这样的处理流程更加清晰，且能更好地处理和跟踪外部数据的匹配情况。

---

_**User**_

更新文档

---

_**Assistant**_

我来帮您更新文档注释，使其更准确地反映新的处理流程。

```coffee:appweb/src/batch/school/generateSchMerged.coffee
###
生成sch_rm_merged表的批处理脚本
该脚本将处理以下步骤：
1. 从sch_findschool表读取学校数据
2. 标准化处理学校基础信息（地址、电话、名称等）
3. 保存处理后的数据到sch_rm_merged_create表
4. 尝试匹配外部数据：
   - 优先使用原始数据中的fraserId匹配
   - 根据地理位置信息(省份、城市、邮编)匹配sch_eqao和sch_fraser表
5. 更新匹配到的外部数据ID

Author:         liurui
Usage:          sh start.sh -t batch -n generateSchMerged -cmd "lib/batchBase.coffee batch/school/generateSchMerged.coffee dryrun"
Run duration:   5min
Run frequency:  when needed
Created:        2025/03/24
Modified:       2025/03/25 - 调整处理流程，优化外部数据匹配逻辑
###

# ... existing code ...

###
匹配外部数据
@param {Object} school - 学校数据对象
@param {String} school.prov - 省份代码
@param {String} school.city - 城市名称
@param {String} [school.zip] - 邮政编码
@param {String} [school.fraserId] - 原始Fraser报告ID
@returns {Promise<Object>} 返回添加了eqaoId和fraserId的学校数据
###
matchExternalData = (school)->
  # ... existing code ...

###
处理单个学校数据
@param {Object} school - 学校数据对象
@returns {Promise<Object>} 处理后的标准化学校数据，包含：
  - 标准化的学校名称
  - 格式化的地址（分离单元号）
  - 格式化的电话和传真号码
###
processSchool = (school)->
  # ... existing code ...

###
主处理函数
处理流程：
1. 读取sch_findschool表数据
2. 对每条数据进行标准化处理
3. 保存基础数据到merged表
4. 匹配外部数据源
5. 更新外部数据ID
###
processSchools = ->
  # ... existing code ...
```

主要更新内容：
1. 更新了脚本顶部的主文档，详细说明了处理步骤
2. 添加了修改日期和修改内容说明
3. 完善了各个函数的JSDoc注释，包括：
   - 参数说明更详细
   - 返回值说明更具体
   - 添加了可选参数标记
4. 为主处理函数添加了处理流程说明

这样的文档更清晰地展示了脚本的功能和处理流程，方便其他开发者理解和维护代码。

---

_**User**_

更新md文档

---

_**Assistant**_

我会帮您更新文档，主要是根据新的处理流程来修改内容。以下是修改建议：

```markdown:appweb/docs/Dev_school/sch_rm_merged生成流程.md
# 学校数据批处理流程

本文档描述了生成`sch_rm_merged`表的批处理流程。

## 目录
- [学校数据批处理流程](#学校数据批处理流程)
  - [目录](#目录)
  - [处理流程概述](#处理流程概述)
  - [详细步骤](#详细步骤)
    - [1. 基础数据处理](#1-基础数据处理)
    - [2. 数据标准化](#2-数据标准化)
    - [3. 外部数据匹配](#3-外部数据匹配)
    - [4. 数据更新/插入](#4-数据更新插入)
    - [5. ID生成规则](#5-id生成规则)
  - [示例](#示例)

## 处理流程概述

批处理流程从`sch_findschool`表读取学校信息，经过标准化处理后保存到`sch_rm_merged`表，并与外部评估数据（EQAO和Fraser）进行匹配。

```mermaid
%%{
  init: {
    'theme': 'base',
    'themeVariables': {
      'fontSize': '16px',
      'fontFamily': 'arial'
    }
  }
}%%
flowchart TD
    %% 基础数据处理流程
    subgraph 基础数据处理
        A1[sch_findschool表] --> B1[读取数据]
        B1 --> C1[filterMergeFields过滤]
        C1 --> D1[标准化处理]
        
        D1 --> |过滤电话传真| E1[filterOtherThanTel]
        D1 --> |处理地址| E2[separateUnitFromAddr]
        D1 --> |统一地址| E3[unifyAddress]
        D1 --> |标准化名称| E4[generateSchoolName]
        
        E1 & E2 & E3 & E4 --> F1[处理后的数据]
    end

    %% 外部数据匹配流程
    subgraph 外部数据匹配
        F1 --> G1{检查fraserId}
        G1 --> |有| H1[直接匹配Fraser]
        G1 --> |无| H2[地理位置匹配]
        
        H1 & H2 --> I1[Fraser匹配结果]
        I1 --> J1[EQAO关联匹配]
        
        J1 --> K1[记录外部ID]
    end

    %% 数据保存流程
    subgraph 数据保存
        K1 --> L1{检查是否存在}
        L1 --> |存在| M1[使用现有ID]
        L1 --> |不存在| M2[生成新ID]
        
        M2 --> |读取计数器| N1[(sysdata表)]
        N1 --> |更新计数器| M2
        
        M1 & M2 --> O1[更新merged表]
    end

    %% 样式设置
    classDef primary fill:#f9f,stroke:#333,stroke-width:2px
    classDef process fill:#bbf,stroke:#333,stroke-width:2px
    classDef decision fill:#bfb,stroke:#333,stroke-width:2px
    classDef database fill:#ff9,stroke:#333,stroke-width:2px
    
    class A1 primary
    class B1,C1,D1,E1,E2,E3,E4,F1,H1,H2,I1,J1,K1 process
    class G1,L1 decision
    class N1 database
```

## 详细步骤

### 1. 基础数据处理

从`sch_findschool`表读取学校数据，进行基础处理：
- 使用`filterMergeFields`函数过滤和提取必要字段
- 保留原始数据中的`fraserId`字段（如果存在）

### 2. 数据标准化

对学校数据进行标准化处理：
- 电话和传真号码格式化（使用`filterOtherThanTel`）
  - 只保留数字、加号(+)、连字符(-)和括号
  - 移除多余空格
- 地址处理
  - 分离单元号（使用`separateUnitFromAddr`）
  - 统一地址格式（使用`unifyAddress`）
- 学校名称标准化（使用`generateSchoolName`）

### 3. 外部数据匹配

匹配策略：
1. 优先使用原始数据中的`fraserId`
   - 如果存在，直接查找对应的Fraser报告数据
   - 通过Fraser数据关联查找EQAO数据

2. 如果没有`fraserId`或匹配失败，使用地理位置信息匹配：
   - 按省份（prov）和城市（city）进行初步筛选
   - 使用`matchSchoolInfo`函数进行详细匹配
   - 匹配成功后记录相应的`eqaoId`和`fraserId`

匹配条件：
- `schoolMatched`: 基本信息匹配
- `gradeMatched`: 年级信息匹配

### 4. 数据更新/插入

保存策略：
1. 检查`sch_rm_merged`表是否存在该学校（按`nm`和`prov`匹配）
2. 如果存在：
   - 使用现有ID
   - 更新其他字段
3. 如果不存在：
   - 生成新ID
   - 插入完整记录

### 5. ID生成规则

[保持原有ID生成规则部分不变...]

## 示例

```javascript
// 原始数据
{
  _id: 1001,
  nm: "ABC School",
  addr: "Unit 2B, 123 Main St",
  tel: "(************* ext 890",
  fraserId: "F2024001"
}

// 标准化处理后
{
  nm: "ABC School",
  unit: "2B",
  addr: "123 Main St",
  tel: "************",
  fraserId: "F2024001",
  eqaoId: "E2024001"  // 通过Fraser关联获取
}

// 最终保存
{
  _id: "ON000160",
  nm: "ABC School",
  unit: "2B",
  addr: "123 Main St",
  tel: "************",
  fraserId: "F2024001",
  eqaoId: "E2024001"
}
```
```

主要更新内容：
1. 修改了处理流程图，反映新的处理顺序
2. 更新了外部数据匹配策略，优先使用原始fraserId
3. 简化了数据处理步骤的描述
4. 添加了更具体的示例代码
5. 调整了文档结构，使其更符合实际处理流程

---

_**User**_

将处理好的allSchools数据保存到WaitUpdateCol中，查找WaitUpdateCol表中数据，按照nm排序从a-z排序返回，进程结束时删除WaitUpdateCol表

---

_**Assistant**_

我来帮您修改代码，添加临时表处理逻辑。

```coffee:appweb/src/batch/school/generateSchMerged.coffee
# 添加临时表集合
WaitUpdateCol = COLLECTION 'vow', 'sch_wait_update'

# ... existing code ...

processSchools = ->
  try
    counter = 0
    startTs = new Date()

    if dryRun
      console.log "运行在测试模式(dry run),不会修改数据库"

    # 获取学校计数器和更新时间
    {counters, updated_at} = await getSchoolCounter()

    # 初始化处理
    query = {}
    if isInit
      await initTables()
      # 确保临时表为空
      if !dryRun
        await WaitUpdateCol.deleteMany({})
    else
      query._mt = {$gt: updated_at}

    # 从两个数据源获取数据
    [manualSchools, findSchools] = await Promise.all([
      SchManualCol.find(query).toArray(),
      SchFindschoolCol.find(query).toArray()
    ])

    # 合并数据源
    allSchools = mergeSchoolData(manualSchools, findSchools)
    
    # 保存到临时表
    if !dryRun
      await WaitUpdateCol.insertMany(allSchools)
    
    # 从临时表按名称排序读取数据
    WaitUpdateCol.find({}).sort({nm: 1}, (err, cursor)->
      return EXIT(err) if err
      
      stream = cursor.stream()
      # 使用streaming处理数据
      obj =
        verbose: 1
        stream: stream
        high: 10
        end: (err) ->
          if err
            debug.error "Error in end: #{err}"
            return EXIT(err)
          
          try
            # 批处理完成后更新计数器
            if !dryRun
              await updateSchoolCounter(counters)
              # 删除临时表
              await WaitUpdateCol.deleteMany({})
            
            endTs = new Date()
            processTs = (endTs.getTime() - startTs.getTime())/(1000)
            console.log "统计结果:"
            console.log "----------------------------------------"
            console.log "处理开始时间: #{startTs}"
            console.log "处理结束时间: #{endTs}"
            console.log "总处理时间: #{processTs}秒"
            console.log "处理学校总数: #{counter}所"
            console.log "----------------------------------------"
            return EXIT(0)
          catch cleanupErr
            debug.error "清理临时数据失败: #{cleanupErr}"
            return EXIT(cleanupErr)
            
        process: (school, callback) ->
          try
            counter++
            # 处理学校数据
            await processSchool(school, callback)
          catch err
            debug.error "处理学校数据失败: #{err}", school
            callback(err)

      helpers.streaming obj
    )
  catch err
    debug.error "处理学校数据主流程出错: #{err}"
    # 确保在出错时也清理临时表
    try
      if !dryRun
        await WaitUpdateCol.deleteMany({})
    catch cleanupErr
      debug.error "清理临时数据失败: #{cleanupErr}"
    EXIT(1)
```

主要修改内容：
1. 添加了临时表集合 `WaitUpdateCol`
2. 在初始化时清空临时表
3. 将合并后的数据保存到临时表
4. 从临时表按学校名称（nm）升序读取数据进行处理
5. 在处理完成或出错时删除临时表
6. 添加了错误处理，确保临时表能被正确清理

这样处理的好处是：
1. 数据按名称有序处理
2. 避免内存中存储大量数据
3. 利用数据库的排序功能
4. 确保临时数据不会残留在数据库中

注意：
- 所有对临时表的操作都会遵循 `dryRun` 模式
- 添加了额外的错误处理，确保临时表能被正确清理
- 使用 `deleteMany` 而不是 `drop` 以避免权限问题

---

_**User**_

修改到appweb/src/batch/school/generateSchRmMerged.coffee文件中

---

_**Assistant**_

好的，我来帮您修改 `generateSchRmMerged.coffee` 文件。这个文件看起来是新的目标文件。

```coffee:appweb/src/batch/school/generateSchRmMerged.coffee
###
生成sch_rm_merged表的批处理脚本
该脚本将处理以下步骤：
1. 从sch_findschool表和sch_rm_manual表读取学校数据
2. 标准化处理学校基础信息（地址、电话、名称等）
3. 保存处理后的数据到sch_rm_merged_create表
4. 尝试匹配外部数据：
   - 优先使用原始数据中的fraserId匹配
   - 根据地理位置信息(省份、城市、邮编)匹配sch_eqao和sch_fraser表
5. 更新匹配到的外部数据ID

Author:         liurui
Usage:          sh start.sh -t batch -n generateSchMerged -cmd "lib/batchBase.coffee batch/school/generateSchRmMerged.coffee [init|routine] [dryrun]"
Run duration:   5min
Run frequency:  when needed
Created:        2025/03/24
###

# 引入必要的集合
SchFindschoolCol = COLLECTION 'vow', 'sch_findschool'
SchManualCol = COLLECTION 'vow', 'sch_rm_manual'
SchMergedCol = COLLECTION 'vow', 'sch_rm_merged_create'
SchEqaoCol = COLLECTION 'vow', 'sch_eqao'
SchFraserCol = COLLECTION 'vow', 'sch_fraser'
SysDataCol = COLLECTION 'vow', 'sysdata'

# 添加临时表集合
WaitUpdateCol = COLLECTION 'vow', 'sch_wait_update'

# 引入辅助函数
{matchSchoolInfo,filterMergeFields, generateSchoolName} = INCLUDE 'libapp.schoolHelper'
{unifyAddress, filterOtherThanTel} = INCLUDE 'lib.helpers_string'
{separateUnitFromAddr} = INCLUDE 'lib.propAddress'

debug = DEBUG()
helpers = INCLUDE 'lib.helpers'

# 检查运行参数
isInit = AVGS.indexOf('init') >= 0
isRoutine = AVGS.indexOf('routine') >= 0


---



---

