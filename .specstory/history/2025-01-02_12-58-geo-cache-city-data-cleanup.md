<!-- Generated by SpecStory -->

# Geo Cache City Data Cleanup (2025-01-02 20:58:45)

_**User**_

如果splitCities中有部分重复的数据，删除cityMap对应的数据

---

_**Assistant**_

要在 `splitCities` 中删除重复的数据，并相应地从 `cityMap` 中删除对应的城市，可以在处理 `splitCities` 后添加一些逻辑。以下是修改后的代码：

```appweb/src/libapp/propertiesTranslate.coffee
module.exports.getFormatCityName = getFormatCityName = (prop, locale) ->
  {_ab,l10n} = getTranslator(locale)
  cityMap = {}
  cities = [prop.origCity, prop.city_en, prop.area].filter(Boolean) # 过滤掉空值
  console.log("🚀 ~ cities:", cities)
  uniqueCities = new Set()

  for city in cities
    cityLower = city.toLowerCase().replace(/[^\w\s]/g, '') # 转为小写并去除特殊字符
    console.log("🚀 ~ cityLower:", cityLower)
    uniqueCities.add(cityLower)
    cityMap[cityLower] = city
  console.log("🚀 ~ uniqueCities:", uniqueCities)

  # 检查唯一性
  if uniqueCities.size == 1
    return l10n(cityMap[Array.from(uniqueCities)[0]]) # 只有一个唯一值

  # 处理包含关系
  sortedCities = Array.from(uniqueCities).sort((a, b) -> a.length - b.length) # 按长度降序排序
  console.log("🚀 ~ sortedCities:", sortedCities)

  # 以空格分割城市名
  splitCities = sortedCities.map((city) -> city.split(' '))
  console.log("🚀 ~ splitCities:", splitCities)

  # 删除重复的城市
  uniqueSplitCities = new Set()
  for cityArray in splitCities
    for city in cityArray
      uniqueSplitCities.add(city)

  # 更新 cityMap，删除重复的城市
  for city in uniqueCities
    if not uniqueSplitCities.has(city)
      delete cityMap[city]

  # 如果没有包含关系，返回拼接的字符串
  return Object.values(cityMap).map((city) -> l10n(city)).join(', ')
```

在这个修改中，我们首先创建了一个 `uniqueSplitCities` 的集合来存储 `splitCities` 中的唯一城市。然后，我们遍历 `uniqueCities`，如果某个城市不在 `uniqueSplitCities` 中，就从 `cityMap` 中删除该城市。这样可以确保 `cityMap` 中只保留不重复的城市。

---

_**User**_

splitCities中第一层数据为一组，是通过空格分割的，需要在数组中和其他值对比，判断是否有部分数据一致，第二层array是判断的标准，如果第二层array中有一致部分，则删除cityMap中的数据

---

_**Assistant**_

明白了，您希望在 `splitCities` 的第一层数组中与其他城市进行比较，判断是否有部分数据一致。如果在第二层数组中有一致的部分，则从 `cityMap` 中删除对应的城市。以下是修改后的代码：

```appweb/src/libapp/propertiesTranslate.coffee
module.exports.getFormatCityName = getFormatCityName = (prop, locale) ->
  {_ab,l10n} = getTranslator(locale)
  cityMap = {}
  cities = [prop.origCity, prop.city_en, prop.area].filter(Boolean) # 过滤掉空值
  console.log("🚀 ~ cities:", cities)
  uniqueCities = new Set()

  for city in cities
    cityLower = city.toLowerCase().replace(/[^\w\s]/g, '') # 转为小写并去除特殊字符
    console.log("🚀 ~ cityLower:", cityLower)
    uniqueCities.add(cityLower)
    cityMap[cityLower] = city
  console.log("🚀 ~ uniqueCities:", uniqueCities)

  # 检查唯一性
  if uniqueCities.size == 1
    return l10n(cityMap[Array.from(uniqueCities)[0]]) # 只有一个唯一值

  # 处理包含关系
  sortedCities = Array.from(uniqueCities).sort((a, b) -> a.length - b.length) # 按长度降序排序
  console.log("🚀 ~ sortedCities:", sortedCities)

  # 以空格分割城市名
  splitCities = sortedCities.map((city) -> city.split(' '))
  console.log("🚀 ~ splitCities:", splitCities)

  # 删除 cityMap 中与 splitCities 中有部分一致的城市
  for cityKey in uniqueCities
    for cityArray in splitCities
      if cityArray.some((part) -> cityKey.includes(part))
        delete cityMap[cityKey]
        break # 找到一致部分后，跳出内层循环

  # 如果没有包含关系，返回拼接的字符串
  return Object.values(cityMap).map((city) -> l10n(city)).join(', ')
```

### 代码说明：
1. **分割城市名**：使用空格分割城市名并存储在 `splitCities` 中。
2. **删除逻辑**：遍历 `uniqueCities` 中的每个城市名，并与 `splitCities` 中的每个数组进行比较。如果 `cityKey` 中包含 `cityArray` 中的任何部分，则从 `cityMap` 中删除该城市名。
3. **跳出循环**：一旦找到一致的部分，就跳出内层循环，以提高效率。

这样，您就可以确保 `cityMap` 中只保留没有部分一致的城市。

---

_**User**_

需要判断cityArray和cityKey 不是同一个城市

---

_**Assistant**_

为了确保在删除 `cityMap` 中的城市时，`cityArray` 和 `cityKey` 不是同一个城市，我们可以在比较时添加一个额外的条件。以下是修改后的代码：

```appweb/src/libapp/propertiesTranslate.coffee
module.exports.getFormatCityName = getFormatCityName = (prop, locale) ->
  {_ab,l10n} = getTranslator(locale)
  cityMap = {}
  cities = [prop.origCity, prop.city_en, prop.area].filter(Boolean) # 过滤掉空值
  console.log("🚀 ~ cities:", cities)
  uniqueCities = new Set()

  for city in cities
    cityLower = city.toLowerCase().replace(/[^\w\s]/g, '') # 转为小写并去除特殊字符
    console.log("🚀 ~ cityLower:", cityLower)
    uniqueCities.add(cityLower)
    cityMap[cityLower] = city
  console.log("🚀 ~ uniqueCities:", uniqueCities)

  # 检查唯一性
  if uniqueCities.size == 1
    return l10n(cityMap[Array.from(uniqueCities)[0]]) # 只有一个唯一值

  # 处理包含关系
  sortedCities = Array.from(uniqueCities).sort((a, b) -> a.length - b.length) # 按长度降序排序
  console.log("🚀 ~ sortedCities:", sortedCities)

  # 以空格分割城市名
  splitCities = sortedCities.map((city) -> city.split(' '))
  console.log("🚀 ~ splitCities:", splitCities)

  # 删除 cityMap 中与 splitCities 中有部分一致的城市
  for cityKey in uniqueCities
    for cityArray in splitCities
      # 确保 cityKey 和 cityArray 不是同一个城市
      if cityKey != cityArray.join(' ') and cityArray.some((part) -> cityKey.includes(part))
        delete cityMap[cityKey]
        break # 找到一致部分后，跳出内层循环

  # 如果没有包含关系，返回拼接的字符串
  return Object.values(cityMap).map((city) -> l10n(city)).join(', ')
```

### 代码修改说明：
1. **添加条件**：在判断 `cityKey` 和 `cityArray` 时，增加了 `cityKey != cityArray.join(' ')` 的条件，以确保它们不是同一个城市。
2. **逻辑保持不变**：其他逻辑保持不变，依然会在找到部分一致的情况下删除 `cityMap` 中的城市。

这样可以确保只有在 `cityKey` 和 `cityArray` 不是同一个城市的情况下，才会进行删除操作。

---



---

